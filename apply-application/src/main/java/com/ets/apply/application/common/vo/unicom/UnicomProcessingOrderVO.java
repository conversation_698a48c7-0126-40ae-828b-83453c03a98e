package com.ets.apply.application.common.vo.unicom;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class UnicomProcessingOrderVO {

    /**
     * 申请单号
     */
    private String unicomApplySn;

    /**
     * 用户uid
     */
    private Long uid;

    /**
     * 办理手机号码（加密）
     */
    private String phone;

    /**
     * 号卡订单状态，1激活2退单3销户4首充达标5待开户6累充达标
     */
    private Integer unicomOrderStatus;

    /**
     * 申请状态
     */
    private Integer status;

    /**
     * 前端显示状态
     */
    private Integer frontendStatus;

    /**
     * 是否已首冲200
     */
    private Boolean rechargeComplete;

    /**
     * 号卡资格有效期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expireDate;

    /**
     * 拒绝原因
     */
    private String rejectInfo;
}
