package com.ets.apply.application.infra.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 请求第三方接口日志
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("third_interface_log")
public class ThirdInterfaceLogEntity extends BaseEntity<ThirdInterfaceLogEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 请求方法
     */
    private String logMethod;

    /**
     * 请求参数
     */
    private String logParams;

    /**
     * 请求接口
     */
    private String logRequest;

    /**
     * 返回结果
     */
    private String logRespone;

    /**
     * 结果，0异常1正常
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;


}
