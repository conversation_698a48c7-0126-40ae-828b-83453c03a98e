package com.ets.apply.application.common.consts.orderBenefitRecord;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum OrderBenefitRecordStatusEnum {

    DEFAULT(0, "初始化"),
    SUCCESS(1, "成功"),
    FAIL(2, "失败"),
    ;

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;

    static {
        map = Arrays.stream(OrderBenefitRecordStatusEnum.values()).collect(Collectors.toMap(OrderBenefitRecordStatusEnum::getValue, OrderBenefitRecordStatusEnum::getDesc));
    }
}
