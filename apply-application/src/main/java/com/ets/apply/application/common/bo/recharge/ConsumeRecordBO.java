package com.ets.apply.application.common.bo.recharge;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ConsumeRecordBO {

    /**
     * 交易流水号
     */
    private String linkNo;

    /**
     * 透支金额
     */
    private Integer inPay;

    /**
     * 交易金额 单位:分
     */
    private Long transMoney;

    /**
     * 交易类型 2: 圈存, 6 圈存冲正,9 过站消费
     */
    private Integer transId;

    /**
     * 终端机编号
     */
    private String terminalId;

    /**
     * 交易时间
     */
    private String inTransTime;
}
