package com.ets.apply.application.common.dto.external.chuanqi;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class ChuanQiAfterSaleCreateDTO {

    @NotBlank(message = "外部订单号不能为空")
    private String orderNo;

    @NotBlank(message = "操作类型不能为空")
    private String operateType;

    /**
     * 问题描述
     */
    private String returnReason;

    private ReturnInfo returnInfo;

    @Data
    public static class ReturnInfo {

        private String logisticsNo;

        /**
         * 寄回的快递公司
         */
        private String logisticsCompany = "默认";

        private String logisticsCode;

    }

}
