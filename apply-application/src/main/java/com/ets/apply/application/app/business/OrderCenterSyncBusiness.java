package com.ets.apply.application.app.business;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ets.apply.application.app.factory.task.TaskFactory;
import com.ets.apply.application.app.thirdservice.feign.CallGoodsApplication;
import com.ets.apply.application.common.bo.orderCenter.OrderCenterApplyBO;
import com.ets.apply.application.common.bo.orderCenter.OrderCenterTaskBO;
import com.ets.apply.application.common.consts.order.AftersaleStatus;
import com.ets.apply.application.common.consts.order.FlowTypeEnum;
import com.ets.apply.application.common.consts.order.SceneEnum;
import com.ets.apply.application.common.consts.order.StatusEnum;
import com.ets.apply.application.common.consts.productPackage.DeviceTypeEnum;
import com.ets.apply.application.common.consts.taskRecord.TaskRecordReferTypeEnum;
import com.ets.apply.application.common.dto.task.TaskRecordDTO;
import com.ets.apply.application.infra.entity.OrderOrderEntity;
import com.ets.apply.application.infra.entity.ProductPackageEntity;
import com.ets.apply.application.infra.service.OrderOrderService;
import com.ets.apply.application.infra.service.ProductPackageService;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;

@Component
public class OrderCenterSyncBusiness {

    @Autowired
    private OrderOrderService orderService;
    @Autowired
    private ProductPackageService productPackageService;
    @Autowired
    private RecoveryBusiness recoveryBusiness;
    @Autowired
    private CallGoodsApplication callJavaGoodsFeign;

    public void orderCenterSync(String orderSn,Boolean checkSequence) {
        OrderOrderEntity order = orderService.getByOrderSn(orderSn);
        OrderCenterApplyBO orderCenterApplyBO = new OrderCenterApplyBO();
        OrderCenterApplyBO.Extra extra = new OrderCenterApplyBO.Extra();
        orderCenterApplyBO.setUid(order.getUid());
        orderCenterApplyBO.setAmount(order.getNeedPay());
        orderCenterApplyBO.setCreateTime(order.getCreatedAt().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        orderCenterApplyBO.setProductType(4);//申办订单
        orderCenterApplyBO.setProductSn(order.getOrderSn());
        orderCenterApplyBO.setStatus(getStatusByOrderStatus(order));
        orderCenterApplyBO.setIsShow(isShow(order));
        orderCenterApplyBO.setCheckSequence(checkSequence);//申办订单
        orderCenterApplyBO.setIsMain(isMain(order));
        orderCenterApplyBO.setPlateNo(order.getPlateNo());
        orderCenterApplyBO.setPlateColor(order.getPlateColor());
        // 客车货车
        if (order.getScene() == SceneEnum.TRUCK.getCode()) {
            extra.setBusinessType(2);
            extra.setBusinessTypeStr("ETC办理（货车）");
        }else{
            extra.setBusinessType(1);
            extra.setBusinessTypeStr("ETC办理（小汽车）");
        }
        extra.setOrderStatus(getCombineStatus(order));
        extra.setPlateNo(order.getPlateNo());
        extra.setPlateColor(order.getPlateColor());
        extra.setActivatedStatus(order.getActivatedStatus());
        extra.setAftersaleStatus(order.getAftersaleStatus());
        if(!StringUtils.isEmpty(order.getPackageSn())){
            ProductPackageEntity productPackage = productPackageService.getBySnWithCache(order.getPackageSn());
            if(productPackage != null){
                orderCenterApplyBO.setProductName(productPackage.getPackageName());
                // 回收按钮
                extra.setShowRecovery(recoveryBusiness.isAllowRecovery(order, productPackage));
                // 设备名称、图片
                if (ObjectUtils.isNotEmpty(productPackage)) {
                    // 产品包获取设备类型
                    JSONObject jsonObject = productPackage.getPackageInfoObj();
                    if (ObjectUtils.isNotEmpty(jsonObject)) {
                        String productImg = jsonObject.getString("product_img");
                        if (ObjectUtils.isNotEmpty(productImg)) {
                            extra.setGoodsImgUrl(productImg);
                        }
                        String deviceName = jsonObject.getString("device_name");
                        if (ObjectUtils.isNotEmpty(deviceName)) {
                            orderCenterApplyBO.setProductName(deviceName);
                        } else {
                            Integer deviceType = jsonObject.getInteger("device_type");
                            orderCenterApplyBO.setProductName(DeviceTypeEnum.nameMap.getOrDefault(deviceType, "一代-经典ETC"));
                        }
                    }
                }
            }

        }
        orderCenterApplyBO.setExtra(extra);
        callJavaGoodsFeign.orderCenterUpdateInfo(orderCenterApplyBO);
    }
    /*
     *    CREATED(1, "已创建","待支付", 1),

          WAIT_PAY(2, "待支付","待支付", 2),

          PROCESSING(3, "进行中","进行中", 3),

          CANCELED(4, "已取消","已完结", 5),

          FINISHED(5, "已完成","已完结", 4);``
     */
    public Integer getStatusByOrderStatus(OrderOrderEntity order){
        if(order.getAftersaleStatus().equals(AftersaleStatus.STATUS_APPLY_FINISH.getValue())){
            return 4;
        }
        Integer returnStatus = 2;
        switch (order.getStatus()){
            case 2:
            case 3:
                returnStatus = 3;
                break;
            case 4:
                if(Arrays.asList(1,2).contains(order.getActivatedStatus())){
                    returnStatus = 5;
                }else{
                    returnStatus = 3;
                }

                break;
            case 5:
                returnStatus = 4;
                break;

        }
        return returnStatus;
    }
    /*
     * 是否在订单中心展示
     *  注销重办的不展示
     */
    public static Boolean isShow(OrderOrderEntity order){
        if(order.getStatus() == StatusEnum.CANCELED.getCode()){
            return false;
        }
        return !FlowTypeEnum.checkIsReapply(order.getFlowType());
    }
    /*
     * 订单状态处理
     */
    public Integer getCombineStatus(OrderOrderEntity order){
        Integer combineStatus = order.getStatus();
        if(
                combineStatus.equals(StatusEnum.WAIT_FOR_RECEIVE.getCode()) &&
            order.getAftersaleStatus().equals(AftersaleStatus.STATUS_APPLY_FINISH.getValue())
        ){
            combineStatus = StatusEnum.FINISH.getCode();
        }
        return combineStatus;
    }
    /*
     * 生成同步的task
     */
    public void addTask(String orderSn,String trigger){
        //塞队列进行发货操作
        TaskRecordDTO taskRecordDTO = new TaskRecordDTO();
        taskRecordDTO.setReferSn(orderSn);
        taskRecordDTO.setReferType(TaskRecordReferTypeEnum.TASK_ORDER_CENTER_SYNC.getType());
        taskRecordDTO.setNextExecTime(LocalDateTime.now().plusSeconds(5));
        taskRecordDTO.setNotifyContent(JSON.toJSONString(new OrderCenterTaskBO(orderSn,trigger)));
        TaskFactory.create(TaskRecordReferTypeEnum.TASK_ORDER_AFTERSALES_APPLY.getType()).addAndPush(taskRecordDTO);
    }
    /*
     * 是否主订单
     */
    public static Boolean isMain(OrderOrderEntity order){
        return FlowTypeEnum.checkIsReapply(order.getFlowType())?false:true;
    }
}
