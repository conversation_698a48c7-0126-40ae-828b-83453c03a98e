package com.ets.apply.application.common.dto.productPackagePopup;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 */
@Data
public class PopupModifyDTO {

    @NotBlank(message = "请输入编号")
    private String popupSn;
    private Integer popupType;

    private String name;


    @Length(max = 200, min = 1, message = "备注长度不能超过200")
    private String remark;

    @Length(max = 200, message = "url长度不能超过200")
    private String url;
}
