package com.ets.apply.application.common.consts.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AftersaleStatus {

    // * 售后状态:0:未售后,1:申请退货退款中,2已退货退款,3:部分退货退款，9:取消售后申请
    STATUS_NORMAL(0, "正常"),
    STATUS_APPLY(1, "申请退货退款中"),
    STATUS_APPLY_FINISH(2, "已退货退款"),
    STATUS_APPLY_PART_FINISH(3, "部分退货退款"),
    STATUS_APPLY_CANCEL(9, "取消售后申请");

    private final Integer value;
    private final String desc;
}
