package com.ets.apply.application.app.business.sales;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.apply.application.app.thirdservice.feign.CallPhpApplyFeign;
import com.ets.apply.application.common.bo.adminSaleUpgrade.SaleUpgradeRuleConditionBO;
import com.ets.apply.application.common.consts.salesUpgrade.SaleCacheMainKeyEnum;
import com.ets.apply.application.common.consts.salesUpgrade.SaleCacheTypeEnum;
import com.ets.apply.application.common.consts.salesUpgrade.SaleUpgradeStatusEnum;
import com.ets.apply.application.common.dto.adminSalesUpgrade.*;
import com.ets.apply.application.common.vo.adminSalesUpgrade.PlanIdListVO;
import com.ets.apply.application.common.vo.adminSalesUpgrade.SalesUpgradeInfoVO;
import com.ets.apply.application.common.vo.adminSalesUpgrade.SalesUpgradeListVO;
import com.ets.apply.application.infra.entity.sales.SalesCacheDatasEntity;
import com.ets.apply.application.infra.entity.sales.SalesUpgradeEntity;
import com.ets.apply.application.infra.entity.sales.SalesUpgradePackageEntity;
import com.ets.apply.application.infra.mapper.SalesUpgradePackageMapper;
import com.ets.apply.application.infra.service.SalesCacheDatasService;
import com.ets.apply.application.infra.service.SalesLogService;
import com.ets.apply.application.infra.service.SalesUpgradePackageService;
import com.ets.apply.application.infra.service.SalesUpgradeService;
import com.ets.common.ToolsHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class SalesUpgradeBusiness {
    @Autowired
    private SalesUpgradeService salesUpgradeService;
    @Autowired
    private SalesUpgradePackageMapper salesUpgradePackageMapper;
    @Autowired
    private SalesUpgradePackageService salesUpgradePackageService;
    @Autowired
    private SalesLogService salesLogService;
    @Autowired
    private SalesCacheDatasService salesCacheDatasService;
    @Autowired
    private CallPhpApplyFeign callPhpApplyFeign;


    /*
     * 获取全部模块列表
     */
    public IPage<SalesUpgradeListVO> getList(SalesUpgradeGetListDTO dto) {
        // 分页设置
        IPage<SalesUpgradeEntity> oPage = new Page<>(dto.getPageNum(), dto.getPageSize(),  true);
        // 查询条件设置
        LambdaQueryWrapper<SalesUpgradeEntity> wrapper = new LambdaQueryWrapper<>();
        //如果产品包编码不为空
        if(StringUtils.isNotEmpty(dto.getPackageSn())){
           List<SalesUpgradePackageEntity>  planIdListVO = salesUpgradePackageService.getListByPackageSn(dto.getPackageSn());
           if(planIdListVO.size() < 1){
               return new Page<>();
           }
           List<Integer> planIdList = new ArrayList<>();
           for (SalesUpgradePackageEntity idListVO : planIdListVO) {
               planIdList.add(idListVO.getPlanId());
           }
           wrapper.in(SalesUpgradeEntity::getId,planIdList);
        }
        wrapper.like(StringUtils.isNotEmpty(dto.getPlanName()),SalesUpgradeEntity::getPlanName, dto.getPlanName())
                .orderByDesc(SalesUpgradeEntity::getPriority);

        IPage<SalesUpgradeEntity> pageList = salesUpgradeService.getPageListByWrapper(oPage, wrapper);

        return pageList.convert(record -> {
            SalesUpgradeListVO vo = new SalesUpgradeListVO();
            BeanUtils.copyProperties(record, vo);
            vo.setStatusStr(SaleUpgradeStatusEnum.getDescByStatus(record.getStatus()));
            vo.setReleaseStatusStr((record.getReleaseStatus() == 1 ? "待发布" : "已发布"));
            //获取产品包数据
            List<String> packageSns = new ArrayList<>();
            List<SalesUpgradePackageEntity> salesUpgradePackageList = salesUpgradePackageService.getListByPlanId(record.getId());
            if(salesUpgradePackageList != null){
                salesUpgradePackageList.forEach(salesUpgradePackageEntity -> {
                    packageSns.add(salesUpgradePackageEntity.getPackageSn());
                });
            }
            vo.setPackageSns(packageSns);
            return vo;
        });
    }
    /*
     * 新增营销计划
     */
    public Boolean addPlan(SalesUpgradeAddDTO dto,String loginCode) {
        SalesUpgradeEntity salesUpgrade = new SalesUpgradeEntity();
        //检查优先级是否唯一
        if(!salesUpgradeService.checkProrityIsValid(dto.getPriority(),0)){
            ToolsHelper.throwException("优先级"+dto.getPriority()+"已存在");
        }
        salesUpgrade.setPlanName(dto.getPlanName());
        salesUpgrade.setPriority(dto.getPriority());
        salesUpgrade.setRuleConditions(JSON.toJSONString(dto.getRuleConditions()));
        salesUpgrade.setStatus(1);
        salesUpgrade.setReleaseStatus(1);
        salesUpgrade.setOperator(loginCode);
        salesUpgradeService.create(salesUpgrade);
        //生成产品包的数据
        for (int i = 0; i < dto.getSalesUpgradePackageList().size(); i++) {
            SalesUpgradePackageEntity salesUpgradePackage = new SalesUpgradePackageEntity();
            salesUpgradePackage.setPlanId(salesUpgrade.getId());
            salesUpgradePackage.setRate(dto.getSalesUpgradePackageList().get(i).getRate());
            salesUpgradePackage.setPackageSn(dto.getSalesUpgradePackageList().get(i).getPackageSn());
            salesUpgradePackage.setFrontendContent(dto.getSalesUpgradePackageList().get(i).getFrontendContent());
            salesUpgradePackage.setSmsTplId(dto.getSalesUpgradePackageList().get(i).getSmsTplId());
            salesUpgradePackage.setSmsSendTime(dto.getSalesUpgradePackageList().get(i).getSmsSendTime());
            salesUpgradePackage.setMinaTplId(dto.getSalesUpgradePackageList().get(i).getMinaTplId());
            salesUpgradePackage.setMinaSendTime(dto.getSalesUpgradePackageList().get(i).getMinaSendTime());
            salesUpgradePackage.setOperator(loginCode);
            salesUpgradePackageService.create(salesUpgradePackage);
        }
        //记录日志
        salesLogService.addLog(
            loginCode,
            "salesUpgrade",
            salesUpgrade.getId(),
            "addPlan",
            "新增营销计划",
            "",
            JSONObject.toJSONString(dto)
        );
        return true;
    }

    /*
     * 修改营销计划
     */
    public Boolean modifyPlan(SalesUpgradeEditDTO dto, String loginCode) {
        SalesUpgradeEntity salesUpgrade = salesUpgradeService.getById(dto.getId());
        if(salesUpgrade == null){
            ToolsHelper.throwException("营销计划不存在");
        }
        //检查优先级是否唯一
        if(!salesUpgradeService.checkProrityIsValid(dto.getPriority(),salesUpgrade.getId())){
            ToolsHelper.throwException("优先级"+dto.getPriority()+"已存在");
        }
        //获取产品包数据
        List<SalesUpgradePackageEntity> salesUpgradePackageList = salesUpgradePackageService.getListByPlanId(salesUpgrade.getId());

        //对比数据
        JSONObject compareDifffObject = compareDiff(salesUpgrade, salesUpgradePackageList,dto);
        if((compareDifffObject.getJSONObject("diff")).isEmpty()){
            ToolsHelper.throwException("数据无变更");
        }
        LambdaUpdateWrapper<SalesUpgradeEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(SalesUpgradeEntity::getId, dto.getId())
                .set(SalesUpgradeEntity::getPlanName, dto.getPlanName())
                .set(SalesUpgradeEntity::getPriority, dto.getPriority())
                .set(SalesUpgradeEntity::getRuleConditions, JSON.toJSONString(dto.getRuleConditions()))
                .set(StringUtils.isNotEmpty(loginCode),SalesUpgradeEntity::getOperator, loginCode)
                .set(SalesUpgradeEntity::getReleaseStatus, 1)
                .set(SalesUpgradeEntity::getUpdatedAt, LocalDateTime.now());
        salesUpgradeService.updateByWrapper(wrapper);
        //更新产品包数据
        salesUpgradePackageService.delByPlan(salesUpgrade.getId());
        //生成产品包的数据
        for (int i = 0; i < dto.getSalesUpgradePackageList().size(); i++) {
            SalesUpgradePackageEntity salesUpgradePackage = new SalesUpgradePackageEntity();
            salesUpgradePackage.setPlanId(salesUpgrade.getId());
            salesUpgradePackage.setRate(dto.getSalesUpgradePackageList().get(i).getRate());
            salesUpgradePackage.setPackageSn(dto.getSalesUpgradePackageList().get(i).getPackageSn());
            salesUpgradePackage.setFrontendContent(dto.getSalesUpgradePackageList().get(i).getFrontendContent());
            salesUpgradePackage.setSmsTplId(dto.getSalesUpgradePackageList().get(i).getSmsTplId());
            salesUpgradePackage.setSmsSendTime(dto.getSalesUpgradePackageList().get(i).getSmsSendTime());
            salesUpgradePackage.setMinaTplId(dto.getSalesUpgradePackageList().get(i).getMinaTplId());
            salesUpgradePackage.setMinaSendTime(dto.getSalesUpgradePackageList().get(i).getMinaSendTime());
            salesUpgradePackage.setOperator(loginCode);
            salesUpgradePackageService.create(salesUpgradePackage);
        }
        //记录日志
        salesLogService.addLog(
                loginCode,
                "salesUpgrade",
                salesUpgrade.getId(),
                "updatePlan",
                "更新计划",
                (compareDifffObject.getJSONObject("preOperate")).toJSONString(),
                (compareDifffObject.getJSONObject("afterOperate")).toJSONString()
        );
        return true;
    }

    /*
     *  对比 数据是否有变化
     */
    public JSONObject compareDiff(SalesUpgradeEntity salesUpgrade, List<SalesUpgradePackageEntity> salesUpgradePackageList,SalesUpgradeEditDTO dto){

        JSONObject preOperate = new JSONObject();
        JSONObject afterOperate = new JSONObject();
        JSONObject diff = new JSONObject();
        preOperate.put("id",salesUpgrade.getId());
        //名称
        if(!Objects.equals(salesUpgrade.getPlanName(), dto.getPlanName())){
            preOperate.put("planName",salesUpgrade.getPlanName());
            afterOperate.put("planName",dto.getPlanName());
            diff.put("planName","{\"old\":"+salesUpgrade.getPlanName()+",\"new\":" + dto.getPlanName() + "}");
        }
        //优先级
        if(!Objects.equals(salesUpgrade.getPriority(), dto.getPriority())){
            preOperate.put("priority",salesUpgrade.getPriority());
            afterOperate.put("priority",dto.getPriority());
            diff.put("priority","{\"old\":"+salesUpgrade.getPriority()+",\"new\":" + dto.getPriority() + "}");
        }
        //规则条件
        if(!Objects.equals(salesUpgrade.getRuleConditions(), dto.getRuleConditions())){
            preOperate.put("ruleConditions",salesUpgrade.getRuleConditions());
            afterOperate.put("ruleConditions",dto.getRuleConditions());
            diff.put("ruleConditions","{\"old\":"+salesUpgrade.getRuleConditions()+",\"new\":" + dto.getRuleConditions() + "}");
        }
        //对比产品包
        if(salesUpgradePackageList == null || dto.getSalesUpgradePackageList() ==  null){
            preOperate.put("salesUpgradePackageList",salesUpgradePackageList);
            afterOperate.put("salesUpgradePackageList",dto.getSalesUpgradePackageList());
            diff.put("salesUpgradePackageList","{\"old\":"+salesUpgradePackageList+",\"new\":" + dto.getSalesUpgradePackageList() + "}");
        }else{
            int oldKey = salesUpgradePackageList.size() -1;
            int newKey = dto.getSalesUpgradePackageList().size() - 1;
            int maxLen = Math.max(salesUpgradePackageList.size(),dto.getSalesUpgradePackageList().size());
            for (int k = 0; k < maxLen;  k++){
                if(k <= oldKey && k <= newKey){
                    if(!Objects.equals(salesUpgradePackageList.get(k).getPackageSn(), dto.getSalesUpgradePackageList().get(k).getPackageSn())){
                        preOperate.put("packageSn["+k+"]",salesUpgradePackageList.get(k).getPackageSn());
                        afterOperate.put("packageSn["+k+"]",dto.getSalesUpgradePackageList().get(k).getPackageSn());
                        diff.put("packageSn["+k+"]","{\"old\":"+salesUpgradePackageList.get(k).getPackageSn()+",\"new\":" + dto.getSalesUpgradePackageList().get(k).getPackageSn() + "}");
                    }
                    if(!Objects.equals(salesUpgradePackageList.get(k).getSmsTplId(), dto.getSalesUpgradePackageList().get(k).getSmsTplId())){
                        preOperate.put("SmsTplId["+k+"]",salesUpgradePackageList.get(k).getSmsTplId());
                        afterOperate.put("SmsTplId["+k+"]",dto.getSalesUpgradePackageList().get(k).getSmsTplId());
                        diff.put("SmsTplId["+k+"]","{\"old\":"+salesUpgradePackageList.get(k).getSmsTplId()+",\"new\":" + dto.getSalesUpgradePackageList().get(k).getSmsTplId() + "}");
                    }
                    if(!Objects.equals(salesUpgradePackageList.get(k).getSmsSendTime(), dto.getSalesUpgradePackageList().get(k).getSmsSendTime())){
                        preOperate.put("SmsSendTime["+k+"]",salesUpgradePackageList.get(k).getSmsSendTime());
                        afterOperate.put("SmsSendTime["+k+"]",dto.getSalesUpgradePackageList().get(k).getSmsSendTime());
                        diff.put("SmsSendTime["+k+"]","{\"old\":"+salesUpgradePackageList.get(k).getSmsSendTime()+",\"new\":" + dto.getSalesUpgradePackageList().get(k).getSmsSendTime() + "}");
                    }
                    if(!Objects.equals(salesUpgradePackageList.get(k).getMinaTplId(), dto.getSalesUpgradePackageList().get(k).getMinaTplId())){
                        preOperate.put("MinaTplId["+k+"]",salesUpgradePackageList.get(k).getMinaTplId());
                        afterOperate.put("MinaTplId["+k+"]",dto.getSalesUpgradePackageList().get(k).getMinaTplId());
                        diff.put("MinaTplId["+k+"]","{\"old\":"+salesUpgradePackageList.get(k).getMinaTplId()+",\"new\":" + dto.getSalesUpgradePackageList().get(k).getMinaTplId() + "}");
                    }
                    if(!Objects.equals(salesUpgradePackageList.get(k).getMinaSendTime(), dto.getSalesUpgradePackageList().get(k).getMinaSendTime())){
                        preOperate.put("MinaSendTime["+k+"]",salesUpgradePackageList.get(k).getMinaSendTime());
                        afterOperate.put("MinaSendTime["+k+"]",dto.getSalesUpgradePackageList().get(k).getMinaSendTime());
                        diff.put("MinaSendTime["+k+"]","{\"old\":"+salesUpgradePackageList.get(k).getMinaSendTime()+",\"new\":" + dto.getSalesUpgradePackageList().get(k).getMinaSendTime() + "}");
                    }
                    if(!Objects.equals(salesUpgradePackageList.get(k).getFrontendContent(), dto.getSalesUpgradePackageList().get(k).getFrontendContent())){
                        preOperate.put("FrontendContent["+k+"]",salesUpgradePackageList.get(k).getFrontendContent());
                        afterOperate.put("FrontendContent["+k+"]",dto.getSalesUpgradePackageList().get(k).getFrontendContent());
                        diff.put("FrontendContent["+k+"]","{\"old\":"+salesUpgradePackageList.get(k).getFrontendContent()+",\"new\":" + dto.getSalesUpgradePackageList().get(k).getFrontendContent() + "}");
                    }
                    if(!Objects.equals(salesUpgradePackageList.get(k).getRate(), dto.getSalesUpgradePackageList().get(k).getRate())){
                        preOperate.put("Rate["+k+"]",salesUpgradePackageList.get(k).getRate());
                        afterOperate.put("Rate["+k+"]",dto.getSalesUpgradePackageList().get(k).getRate());
                        diff.put("Rate["+k+"]","{\"old\":"+salesUpgradePackageList.get(k).getRate()+",\"new\":" + dto.getSalesUpgradePackageList().get(k).getRate() + "}");
                    }
                }else if (k <= oldKey && k > newKey){
                    preOperate.put("salesUpgradePackageList["+k+"]",salesUpgradePackageList.get(k));
                    afterOperate.put("salesUpgradePackageList["+k+"]",null);
                    diff.put("salesUpgradePackageList["+k+"]","{\"old\":"+salesUpgradePackageList.get(k)+",\"new\":" +null + "}");
                }else if (k > oldKey && k <= newKey){
                    preOperate.put("salesUpgradePackageList["+k+"]",null);
                    afterOperate.put("salesUpgradePackageList["+k+"]",dto.getSalesUpgradePackageList().get(k));
                    diff.put("salesUpgradePackageList["+k+"]","{\"old\":"+null+",\"new\":" +dto.getSalesUpgradePackageList().get(k) + "}");
                }
            }
        }

        JSONObject returnObject = new JSONObject();
        returnObject.put("preOperate",preOperate);
        returnObject.put("afterOperate",afterOperate);
        returnObject.put("diff",diff);
        return returnObject;
    }



    /*
     * 上下架计划
     */
    public Boolean upOrDown(SalesUpgradeUpOrDownDTO dto, String loginCode) {
        SalesUpgradeEntity salesUpgrade = salesUpgradeService.getById(dto.getId());
        if(salesUpgrade == null){
            ToolsHelper.throwException("营销计划不存在");
        }
        if(Objects.equals(salesUpgrade.getStatus(),dto.getStatus())){
            return true;
        }
        String msg = dto.getReason();
        switch (dto.getStatus()) {
            case 2 -> {
                msg = "变更为上架状态，" + msg;
                //检查优先级是否唯一
                if (!salesUpgradeService.checkProrityIsValid(salesUpgrade.getPriority(), salesUpgrade.getId())) {
                    ToolsHelper.throwException("优先级" + salesUpgrade.getPriority() + "已存在");
                }
            }
            case 3 -> msg = "变更为下架状态，" + msg;
            default -> ToolsHelper.throwException("更新状态错误：" + dto.getStatus());
        }




        LambdaUpdateWrapper<SalesUpgradeEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(SalesUpgradeEntity::getId, dto.getId())
                .set(SalesUpgradeEntity::getStatus, dto.getStatus())
                .set(SalesUpgradeEntity::getReleaseStatus, 2)
                .set(SalesUpgradeEntity::getUpdatedAt, LocalDateTime.now());
        salesUpgradeService.updateByWrapper(wrapper);

        //上下架直接发布
        SalesUpgradeProdDTO prodDTO = new SalesUpgradeProdDTO();
        prodDTO.setId(salesUpgrade.getId());
        prodDTO.setReason("上下架更新发布");
        prod(prodDTO, loginCode);

        //记录日志
        salesLogService.addLog(
                loginCode,
                "salesUpgrade",
                salesUpgrade.getId(),
                "upOrDown",
                msg,
                "",
                ""
        );
        return true;
    }


    /*
     * 获取全部模块列表
     */
    public SalesUpgradeInfoVO getInfo(SalesUpgradeGetInfoDTO dto) {
        SalesUpgradeEntity salesUpgrade = salesUpgradeService.getById(dto.getId());
        if(salesUpgrade == null){
            ToolsHelper.throwException("营销计划不存在");
        }
        SalesUpgradeInfoVO vo = new SalesUpgradeInfoVO();
        BeanUtils.copyProperties(salesUpgrade, vo);
        vo.setStatusStr(SaleUpgradeStatusEnum.getDescByStatus(salesUpgrade.getStatus()));
        vo.setRuleConditions(JSONObject.parseArray(salesUpgrade.getRuleConditions(), SaleUpgradeRuleConditionBO.class));
        List<SalesUpgradePackageEntity> salesUpgradePackageList = salesUpgradePackageService.getListByPlanId(salesUpgrade.getId());
        vo.setPackageSns(salesUpgradePackageList);
        return vo;

    }




    /*
     * 获取全部模块列表
     */
    public JSONObject compareInfo(SalesUpgradeGetInfoDTO dto) {
        SalesUpgradeEntity tmpSalesUpgrade = salesUpgradeService.getById(dto.getId());
        if(tmpSalesUpgrade == null){
            ToolsHelper.throwException("营销计划不存在");
        }
        SalesUpgradeEntity prodSalesUpgrade = new SalesUpgradeEntity();
        JSONArray prodPackageListArray = new JSONArray();
        //获取已发布数据
        JSONObject prodCacheValues = salesCacheDatasService.getCacheValuesByParams(SaleCacheMainKeyEnum.CACHE_MAIN_KEY_UPGRADE.getValue(), tmpSalesUpgrade.getId().toString(), "prod");
        if(prodCacheValues != null){
             prodSalesUpgrade = prodCacheValues.getObject("salesUpgrade", SalesUpgradeEntity.class);
            //无生产数据
            if(prodSalesUpgrade == null){
                prodSalesUpgrade = new SalesUpgradeEntity();
            }
             prodPackageListArray = prodCacheValues.getJSONArray("salesUpgradePackageList");
        }
        JSONObject tmpDatas = new JSONObject();
        JSONObject prodDatas = new JSONObject();
        JSONObject diff = new JSONObject();
        tmpDatas.put("id",tmpSalesUpgrade.getId());

        //名称
        if(!Objects.equals(prodSalesUpgrade.getPlanName(), tmpSalesUpgrade.getPlanName())){
            prodDatas.put("planName",prodSalesUpgrade.getPlanName());
            tmpDatas.put("planName",tmpSalesUpgrade.getPlanName());
            diff.put("planName","{\"old\":"+prodSalesUpgrade.getStatus()+",\"new\":" + tmpSalesUpgrade.getPlanName() + "}");
        }
        //优先级
        if(!Objects.equals(prodSalesUpgrade.getPriority(), tmpSalesUpgrade.getPriority())){
            prodDatas.put("priority",prodSalesUpgrade.getPriority());
            tmpDatas.put("priority",tmpSalesUpgrade.getPriority());
            diff.put("priority","{\"old\":"+prodSalesUpgrade.getPriority()+",\"new\":" + tmpSalesUpgrade.getPriority() + "}");
        }
        //规则条件
        if(!Objects.equals(prodSalesUpgrade.getRuleConditions(), tmpSalesUpgrade.getRuleConditions())){
            prodDatas.put("ruleConditions",prodSalesUpgrade.getRuleConditions());
            tmpDatas.put("ruleConditions",tmpSalesUpgrade.getRuleConditions());
            diff.put("ruleConditions","{\"old\":"+prodSalesUpgrade.getRuleConditions()+",\"new\":" + tmpSalesUpgrade.getRuleConditions() + "}");
        }



        List<SalesUpgradePackageEntity> tmpSalesUpgradePackageList = salesUpgradePackageService.getListByPlanId(tmpSalesUpgrade.getId());

        //对比产品包
        if(tmpSalesUpgradePackageList == null || prodPackageListArray == null){
            prodDatas.put("salesUpgradePackageList",prodPackageListArray);
            tmpDatas.put("salesUpgradePackageList",tmpSalesUpgradePackageList);
            diff.put("salesUpgradePackageList","{\"old\":"+prodPackageListArray+",\"new\":" + tmpSalesUpgradePackageList + "}");
        }else{
            int maxLen = Math.max(prodPackageListArray.size(),tmpSalesUpgradePackageList.size());
            for (int k = 0; k < maxLen;  k++){
                if(k < prodPackageListArray.size() && k < tmpSalesUpgradePackageList.size()){
                    SalesUpgradePackageEntity prodPackage = prodPackageListArray.getObject(k, SalesUpgradePackageEntity.class);
                    SalesUpgradePackageEntity tmpPackage = tmpSalesUpgradePackageList.get(k);

                    if(!Objects.equals(prodPackage.getPackageSn(), tmpPackage.getPackageSn())){
                        prodDatas.put("packageSn["+k+"]",prodPackage.getPackageSn());
                        tmpDatas.put("packageSn["+k+"]",tmpPackage.getPackageSn());
                        diff.put("packageSn["+k+"]","{\"old\":"+prodPackage.getPackageSn()+",\"new\":" + tmpPackage.getPackageSn() + "}");
                    }
                    if(!Objects.equals(prodPackage.getSmsTplId(), tmpPackage.getSmsTplId())){
                        prodDatas.put("SmsTplId["+k+"]",prodPackage.getSmsTplId());
                        tmpDatas.put("SmsTplId["+k+"]",tmpPackage.getSmsTplId());
                        diff.put("SmsTplId["+k+"]","{\"old\":"+prodPackage.getSmsTplId()+",\"new\":" + tmpPackage.getSmsTplId() + "}");
                    }
                    if(!Objects.equals(prodPackage.getSmsSendTime(), tmpPackage.getSmsSendTime())){
                        prodDatas.put("SmsSendTime["+k+"]",prodPackage.getSmsSendTime());
                        tmpDatas.put("SmsSendTime["+k+"]",tmpPackage.getSmsSendTime());
                        diff.put("SmsSendTime["+k+"]","{\"old\":"+prodPackage.getSmsSendTime()+",\"new\":" + tmpPackage.getSmsSendTime() + "}");
                    }
                    if(!Objects.equals(prodPackage.getMinaTplId(), tmpPackage.getMinaTplId())){
                        prodDatas.put("MinaTplId["+k+"]",prodPackage.getMinaTplId());
                        tmpDatas.put("MinaTplId["+k+"]",tmpPackage.getMinaTplId());
                        diff.put("MinaTplId["+k+"]","{\"old\":"+prodPackage.getMinaTplId()+",\"new\":" + tmpPackage.getMinaTplId() + "}");
                    }
                    if(!Objects.equals(prodPackage.getMinaSendTime(), tmpPackage.getMinaSendTime())){
                        prodDatas.put("MinaSendTime["+k+"]",prodPackage.getMinaSendTime());
                        tmpDatas.put("MinaSendTime["+k+"]",tmpPackage.getMinaSendTime());
                        diff.put("MinaSendTime["+k+"]","{\"old\":"+prodPackage.getMinaSendTime()+",\"new\":" + tmpPackage.getMinaSendTime() + "}");
                    }
                    if(!Objects.equals(prodPackage.getFrontendContent(), tmpPackage.getFrontendContent())){
                        prodDatas.put("FrontendContent["+k+"]",prodPackage.getFrontendContent());
                        tmpDatas.put("FrontendContent["+k+"]",tmpPackage.getFrontendContent());
                        diff.put("FrontendContent["+k+"]","{\"old\":"+prodPackage.getFrontendContent()+",\"new\":" + tmpPackage.getFrontendContent() + "}");
                    }
                    if(!Objects.equals(prodPackage.getRate(), tmpPackage.getRate())){
                        prodDatas.put("Rate["+k+"]",prodPackage.getRate());
                        tmpDatas.put("Rate["+k+"]",tmpPackage.getRate());
                        diff.put("Rate["+k+"]","{\"old\":"+prodPackage.getRate()+",\"new\":" + tmpPackage.getRate() + "}");
                    }
                }else if (k < prodPackageListArray.size() && k > tmpSalesUpgradePackageList.size()){
                    prodDatas.put("salesUpgradePackageList["+k+"]",prodPackageListArray.getObject(k, SalesUpgradePackageEntity.class));
                    tmpDatas.put("salesUpgradePackageList["+k+"]",null);
                    diff.put("salesUpgradePackageList["+k+"]","{\"old\":"+ prodPackageListArray.getObject(k, SalesUpgradePackageEntity.class)+",\"new\":" +null + "}");
                }else{
                    prodDatas.put("salesUpgradePackageList["+k+"]",null);
                    tmpDatas.put("salesUpgradePackageList["+k+"]",tmpSalesUpgradePackageList.get(k));
                    diff.put("salesUpgradePackageList["+k+"]","{\"old\":"+null+",\"new\":" +tmpSalesUpgradePackageList.get(k) + "}");
                }
            }
        }
        JSONObject returnObject = new JSONObject();
        returnObject.put("prodDatas",prodDatas);
        returnObject.put("tmpDatas",tmpDatas);
        returnObject.put("diff",diff);
        return returnObject;
    }


    /*
     * 获取全部模块列表
     */
    public SalesUpgradeEntity copy(SalesUpgradeGetInfoDTO dto,String loginCode) {
        SalesUpgradeEntity salesUpgrade = salesUpgradeService.getById(dto.getId());
        if(salesUpgrade == null){
            ToolsHelper.throwException("营销计划不存在");
        }
        SalesUpgradeEntity salesUpgradeCopy = new SalesUpgradeEntity();
        salesUpgradeCopy.setPlanName(salesUpgrade.getPlanName()+" copy");
        salesUpgradeCopy.setPriority(salesUpgrade.getPriority());
        salesUpgradeCopy.setRuleConditions(salesUpgrade.getRuleConditions());
        salesUpgradeCopy.setStatus(1);
        salesUpgradeCopy.setReleaseStatus(1);
        salesUpgradeCopy.setOperator(loginCode);
        salesUpgradeService.create(salesUpgradeCopy);

        List<SalesUpgradePackageEntity> tmpSalesUpgradePackageList = salesUpgradePackageService.getListByPlanId(salesUpgrade.getId());

        //生成产品包的数据
        for (SalesUpgradePackageEntity salesUpgradePackageEntity : tmpSalesUpgradePackageList) {
            SalesUpgradePackageEntity salesUpgradePackage = new SalesUpgradePackageEntity();
            salesUpgradePackage.setPlanId(salesUpgradeCopy.getId());
            salesUpgradePackage.setRate(salesUpgradePackageEntity.getRate());
            salesUpgradePackage.setPackageSn(salesUpgradePackageEntity.getPackageSn());
            salesUpgradePackage.setFrontendContent(salesUpgradePackageEntity.getFrontendContent());
            salesUpgradePackage.setSmsTplId(salesUpgradePackageEntity.getSmsTplId());
            salesUpgradePackage.setSmsSendTime(salesUpgradePackageEntity.getSmsSendTime());
            salesUpgradePackage.setMinaTplId(salesUpgradePackageEntity.getMinaTplId());
            salesUpgradePackage.setMinaSendTime(salesUpgradePackageEntity.getMinaSendTime());
            salesUpgradePackage.setOperator(loginCode);
            salesUpgradePackage.setCreatedAt(salesUpgradePackageEntity.getCreatedAt());
            salesUpgradePackageService.create(salesUpgradePackage);
        }
        //记录日志
        salesLogService.addLog(
                loginCode,
                "salesUpgrade",
                salesUpgradeCopy.getId(),
                "addPlan",
                "复制营销计划",
                "",
                JSONObject.toJSONString(dto)
        );
        return salesUpgradeCopy;
    }

    /*
     * 获取全部模块列表
     */
    public Boolean prod(SalesUpgradeProdDTO dto,String loginCode) {
        SalesUpgradeEntity salesUpgrade = salesUpgradeService.getById(dto.getId());
        if(salesUpgrade == null){
            ToolsHelper.throwException("营销计划不存在");
        }
        //检查优先级是否唯一
        if(!salesUpgradeService.checkProrityIsValid(salesUpgrade.getPriority(),salesUpgrade.getId())){
            ToolsHelper.throwException("优先级"+salesUpgrade.getPriority()+"已存在");
        }
        //对应的产品包数据
        List<SalesUpgradePackageEntity> salesUpgradePackageList = salesUpgradePackageService.getListByPlanId(salesUpgrade.getId());
        JSONObject saveDatasObject = new JSONObject();
        saveDatasObject.put("salesUpgrade", salesUpgrade);
        saveDatasObject.put("salesUpgradePackageList", salesUpgradePackageList);

        SalesCacheDatasEntity cacheDatas = salesCacheDatasService.getOneByParams(SaleCacheMainKeyEnum.CACHE_MAIN_KEY_UPGRADE.getValue(), salesUpgrade.getId().toString(), SaleCacheTypeEnum.TYPE_PROD.getType());
        if(cacheDatas == null){
            SalesCacheDatasEntity cacheDatasNew = new SalesCacheDatasEntity();
            cacheDatasNew.setCacheMainKey(SaleCacheMainKeyEnum.CACHE_MAIN_KEY_UPGRADE.getValue());
            cacheDatasNew.setCacheKey(salesUpgrade.getId().toString());
            cacheDatasNew.setCacheValues(saveDatasObject.toJSONString());
            cacheDatasNew.setType(SaleCacheTypeEnum.TYPE_PROD.getType());
            salesCacheDatasService.create(cacheDatasNew);
        }else{
            cacheDatas.setCacheValues(saveDatasObject.toJSONString());
            salesCacheDatasService.updateById(cacheDatas);
        }

        //todo 生成需要的缓存数据,更新etc_apply的缓存数据
        callPhpApplyFeign.salesUpgradeProd(dto);

        LambdaUpdateWrapper<SalesUpgradeEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(SalesUpgradeEntity::getId, salesUpgrade.getId())
                .set(SalesUpgradeEntity::getReleaseStatus,2)
                .set(SalesUpgradeEntity::getUpdatedAt, LocalDateTime.now());
        salesUpgradeService.updateByWrapper(wrapper);
        //记录日志
        salesLogService.addLog(
                loginCode,
                "salesUpgrade",
                salesUpgrade.getId(),
                "prod",
                "发布生产："+dto.getReason(),
                "",
                JSONObject.toJSONString(dto)
        );
        return true;
    }
}
