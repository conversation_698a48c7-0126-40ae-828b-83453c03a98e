package com.ets.apply.feign.fallback;

import com.ets.apply.feign.feign.OrderFeign;
import com.ets.apply.feign.request.FindOneOrderReq;
import com.ets.apply.feign.request.OrderListDTO;
import com.ets.apply.feign.response.OrderAddressResponse;
import com.ets.apply.feign.response.OrderPackageInfoResponse;
import com.ets.apply.feign.response.OrderResponse;
import com.ets.common.JsonResult;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;

@Component
public class OrderFeignFallback implements OrderFeign {

    @Override
    public JsonResult<?> getUserValidCarOrdersByUid(@NotNull(message = "用户Uid不能为空") @RequestParam(value = "uid", required = false) Long uid) {
        return JsonResult.error("服务器繁忙，请稍后再试");
    }

    @Override
    public JsonResult<OrderResponse> findOneByPlate(@Valid @RequestBody FindOneOrderReq req) {
        return JsonResult.error("服务器繁忙，请稍后再试");
    }

    @Override
    public JsonResult<OrderResponse> getOrderByOrderSn(@NotBlank(message = "订单号不能为空") @RequestParam(value = "orderSn", required = false) String orderSn) {
        return JsonResult.error("服务器繁忙，请稍后再试");
    }

    @Override
    public JsonResult<List<OrderResponse>> getOrderListByPlate(@RequestBody @Valid OrderListDTO orderListDTO) {
        return JsonResult.error("服务器繁忙，请稍后再试");
    }

    @Override
    public JsonResult<OrderAddressResponse> getOrderAddressByOrderSn(@NotBlank(message = "订单号不能为空") @RequestParam(value = "orderSn", required = false) String orderSn) {
        return JsonResult.error("服务器繁忙，请稍后再试");
    }

    @Override
    public JsonResult<OrderPackageInfoResponse> getOrderPackageInfoByOrderSn(@NotBlank(message = "订单号不能为空") @RequestParam(value = "orderSn") String orderSn) {
        return JsonResult.error("服务器繁忙，请稍后再试");
    }

}
