package com.ets.apply.application.common.consts.deposit;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 保证金状态枚举
 */
@Getter
@AllArgsConstructor
public enum DepositStatusEnum {

    NORMAL(1, "正常"),
    FROZEN(2, "冻结"),
    CANCELED(3, "注销"),
    REFUNDED(4, "退款"),
    ;

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;

    static {
        map = Arrays.stream(DepositStatusEnum.values()).collect(Collectors.toMap(DepositStatusEnum::getValue, DepositStatusEnum::getDesc));
    }
}
