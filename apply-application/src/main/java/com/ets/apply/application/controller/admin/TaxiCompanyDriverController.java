package com.ets.apply.application.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.apply.application.app.business.TaxiCompanyDriverBusiness;
import com.ets.apply.application.common.dto.request.taxiCompanyDriver.DeleteByIdDTO;
import com.ets.apply.application.common.dto.request.taxiCompanyDriver.TaxiCompanyDriverLogDTO;
import com.ets.apply.application.common.vo.taxiCompanyDriver.TaxiCompanyDriverLogListVo;
import com.ets.apply.application.controller.BaseController;
import com.ets.common.JsonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import jakarta.validation.Valid;

@Controller
@RequestMapping("/admin/taxi-company-driver")
public class TaxiCompanyDriverController extends BaseController {


    @Autowired
    private TaxiCompanyDriverBusiness taxiCompanyDriverBusiness;

    /**
     * 软删除出租车司机，如果司机是首签司机，需要进行拉黑，并且记录日志
     * @yapi http://yapi.etczs.net/project/312/interface/api/26814
     * @param deleteByIdDTO
     * @return
     */
    @RequestMapping("/delete")
    @ResponseBody
    public JsonResult<?> delete(@RequestBody() @Valid DeleteByIdDTO deleteByIdDTO) {
        taxiCompanyDriverBusiness.delete(deleteByIdDTO);
        return JsonResult.ok();
    }

    /**
     * @yapi http://yapi.etczs.net/project/312/interface/api/26805
     * 出租车操作日志
     * @param taxiCompanyDriverLogDTO
     * @return
     */
    @RequestMapping("/get-log-list")
    @ResponseBody
    public JsonResult<IPage<TaxiCompanyDriverLogListVo>> getLogList(@RequestBody(required = true) @Valid TaxiCompanyDriverLogDTO taxiCompanyDriverLogDTO) {


        return JsonResult.ok(taxiCompanyDriverBusiness.getLogList(taxiCompanyDriverLogDTO));
    }


}
