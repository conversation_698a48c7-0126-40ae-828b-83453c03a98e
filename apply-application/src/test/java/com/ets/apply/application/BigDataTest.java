package com.ets.apply.application;

import com.ets.apply.application.app.service.bigdata.BigDataService;
import com.ets.apply.application.common.vo.BigDataRechargeNoticeVO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@SpringBootTest(classes = ApplyApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringRunner.class)
@ActiveProfiles(value = "dev")
public class BigDataTest {

    @Autowired
    private BigDataService bigDataService;

    @Test
    public void getRechargeNotice() {
        int index = 0;
        int count = 500;
        List<BigDataRechargeNoticeVO> tmpList = bigDataService.rechargeNotice(index, count);
        System.out.println(tmpList);
    }
}
