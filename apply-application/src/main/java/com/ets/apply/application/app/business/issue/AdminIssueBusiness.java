package com.ets.apply.application.app.business.issue;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.apply.application.app.thirdservice.feign.CallGoodsApplication;
import com.ets.apply.application.common.consts.issueService.*;
import com.ets.apply.application.common.consts.order.PlateColorEnum;
import com.ets.apply.application.common.dto.goods.OrderCenterUpdateDTO;
import com.ets.apply.application.common.dto.issue.IssueGetListDTO;
import com.ets.apply.application.common.vo.SelectOptionsVO;
import com.ets.apply.application.common.vo.issue.IssueLogListVO;
import com.ets.apply.application.common.vo.issue.IssuePageListVO;
import com.ets.apply.application.infra.entity.IssueServiceEntity;
import com.ets.apply.application.infra.entity.IssueServiceLogEntity;
import com.ets.apply.application.infra.service.IssueServiceLogService;
import com.ets.apply.application.infra.service.IssueServiceService;
import com.ets.common.BeanHelper;
import com.ets.common.ToolsHelper;
import com.ets.common.util.NumberUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Component
@Slf4j
public class AdminIssueBusiness {

    @Autowired
    private IssueServiceService issueServiceService;

    @Autowired
    private IssueServiceLogService issueServiceLogService;

    @Autowired
    private CallGoodsApplication callGoodsApplication;

    public HashMap<String, List<SelectOptionsVO>> getSelectOptions() {

        HashMap<String, List<SelectOptionsVO>> map = new HashMap<>();
        map.put("status", IssueServiceStatusEnum.getSelectOptions());
        map.put("type", IssueServiceTypeEnum.getSelectOptions());
        map.put("activatedStatus", IssueServiceActivatedStatusEnum.getSelectOptions());

        return map;
    }

    public IPage<IssuePageListVO> getPageList(IssueGetListDTO dto) {

        LambdaQueryWrapper<IssueServiceEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(NumberUtil.isPositive(dto.getUid()), IssueServiceEntity::getUid, dto.getUid())
                .eq(NumberUtil.isPositive(dto.getType()), IssueServiceEntity::getType, dto.getType())
                .eq(StringUtils.isNotEmpty(dto.getPlateNo()), IssueServiceEntity::getPlateNo, dto.getPlateNo())
                .eq(StringUtils.isNotEmpty(dto.getServiceSn()), IssueServiceEntity::getServiceSn, dto.getServiceSn())
                .eq(StringUtils.isNotEmpty(dto.getContractId()), IssueServiceEntity::getContractId, dto.getContractId())
                .eq(StringUtils.isNotEmpty(dto.getGoodsOrderSn()), IssueServiceEntity::getGoodsOrderSn, dto.getGoodsOrderSn())
                .eq(dto.getStatus() != null, IssueServiceEntity::getStatus, dto.getStatus())
                .eq(dto.getActivatedStatus() != null, IssueServiceEntity::getActivatedStatus, dto.getActivatedStatus())
                .gt(StringUtils.isNotEmpty(dto.getCreatedAtStart()), IssueServiceEntity::getCreatedAt, dto.getCreatedAtStart())
                .lt(StringUtils.isNotEmpty(dto.getCreatedAtEnd()), IssueServiceEntity::getCreatedAt, dto.getCreatedAtEnd() + " 23:59:59")
                .orderByDesc(IssueServiceEntity::getCreatedAt);

        IPage<IssueServiceEntity> oPage = new Page<>(dto.getPage(), dto.getPageSize(), true);

        IPage<IssueServiceEntity> pageList = issueServiceService.getPageListByWrapper(oPage, wrapper);
        if(pageList.getSize() == 0){
            return null;
        }

        return pageList.convert(
                entity -> {
                    IssuePageListVO vo = BeanUtil.copyProperties(entity, IssuePageListVO.class);

                    vo.setPlateColorStr(PlateColorEnum.getDescByCode(entity.getPlateColor()));
                    vo.setRefundStatusStr(IssueServiceRefundStatusEnum.getDescByCode(entity.getRefundStatus()));

                    if (entity.getType().equals(IssueServiceTypeEnum.XIN_JIANG.getCode())) {
                        vo.setActivatedStatusStr(IssueServiceActivatedStatusEnum.getDescByCode(entity.getActivatedStatus()));
                    }
                    vo.setTypeStr(IssueServiceTypeEnum.getDescByCode(entity.getType()));
                    vo.setStatusStr(IssueServiceStatusEnum.getDescByCode(entity.getStatus()));

                    return vo;
                }
        );
    }

    public List<IssueLogListVO> getLogList(String serviceSn) {

        List<IssueServiceLogEntity> logList = issueServiceLogService.getListByServiceSn(serviceSn);

        List<IssueLogListVO> list = new ArrayList<>();

        for(IssueServiceLogEntity entity: logList) {

            IssueLogListVO vo = BeanHelper.copy(IssueLogListVO.class, entity);

            vo.setLogTypeStr(IssueServiceLogTypeEnum.getDescByCode(entity.getLogType()));

            list.add(vo);
        }

        return list;
    }

    public void syncToOrderCenterByTime(String beginTime, Integer limit) {

        List<IssueServiceEntity> list = issueServiceService.getListByBeginTime(beginTime, limit);

        doSyncToOrderCenter(list, limit);
    }

    public void syncToOrderCenter(Integer minute, Integer offset, Integer limit) {

        List<IssueServiceEntity> list = issueServiceService.getLastMinuteUpdatedList(minute, offset + "," + limit);

        doSyncToOrderCenter(list, limit);
    }

    public void doSyncToOrderCenter(List<IssueServiceEntity> list, Integer limit) {
        // 每分钟自动同步数据到订单中心

        if (list == null || list.isEmpty()) {
            return;
        }

        // 处理内容
        list.forEach(entity -> {
            // 调用订单中心接口，同步数据
            try {
                OrderCenterUpdateDTO dto = new OrderCenterUpdateDTO();
                dto.setProductName("前装ETC");
                dto.setProductSn(entity.getServiceSn());
                dto.setProductType(2);
                dto.setAmount(entity.getPaidFee());
                dto.setUid(entity.getUid());
                dto.setCreateTime(ToolsHelper.localDateTimeToString(entity.getCreatedAt()));
                dto.setPlateNo(entity.getPlateNo());
                dto.setPlateColor(entity.getPlateColor());

                dto.setStatus(IssueServiceStatusEnum.getOrderCenterStatus(entity.getStatus()));
                // 需要支付的订单，设置为待支付
                if (dto.getStatus().equals(1) && NumberUtil.isPositive(entity.getPaidFee())) {
                    dto.setStatus(2);
                }

                dto.setIsShow(entity.getStatus().equals(IssueServiceStatusEnum.STATUS_FINISHED.getCode()));

                HashMap<String, Object> extra = new HashMap<>();
                extra.put("plate_no", entity.getPlateNo());
                extra.put("plate_color", entity.getPlateColor());
                extra.put("plate_color_str", PlateColorEnum.getDescByCode(entity.getPlateColor()));
                extra.put("paid_fee", entity.getPaidFee());

                dto.setExtra(extra);

                callGoodsApplication.updateInfo(dto);

            } catch (Exception e) {
                log.error("同步纯签约数据到订单中心失败：" + e.getMessage());
            }
        });

        // 未结束，则继续处理下一页
        if (list.size() == limit) {
            log.error("同步纯签约数据单页数量超过上限：" + limit);
            //syncToOrderCenter(minute, offset + limit, limit);
        }
    }

}
