package com.ets.apply.application.common.consts.creditCard;

public class BankConstant {
    public static final int  PING_AN_ID   = 1;
    public static final int PING_AN_FREE = 2;
    public static final int JIAOTONG_ID  = 3;
    public static final int JIAOTONG_FREE_ID  = 4;
    //通过接口方式自动审核
    public static final int PING_AN_INTERFACE  = 30;
    public static final int GUANG_FA_INTERFACE = 31;
    public static final int GUANGFA_ID  = 7;
    public static final int NINGXIA_ID  = 8;
    public static final int NINGXIA_NEW_ID  = 25;
    public static final int MINSHENG_ID = 10;
    //交通通过接口
    public static final int JIAO_TONG   = 32;
    public static final int PUFA_ID = 11;

    public static final int BOC_JS_ID = 36; //中国银行(新信用卡，发苏通卡)
    public static final int BOC_BJ_ID = 38; //中国银行(新信用卡，发速通卡)

    public static final int GEELY_ID = 78; //吉利
    public static final int   CITIC_CREDIT_BANK_ID_GUANGXI = 80; // 中信银行信用卡-广西
    public static final int   CITIC_CREDIT_BANK_ID_JSUTONG = 81; // 中信银行信用卡-江苏
    public static final int   CITIC_CREDIT_BANK_ID_NEIMENG = 122;// 中信银行信用卡-内蒙


    public static final int   PURCHASE_PARTY_CMBC = 161; // 民生银行
    public static final int PURCHASE_PARTY_SPD_CREDIT_BANK_GUAGNXI = 162;// 浦发银行信用卡- 广西
    public static final int PURCHASE_PARTY_SPD_CREDIT_BANK_JSUTONG = 163;// 浦发银行信用卡- 江苏
    public static final int PURCHASE_PARTY_SPD_CREDIT_BANK_NEIMENG = 164;// 浦发银行信用卡- 内蒙

    public static final int PURCHASE_PARTY_GDB_CREDIT_CARD_GUANGXI = 171; // 广发信用卡签约指定卡-广西
    public static final int PURCHASE_PARTY_GDB_CREDIT_CARD_JSUTONG = 172; // 广发信用卡签约指定卡-江苏
    public static final int PURCHASE_PARTY_GDB_CREDIT_CARD_NEIMENG = 173; // 广发信用卡签约指定卡-内蒙


    public static final int  PURCHASE_PARTY_COMM_CREDIT_CARD_GUANGXI = 174; // 交通信用卡签约指定卡-广西
    public static final int PURCHASE_PARTY_COMM_CREDIT_CARD_JSUTONG = 175;// 交通信用卡签约指定卡-江苏
    public static final int  PURCHASE_PARTY_COMM_CREDIT_CARD_NEIMENG = 176; // 交通信用卡签约指定卡-内蒙


    public static final int PURCHASE_PARTY_CMBC_CREDIT_CARD_GUANGXI = 177;// 民生信用卡签约指定卡-广西
    public static final int PURCHASE_PARTY_CMBC_CREDIT_CARD_JSUTONG = 178;// 民生信用卡签约指定卡-江苏
    public static final int  PURCHASE_PARTY_CMBC_CREDIT_CARD_NEIMENG = 179; // 民生信用卡签约指定卡-内蒙

    public static final int PURCHASE_PARTY_PAB_CREDIT_CARD_GUANGXI = 180; // 平安信用卡签约指定卡-广西
    public static final int PURCHASE_PARTY_PAB_CREDIT_CARD_JSUTONG = 181; // 平安信用卡签约指定卡-江苏
    public static final int PURCHASE_PARTY_PAB_CREDIT_CARD_NEIMENG = 182; // 平安信用卡签约指定卡-内蒙

    public static final int PURCHASE_PARTY_SPD_CREDIT_BANK_WANG_LU_ZL = 165;//浦发银行信用卡-指定卡- 网络智联
    public static final int PURCHASE_PARTY_CITIC_CREDIT_BANK_WANG_LU_ZL = 166;//中信银行信用卡-指定卡- 网络智联
    public static final int  PURCHASE_PARTY_GDB_CREDIT_BANK_WANG_LU_ZL = 167;//广发银行信用卡-指定卡- 网络智联
    public static final int PURCHASE_PARTY_PAB_CREDIT_BANK_WANG_LU_ZL = 168;//平安银行信用卡-指定卡- 网络智联
    public static final int PURCHASE_PARTY_COMM_CREDIT_BANK_WANG_LU_ZL = 169;//交通银行信用卡-指定卡- 网络智联
    public static final int  PURCHASE_PARTY_CMBC_CREDIT_BANK_WANG_LU_ZL = 170;//民生银行信用卡-指定卡- 网络智联
}
