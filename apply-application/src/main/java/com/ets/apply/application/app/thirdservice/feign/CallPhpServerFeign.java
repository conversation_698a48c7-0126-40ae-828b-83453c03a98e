package com.ets.apply.application.app.thirdservice.feign;

import com.ets.apply.application.app.thirdservice.fallback.CallPhpApplyFallbackFactory;
import com.ets.apply.application.common.dto.request.bank.common.CreditCardApplyOrderDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 调用PHP队列
 */
@FeignClient(
        url = "${params.microUrls.etc-server}",
        name = "CallPhpServerFeign",
        fallbackFactory = CallPhpApplyFallbackFactory.class
)
public interface CallPhpServerFeign {

    @PostMapping(value = "/apply/credit-card-apply/third-apply-order")
    String thirdApplyOrder(@RequestBody CreditCardApplyOrderDTO params);

}
