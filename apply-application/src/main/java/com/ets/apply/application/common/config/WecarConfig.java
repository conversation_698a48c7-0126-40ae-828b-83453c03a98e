package com.ets.apply.application.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.List;
@RefreshScope
@Configuration
@Data
@ConfigurationProperties(prefix = "params.wecar")
public class WecarConfig {

    /**
     * 商户id
     */
    private String apiKey;

    /**
     * 秘钥
     */
    private String secretKey;


    /**
     * 通知状态路径
     */
    private String notifyUrl;

    /**
     * 需要退押金的产品包
     */
    private List<String> needRefundPackageSns;
}
