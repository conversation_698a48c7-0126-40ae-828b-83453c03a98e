package com.ets.apply.application.common.utils.bank.cgb;

import cn.com.infosec.isfj.cryptoutil.SymKeyUtil;
import cn.com.infosec.isfj.enums.BlockCipherModeEnum;
import cn.com.infosec.isfj.enums.SymmetricAlgorithm;

import static cn.com.infosec.isfj.enums.PaddingMethodEnum.PKCS5Padding;
import static cn.com.infosec.isfj.enums.PaddingMethodEnum.PKCS7Padding;

/**
 * SM4对称算法
 *
 * <AUTHOR>
 * @date 2022-07-15-15:09
 */
public class Sm4Utils {
    private Sm4Utils() {
    }

    /**
     * SM4 CBC对称算法加解密
     *
     * @param sm4key SM4密钥
     * @param data   请求数据
     * @param isEnc  是否加解密 true加密 false解密
     * @return
     */
    public static String symmetricCryptToCbc(String sm4key, String data, boolean isEnc) {
        return SymKeyUtil.symmetricCrypt(sm4key, data, sm4key, null, 0,
                SymmetricAlgorithm.SM4, BlockCipherModeEnum.CBC, PKCS7Padding, isEnc);
    }

    /**
     * SM4 ECB对称算法加解密
     *
     * @param sm4key SM4密钥
     * @param data   请求数据
     * @param isEnc  是否加解密 true加密 false解密
     * @return
     */
    public static String symmetricCryptToEcb(String sm4key, String data, boolean isEnc) {
        return SymKeyUtil.symmetricCrypt(sm4key, data, sm4key, null, 0,
                SymmetricAlgorithm.SM4, BlockCipherModeEnum.ECB, PKCS5Padding, isEnc);
    }
}

