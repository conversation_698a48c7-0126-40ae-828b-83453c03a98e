package com.ets.apply.application.common.vo.applyGuide;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class ApplyGuideResultWithItemVO {
    /**
     * 结果ID
     */
    private Integer id;

    /**
     * 问题组合
     */
    private String questionMapGroup;

    /**
     * 产品编号
     */
    private String packageSn;

    /**
     * 产品标题
     */
    private String productTitle;

    /**
     * 产品图片URL
     */
    private String productImgUrl;

    /**
     * 产品包价格
     */
    private BigDecimal packageFee;
    
    /**
     * 产品包原价
     */
    private BigDecimal originalPrice;

    /**
     * 功能标签
     */
    private List<ApplyGuideTagVO> tagList;

    /**
     * 问题项列表
     */
    private List<ApplyGuideMapVO> questionItems;
} 