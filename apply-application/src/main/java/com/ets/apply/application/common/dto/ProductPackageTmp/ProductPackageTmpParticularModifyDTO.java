package com.ets.apply.application.common.dto.ProductPackageTmp;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import java.util.List;

@Data
public class ProductPackageTmpParticularModifyDTO {
//     * 激活引导
//     *
//     * 设备名称
//     * 产品包名 package_name
//     * 业务归属 biz_type
//     * 可用券
//     * 收货配置id address_config_id



    @NotNull(message = "packageSn不可为空")
    private String packageSn;


    @Length(max = 50, message = "package_name长度不能超过50")
    private String packageName;

    @Min(value = 0, message = "业务分类最小值为0")
    @Max(value = 999, message = "业务分类最大值为999")
    private Integer bizType;


    @Min(value = 0, message = "发货配置id最小值为0")
    private Integer addressConfigId;


    @Length(max = 50, message = "device_name长度不能超过50个字符")
    private String deviceName;


    /**
     * 产品包可用优惠券批次号
     */
    private List<String> canUseCouponBatchNo;


    @Min(value = 0, message = "device_tutorial最小值为0")
    private Integer deviceTutorial;

}
