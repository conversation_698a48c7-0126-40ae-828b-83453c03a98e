package com.ets.apply.application.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.apply.application.app.business.map.MapRecommendBusiness;
import com.ets.apply.application.app.business.map.MapRuleBusiness;
import com.ets.apply.application.common.dto.map.*;
import com.ets.apply.application.common.utils.UserUtil;
import com.ets.apply.application.common.vo.map.MapRecommendListVO;
import com.ets.apply.application.common.vo.map.MapRuleCombineListVO;
import com.ets.apply.application.common.vo.map.MapRuleItemListVO;
import com.ets.apply.application.controller.BaseController;
import com.ets.common.JsonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import jakarta.validation.Valid;

@Controller
@RequestMapping("/admin/mapRecommend")
public class MapRecommendController extends BaseController {
    @Autowired
    private MapRecommendBusiness mapRecommendBusiness;

    /**
     * 模块列表查询
     * @return
     */
    @RequestMapping("/get-list")
    @ResponseBody
    public JsonResult<IPage<MapRecommendListVO>> getList(@RequestBody(required = false) @Valid MapRecommendDTO dto) {
        return JsonResult.ok(mapRecommendBusiness.getList(dto));
    }



    /**
     *  添加产品包
     * @return
     */
    @RequestMapping("/add")
    @ResponseBody
    public JsonResult<Boolean> add(@RequestBody(required = false) @Valid MapRecommendEditDTO dto) {
        mapRecommendBusiness.addRecommend(dto, UserUtil.getLoginCode());
        return JsonResult.ok(true);
    }
    /**
     *  修改产品包
     * @return
     */
    @RequestMapping("/modify")
    @ResponseBody
    public JsonResult<Boolean> modify(@RequestBody(required = false) @Valid MapRecommendEditDTO dto) {
        mapRecommendBusiness.updateRecommend(dto,UserUtil.getLoginCode());
        return JsonResult.ok(true);
    }
    /**
     *  修改产品包
     * @return
     */
    @RequestMapping("/delRecommend")
    @ResponseBody
    public JsonResult<Boolean> delRecommend(@RequestBody(required = false) @Valid MapRecommendEditDTO dto) {
        mapRecommendBusiness.delRecommend(dto,UserUtil.getLoginCode());
        return JsonResult.ok(true);
    }

    /**
     *  发布生产
     * @return
     */
    @RequestMapping("/prod")
    @ResponseBody
    public JsonResult<Boolean> prod(@RequestBody(required = false) @Valid MapRecommendPordDTO dto) {
        mapRecommendBusiness.prod(dto);
        return JsonResult.ok(true);
    }
}
