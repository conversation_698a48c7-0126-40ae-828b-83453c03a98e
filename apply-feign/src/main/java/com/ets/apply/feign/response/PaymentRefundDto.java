package com.ets.apply.feign.response;


import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

@Data
public class PaymentRefundDto {

    @JSONField(name = "refund_sn")
    private String refundSn;
    private String uid;
    private double amount;
    @JSONField(name = "pay_amount")
    private String payAmount;
    @JSONField(name = "wx_amount")
    private String wxAmount;
    @JSONField(name = "payment_sn")
    private String paymentSn;
    @JSONField(name = "product_id")
    private String productId;
    @JSONField(name = "product_type")
    private String productType;
    @JSONField(name = "transaction_id")
    private String transactionId;
    private int status;
    private String reason;
    @JSONField(name = "third_notify_url")
    private String thirdNotifyUrl;
    @JSONField(name = "refund_type")
    private int refundType;
    @JSONField(name = "created_at")
    private Date createdAt;
    @<PERSON><PERSON>NField(name = "updated_at")
    private Date updatedAt;
    private int id;

}