package com.ets.apply.application.app.service.thirdPartner;

import cn.hutool.core.util.HexUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.ets.apply.application.app.business.ThirdInterfaceLogBusiness;
import com.ets.apply.application.app.thirdservice.feign.CallDongFengFeign;
import com.ets.apply.application.common.config.DongFengConfig;
import com.ets.apply.application.common.consts.dongFengNissan.DongFengNissanResultEnum;
import com.ets.apply.application.common.dto.request.ThirdInterfaceLogDTO;
import com.ets.apply.application.common.utils.dongFeng.Sha512Utils;
import com.ets.apply.application.common.utils.dongFeng.Sm2Utils;
import com.ets.common.JsonResult;
import com.ets.common.ToolsHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component(value = "DongFengService")
public class DongFengService {

    @Autowired
    private DongFengConfig dongFengConfig;

    @Autowired
    private CallDongFengFeign dongFengFeign;

    @Autowired
    private ThirdInterfaceLogBusiness thirdInterfaceLogBusiness;



    private Map<String, String> headerMap(String api, String encryptJsonStr) {
        String noncestr = java.util.UUID.randomUUID().toString().replace("-", "");
        long timestamp = System.currentTimeMillis();

        String tempKeysignStr = dongFengConfig.getAppid() + dongFengConfig.getAppkey() + api + noncestr + timestamp + encryptJsonStr;


        String keysign = Sha512Utils.encrypt(tempKeysignStr);

        return new HashMap<String, String>() {{
            put("api", api);
            put("content-type", MediaType.APPLICATION_JSON_VALUE);
            put("appid", dongFengConfig.getAppid());
            put("keysign", keysign);
            put("noncestr", noncestr);
            put("timestamp", "" + timestamp);
        }};
    }

    public void sendNotify(HashMap<String,Object> params,String api){

        log.info("东风日产通知参数{}",JSONUtil.toJsonStr(params));

        //补充日志记录
        ThirdInterfaceLogDTO thirdInterfaceLogDTO = new ThirdInterfaceLogDTO();
        thirdInterfaceLogDTO.setLogMethod("DongFengService:sendNotify");
        thirdInterfaceLogDTO.setLogRequest(api);
        thirdInterfaceLogDTO.setLogParams(JSONUtil.toJsonStr(params));

        String encryptStr = HexUtil.encodeHexStr(Sm2Utils.encrypt(JSONUtil.toJsonStr(params),
                dongFengConfig.getServerPublicKey()));
        BodyVO body = new BodyVO(encryptStr);

        String bodyEncryptStr = JSONUtil.toJsonStr(body);

        String result = dongFengFeign.orderNotify(bodyEncryptStr, headerMap(api, bodyEncryptStr));

        log.info("东风日产通知结果{}",result);

        JSONObject jsonObjectResult = JSONObject.parseObject(result);
        String v = jsonObjectResult.getString("v");
        String resultDecryptStr =  Sm2Utils.decrypt(v, dongFengConfig.getClientPrivateKey());
        // 结果处理
        log.info("东风日产返回结果解密: {}", resultDecryptStr);

        JSONObject jsonObjectResultDecrypt = JSONObject.parseObject(resultDecryptStr);
        thirdInterfaceLogDTO.setLogRespone(resultDecryptStr);
        thirdInterfaceLogBusiness.addLog(thirdInterfaceLogDTO);

        String resultCode = jsonObjectResultDecrypt.getString("result");
        if(!DongFengNissanResultEnum.SUCCESS.getResult().equals(resultCode)){
            log.error("东风日产通知失败: {}",resultCode);
            ToolsHelper.throwException("东风日产通知失败: " + resultCode);
        }

    }

    public class BodyVO {
        private String v;

        public BodyVO() {
        }

        public BodyVO(String v) {
            this.v = v;
        }

        public String getV() {
            return v;
        }

        public void setV(String v) {
            this.v = v;
        }
    }
}
