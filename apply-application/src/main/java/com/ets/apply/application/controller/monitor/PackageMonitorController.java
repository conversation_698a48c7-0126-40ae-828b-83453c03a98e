package com.ets.apply.application.controller.monitor;

import com.ets.apply.application.app.business.monitor.PackageMonitorBusiness;
import com.ets.apply.application.common.dto.monitor.packages.CheckPackageStockDTO;
import com.ets.apply.application.common.vo.monitor.packages.CheckPackageStockVO;
import com.ets.common.JsonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/monitor/package")
public class PackageMonitorController {

    @Autowired
    private PackageMonitorBusiness packageMonitorBusiness;

    @RequestMapping("/checkStockAlarm")
    public JsonResult<List<CheckPackageStockVO>> checkStockAlarm(@RequestBody CheckPackageStockDTO stockDTO) {
        return JsonResult.ok(packageMonitorBusiness.checkStockAlarm(stockDTO));
    }
}
