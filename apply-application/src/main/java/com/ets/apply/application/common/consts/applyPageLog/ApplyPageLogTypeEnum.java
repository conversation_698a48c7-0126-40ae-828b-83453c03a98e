package com.ets.apply.application.common.consts.applyPageLog;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ApplyPageLogTypeEnum {

    CREATED(1, "创建"),
    LISTING(2, "上架"),
    DELIST(3, "下架"),
    SET_PAGE_TYPE(4, "设置模板"),
    MODIFY(5, "修改");

    private final Integer code;

    private final String description;

    public static String getDescByCode(Integer code) {
        for (ApplyPageLogTypeEnum node : ApplyPageLogTypeEnum.values()) {
            if (node.getCode().equals(code)) {
                return node.getDescription();
            }
        }

        return "";
    }

}