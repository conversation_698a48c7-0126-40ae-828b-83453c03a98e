package com.ets.apply.application.common.consts.reviewOrder;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum LocalReviewStatus {

    waiting(1, "待审核"),
    success(2, "审核通过"),
    fail(3, "自动审核失败"),
    pass(4, "无需审核");


    private final int code;
    private final String description;

    public static String getDescByCode(int code) {
        for (LocalReviewStatus node : LocalReviewStatus.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }
}
