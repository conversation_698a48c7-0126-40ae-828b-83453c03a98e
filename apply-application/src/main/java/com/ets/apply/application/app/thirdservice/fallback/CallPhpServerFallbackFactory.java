package com.ets.apply.application.app.thirdservice.fallback;


import com.ets.apply.application.app.thirdservice.feign.CallPhpServerFeign;
import com.ets.apply.application.common.dto.request.bank.common.CreditCardApplyOrderDTO;
import com.ets.common.JsonResult;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

@Component
public class CallPhpServerFallbackFactory implements FallbackFactory<CallPhpServerFeign> {

    @Override
    public CallPhpServerFeign create(Throwable cause) {
        return new CallPhpServerFeign() {
            @Override
            public String thirdApplyOrder(@RequestBody CreditCardApplyOrderDTO params) {
                return JsonResult.error("请求PHP server 服务失败: " + cause.getMessage()).toString();
            }
        };
    }
}