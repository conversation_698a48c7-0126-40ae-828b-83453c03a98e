package com.ets.apply.application.controller.admin;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.apply.application.app.business.sales.SalesPackageCouponBusiness;
import com.ets.apply.application.common.dto.adminSalesCoupon.*;
import com.ets.apply.application.common.vo.adminSalesCoupon.SalesCouponInfoVO;
import com.ets.apply.application.common.vo.adminSalesCoupon.SalesCouponListVO;
import com.ets.apply.application.controller.BaseController;
import com.ets.common.JsonResult;
import com.ets.common.RequestHelper;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;


@Controller
@RequestMapping("/admin/salesCoupon")
public class AdminSalesCouponController extends BaseController {
    @Autowired
    private SalesPackageCouponBusiness salesCouponBusiness;

    /**
     * 营销列表查询
     * @return
     */
    @RequestMapping("/getList")
    @ResponseBody
    public JsonResult<IPage<SalesCouponListVO>> getList(@RequestBody(required = false) @Valid SalesCouponGetListDTO dto) {
        return JsonResult.ok(salesCouponBusiness.getList(dto));
    }


    /**
     *  新增计划
     * @return
     */
    @RequestMapping("/addPlan")
    @ResponseBody
    public JsonResult<Boolean> addPlan(@RequestBody(required = false) @Valid SalesCouponAddDTO dto) {
        salesCouponBusiness.addPlan(dto, RequestHelper.getHttpServletRequest().getHeader("loginCode"));
        return JsonResult.ok(true);
    }

    /**
     *  修改产品包
     * @return
     */
    @RequestMapping("/modifyPlan")
    @ResponseBody
    public JsonResult<Boolean> modifyPlan(@RequestBody(required = false) @Valid SalesCouponEditDTO dto) {
        salesCouponBusiness.modifyPlan(dto, RequestHelper.getHttpServletRequest().getHeader("loginCode"));
        return JsonResult.ok(true);
    }

    /**
     *  上下架计划
     * @return
     */
    @RequestMapping("/upOrDown")
    @ResponseBody
    public JsonResult<Boolean> upOrDown(@RequestBody(required = false) @Valid SalesCouponUpOrDownDTO dto) {
        salesCouponBusiness.upOrDown(dto, RequestHelper.getHttpServletRequest().getHeader("loginCode"));
        return JsonResult.ok(true);
    }

    /**
     * 获取详情
     * @return
     */
    @RequestMapping("/getInfo")
    @ResponseBody
    public JsonResult<SalesCouponInfoVO> getInfo(@RequestBody(required = false) @Valid SalesCouponGetInfoDTO dto) {
        return JsonResult.ok(salesCouponBusiness.getInfo(dto));
    }


    /**
     * 发布前对比
     * @return
     */
    @RequestMapping("/compareInfo")
    @ResponseBody
    public JsonResult<JSONObject> compareInfo(@RequestBody(required = false) @Valid SalesCouponGetInfoDTO dto) {
        return JsonResult.ok(salesCouponBusiness.compareInfo(dto));
    }

    /**
     * 发布
     * @return
     */
    @RequestMapping("/prod")
    @ResponseBody
    public JsonResult<Boolean> prod(@RequestBody(required = false) @Valid SalesCouponProdDTO dto) {
        return JsonResult.ok(salesCouponBusiness.prod(dto, RequestHelper.getHttpServletRequest().getHeader("loginCode")));
    }


}
