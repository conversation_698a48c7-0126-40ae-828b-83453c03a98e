package com.ets.apply.application.common.consts.segmentBenefit;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum SegmentBenefitStatusEnum {

    NORMAL(1, "正常"),
    DELETE(-1, "删除");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;

    static {
        map = Arrays.stream(SegmentBenefitStatusEnum.values()).collect(Collectors.toMap(SegmentBenefitStatusEnum::getValue, SegmentBenefitStatusEnum::getDesc));
    }
}
