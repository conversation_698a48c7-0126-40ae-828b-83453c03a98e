<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ets.apply.application.infra.mapper.ActivityCreditCardUsersInfoMapper">

    <select id="getCreditCardList" resultType="com.ets.apply.application.common.vo.creditCard.CreditCardListVO">

        SELECT u.id,u.id, u.uid, u.order_sn, u.which_bank,
        u.plate_no, u.created_at, u.status,
        b.is_new_user, b.audit_time,
        b.activate_time, b.first_use_date
        FROM etc_activity_credit_card_users_info u
        JOIN etc_activity_credit_card_bank_users b
        ON u.order_sn=b.apply_number

        <where>
            <if test="whichBank != null and whichBank != ''">
                and u.which_bank = #{whichBank}
            </if>
            <if test="uid != null and uid != ''">
                and u.uid = #{uid}
            </if>
            <if test="status != null and status != ''">
                and u.status = #{status}
            </if>

            <if test="plateNo != null and plateNo != ''">
                and u.plate_no = #{plateNo}
            </if>
            <if test="auditStartTime != null and auditStartTime != ''">
                and b.audit_time >= #{auditStartTime}
            </if>
            <if test="auditEndTime != null and auditEndTime != ''">
                <![CDATA[ and b.audit_time <= #{auditEndTime} ]]>
            </if>

        </where>
    </select>
</mapper>
