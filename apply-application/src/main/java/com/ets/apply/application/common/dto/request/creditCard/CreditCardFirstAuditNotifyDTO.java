package com.ets.apply.application.common.dto.request.creditCard;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class CreditCardFirstAuditNotifyDTO {

    /**
     * uid
     */
    private Long uid;

    /**
     * 银行
     */
    @JsonProperty(value = "which_bank")
    private Integer whichBank;

    @JsonProperty(value = "apply_number")
    private String applyNumber;

    @JsonProperty(value = "first_audit_time")
    private String firstAuditTime;

    @JsonProperty(value = "refer_type")
    private Integer referType;

    private Integer status;
}
