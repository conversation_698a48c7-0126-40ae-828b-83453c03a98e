package com.ets.apply.application.common.consts.activityCreditCardBankUser;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum ActivityCreditCardBankUserIsFirstUseEnum {

    FIRST_USER_DEFAULT(0, "未首刷"),
    FIRST_USE_YES(1, "已首刷");

    private final int code;
    private final String description;

    public static String getDescByCode(int code) {
        for (ActivityCreditCardBankUserIsFirstUseEnum node : ActivityCreditCardBankUserIsFirstUseEnum.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }

    public static List<Map<String, String>> getLabelList() {
        List<Map<String, String>> list = new ArrayList<>();

        for (ActivityCreditCardBankUserIsFirstUseEnum node : ActivityCreditCardBankUserIsFirstUseEnum.values()) {
            Map<String, String> row = new LinkedHashMap<>();
            row.put("label", node.getDescription());
            row.put("value", String.valueOf(node.getCode()));

            list.add(row);
        }

        return list;
    }
}
