package com.ets.apply.application.app.thirdservice.feign;

import com.ets.apply.application.app.thirdservice.fallback.CallJavaBaseFallbackFactory;
import com.ets.apply.application.app.thirdservice.request.javaBase.CosGetSignDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 调用java-base接口
 */
@FeignClient( url = "${params.microUrls.etc-java-base}", name = "CallJavaBaseFeign", fallbackFactory = CallJavaBaseFallbackFactory.class)
public interface CallJavaBaseFeign {

    @PostMapping("/cos/getSignUrl")
    String getSignUrl(@RequestBody CosGetSignDTO dto);

}
