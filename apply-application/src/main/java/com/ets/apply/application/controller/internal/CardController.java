package com.ets.apply.application.controller.internal;

import com.ets.apply.application.app.business.CardBusiness;
import com.ets.apply.application.common.vo.card.CardVO;
import com.ets.apply.application.controller.BaseController;
import com.ets.common.BizException;
import com.ets.common.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.constraints.NotNull;


@RequestMapping("/card")
@RefreshScope
@RestController
@Slf4j
public class CardController extends BaseController {
    @Autowired
    private CardBusiness cardBusiness;

    @RequestMapping("/getCardInfo")
    public JsonResult<CardVO> getInfo(
            @NotNull(message = "卡id不能为空") @RequestParam(value = "cardId", required = false) Integer cardId
    ) throws BizException {
        return JsonResult.ok(cardBusiness.getCardInfo(cardId));
    }


}
