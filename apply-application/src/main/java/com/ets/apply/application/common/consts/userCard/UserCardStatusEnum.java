package com.ets.apply.application.common.consts.userCard;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum UserCardStatusEnum {

    Normal(0, "正常"),

    Loss(2, "挂失"),

    Reissue(3, "补办中"),

    Void(4, "作废"),

    Cancel(8, "注销中"),

    Closed(9, "已注销");

    private final int code;
    private final String description;

    public static String getDescByCode(int code) {
        for (UserCardStatusEnum node : UserCardStatusEnum.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }
}
