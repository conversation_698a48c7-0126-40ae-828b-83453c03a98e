package com.ets.apply.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ets.apply.application.common.consts.recall.RecallStatus;
import com.ets.apply.application.infra.entity.RecallEntity;
import com.ets.apply.application.infra.mapper.RecallMapper;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 申办召回 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-11
 */
@Service
@DS("db-apply")
public class RecallService extends BaseService<RecallMapper, RecallEntity> {
    public Boolean isExistRecallQualification(String orderSn, Integer recallType) {

        LambdaQueryWrapper<RecallEntity> wrapper = new LambdaQueryWrapper<>();

        wrapper.eq(RecallEntity::getOriginOrderSn, orderSn)
                .eq(RecallEntity::getRecallType, recallType)
                .eq(RecallEntity::getRecallStatus, RecallStatus.RECALL_USED.getCode());
        RecallEntity entity = getOneByWrapper(wrapper);

        return entity != null;
    }
}
