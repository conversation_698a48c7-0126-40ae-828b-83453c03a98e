package com.ets.apply.application.common.consts.blacklist;

import com.ets.apply.application.common.consts.validatePlateNo.ValidatePlateNoEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum BlacklistOperationEnum {
    ADD("add", "拉黑"),
    CANCEL("cancel", "取消"),
    REMOVE("remove", "移除");
    private final String operation;

    private final String description;

    public static String getDescByCode(String operation) {
        for (BlacklistOperationEnum node : BlacklistOperationEnum.values()) {
            if (Objects.equals(node.getOperation(), operation)) {
                return node.getDescription();
            }
        }

        return "";
    }

    public static BlacklistOperationEnum getByCode(String operation) {
        for (BlacklistOperationEnum node : BlacklistOperationEnum.values()) {
            if (Objects.equals(node.getOperation(), operation)) {
                return node;
            }
        }

        return null;
    }
}
