package com.ets.apply.application.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.apply.application.app.service.bigdata.BigDataService;
import com.ets.apply.application.common.dto.adminBigData.TagGetListDTO;
import com.ets.apply.application.common.vo.admin.bigData.TagListVO;
import com.ets.common.JsonResult;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/admin/bigData")
@Slf4j
public class AdminBigDataController {

	@Autowired
	private BigDataService bigDataService;
	/*
	 *  获取人群包标签列表
	 */
	@PostMapping("/getTagList")
	public JsonResult<IPage<TagListVO>> getTagList(@Valid @RequestBody TagGetListDTO dto) {

		return JsonResult.ok(bigDataService.atomcmpsTagList(dto));
	}




}
