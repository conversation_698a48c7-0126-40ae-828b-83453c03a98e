<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.ets</groupId>
        <artifactId>apply</artifactId>
        <version>2.0.2-SNAPSHOT</version>
    </parent>

    <artifactId>apply-application</artifactId>
    <packaging>jar</packaging>

    <name>apply-application</name>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.ets</groupId>
            <artifactId>ets-spring-boot-starter</artifactId>
            <version>1.2.4</version>
        </dependency>
        <dependency>
            <groupId>com.ets</groupId>
            <artifactId>ets-db-starter</artifactId>
            <version>1.2.3</version>
        </dependency>
        <dependency>
            <groupId>com.ets</groupId>
            <artifactId>ets-smooth-starter</artifactId>
            <version>1.2.3</version>
        </dependency>

        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-httpclient</artifactId>
            <version>10.10.1</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
        </dependency>

        <!-- excel读写 -->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>5.3.0</version>
        </dependency>

        <dependency>
            <groupId>com.qcloud</groupId>
            <artifactId>cos_api</artifactId>
            <version>5.6.234</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.1.3</version>
        </dependency>

        <dependency>
            <groupId>cfca.sadk.cgb</groupId>
            <artifactId>toolkit</artifactId>
            <version>3.2.1</version>
        </dependency>

        <!-- 国密加密依赖 民生要求1.68 版本 -->
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15to18</artifactId>
            <version>1.68</version>
        </dependency>

        <dependency>
            <groupId>net.sf.json-lib</groupId>
            <artifactId>json-lib</artifactId>
            <version>2.2.2</version>
            <classifier>jdk15</classifier>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
        </dependency>

        <!--        广发银行加密依赖-->
        <dependency>
            <groupId>com.ets</groupId>
            <artifactId>isec-func</artifactId>
            <version>2.0.1.1</version>
        </dependency>

        <dependency>
            <groupId>com.ets</groupId>
            <artifactId>isec-jce</artifactId>
            <version>jdk1.6-2.0.1.1</version>
        </dependency>

        <!-- 国密加密依赖 平安 1.58 版本 -->
        <dependency>
            <groupId>com.ets.shade.bouncycastle</groupId>
            <artifactId>maven-shade-bouncycastle-one-fifty-eight</artifactId>
            <version>1.0</version>
        </dependency>

        <!-- 微服务依赖 -->
        <dependency>
            <groupId>com.ets</groupId>
            <artifactId>apply-feign</artifactId>
            <version>2.0.2-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.ets</groupId>
            <artifactId>user-feign</artifactId>
            <version>2.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.ets</groupId>
            <artifactId>pay-feign</artifactId>
            <version>2.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.ets</groupId>
            <artifactId>base-feign</artifactId>
            <version>2.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.ets</groupId>
            <artifactId>delivery-feign</artifactId>
            <version>2.0.1</version>
        </dependency>

        <!-- Swagger API 文档 -->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>2.9.2</version>
        </dependency>
        
        <!-- 参数校验 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

    </dependencies>


    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <skip>false</skip>
                    <mainClass>com.ets.apply.application.ApplyApplication</mainClass>
                </configuration>
                <version>2.4.5</version>
            </plugin>
        </plugins>
    </build>
</project>
