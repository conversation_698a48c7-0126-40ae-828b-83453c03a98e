package com.ets.apply.application.common.utils.dongFeng;

import cn.hutool.core.util.HexUtil;
import org.apache.commons.codec.DecoderException;
import org.apache.commons.codec.binary.Hex;
import org.bouncycastle.asn1.gm.GMObjectIdentifiers;
import org.bouncycastle.crypto.CipherParameters;
import org.bouncycastle.crypto.InvalidCipherTextException;
import org.bouncycastle.crypto.digests.SM3Digest;
import org.bouncycastle.crypto.engines.SM2Engine;
import org.bouncycastle.crypto.params.ParametersWithRandom;
import org.bouncycastle.jcajce.provider.asymmetric.util.ECUtil;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.springframework.util.StringUtils;

import java.security.*;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

public class Sm2Utils {

    public static String decrypt(String hexStr, String clientPrivateKey) {
        if (StringUtils.isEmpty(hexStr)) {
            return "";
        }
        return decrypt(HexUtil.decodeHex(hexStr), clientPrivateKey);
    }

    /**
     * 加签
     */
    public static String sign(String plainText, String privateKeyStr) {
        BouncyCastleProvider provider = new BouncyCastleProvider();
        try {
            // 获取椭圆曲线KEY生成器
            KeyFactory keyFactory = KeyFactory.getInstance("EC", provider);
            byte[] privateKeyData = Base64.getDecoder().decode(privateKeyStr);
            PKCS8EncodedKeySpec privateKeySpec = new PKCS8EncodedKeySpec(privateKeyData);
            Signature rsaSignature = Signature.getInstance(GMObjectIdentifiers.sm2sign_with_sm3.toString(), provider);
            rsaSignature.initSign(keyFactory.generatePrivate(privateKeySpec));
            rsaSignature.update(plainText.getBytes());
            byte[] signed = rsaSignature.sign();
            return Base64.getEncoder().encodeToString(signed);
        } catch (NoSuchAlgorithmException | InvalidKeySpecException | InvalidKeyException | SignatureException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 验签
     */
    public static boolean verify(String plainText, String signatureValue, String publicKeyStr) {
        BouncyCastleProvider provider = new BouncyCastleProvider();
        try {
            // 获取椭圆曲线KEY生成器
            KeyFactory keyFactory = KeyFactory.getInstance("EC", provider);
            byte[] publicKeyData = Base64.getDecoder().decode(publicKeyStr);
            X509EncodedKeySpec publicKeySpec = new X509EncodedKeySpec(publicKeyData);
            // 初始化为验签状态
            Signature signature = Signature.getInstance(GMObjectIdentifiers.sm2sign_with_sm3.toString(), provider);
            signature.initVerify(keyFactory.generatePublic(publicKeySpec));
            signature.update(Hex.decodeHex(plainText.toCharArray()));
            return signature.verify(Hex.decodeHex(signatureValue.toCharArray()));
        } catch (NoSuchAlgorithmException | InvalidKeySpecException | InvalidKeyException | SignatureException e) {
            throw new RuntimeException(e);
        } catch (IllegalArgumentException e) {
            return false;
        } catch (DecoderException e) {
            return false;
        }
    }

    public static byte[] encrypt(String plainText, String publicKeyStr) {
        Security.addProvider(new BouncyCastleProvider());
        try {
            // 获取椭圆曲线KEY生成器
            KeyFactory keyFactory = KeyFactory.getInstance("EC");
            byte[] publicKeyData = Base64.getDecoder().decode(publicKeyStr);
            X509EncodedKeySpec publicKeySpec = new X509EncodedKeySpec(publicKeyData);
            PublicKey publicKey = keyFactory.generatePublic(publicKeySpec);
            CipherParameters publicKeyParamerters = ECUtil.generatePublicKeyParameter(publicKey);
            //数据加密
            StandardSM2Engine engine = new StandardSM2Engine(new SM3Digest(), SM2Engine.Mode.C1C3C2);
            engine.init(true, new ParametersWithRandom(publicKeyParamerters));
            byte[] encryptData = engine.processBlock(plainText.getBytes(), 0, plainText.getBytes().length);
            return encryptData;
        } catch (NoSuchAlgorithmException | InvalidKeySpecException
                | InvalidKeyException | InvalidCipherTextException e) {
            throw new RuntimeException(e);
        }
    }

    public static String decrypt(byte[] encryptedData, String privateKeyStr) {
        Security.addProvider(new BouncyCastleProvider());
        try {
            // 获取椭圆曲线KEY生成器
            KeyFactory keyFactory = KeyFactory.getInstance("EC");
            byte[] privateKeyData = Base64.getDecoder().decode(privateKeyStr);
            PKCS8EncodedKeySpec privateKeySpec = new PKCS8EncodedKeySpec(privateKeyData);
            PrivateKey privateKey = keyFactory.generatePrivate(privateKeySpec);
            CipherParameters privateKeyParamerters = ECUtil.generatePrivateKeyParameter(privateKey);
            //数据解密
            StandardSM2Engine engine = new StandardSM2Engine(new SM3Digest(), SM2Engine.Mode.C1C3C2);
            engine.init(false, privateKeyParamerters);
            byte[] plainText = engine.processBlock(encryptedData, 0, encryptedData.length);
            return new String(plainText);
        } catch (NoSuchAlgorithmException | InvalidKeySpecException
                | InvalidKeyException | InvalidCipherTextException e) {
            throw new RuntimeException(e);
        }
    }
}
