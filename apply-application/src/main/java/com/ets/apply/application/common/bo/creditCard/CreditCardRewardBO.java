package com.ets.apply.application.common.bo.creditCard;

import lombok.Data;

import java.util.List;

@Data
public class CreditCardRewardBO {

    /**
     * 获取某些时间之前的单
     */
    private Integer beforeHours;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 可发券的银行集合
     */
    List<Integer>  whichBankList;

    /**
     * 可处理的信用卡申请类型
     */
    List<Integer> classify;
}
