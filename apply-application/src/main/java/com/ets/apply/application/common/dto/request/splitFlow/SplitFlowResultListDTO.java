package com.ets.apply.application.common.dto.request.splitFlow;

import jakarta.validation.constraints.Min;
import lombok.Data;
import org.hibernate.validator.constraints.Length;


@Data
public class SplitFlowResultListDTO {
    /**
     * 分流类型[etc_2.0_before_pay-2.0支付前]
     */
    @Length(min = 1, max = 20, message = "分流类型请输入20个以内字符")
    private String splitType;

    /**
     * 分流的周期：20210420
     */
    private String splitTerm;
    private String splitResult;
    /**
     * 用户uid
     */
    private Long uid;

    /**
     * 非必填参数
     */
    private String orderSn;

    private String createTimeBegin;
    private String createTimeEnd;
    @Min(1)
    private Integer pageNum = 1;
    @Min(1)
    private Integer pageSize = 20;
}
