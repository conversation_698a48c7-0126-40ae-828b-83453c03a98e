package com.ets.apply.application.controller.frontend;

import com.ets.apply.application.app.business.dynamicRecommend.DynamicRecommendBusiness;
import com.ets.apply.application.app.thirdservice.request.bigData.DynamicRecommendDTO;
import com.ets.apply.application.common.dto.dynamicRecommend.RecommendDTO;
import com.ets.apply.application.common.utils.UserUtil;
import com.ets.apply.application.common.vo.dynamicRecommend.DynamicRecommendVO;
import com.ets.common.JsonResult;
import com.ets.common.annotation.RateLimiterAnnotation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/frontend/dynamic-recommend")
public class DynamicRecommendController {

    @Autowired
    private DynamicRecommendBusiness dynamicRecommendBusiness;

    /**
     * @deprecated
     * @link <a href="https://yapi.etczs.net/project/312/interface/api/30669">动态推荐</a>
     */
    @PostMapping("/recommend")
    @RateLimiterAnnotation(qps = 2000, msg = "请求繁忙超过限制，请稍后再试")
    JsonResult<DynamicRecommendVO> recommend() {

        return JsonResult.ok(dynamicRecommendBusiness.recommend(UserUtil.getUid()));
    }

    @PostMapping("/recommend-v2")
    @RateLimiterAnnotation(qps = 2000, msg = "请求繁忙超过限制，请稍后再试")
    JsonResult<DynamicRecommendVO> recommendV2(@RequestBody RecommendDTO recommendDTO) {

        return JsonResult.ok(dynamicRecommendBusiness.recommendV2(UserUtil.getUid(),recommendDTO));
    }
}
