package com.ets.apply.application.app.thirdservice.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.net.URI;
import java.util.HashMap;

/**
 * 通知类
 */
@FeignClient(name = "ExternalNotifyFeign", url = "uri")
public interface ExternalNotifyFeign {

    @PostMapping
    String shippedNotify(
            URI uri,
            @RequestHeader HashMap<String, String> headers,
            @RequestBody String body
    );

    @PostMapping
    String afterSaleFinishNotify(
            URI uri,
            @RequestHeader HashMap<String, String> headers,
            @RequestBody String body
    );

    @PostMapping
    String activatedNotify(
            URI uri,
            @RequestHeader HashMap<String, String> headers,
            @RequestBody String body
    );

}
