package com.ets.apply.application.controller.frontend;

import com.ets.apply.application.app.business.unicom.UnicomApplyBusiness;
import com.ets.apply.application.common.dto.unicom.*;
import com.ets.apply.application.common.vo.unicom.*;
import com.ets.apply.application.controller.BaseController;
import com.ets.common.JsonResult;
import com.ets.common.annotation.RateLimiterAnnotation;
import com.ets.common.util.UserUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/frontend/unicomApply")
public class FrontendUnicomApplyController extends BaseController {

    @Autowired
    private UnicomApplyBusiness unicomApplyBusiness;

    @PostMapping("/chooseNumber")
    @RateLimiterAnnotation(qps = 30)
    public JsonResult<List<UnicomChooseNumberVO>> chooseNumber(@RequestBody @Valid UnicomChooseNumberDTO dto) {

        return JsonResult.ok(unicomApplyBusiness.chooseNumber(dto));
    }

    @PostMapping("/getExistOrder")
    public JsonResult<UnicomExistOrderVO> getExistOrder(@RequestBody @Valid UnicomExistOrderDTO dto) {

        dto.setUid(UserUtil.getUid());

        return JsonResult.ok(unicomApplyBusiness.getExistOrder(dto));
    }

    @PostMapping("/checkHasOrder")
    public JsonResult<UnicomCheckHasOrderVO> checkHasOrder(@RequestBody @Valid UnicomExistOrderDTO dto) {

        dto.setUid(UserUtil.getUid());

        return JsonResult.ok(unicomApplyBusiness.checkHasOrder(dto));
    }

    @PostMapping("/bindReuseOrder")
    public JsonResult<UnicomBindReuseOrderVO> bindReuseOrder(@RequestBody @Valid UnicomBindReuseOrderDTO dto) {

        dto.setUid(UserUtil.getUid());

        return JsonResult.ok(unicomApplyBusiness.bindReuseOrder(dto));
    }

    @PostMapping("/getVerifyCode")
    public JsonResult<Object> getVerifyCode(@RequestBody @Valid UnicomGetVerifyCodeDTO dto) {

        dto.setUid(UserUtil.getUid());
        unicomApplyBusiness.getVerifyCode(dto);

        return JsonResult.ok();
    }

    @PostMapping("/createOrder")
    public JsonResult<UnicomCreateOrderVO> createOrder(@RequestBody @Valid UnicomCreateOrderDTO dto) {

        dto.setUid(UserUtil.getUid());

        return JsonResult.ok(unicomApplyBusiness.createOrder(dto));
    }

    @PostMapping("/getProcessingOrder")
    public JsonResult<UnicomProcessingOrderVO> getProcessingOrder(@RequestBody @Valid UnicomGetProcessingOrderDTO dto) {

        dto.setUid(UserUtil.getUid());

        return JsonResult.ok(unicomApplyBusiness.getProcessingOrder(dto, null));
    }

    @PostMapping("/cancel")
    public JsonResult<Object> cancel(@RequestBody @Valid UnicomGetProcessingOrderDTO dto) {

        dto.setUid(UserUtil.getUid());

        unicomApplyBusiness.cancel(dto.getUnicomApplySn(), dto.getUid());

        return JsonResult.ok();
    }
}
