package com.ets.apply.application.common.consts.productOrder;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ProductOrderLogisticStatusEnum {

    UN_PUSH(0, "未推送"),
    PUSHING(1, "推送中"),
    PUSHED(2, "已推送"),
    SHIPPED(3, "已发货"),
    RECEIVED(4, "已签收");

    private final Integer code;

    private final String description;

    public static String getDescByCode(Integer code) {
        for (ProductOrderLogisticStatusEnum node : ProductOrderLogisticStatusEnum.values()) {
            if (node.getCode().equals(code)) {
                return node.getDescription();
            }
        }

        return "N/A";
    }

}
