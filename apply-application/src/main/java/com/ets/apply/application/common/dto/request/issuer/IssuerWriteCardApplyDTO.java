package com.ets.apply.application.common.dto.request.issuer;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class IssuerWriteCardApplyDTO {

    @JsonProperty(value = "order_sn")
    private String orderSn;
    private String steps = "writeCardApply";
    @JsonProperty(value = "card_no")
    private String cardNo;
    @JsonProperty(value = "plate_no")
    private String plateNo;
    @JsonProperty(value = "plate_color")
    private Integer plateColor;
    @JsonProperty(value = "write_card_amount")
    private Integer writeCardAmount;
    @JsonProperty(value = "card_amount")
    private Integer cardAmount;
    private String rand;
    @JsonProperty(value = "trade_no")
    private String tradeNo;
    @JsonProperty(value = "term_id")
    private String termId;
    @JsonProperty(value = "write_card_at")
    private String writeCardAt;
    @JsonProperty(value = "mac_no")
    private String macNo = "";
    @JsonProperty(value = "callback_url")
    private String callbackUrl = "";
}
