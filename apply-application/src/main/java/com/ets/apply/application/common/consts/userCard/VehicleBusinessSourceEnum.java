package com.ets.apply.application.common.consts.userCard;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum VehicleBusinessSourceEnum {
    REAPPLY(3, "补办"),
    REPAIR_EXCHANGE(4, "维修换货"),
    RETURN_REFUNDS(5, "退货退款"),
    REVOKE(6, "注销"),
    REACTIVATE(7, "二次激活"),
    UPGRADE(9, "设备升级");

    private final Integer value;
    private final String desc;
}
