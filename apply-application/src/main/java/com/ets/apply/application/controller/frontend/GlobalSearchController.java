package com.ets.apply.application.controller.frontend;

import com.ets.apply.application.app.business.GlobalSearchBusiness;
import com.ets.apply.application.common.dto.GlobalSearchResultDTO;
import com.ets.apply.application.common.vo.GlobalSearchResultVO;
import com.ets.common.JsonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

@RestController
@RequestMapping("/frontend/global-search")
public class GlobalSearchController {

    @Autowired
    private GlobalSearchBusiness globalSearchBusiness;

    @PostMapping("/get-result")
    public JsonResult<GlobalSearchResultVO> getResult(@RequestBody @Valid GlobalSearchResultDTO searchResultDTO) {
        return JsonResult.ok(globalSearchBusiness.getResult(searchResultDTO));
    }
}
