package com.ets.apply.application.app.factory.productPartner.impl;

import com.alibaba.fastjson.JSON;
import com.ets.apply.application.app.business.ProductOrderBusiness;
import com.ets.apply.application.common.bo.productOrder.ProductOrderRefundBO;
import com.ets.apply.application.common.bo.productOrder.ProductOrderShipBO;
import com.ets.apply.application.common.bo.productOrder.ProductOrderThirdSyncBO;
import com.ets.apply.application.common.bo.xconnect.FeignResultBO;
import com.ets.apply.application.common.consts.productOrder.ProductOrderStatusEnum;

import com.ets.apply.application.common.dto.request.productOrder.ThirdOrderNotifyDTO;
import com.ets.apply.application.infra.entity.ProductOrderEntity;
import com.ets.apply.application.infra.service.ProductOrderService;
import com.ets.common.ToolsHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;

@Component
public class GeelyPartnerValue extends ProductPartnerBase {

    @Autowired
    ProductOrderBusiness productOrderBusiness;
    @Autowired
    private ProductOrderService productOrderService;
    /**
     *  退款
     */
    public String refund(ProductOrderRefundBO productOrderRefundBO) {
        productOrderBusiness.thirdOrderRefund(productOrderRefundBO);
        return "";
    }


    /**
     * 通知wecar 发货
     *
     * @param productOrderShipBO
     */
    public void ship(ProductOrderShipBO productOrderShipBO) {
        ProductOrderEntity productOrderEntity = productOrderService.getOneByColumn(productOrderShipBO.getProductOrderSn(), ProductOrderEntity::getProductOrderSn);
        //已关闭或者退款中，不需要通知
        if(Arrays.asList(
            ProductOrderStatusEnum.CLOSED.getCode(),
            ProductOrderStatusEnum.REFUNDING.getCode()
        ).contains(productOrderEntity.getOrderStatus())){
            return;
        }
        ThirdOrderNotifyDTO dto = new ThirdOrderNotifyDTO();
        dto.setSpOrderId(productOrderShipBO.getProductOrderSn());
        dto.setOrderStatus(ProductOrderStatusEnum.SHIPPED.getCode());
        productOrderBusiness.notify(dto);

    }
    /*
     * 订单状态同步
     */
    public  void orderSync(ProductOrderEntity productOrder, ProductOrderThirdSyncBO bo){
        productOrderBusiness.thirdOrderStatusNotify(productOrder, bo.getStatus());
    }
}
