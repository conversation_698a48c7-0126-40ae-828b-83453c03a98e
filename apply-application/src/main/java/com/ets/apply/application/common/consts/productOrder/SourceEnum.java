package com.ets.apply.application.common.consts.productOrder;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.ObjectUtils;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum SourceEnum {
//    NORMAL(0, "通用产品"),
    JD("1260", "京东", false),
    BDH5("1256", "百度H5", false),
    BD("1257", "百度小程序", false),
    TIKTOK("1258", "抖音", false),
    XHS("1259", "小红书", false),
    TAOBAO("1262", "淘宝商城", false),
    PIN_DUO_DUO("1263", "拼多多商城", false),
    VASP("1553", "人寿财险", false),
    TIKTOK_MARKET("1700", "市场部抖音专用", true),
    JD_SELF("1976", "京东自营", false),
    JS_DOUYIN("2352", "江苏抖音直播间", true),
    WECAR("2396", "腾讯出行", false),
    TUANYOU("2437", "团油", false),
    SHUN_FENG_TONG_CHENG("2461", "顺丰同城", false),
    TIAN_MAO_SUPERMARKET("2554", "天猫超市", false),
    T3_GO("2550", "T3出行", false),
    DONG_FENG_NISSAN("2568", "东风日产", false),
    GUANG_FA_SHOP("2574", "广发商城", false),
    TUANYOU_B("2445", "新团油", false),
    KUAI_DIAN("2444", "快电", false),
    GEELY("903101", "吉利", false),
    CHUANQI("945801", "传祺APP", false),;


    private final String  code;
    private final String description;
    private final Boolean exclude;


    public static List<Map<String, String>> getLabelList(List<String> sourceCodeList) {
        List<Map<String, String>> list = new ArrayList<>();

        for (SourceEnum node : SourceEnum.values()) {
            if (ObjectUtils.isEmpty(sourceCodeList)) {
                // 未传入sourceCodeList时，只显示普通渠道
                if (!node.exclude) {
                    Map<String, String> row = new LinkedHashMap<>();
                    row.put("label", node.getDescription());
                    row.put("value", node.getCode());
                    list.add(row);
                }
            } else {
                if (sourceCodeList.contains(node.getCode())) {
                    Map<String, String> row = new LinkedHashMap<>();
                    row.put("label", node.getDescription());
                    row.put("value", node.getCode());
                    list.add(row);
                }
            }
        }

        return list;
    }

    public static List<String> getExcludeSourceList() {
        List<String> list = new ArrayList<>();

        for (SourceEnum node : SourceEnum.values()) {
            if (node.exclude) {
                list.add(node.getCode());
            }
        }

        return list;
    }

}
