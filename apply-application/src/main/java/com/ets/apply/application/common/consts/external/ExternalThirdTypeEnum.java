package com.ets.apply.application.common.consts.external;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ExternalThirdTypeEnum {

    // todo
    CHUAN_QI(50, "945801", "传祺APP");

    private final int type;

    private final String source;

    private final String description;

    public static String getSourceByType(int type) {
        for (ExternalThirdTypeEnum node : ExternalThirdTypeEnum.values()) {
            if (node.getType() == type) {
                return node.getSource();
            }
        }

        return "";
    }

    public static String getDescByType(int type) {
        for (ExternalThirdTypeEnum node : ExternalThirdTypeEnum.values()) {
            if (node.getType() == type) {
                return node.getDescription();
            }
        }

        return "";
    }
}
