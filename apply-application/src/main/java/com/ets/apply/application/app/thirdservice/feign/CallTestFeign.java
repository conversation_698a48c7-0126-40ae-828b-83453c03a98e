package com.ets.apply.application.app.thirdservice.feign;

import com.alibaba.fastjson.JSONObject;
import com.ets.common.JsonResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 调用测试接口
 */
@FeignClient(
        url = "http://127.0.0.1:20070",
        name = "CallTestFeign")
public interface CallTestFeign {

    @PostMapping(value = "/test/responseMock")
    JsonResult<JSONObject> responseMock(@RequestBody String dto);

}
