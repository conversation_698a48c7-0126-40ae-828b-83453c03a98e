package com.ets.apply.application.common.consts.writeCard;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
@AllArgsConstructor
public enum IssuerOrderStatusEnum {
    DEFAULT(0, "未圈存"),
    SUCCESS(1, "已圈存"),
    FAIL(2, "圈存失败"),
    REVERSE(4, "冲正"),
    REFUND(5, "退款");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;

    static {
        IssuerOrderStatusEnum[] enums = IssuerOrderStatusEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
