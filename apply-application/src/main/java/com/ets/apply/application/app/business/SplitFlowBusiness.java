package com.ets.apply.application.app.business;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.apply.application.common.bo.splitFlowConfig.SplitRuleBO;
import com.ets.apply.application.common.dto.request.splitFlow.AssignUserSplitFlowDTO;
import com.ets.apply.application.common.dto.request.splitFlow.SplitFlowGetResultDTO;
import com.ets.apply.application.common.dto.request.splitFlow.SplitFlowResultListDTO;
import com.ets.apply.application.common.vo.splitFlow.SplitFlowApplyPageVo;
import com.ets.apply.application.common.vo.splitFlow.SplitFlowGetResultVo;
import com.ets.apply.application.common.vo.splitFlow.SplitFlowResultGetListVo;
import com.ets.apply.application.infra.entity.splitFlow.SplitFlowConfigEntity;
import com.ets.apply.application.infra.entity.splitFlow.SplitFlowEntity;
import com.ets.apply.application.infra.relation.SplitFlowBindConfigRelation;
import com.ets.apply.application.infra.service.CardsService;
import com.ets.apply.application.infra.service.SplitFlowConfigService;
import com.ets.apply.application.infra.service.SplitFlowService;
import com.ets.common.BizException;
import com.ets.common.ToolsHelper;
import com.ets.common.util.NumberUtil;
import com.ets.user.feign.feign.UsersCardsFeign;
import com.ets.user.feign.response.UsersCardsResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.stream.Collectors;


@Slf4j
@Component
public class SplitFlowBusiness extends BaseBusiness {
    @Autowired
    private SplitFlowService splitFlowService;
    @Autowired
    private SplitFlowConfigService splitFlowConfigService;

    @Autowired
    private CardsService cardsService;

    @Autowired
    private ApplyPageBusiness applyPageBusiness;

    @Autowired
    private UsersCardsFeign usersCardsFeign;

    @Resource(name = "redisPermanentTemplate")
    private StringRedisTemplate redisPermanentTemplate;

    /*
     * 获取分流结果
     */
    public SplitFlowGetResultVo getResult(SplitFlowGetResultDTO dto) throws BizException {
        SplitFlowGetResultVo vo = new SplitFlowGetResultVo();
        //获取配置
        SplitFlowConfigEntity splitFlowConfig = splitFlowConfigService.getOneByColumn(dto.getSplitType(), SplitFlowConfigEntity::getSplitType);
        if(splitFlowConfig == null){
            ToolsHelper.throwException("记录不存在："+dto.getSplitType());
        }
        //是否已结束
        LocalDateTime currentDateTime = LocalDateTime.now();
        if(currentDateTime.isAfter(splitFlowConfig.getExpireDate())){
            vo.setResult(splitFlowConfig.getDefaultResult());
        }else{
            //优先读取结果
            if(StringUtils.isEmpty(dto.getSplitTerm())){
                dto.setSplitTerm(splitFlowConfig.getSplitTerm());
            }
            SplitFlowEntity splitFlowEntity = splitFlowService.findByCondition(dto.getSplitType(),dto.getSplitTerm(),dto.getUid());
            if(splitFlowEntity == null){
                //生成新的分流结果记录
                splitFlowEntity = generateSplitFlow(dto,splitFlowConfig);
            }
            vo.setResult(splitFlowEntity.getSplitResult());
            vo.setSplitSn(splitFlowEntity.getSplitSn());
        }

        vo.setResultReturnParams(getResultReturnParams(vo.getResult(),splitFlowConfig.getResultReturnParams()));
        return vo;
    }

    /*
     * 获取分流结果待返回的对象
     *             JSONObject obj = (JSONObject) splitRuleArr.get(i);
            //System.out.println(obj.getString("method"));
     */
    public Object getResultReturnParams(String splitResult,String resultReturnParams) throws BizException {
        if (ObjectUtils.isEmpty(resultReturnParams)) {
            return null;
        }
        JSONObject jsonObject = JSONObject.parseObject(resultReturnParams);
        if(jsonObject!=null && jsonObject.containsKey(splitResult)){
            JSONObject  returnObject = jsonObject.getObject(splitResult,JSONObject.class);
            //存在apply_page
            if(returnObject.containsKey("applyPage") && StringUtils.isNotEmpty(returnObject.getString("applyPage"))){
                String pageSn = returnObject.getString("applyPage");
                SplitFlowApplyPageVo splitFlowApplyPageVo = new SplitFlowApplyPageVo();
                splitFlowApplyPageVo.setApplyPage(applyPageBusiness.getByPageSnWithTemplate(pageSn,null));
                splitFlowApplyPageVo.setOrderSource(returnObject.getString("orderSource"));
                // 如果获取的是兜底的，不要返回order_source
                if(splitFlowApplyPageVo.getApplyPage() != null && !splitFlowApplyPageVo.getApplyPage().getPageSn().equals(pageSn)){
                    splitFlowApplyPageVo.setOrderSource("");
                }
                //合并对象
                try{
                    returnObject.put("orderSource",splitFlowApplyPageVo.getOrderSource());
                    returnObject.put("applyPage",splitFlowApplyPageVo.getApplyPage());
                }catch (Exception e) {
                    log.error("getResultReturnParams 合并对象处理异常" + e.getMessage());
                }
            }
            return returnObject;
        }
        return null;
    }

    /*
     * 计算分流结果
     */
    public SplitFlowEntity generateSplitFlow(SplitFlowGetResultDTO dto,SplitFlowConfigEntity splitFlowConfig) throws BizException {
        //默认分流值
        String splitResult = null;
        List<SplitRuleBO> splitRuleList = JSON.parseArray(splitFlowConfig.getSplitRule(), SplitRuleBO.class);
        for (SplitRuleBO rule : splitRuleList) {
            List<String> splitResultArr = rule.getSplitResult();
            switch (rule.getMethod()) {
                //自然分流
                case "redisIncr":
                    splitResult = redisIncr(splitFlowConfig, splitResultArr);
                    break;
                //白名单
                case "whiteList":
                    splitResult = whiteList(rule, dto, splitResultArr);
                    break;
                // 用户存在客车有效卡
                case "userHasCarValidCard":
                    splitResult = userHasCarValidCard(dto.getUid(), splitResultArr);
                    break;
            }
            if (StringUtils.isNotEmpty(splitResult)) {
                break;
            }
        }
        splitResult = StringUtils.isEmpty(splitResult) ? splitFlowConfig.getDefaultResult() : splitResult;
        //新增记录
        SplitFlowEntity splitFlow = new SplitFlowEntity();
        splitFlow.setSplitSn(ToolsHelper.genNum(redisPermanentTemplate, "SplitFlowEntity", "prod", 8));
        splitFlow.setSplitType(splitFlowConfig.getSplitType());
        splitFlow.setSplitTerm(splitFlowConfig.getSplitTerm());
        splitFlow.setSplitResult(splitResult);
        splitFlow.setUid(dto.getUid());
        splitFlow.setOrderSn(dto.getOrderSn());
        splitFlowService.create(splitFlow);
        return splitFlow;
    }

    /**
     * 自然分流
     *
     * @param splitFlowConfig 分流配置
     * @param splitResultArr 分流结果
     * @return String
     */
    private String redisIncr(SplitFlowConfigEntity splitFlowConfig, List<String> splitResultArr) {
        String redisKey = splitFlowConfig.getSplitType() + ":" + splitFlowConfig.getSplitTerm() + ":" + splitFlowConfig.getId();
        long num = redisPermanentTemplate.opsForValue().increment(redisKey);
        return splitResultArr.get((int) (num % splitResultArr.size()));
    }

    /**
     * 白名单
     *
     * @param ruleBO 分流规则
     * @param dto 请求参数
     * @param splitResultArr 分流结果合集
     * @return 结果
     */
    private String whiteList(SplitRuleBO ruleBO, SplitFlowGetResultDTO dto, List<String> splitResultArr) {
        String splitResult = null;
        switch (ruleBO.getKey()) {
            case "pl":
                if (ruleBO.getKeyArr().contains(dto.getPl())) {
                    splitResult = splitResultArr.get(0);
                }
                break;
            default:
                break;
        }
        return splitResult;
    }

    /**
     * 有客车有效卡用户
     *
     * @param uid 用户id
     * @return String
     */
    private String userHasCarValidCard(Long uid, List<String> splitResultArr) {
        String splitResult = null;
        // 获取货车cardId
        List<Integer> cardIds = cardsService.getAllTruckIds();
        List<UsersCardsResponse> usersCardsList = usersCardsFeign.getUserActivatedCardListByUid(uid).getData();
        if (ObjectUtils.isEmpty(usersCardsList)) {
            return splitResult;
        }

        // 过滤出有客车有效卡的用户
        List<UsersCardsResponse> list = usersCardsList.stream().filter(usersCards -> !cardIds.contains(usersCards.getCardId())).collect(Collectors.toList());
        if (!list.isEmpty()) {
            splitResult = splitResultArr.get(0);
        }
        return splitResult;
    }

    /**
     * 分流结果
     */
    public void assignUserSplitFlow(AssignUserSplitFlowDTO dto) {
        // 根据splitSn 获取分流结果
        SplitFlowEntity splitFlow = splitFlowService.getOneByColumn(dto.getSplitSn(), SplitFlowEntity::getSplitSn);
        if(ObjectUtils.isEmpty(splitFlow)){
            ToolsHelper.throwException("找不到对应的分流结果");
        }

        if(splitFlow.getSplitResult().equals(dto.getSplitResult())){
            return;
        }
        splitFlow.setSplitResult(dto.getSplitResult());
        splitFlow.setUpdatedAt(LocalDateTime.now());
        splitFlowService.updateById(splitFlow);

    }


    /*
     * 获取分流结果列表
     */
    public IPage<SplitFlowResultGetListVo> getResultList(SplitFlowResultListDTO dto) {
        // 分页设置
        IPage<SplitFlowEntity> oPage = new Page<>(dto.getPageNum(), dto.getPageSize(), true);
        LocalDateTime beginTime = null;
        LocalDateTime endTime = null;
        if(org.apache.commons.lang3.StringUtils.isNotEmpty(dto.getCreateTimeBegin())){
            beginTime = ToolsHelper.getLocalDateTime(dto.getCreateTimeBegin() + " 00:00:00");
        }
        if(org.apache.commons.lang3.StringUtils.isNotEmpty(dto.getCreateTimeEnd())){
            endTime = ToolsHelper.getLocalDateTime(dto.getCreateTimeEnd() + " 23:59:59");
        }
        LambdaQueryWrapper<SplitFlowEntity> wrapper = new LambdaQueryWrapper<>();
        if(
                org.apache.commons.lang3.StringUtils.isEmpty(dto.getSplitType()) && !NumberUtil.isPositive(dto.getUid())
        ){
            // 时间范围限制在一个月内
            if (
                    org.apache.commons.lang3.StringUtils.isEmpty(dto.getCreateTimeBegin()) ||
                            org.apache.commons.lang3.StringUtils.isEmpty(dto.getCreateTimeEnd())
            ) {
                ToolsHelper.throwException("请选择开始和结束时间");
            }
            if(ChronoUnit.MONTHS.between(beginTime, endTime) > 1){
                ToolsHelper.throwException("开始和结束时间不可超过一个月");
            }

        }
        wrapper.eq(org.apache.commons.lang3.StringUtils.isNotEmpty(dto.getSplitType()), SplitFlowEntity::getSplitType, dto.getSplitType())
                .eq(org.apache.commons.lang3.StringUtils.isNotEmpty(dto.getSplitResult()), SplitFlowEntity::getSplitResult, dto.getSplitResult())
                .eq(org.apache.commons.lang3.StringUtils.isNotEmpty(dto.getOrderSn()), SplitFlowEntity::getOrderSn, dto.getOrderSn())
                .eq(NumberUtil.isPositive(dto.getUid()), SplitFlowEntity::getUid, dto.getUid())
                .ge(org.apache.commons.lang3.StringUtils.isNotEmpty(dto.getCreateTimeBegin()), SplitFlowEntity::getCreatedAt, beginTime)
                .le(org.apache.commons.lang3.StringUtils.isNotEmpty(dto.getCreateTimeEnd()), SplitFlowEntity::getCreatedAt, endTime)
                .orderByDesc(SplitFlowEntity::getCreatedAt);

        IPage<SplitFlowEntity> pageList = splitFlowService.getPageListByWrapper(oPage, wrapper);
        if(pageList.getRecords().isEmpty()){
            return null;
        }
        splitFlowConfigService.bindToMasterEntityList(pageList.getRecords(), SplitFlowBindConfigRelation.class);
        return pageList.convert(record -> {
            SplitFlowResultGetListVo vo = new SplitFlowResultGetListVo();
            BeanUtils.copyProperties(record, vo);
            vo.setSplitName(record.getSplitFlowConfigEntity().getSplitName());
            return vo;
        });
    }
}
