package com.ets.apply.application.app.business.creditCard;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ets.apply.application.app.factory.creditCard.CreditCardFactory;
import com.ets.apply.application.app.thirdservice.feign.CallCenterMarketingFeign;
import com.ets.apply.application.common.bo.creditCard.CreditCardRewardBO;
import com.ets.apply.application.common.bo.creditCard.CreditCardRewardChannelBO;
import com.ets.apply.application.common.consts.activityCreditCardBankUser.ActivityCreditCardBankUserApplyStatusEnum;
import com.ets.apply.application.common.consts.activityCreditCardUsersInfo.ActivityCreditCardUserInfoStatusEnum;
import com.ets.apply.application.common.consts.activityCreditCardUsersInfo.ActivityCreditCardUserInfoWhichBankEnum;
import com.ets.apply.application.common.consts.creditCard.CreditCardClassifyConstant;
import com.ets.apply.application.common.dto.request.creditCard.CreditCardCoopDTO;
import com.ets.apply.application.common.dto.request.creditCard.CreditCardStreamDTO;
import com.ets.apply.application.infra.entity.ActivityCreditCardBankUsersEntity;
import com.ets.apply.application.infra.entity.ActivityCreditCardUsersInfoEntity;
import com.ets.apply.application.infra.service.ActivityCreditCardBankUsersService;
import com.ets.apply.application.infra.service.ActivityCreditCardUsersInfoService;
import com.ets.common.ToolsHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;

@Component
@Slf4j

public class CreditCardRewardBusiness {
    @Autowired
    private ActivityCreditCardUsersInfoService activityCreditCardUsersInfoService;

    @Autowired
    private ActivityCreditCardBankUsersService activityCreditCardBankUsersService;
    @Autowired
    private CallCenterMarketingFeign callCenterMarketingFeign;

    public void reward( CreditCardRewardBO creditCardRewardBO) {

        // 5.3 活动限制：根据申办的银行，满足对应的条件后才能获取领券资格。判断条件如下：
        //
        //1、广发：当新户核卡成功后，记录该用户拥有领券资格，若用户不为新户则不满足此条件；
        //
        //2、平安：当新户首刷成功后，记录该用户拥有领券资格
        //
        //3、交行：当新户激活后，记录该用户拥有领券资格

        if (!CollectionUtils.isEmpty(creditCardRewardBO.getWhichBankList())) {
            creditCardRewardBO.getWhichBankList().forEach(whichBank -> {
                try {
                    CreditCardFactory.create(whichBank).batchReward(creditCardRewardBO);
                } catch (Exception e) {
                    log.info("发送消费券资格失败，银行：" + whichBank + e.getMessage());
                }
            });
        }

    }

    //一个用户一个月内仅允许领取一次消费券。若同一用户同时申办多家信用卡且都核卡成功并为新户，也仅能享有一次领券资格。
    public void checkAndMarkQualification(Long uid, Integer userInfoId, Integer creditCardBankId) {
        if (activityCreditCardUsersInfoService.hasSendQualification(uid)) {
            ActivityCreditCardUsersInfoEntity activityCreditCardUsersInfoEntity = new ActivityCreditCardUsersInfoEntity();
            activityCreditCardUsersInfoEntity.setId(userInfoId);
            activityCreditCardUsersInfoEntity.setUpdatedAt(LocalDateTime.now());
            activityCreditCardUsersInfoEntity.setStatus(ActivityCreditCardUserInfoStatusEnum.STATUS_GOT.getCode());
            activityCreditCardUsersInfoService.updateById(activityCreditCardUsersInfoEntity);

            // bank 表也做记录
            ActivityCreditCardBankUsersEntity activityCreditCardBankUsersEntity = new ActivityCreditCardBankUsersEntity();
            activityCreditCardBankUsersEntity.setId(creditCardBankId);
            activityCreditCardBankUsersEntity.setApplicationStatus(ActivityCreditCardBankUserApplyStatusEnum.APPLY_USED.getCode());
            activityCreditCardBankUsersEntity.setUpdatedAt(LocalDateTime.now());
            activityCreditCardBankUsersService.updateById(activityCreditCardBankUsersEntity);
            ToolsHelper.throwException("用户已领取过资格，不重复发送");
        }
    }

    /**
     * 发券
     * 暂时不加事务，etc-server 有操作，防止相互锁
     * @param uid
     * @param userInfoId
     */
    public void sendAndMarkQualification(Long uid, Integer userInfoId, Integer creditCardBankId) {
        CreditCardStreamDTO creditCardStreamDTO = new CreditCardStreamDTO();
        creditCardStreamDTO.setUserId(uid);
        // 发送券权益
        String result =  callCenterMarketingFeign.receive(creditCardStreamDTO);
        JSONObject jsonObject = JSON.parseObject(result);
        Integer resultCode = jsonObject.getInteger("code");
        // 错误
        if (!resultCode.equals(0)) {
            String resultMsg = jsonObject.getString("msg");
            ToolsHelper.throwException(resultMsg);
        }
        // 记录已发放
        ActivityCreditCardUsersInfoEntity activityCreditCardUsersInfoEntity = new ActivityCreditCardUsersInfoEntity();
        activityCreditCardUsersInfoEntity.setId(userInfoId);
        activityCreditCardUsersInfoEntity.setStatus(ActivityCreditCardUserInfoStatusEnum.STATUS_QUALIFICATION_FINISHED.getCode());
        activityCreditCardUsersInfoEntity.setUpdatedAt(LocalDateTime.now());
        activityCreditCardUsersInfoService.updateById(activityCreditCardUsersInfoEntity);

        // bank 表也做记录
        ActivityCreditCardBankUsersEntity activityCreditCardBankUsersEntity = new ActivityCreditCardBankUsersEntity();
        activityCreditCardBankUsersEntity.setId(creditCardBankId);
        activityCreditCardBankUsersEntity.setApplicationStatus(ActivityCreditCardBankUserApplyStatusEnum.APPLY_USED.getCode());
        activityCreditCardBankUsersEntity.setUpdatedAt(LocalDateTime.now());
        activityCreditCardBankUsersService.updateById(activityCreditCardBankUsersEntity);
    }

    public void sendAndMarkCoopRights(Long uid, Integer userInfoId, Integer creditCardBankId,String orderSn,Integer whichBank) {
        CreditCardCoopDTO creditCardCoopDTO = new CreditCardCoopDTO();
        creditCardCoopDTO.setUserId(uid);
        creditCardCoopDTO.setBank(ActivityCreditCardUserInfoWhichBankEnum.getCoopBankByWhichBank(whichBank));
        creditCardCoopDTO.setOrderNo(orderSn); 
        // 发送券权益
        String result = callCenterMarketingFeign.coopReceive(creditCardCoopDTO);
        JSONObject jsonObject = JSON.parseObject(result);
        Integer resultCode = jsonObject.getInteger("code");
        // 错误
        if (!resultCode.equals(0)) {
            String resultMsg = jsonObject.getString("msg");
            ToolsHelper.throwException(resultMsg);
        }
        // 记录已发放
        ActivityCreditCardUsersInfoEntity activityCreditCardUsersInfoEntity = new ActivityCreditCardUsersInfoEntity();
        activityCreditCardUsersInfoEntity.setId(userInfoId);
        activityCreditCardUsersInfoEntity.setStatus(ActivityCreditCardUserInfoStatusEnum.STATUS_QUALIFICATION_FINISHED.getCode());
        activityCreditCardUsersInfoEntity.setUpdatedAt(LocalDateTime.now());
        activityCreditCardUsersInfoService.updateById(activityCreditCardUsersInfoEntity);

        // bank 表也做记录
        ActivityCreditCardBankUsersEntity activityCreditCardBankUsersEntity = new ActivityCreditCardBankUsersEntity();
        activityCreditCardBankUsersEntity.setId(creditCardBankId);
        activityCreditCardBankUsersEntity.setApplicationStatus(ActivityCreditCardBankUserApplyStatusEnum.APPLY_USED.getCode());
        activityCreditCardBankUsersEntity.setUpdatedAt(LocalDateTime.now());
        activityCreditCardBankUsersService.updateById(activityCreditCardBankUsersEntity);
    }


    /**
     * 信用卡奖励通道
     * @param rewardChannelBO
     */
    public void rewardChannel(CreditCardRewardChannelBO rewardChannelBO) {
        switch (rewardChannelBO.getClassify()) {
            case CreditCardClassifyConstant.TYPE_NORMAL_CREDIT_APPLY:
                //  检查权限
                this.checkAndMarkQualification(rewardChannelBO.getUid(), rewardChannelBO.getUserInfoId(), rewardChannelBO.getBankUserId());
                // 发送券
                this.sendAndMarkQualification(rewardChannelBO.getUid(), rewardChannelBO.getUserInfoId(), rewardChannelBO.getBankUserId());

                break;
            case CreditCardClassifyConstant.TYPE_INCREASE_APPLY:
                this.sendAndMarkCoopRights(rewardChannelBO.getUid(), rewardChannelBO.getUserInfoId(), rewardChannelBO.getBankUserId(), rewardChannelBO.getOrderSn(), rewardChannelBO.getWhichBank());
                break;
            default:
                log.info("银行奖励发送无匹配 userInfoId:" + rewardChannelBO.getUserInfoId());
        }
    }

}
