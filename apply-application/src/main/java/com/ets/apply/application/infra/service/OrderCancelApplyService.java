package com.ets.apply.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.apply.application.infra.entity.OrderCancelApply;
import com.ets.apply.application.infra.mapper.OrderCancelApplyMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 订单取消申请表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-04
 */
@Service
@DS("db-order")
public class OrderCancelApplyService extends BaseService<OrderCancelApplyMapper, OrderCancelApply> {
    public List<OrderCancelApply> getByCancelSns(List<String> cancelSns) {
        LambdaQueryWrapper<OrderCancelApply> wrapper = Wrappers.<OrderCancelApply>lambdaQuery()
                .in(OrderCancelApply::getCancelSn, cancelSns);
        return super.baseMapper.selectList(wrapper);
    }
}
