package com.ets.apply.application.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.apply.application.app.business.SplitFlowBusiness;
import com.ets.apply.application.common.dto.request.splitFlow.AssignUserSplitFlowDTO;
import com.ets.apply.application.common.dto.request.splitFlow.SplitFlowGetResultDTO;
import com.ets.apply.application.common.dto.request.splitFlow.SplitFlowResultListDTO;
import com.ets.apply.application.common.vo.splitFlow.SplitFlowGetResultVo;
import com.ets.apply.application.common.vo.splitFlow.SplitFlowResultGetListVo;
import com.ets.apply.application.infra.service.SplitFlowConfigService;
import com.ets.common.BizException;
import com.ets.common.JsonResult;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping("/admin/splitFlow")
@RestController

public class SplitFlowAdminController {

    @Autowired
    SplitFlowBusiness splitFlowBusiness;
    @Autowired
    SplitFlowConfigService splitFlowConfigService;
    @RequestMapping("/getResult")
    public JsonResult<SplitFlowGetResultVo> getResult(@RequestBody @Valid SplitFlowGetResultDTO dto) {
        return JsonResult.ok(splitFlowBusiness.getResult(dto));
    }

    @RequestMapping("/assignSplitFlow")
    public JsonResult<Object> assignSplitFlow(@RequestBody @Valid AssignUserSplitFlowDTO dto) {
        splitFlowBusiness.assignUserSplitFlow(dto);
        return JsonResult.ok();
    }

    @RequestMapping("/getList")
    public JsonResult<IPage<SplitFlowResultGetListVo>> getResultList(@RequestBody @Valid SplitFlowResultListDTO dto) {
        return JsonResult.ok(splitFlowBusiness.getResultList(dto));
    }

    /**
     * 获取筛选项数组
     * @return JsonResult
     * @throws BizException 异常
     */
    @RequestMapping("/getSelectOptions")
    @ResponseBody
    public JsonResult getSelectOptions() throws BizException {
        //创建map对象
        Map<String,Object> map = new HashMap<String,Object>();
        //给Map中添加元素
        map.put("splitType",splitFlowConfigService.getSelectOptionsList());
        return JsonResult.ok(map);
    }
}
