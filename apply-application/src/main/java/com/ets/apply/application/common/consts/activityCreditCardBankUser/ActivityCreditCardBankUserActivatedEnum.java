package com.ets.apply.application.common.consts.activityCreditCardBankUser;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum ActivityCreditCardBankUserActivatedEnum {

    STATUS_ACTIVATED_UNKNOWN(0, "未激活"),
    STATUS_ACTIVATED_YES(1, "已激活");

    private final int code;
    private final String description;

    public static String getDescByCode(int code) {
        for (ActivityCreditCardBankUserActivatedEnum node : ActivityCreditCardBankUserActivatedEnum.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }
        return "";
    }

    public static List<Map<String, String>> getLabelList() {
        List<Map<String, String>> list = new ArrayList<>();

        for (ActivityCreditCardBankUserActivatedEnum node : ActivityCreditCardBankUserActivatedEnum.values()) {
            Map<String, String> row = new LinkedHashMap<>();
            row.put("label", node.getDescription());
            row.put("value", String.valueOf(node.getCode()));

            list.add(row);
        }

        return list;
    }

}
