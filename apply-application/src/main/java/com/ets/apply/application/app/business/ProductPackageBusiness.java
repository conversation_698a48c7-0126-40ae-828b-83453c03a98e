package com.ets.apply.application.app.business;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONException;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.apply.application.app.thirdservice.feign.CallGoodsApplication;
import com.ets.apply.application.app.thirdservice.feign.CallPhpApplyFeign;
import com.ets.apply.application.app.thirdservice.response.goods.CheckStallSkuAttrVO;
import com.ets.apply.application.common.bo.goods.GetSkuByAttrBO;
import com.ets.apply.application.common.bo.source.SourceCategoryBO;
import com.ets.apply.application.common.config.LoginCodeLimitsConfig;
import com.ets.apply.application.common.config.ProductPackageConfig;
import com.ets.apply.application.common.consts.cache.CacheKeyConstant;
import com.ets.apply.application.common.consts.issuer.IssuerIdEnum;
import com.ets.apply.application.common.consts.issuer.IssuerIdServiceCodeMap;
import com.ets.apply.application.common.consts.issuer.IssuerServiceCodeMap;
import com.ets.apply.application.common.consts.map.MapSetCacheEnum;
import com.ets.apply.application.common.consts.order.FlowTypeEnum;
import com.ets.apply.application.common.consts.order.PurchasePartyEnum;
import com.ets.apply.application.common.consts.order.PurchaseTypeEnum;
import com.ets.apply.application.common.consts.productOrder.ChannelTypeEnum;
import com.ets.apply.application.common.consts.productPackage.*;
import com.ets.apply.application.common.consts.productPackageTmp.AllowModifyEnum;
import com.ets.apply.application.common.consts.productPackageTmp.ReleaseStatusEnum;
import com.ets.apply.application.common.dto.map.MapDelCacheByPackageSnDTO;
import com.ets.apply.application.common.dto.productPackage.GetInfoByPackageSnDTO;
import com.ets.apply.application.common.dto.productPackage.GetInfoByPackageSnsDTO;
import com.ets.apply.application.common.dto.productPackage.UpDownDTO;
import com.ets.apply.application.common.dto.request.goods.CheckStallSkuAttrDTO;
import com.ets.apply.application.common.dto.request.productPackage.*;
import com.ets.apply.application.common.vo.ProductPackageTmp.ProductPackageTmpAddVo;
import com.ets.apply.application.common.vo.productPackage.*;
import com.ets.apply.application.common.vo.wecar.WecarProductVo;
import com.ets.apply.application.infra.entity.*;
import com.ets.apply.application.infra.service.*;
import com.ets.apply.feign.request.BatchQueryDTO;
import com.ets.apply.feign.response.ProductPackageBatchQueryResponse;
import com.ets.common.BizException;
import com.ets.common.JsonResult;
import com.ets.common.ToolsHelper;
import com.ets.common.util.ListUtils;
import com.ets.common.util.NumberUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ProductPackageBusiness {
    @Autowired
    private ProductPackageService productPackageService;
    @Autowired
    private CardsService cardsService;
    @Autowired
    private ProductPackageConfigService productPackageConfigService;

    @Resource(name = "redisPermanentTemplate")
    private StringRedisTemplate redisPermanentTemplate;

    @Autowired
    private ProductPackageTmpService productPackageTmpService;
    @Autowired
    private PackageChangeLogService packageChangeLogService;
    @Autowired
    private ProductPackageStockService productPackageStockService;
    @Autowired
    private LoginCodeLimitsConfig loginCodeLimitsConfig;

    @Autowired
    private ProductPackageConfig productPackageConfig;

    @Autowired
    private CardsIssuersService cardsIssuersService;

    @Autowired
    private SourceBusiness sourceBusiness;
    @Autowired
    private CallPhpApplyFeign callPhpApplyFeign;

    @Autowired
    private ConfigBizFieldBusiness configBizFieldBusiness;

    @Autowired
    private CallGoodsApplication callGoodsApplication;

    @Autowired
    private ConfigBizFieldValuesService configBizFieldValuesService;

    @Autowired
    private ProductPackagePopupService popupService;


    public List<ProductPackageListOptionVo> getList(List<String> sourceCodeList) {
        // 获取渠道分类
        if (ObjectUtils.isNotEmpty(sourceCodeList)) {
            sourceCodeList.add("0");
        }
        List<SourceCategoryBO> sourceCategory = sourceBusiness.getSourceCategory(sourceCodeList);

        // 获取产品包列表
        List<ProductPackageEntity> list = productPackageService.getAll(sourceBusiness.getChildSourceList(null));
        List<ProductPackageOptionVo> packageOptionVoList = list.stream().map(
                record -> {
                    ProductPackageOptionVo vo = new ProductPackageOptionVo();
                    vo.setPackageSn(record.getPackageSn());
                    vo.setPackageName(record.getPackageName());
                    vo.setSource(record.getSource());
                    vo.setIsShowAdd(record.getIsShowAdd());
                    return vo;
                }
        ).collect(Collectors.toList());

        // 组装返回数据
        List<ProductPackageListOptionVo> optionVoList = new ArrayList<>();
        sourceCategory.forEach(source -> {
            ProductPackageListOptionVo optionVo = new ProductPackageListOptionVo();
            optionVo.setId(source.getId());
            optionVo.setSourceName(source.getSourceName());
            optionVo.setSource(source.getSource());

            // 子节点
            List<ProductPackageListOptionVo> childVoList = ListUtils.convertByClass(source.getItem(), ProductPackageListOptionVo.class);
            childVoList.forEach(childVo -> {
                List<ProductPackageOptionVo> productPackageOptionVoList = new ArrayList<>();
                packageOptionVoList.forEach(packageOptionVo -> {
                    if (childVo.getSource().equals(packageOptionVo.getSource())) {
                        productPackageOptionVoList.add(packageOptionVo);
                    }
                });
                childVo.setProductPackage(productPackageOptionVoList);
            });
            optionVo.setItem(childVoList);

            optionVoList.add(optionVo);
        });

        return optionVoList;
    }

    public List<ProductPackageListOptionVo> getAdminPackageList(String loginCode) {

        //是否受限用户
        List<String> sourceCodeList = new ArrayList<>();
        loginCodeLimitsConfig.getLimitSourceUserMap().forEach((k, v) -> {
            if (v.contains(loginCode)) {
                sourceCodeList.add(k);
            }
        });

        return getList(sourceCodeList);
    }

    public List<String> getAdminPackageSnList(String loginCode) {

        //是否受限用户
        List<String> sourceCodeList = new ArrayList<>();
        loginCodeLimitsConfig.getLimitSourceUserMap().forEach((k, v) -> {
            if (v.contains(loginCode)) {
                sourceCodeList.add(k);
            }
        });

        // 获取普通渠道
        if (ObjectUtils.isEmpty(sourceCodeList)) {
            List<String> sourceList = sourceBusiness.getChildSourceList(0);
            sourceCodeList.addAll(sourceList);
        }

        // 获取渠道值产品包
        List<ProductPackageEntity> productPackage = productPackageService.getAll(sourceCodeList);
        return productPackage.stream().map(ProductPackageEntity::getPackageSn).collect(Collectors.toList());
    }


    /*
     * 获取全部产品包列表
     */
    public IPage<ProductPackageGetListVo> getAllList(ProductPackageGetListDTO dto) {
        // 分页设置
        IPage<ProductPackageTmpEntity> oPage = new Page<>(dto.getPageNum(), dto.getPageSize(), true);

        // 查询条件设置
        LambdaQueryWrapper<ProductPackageTmpEntity> wrapper = new LambdaQueryWrapper<>();

        wrapper.eq(StringUtils.isNotEmpty(dto.getPackageSn()), ProductPackageTmpEntity::getPackageSn, dto.getPackageSn())
            .eq(NumberUtil.isPositive(dto.getStatus()), ProductPackageTmpEntity::getStatus, dto.getStatus())
            .eq(NumberUtil.isPositive(dto.getBizType()), ProductPackageTmpEntity::getBizType, dto.getBizType())
            .eq(StringUtils.isNotEmpty(dto.getPackageSn()), ProductPackageTmpEntity::getPackageSn, dto.getPackageSn())
            .like(StringUtils.isNotEmpty(dto.getPackageName()), ProductPackageTmpEntity::getPackageName, dto.getPackageName())
            .eq(NumberUtil.isPositive(dto.getAddressConfigId()), ProductPackageTmpEntity::getAddressConfigId, dto.getAddressConfigId())
            .orderByDesc(ProductPackageTmpEntity::getCreatedAt);

        IPage<ProductPackageTmpEntity> pageList = productPackageTmpService.getPageListByWrapper(oPage, wrapper);

        HashMap<Integer, String> bizTypeMap = configBizFieldBusiness.getHashMapIntByBizField("biz_type");
        return pageList.convert(record -> {
            ProductPackageGetListVo vo = new ProductPackageGetListVo();
            BeanUtils.copyProperties(record, vo);
            JSONObject jsonObject = JSON.parseObject(record.getPackageInfo());
            if (ObjectUtils.isNotEmpty(jsonObject)) {
                Integer deviceType = jsonObject.getInteger("device_type");
                if (ObjectUtils.isNotEmpty(deviceType)) {
                    vo.setDeviceType(DeviceTypeEnum.getDescByCode(deviceType));
                }
                Integer manufacturer = jsonObject.getInteger("manufacturer");
                if (ObjectUtils.isNotEmpty(manufacturer)) {
                    vo.setManufacturer(ManufacturerEnum.getDescByCode(manufacturer));
                }
                Integer flowType = jsonObject.getInteger("flow_type");
                if (ObjectUtils.isNotEmpty(flowType)) {
                    vo.setFlowType(FlowTypeEnum.getDescByCode(flowType));
                }
                Integer purchaseType = jsonObject.getInteger("purchase_type");
                if (ObjectUtils.isNotEmpty(purchaseType)) {
                    vo.setPurchaseType(PurchaseTypeEnum.getDescByCode(purchaseType));
                }
                Integer purchaseParty = jsonObject.getInteger("purchase_party");
                if (ObjectUtils.isNotEmpty(purchaseParty)) {
                    vo.setPurchaseParty(PurchasePartyEnum.getDescByCode(purchaseParty));
                }
                Integer term = jsonObject.getInteger("term");
                if (ObjectUtils.isNotEmpty(term)) {
                    vo.setTerm(term);
                }

                JSONObject marketConfig = jsonObject.getJSONObject("java");
                if (ObjectUtils.isNotEmpty(marketConfig)) {
                    List<String> canUseCouponBatchNo = JSONArray.parseArray(marketConfig.getString("can_use_coupon_batch_no"), String.class);
                    if (ObjectUtils.isNotEmpty(canUseCouponBatchNo)) {
                        vo.setCanUseCouponBatchNo(canUseCouponBatchNo);
                    }
                }

                Integer cardId = jsonObject.getInteger("card_id");
                if (ObjectUtils.isNotEmpty(cardId)) {
                    vo.setCardId(cardId);
                    if(cardId > 0){
                        CardsEntity cardsEntity = cardsService.getByCardId(cardId);
                        if(cardsEntity != null){
                            vo.setIssuerName(cardsEntity.getProvince());
                        }

                    }

                }


            }
            vo.setStatusStr(ProductPackageStatusEnum.getDescByCode(record.getStatus()));
            vo.setReleaseStatusStr(ReleaseStatusEnum.getDescByCode(record.getReleaseStatus()));
            vo.setBizTypeStr(bizTypeMap.get(record.getBizType()));
            return vo;
        });
    }

    /**
     * 获取产品包列表
     * @param dto
     * @return
     */
    public IPage<ProductPackageListVo> getProductPackageList(ProductPackageListDTO dto) {
        // 分页设置
        IPage<ProductPackageEntity> oPage = new Page<>(dto.getPageNum(), dto.getPageSize(), true);

        // 如果没有传source，则默认查询所有渠道
        if(ObjectUtil.isNull(dto.getSource())){
            List<String> sourceList = sourceBusiness.getChildSourceList(null);
            dto.setSourceList(sourceList);
        }

        // 查询条件设置
        LambdaQueryWrapper<ProductPackageEntity> wrapper = new LambdaQueryWrapper<>();

        wrapper.eq(StringUtils.isNotEmpty(dto.getPackageSn()), ProductPackageEntity::getPackageSn, dto.getPackageSn())
                .eq(NumberUtil.isPositive(dto.getStatus()), ProductPackageEntity::getStatus, dto.getStatus())
                .eq(!Objects.isNull(dto.getIsShowAdd()), ProductPackageEntity::getIsShowAdd, dto.getIsShowAdd())
                .like(StringUtils.isNotEmpty(dto.getPackageName()), ProductPackageEntity::getPackageName, dto.getPackageName())
                .eq(StringUtils.isNotEmpty(dto.getSource()), ProductPackageEntity::getSource, dto.getSource())
                .in(ObjectUtils.isNotEmpty(dto.getSourceList()), ProductPackageEntity::getSource, dto.getSourceList())
                .orderByDesc(ProductPackageEntity::getSort)
                .orderByDesc(ProductPackageEntity::getCreatedAt)
        ;

        IPage<ProductPackageEntity> pageList = productPackageService.getPageListByWrapper(oPage, wrapper);

        return pageList.convert(record->{
            ProductPackageListVo vo = new ProductPackageListVo();
            BeanUtils.copyProperties(record, vo);
            return vo;
        });
    }

    /*
     *  上下架
     */
    public Boolean upDown(UpDownDTO upDownDTO,String loginCode){
        //查找产品包tmp
        ProductPackageTmpEntity productPackageTmpEntity = productPackageTmpService.getBySn(upDownDTO.getObjectCode());
        if(productPackageTmpEntity == null){
            ToolsHelper.throwException("该产品包不存在,"+upDownDTO.getObjectCode());
        }
        //上下架针对的是已发布的产品包
        if(productPackageTmpEntity.getReleaseStatus() != ReleaseStatusEnum.PROD.getValue()){
            ToolsHelper.throwException("该产品包未发布,"+upDownDTO.getObjectCode());
        }
        LambdaUpdateWrapper<ProductPackageEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ProductPackageEntity::getPackageSn, upDownDTO.getObjectCode())
            .set(ProductPackageEntity::getStatus, upDownDTO.getStatus());
        productPackageService.updateByWrapper(wrapper);

        LambdaUpdateWrapper<ProductPackageTmpEntity> wrapperTmp = new LambdaUpdateWrapper<>();
        wrapperTmp.eq(ProductPackageTmpEntity::getPackageSn, productPackageTmpEntity.getPackageSn())
            .set(ProductPackageTmpEntity::getStatus, upDownDTO.getStatus());
        productPackageTmpService.updateByWrapper(wrapperTmp);

        //更新映射产品包缓存
        callPhpApplyFeign.delCacheByPackageSn(new MapDelCacheByPackageSnDTO(MapSetCacheEnum.MAP_SET_CACHE_TYPE_PROD.getCode(), productPackageTmpEntity.getPackageSn()));

        //记录日志
        PackageChangeLogEntity packageChangeLogEntity = new PackageChangeLogEntity();
        packageChangeLogEntity.setPackageSn(productPackageTmpEntity.getPackageSn());
        packageChangeLogEntity.setChangeBefore("");
        packageChangeLogEntity.setChangeAfter(upDownDTO.toString());
        packageChangeLogEntity.setRemark("变更内容："+upDownDTO.toString());
        packageChangeLogEntity.setOperater(loginCode);
        packageChangeLogService.create(packageChangeLogEntity);
        return true;
    }

    public List<ProductPackageBatchQueryResponse> batchQuery(BatchQueryDTO batchQueryDTO) {

        List<ProductPackageEntity> productPackageEntityList = productPackageService.batchQuery(batchQueryDTO.getPackageSns());

        return ListUtils.convertByClass(productPackageEntityList, ProductPackageBatchQueryResponse.class);

    }



    /*
     *  新增
     */
    public ProductPackageTmpAddVo add(ProductPackageAddDTO productPackageAddDTO, String loginCode){

        JSONObject applyConfigInfo = new JSONObject(new LinkedHashMap<>());
        applyConfigInfo.put("sale_sn",productPackageAddDTO.getSaleSn());
        applyConfigInfo.put("goods_sku",productPackageAddDTO.getGoodsSku());
        applyConfigInfo.put("issuer_id",productPackageAddDTO.getIssuerId());
        applyConfigInfo.put("manufacturer",productPackageAddDTO.getManufacturer());
        applyConfigInfo.put("device_type",productPackageAddDTO.getDeviceType());
        applyConfigInfo.put("device_tutorial",productPackageAddDTO.getDeviceTutorial());
        applyConfigInfo.put("device_name",productPackageAddDTO.getDeviceName());
        applyConfigInfo.put("device_version",productPackageAddDTO.getDeviceVersion());
        applyConfigInfo.put("device_color",productPackageAddDTO.getDeviceColor());
        applyConfigInfo.put("payment_mode",productPackageAddDTO.getPaymentMode());
        applyConfigInfo.put("purchase_type",productPackageAddDTO.getPurchaseType());
        applyConfigInfo.put("purchase_party",productPackageAddDTO.getPurchaseParty());
        applyConfigInfo.put("card_id",productPackageAddDTO.getCardId());
        applyConfigInfo.put("flow_type",productPackageAddDTO.getFlowType());
        applyConfigInfo.put("use_deposit", productPackageAddDTO.getUseDeposit());
        Optional.ofNullable(productPackageAddDTO.getMarketSource()).ifPresent(marketSource -> {
            applyConfigInfo.put("market_source", marketSource);
        });
        //根据flow_type ,补充必要参数
        JSONObject needParams = getNeedParamsByFlowType(productPackageAddDTO.getFlowType());
        if (ObjectUtils.isNotEmpty(needParams)){
            applyConfigInfo.getInnerMap().putAll(needParams.getInnerMap());
        }

        applyConfigInfo.put("scene",productPackageAddDTO.getScene());
        applyConfigInfo.put("original_price",productPackageAddDTO.getOriginalPrice());
        if (ObjectUtils.isNotEmpty(productPackageAddDTO.getTollDepositFee())) {
            applyConfigInfo.put("toll_deposit_fee", productPackageAddDTO.getTollDepositFee());
        }

        // 货车非电商模式
        if (productPackageAddDTO.getIsTruck() == 1 && !FlowTypeEnum.FLOW_PRODUCT_ORDER.getKey().equals(productPackageAddDTO.getFlowType())) {
            // 根据purchaseParty设置结算类型和模式
            if (PurchasePartyEnum.checkIsTruckWeekMode(productPackageAddDTO.getPurchaseParty())) {
                applyConfigInfo.put("settle_type", "week"); // 周结
                applyConfigInfo.put("mode", "wx_week"); // 周结模式
            } else if (PurchasePartyEnum.checkIsTruckDayMode(productPackageAddDTO.getPurchaseParty())) {
                applyConfigInfo.put("settle_type", "day"); // 日结
                applyConfigInfo.put("mode", "wx"); // 日结模式
            }

            // contain_fee.service_fee = 0 兼容旧包
            JSONObject containFee = new JSONObject();
            containFee.put("service_fee", 0);
            applyConfigInfo.put("contain_fee", containFee);
        }


        // 检查sku 是否合法
        checkStallSkuAttr(new CheckStallSkuAttrDTO(
                ChannelTypeEnum.getStallCodeByValue(1),
                productPackageAddDTO.getGoodsSku(),
                CheckStallSkuAttrDTO.AttrMap.builder()
                        .issuerId(productPackageAddDTO.getIssuerId().toString())
                        .manufacturer(productPackageAddDTO.getManufacturer().toString())
                        .deviceColor(productPackageAddDTO.getDeviceColor().toString())
                        .deviceType(productPackageAddDTO.getDeviceType().toString())
                        .deviceVersion(productPackageAddDTO.getDeviceVersion().toString())
                        .build()
        ));
        //检查issuerId和cardId
        checkIssuerIdAndCardId(productPackageAddDTO.getIssuerId(),productPackageAddDTO.getCardId(),productPackageAddDTO.getIsTruck());

        applyConfigInfo.put("manufacturer",productPackageAddDTO.getManufacturer());

        JSONObject packageInfo = new JSONObject(applyConfigInfo);
        JSONObject aftersaleConfigInfo = new JSONObject(new LinkedHashMap<>());
        aftersaleConfigInfo.put("revoke_need_pay",productPackageAddDTO.getRevokeNeedPay());
        aftersaleConfigInfo.put("is_only_offline_exchange",productPackageAddDTO.getIsOnlyOfflineExchange());
        aftersaleConfigInfo.put("is_show_reapply",productPackageAddDTO.getIsShowReapply());
        aftersaleConfigInfo.put("warranty_time",productPackageAddDTO.getWarrantyTime());

        packageInfo.put("aftersales",aftersaleConfigInfo);
        //兼容warranty_time
        packageInfo.put("warranty_time",productPackageAddDTO.getWarrantyTime());
        //营销
        JSONObject marketConfigInfo = new JSONObject();
        marketConfigInfo.put("term",productPackageAddDTO.getTerm());
        marketConfigInfo.put("allow_device_valuation",productPackageAddDTO.getAllowDeviceValuation());

        if (ObjectUtils.isNotEmpty(productPackageAddDTO.getTruckTerm())) {
            marketConfigInfo.put("truck_term", productPackageAddDTO.getTruckTerm());
        }

        if (ObjectUtils.isNotEmpty(productPackageAddDTO.getServiceBatch())) {
            marketConfigInfo.put("service_batch", productPackageAddDTO.getServiceBatch());
        }

        if (NumberUtil.isPositive(productPackageAddDTO.getRecoveryAmount())) {
            LinkedHashMap<String, Object> recoveryConfig = new LinkedHashMap<>();
            recoveryConfig.put("open", true);
            recoveryConfig.put("amount", productPackageAddDTO.getRecoveryAmount());
            marketConfigInfo.put("recovery", recoveryConfig);
        }

        packageInfo.put("term",productPackageAddDTO.getTerm());

//        if (ObjectUtil.isNotEmpty(productPackageAddDTO.getCanUseCouponBatchNo())) {
//            marketConfigInfo.put("can_use_coupon_batch_no", productPackageAddDTO.getCanUseCouponBatchNo());
//        }
//        if (ObjectUtil.isNotEmpty(productPackageAddDTO.getCanUseJavaCouponCategory())) {
//            marketConfigInfo.put("can_use_java_coupon_category", productPackageAddDTO.getCanUseJavaCouponCategory());
//            packageInfo.put("can_use_java_coupon_category",productPackageAddDTO.getCanUseJavaCouponCategory());
//        }

        packageInfo.put("java",marketConfigInfo);


        ProductPackageTmpEntity productPackageTmpEntity = new ProductPackageTmpEntity();
        String newGenNumSn = ToolsHelper.genNum(redisPermanentTemplate, "newPackageSn", "prod", 8);
        String newPackageSn = productPackageAddDTO.getIsTruck().equals(1)?("Truck"+newGenNumSn):("CarPackage"+newGenNumSn);
        productPackageTmpEntity.setPackageSn(newPackageSn);
        productPackageTmpEntity.setPackageName(productPackageAddDTO.getPackageName());
        productPackageTmpEntity.setPackageFee(productPackageAddDTO.getPackageFee());
        productPackageTmpEntity.setStatus(0);
        productPackageTmpEntity.setIsBase(productPackageAddDTO.getIsBase());
        productPackageTmpEntity.setIsTruck(productPackageAddDTO.getIsTruck());
        productPackageTmpEntity.setDeliveryType(productPackageAddDTO.getDeliveryType());
        productPackageTmpEntity.setOfflineAutoDelivery(productPackageAddDTO.getOfflineAutoDelivery());
        productPackageTmpEntity.setApplyConfig(applyConfigInfo.toJSONString());
        productPackageTmpEntity.setPackageInfo(JSONUtil.toJsonStr(packageInfo));
        productPackageTmpEntity.setMarketConfig(marketConfigInfo.toJSONString());
        productPackageTmpEntity.setBizType(productPackageAddDTO.getBizType());
        productPackageTmpEntity.setAddressConfigId(productPackageAddDTO.getAddressConfigId());
        productPackageTmpEntity.setSource(productPackageAddDTO.getSource());
        productPackageTmpEntity.setAftersaleConfig(aftersaleConfigInfo.toJSONString());
        productPackageTmpEntity.setExpireDate(LocalDateTime.of(2037, 12, 30, 23, 59));
        productPackageTmpEntity.setTemplateId(productPackageAddDTO.getTemplateId());
        productPackageTmpService.create(productPackageTmpEntity);

        //记录日志
        PackageChangeLogEntity packageChangeLogEntity = new PackageChangeLogEntity();
        packageChangeLogEntity.setPackageSn(productPackageTmpEntity.getPackageSn());
        packageChangeLogEntity.setChangeAfter(productPackageTmpEntity.getPackageInfo());
        packageChangeLogEntity.setRemark("新增产品包："+productPackageTmpEntity.getPackageSn());
        packageChangeLogEntity.setOperater(loginCode);
        packageChangeLogService.create(packageChangeLogEntity);
        ProductPackageTmpAddVo productPackageTmpAddVo  =   new ProductPackageTmpAddVo();
        productPackageTmpAddVo.setPackageSn(newPackageSn);
        return productPackageTmpAddVo;
    }

    /*
     *  修改
     */
    public Boolean modify(ProductPackageModifyDTO productPackageModifyDTO,String loginCode){
        try{
            //查找产品包tmp
            ProductPackageTmpEntity productPackageTmpEntity = productPackageTmpService.getBySn(productPackageModifyDTO.getPackageSn());
            if(productPackageTmpEntity == null){
                ToolsHelper.throwException("该产品包不存在,"+productPackageModifyDTO.getPackageSn());
            }

            JSONObject applyConfigInfo = new JSONObject(new LinkedHashMap<>());
            applyConfigInfo.put("sale_sn",productPackageModifyDTO.getSaleSn());
            applyConfigInfo.put("goods_sku",productPackageModifyDTO.getGoodsSku());
            applyConfigInfo.put("issuer_id",productPackageModifyDTO.getIssuerId());
            applyConfigInfo.put("manufacturer",productPackageModifyDTO.getManufacturer());
            applyConfigInfo.put("device_type",productPackageModifyDTO.getDeviceType());
            applyConfigInfo.put("device_tutorial",productPackageModifyDTO.getDeviceTutorial());
            applyConfigInfo.put("device_name",productPackageModifyDTO.getDeviceName());
            applyConfigInfo.put("device_version",productPackageModifyDTO.getDeviceVersion());
            applyConfigInfo.put("device_color",productPackageModifyDTO.getDeviceColor());
            applyConfigInfo.put("payment_mode",productPackageModifyDTO.getPaymentMode());
            applyConfigInfo.put("purchase_party",productPackageModifyDTO.getPurchaseParty());
            applyConfigInfo.put("purchase_type",productPackageModifyDTO.getPurchaseType());
            applyConfigInfo.put("card_id",productPackageModifyDTO.getCardId());
            applyConfigInfo.put("flow_type",productPackageModifyDTO.getFlowType());
            applyConfigInfo.put("use_deposit", productPackageModifyDTO.getUseDeposit());

            // 检查sku 是否合法
            checkStallSkuAttr(new CheckStallSkuAttrDTO(
                    ChannelTypeEnum.getStallCodeByValue(1),
                    productPackageModifyDTO.getGoodsSku(),
                    CheckStallSkuAttrDTO.AttrMap.builder()
                            .issuerId(productPackageModifyDTO.getIssuerId().toString())
                            .manufacturer(productPackageModifyDTO.getManufacturer().toString())
                            .deviceColor(productPackageModifyDTO.getDeviceColor().toString())
                            .deviceType(productPackageModifyDTO.getDeviceType().toString())
                            .deviceVersion(productPackageModifyDTO.getDeviceVersion().toString())
                            .build()
            ));

            //检查issuerId和cardId
            checkIssuerIdAndCardId(productPackageModifyDTO.getIssuerId(),productPackageModifyDTO.getCardId(),productPackageModifyDTO.getIsTruck());

            Optional.ofNullable(productPackageModifyDTO.getMarketSource()).ifPresent(marketSource -> {
                applyConfigInfo.put("market_source", marketSource);
            });
            //根据flow_type ,补充必要参数
            JSONObject needParams = getNeedParamsByFlowType(productPackageModifyDTO.getFlowType());
            if (ObjectUtils.isNotEmpty(needParams)){
                applyConfigInfo.getInnerMap().putAll(needParams.getInnerMap());
            }

            applyConfigInfo.put("scene",productPackageModifyDTO.getScene());
            applyConfigInfo.put("original_price",productPackageModifyDTO.getOriginalPrice());
            if (ObjectUtils.isNotEmpty(productPackageModifyDTO.getTollDepositFee())) {
                applyConfigInfo.put("toll_deposit_fee", productPackageModifyDTO.getTollDepositFee());
            }

            // 货车非电商模式
            if (productPackageModifyDTO.getIsTruck() == 1 && !FlowTypeEnum.FLOW_PRODUCT_ORDER.getKey().equals(productPackageModifyDTO.getFlowType())) {
                // 根据purchaseParty设置结算类型和模式
                if (PurchasePartyEnum.checkIsTruckWeekMode(productPackageModifyDTO.getPurchaseParty())) {
                    applyConfigInfo.put("settle_type", "week"); // 周结
                    applyConfigInfo.put("mode", "wx_week"); // 周结模式
                } else if (PurchasePartyEnum.checkIsTruckDayMode(productPackageModifyDTO.getPurchaseParty())) {
                    applyConfigInfo.put("settle_type", "day"); // 日结
                    applyConfigInfo.put("mode", "wx"); // 日结模式
                }

                // contain_fee.service_fee = 0 兼容旧包
                JSONObject containFee = new JSONObject();
                containFee.put("service_fee", 0);
                applyConfigInfo.put("contain_fee", containFee);
            }

            // 把applyConfigInfo 的所有值复制到packageInfo
            LinkedHashMap<String, Object> packageInfo = new LinkedHashMap<>(applyConfigInfo.getInnerMap());

            JSONObject aftersaleConfigInfo = new JSONObject(new LinkedHashMap<>());
            aftersaleConfigInfo.put("revoke_need_pay",productPackageModifyDTO.getRevokeNeedPay());
            aftersaleConfigInfo.put("is_only_offline_exchange",productPackageModifyDTO.getIsOnlyOfflineExchange());
            aftersaleConfigInfo.put("is_show_reapply",productPackageModifyDTO.getIsShowReapply());
            aftersaleConfigInfo.put("warranty_time",productPackageModifyDTO.getWarrantyTime());

            packageInfo.put("aftersales",aftersaleConfigInfo);
            //兼容warranty_time
            packageInfo.put("warranty_time",productPackageModifyDTO.getWarrantyTime());
            //营销
            JSONObject marketConfigInfo = productPackageTmpEntity.getMarketConfigObj();
            marketConfigInfo.put("term",productPackageModifyDTO.getTerm());
            marketConfigInfo.put("allow_device_valuation",productPackageModifyDTO.getAllowDeviceValuation());

            if (ObjectUtils.isNotEmpty(productPackageModifyDTO.getTruckTerm())) {
                marketConfigInfo.put("truck_term", productPackageModifyDTO.getTruckTerm());
            }

            if (ObjectUtils.isNotEmpty(productPackageModifyDTO.getServiceBatch())) {
                marketConfigInfo.put("service_batch", productPackageModifyDTO.getServiceBatch());
            }

            if (NumberUtil.isPositive(productPackageModifyDTO.getRecoveryAmount())) {
                LinkedHashMap<String, Object> recoveryConfig = new LinkedHashMap<>();
                recoveryConfig.put("open", true);
                recoveryConfig.put("amount", productPackageModifyDTO.getRecoveryAmount());
                marketConfigInfo.put("recovery", recoveryConfig);
            }
            if (productPackageModifyDTO.getRecoveryAmount() != null
                    && productPackageModifyDTO.getRecoveryAmount().compareTo(BigDecimal.ZERO) == 0
            ) {
                marketConfigInfo.remove("recovery");
            }

            packageInfo.put("term",productPackageModifyDTO.getTerm());

//            marketConfigInfo.put("can_use_coupon_batch_no",productPackageModifyDTO.getCanUseCouponBatchNo());
//
//            marketConfigInfo.put("can_use_java_coupon_category", productPackageModifyDTO.getCanUseJavaCouponCategory());
//            packageInfo.put("can_use_java_coupon_category", productPackageModifyDTO.getCanUseJavaCouponCategory());

            packageInfo.put("java", marketConfigInfo);


            String changeInfo = "";
            if (AllowModifyEnum.NOT_ALLOW_MODIFY_ENUM.getValue().equals(productPackageTmpEntity.getAllowModify())) {
                HashMap<String, JSONObject> changeableKeyMap = new HashMap<>();
                changeableKeyMap.put("aftersaleConfig",
                        compareAndCheckDiffConfig(JSON.parseObject(productPackageTmpEntity.getAftersaleConfig()),
                                aftersaleConfigInfo, productPackageConfig.getChangeableKeys().get("aftersaleConfig")));

                changeableKeyMap.put("marketConfig",
                        compareAndCheckDiffConfig(JSON.parseObject(productPackageTmpEntity.getMarketConfig()),
                                marketConfigInfo, productPackageConfig.getChangeableKeys().get("marketConfig")));

                changeableKeyMap.put("applyConfig",
                        compareAndCheckDiffConfig(JSON.parseObject(productPackageTmpEntity.getApplyConfig()),
                                applyConfigInfo, productPackageConfig.getChangeableKeys().get("applyConfig")));

                // 处理实体其他字段 修改比对, 仅支持修改的字段, 新增字段需要在这里添加
                JSONObject productPackageTmpEntityJSONObject =
                        JSON.parseObject(JSONUtil.toJsonStr(productPackageTmpEntity));
                changeableKeyMap.put("mainConfig",
                        compareAndCheckDiffConfig(
                                getFieldValues(productPackageTmpEntityJSONObject
                                        , productPackageConfig.getMainConfigCheckKeys()),
                                getFieldValues(JSON.parseObject(JSONUtil.toJsonStr(productPackageModifyDTO)),
                                        productPackageConfig.getMainConfigCheckKeys()),
                                productPackageConfig.getChangeableKeys().get("mainConfig")));
                changeInfo = JSONUtil.toJsonStr(changeableKeyMap);
            }

            LambdaUpdateWrapper<ProductPackageTmpEntity> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(ProductPackageTmpEntity::getPackageSn, productPackageTmpEntity.getPackageSn())
                    .set(ProductPackageTmpEntity::getPackageName, productPackageModifyDTO.getPackageName())
                    .set(ProductPackageTmpEntity::getPackageFee, productPackageModifyDTO.getPackageFee())
                    .set(ProductPackageTmpEntity::getIsTruck, productPackageModifyDTO.getIsTruck())
                    .set(ProductPackageTmpEntity::getIsBase, productPackageModifyDTO.getIsBase())
                    .set(ProductPackageTmpEntity::getDeliveryType, productPackageModifyDTO.getDeliveryType())
                    .set(ProductPackageTmpEntity::getOfflineAutoDelivery, productPackageModifyDTO.getOfflineAutoDelivery())
                    .set(ProductPackageTmpEntity::getApplyConfig, applyConfigInfo.toJSONString())
                    .set(ProductPackageTmpEntity::getPackageInfo, JSONUtil.toJsonStr(packageInfo))
                    .set(ProductPackageTmpEntity::getMarketConfig, marketConfigInfo.toJSONString())
                    .set(ProductPackageTmpEntity::getBizType, productPackageModifyDTO.getBizType())
                    .set(ProductPackageTmpEntity::getAddressConfigId, productPackageModifyDTO.getAddressConfigId())
                    .set(ProductPackageTmpEntity::getSource, productPackageModifyDTO.getSource())
                    .set(ProductPackageTmpEntity::getAftersaleConfig, aftersaleConfigInfo.toJSONString())
                    .set(ProductPackageTmpEntity::getReleaseStatus, ReleaseStatusEnum.WAIT.getValue())
                    .set(ProductPackageTmpEntity::getTemplateId, productPackageModifyDTO.getTemplateId())
            ;

            productPackageTmpService.updateByWrapper(wrapper);
            //记录日志
            PackageChangeLogEntity packageChangeLogEntity = new PackageChangeLogEntity();
            packageChangeLogEntity.setPackageSn(productPackageTmpEntity.getPackageSn());
            packageChangeLogEntity.setChangeBefore(productPackageTmpEntity.getApplyConfig());
            packageChangeLogEntity.setChangeAfter(productPackageModifyDTO.toString());
            packageChangeLogEntity.setRemark("变更内容："+ (ObjectUtil.isEmpty(changeInfo) ? JSONUtil.toJsonStr(productPackageModifyDTO) : changeInfo));
            packageChangeLogEntity.setOperater(loginCode);
            packageChangeLogService.create(packageChangeLogEntity);

        }catch (Exception e){
            log.error("修改产品包异常",e);
            ToolsHelper.throwException("修改产品包信息失败："+e.getMessage());
            return false;
        }

        return true;
    }

    public JSONObject getFieldValues(JSONObject compareObject, Set<String> keys) {
        JSONObject jsonObject = new JSONObject();
        Set<String> keySet = compareObject.keySet();
        keySet.forEach(key -> {
            if (keys.contains(key)) {
                jsonObject.put(key, compareObject.get(key));
            }
        });
        return jsonObject;
    }
    /*
     *  申办内容修改
     */
    public Boolean applyConfigModify(ProductPackageApplyModifyDTO productPackageApplyModifyDTO, String loginCode){
        //查找产品包tmp
        ProductPackageTmpEntity productPackageTmpEntity = productPackageTmpService.getBySn(productPackageApplyModifyDTO.getPackageSn());
        if(productPackageTmpEntity == null){
            ToolsHelper.throwException("该产品包不存在,"+productPackageApplyModifyDTO.getPackageSn());
        }
        if(productPackageTmpEntity.getAllowModify() == 0){
            ToolsHelper.throwException("该产品包已发布，不可修改");
        }
        JSONObject applyConfigJsonObject = JSON.parseObject(productPackageTmpEntity.getApplyConfig());
        //匹配申办流程
        ProductPackageConfigEntity productPackageConfigEntity = productPackageConfigService.getByKey("applyConfig");
        JSONObject jsonObject = JSON.parseObject(productPackageConfigEntity.getConfigValues());
        JSONObject flowTypeObject = jsonObject.getJSONObject(productPackageApplyModifyDTO.getFlowType().toString());
        if(flowTypeObject.isEmpty()){
            ToolsHelper.throwException("该申办流程不存在");
        }
        //查看issuer_id是否存在
        JSONObject cardIdObject = flowTypeObject.getJSONObject("card_id");
        if(cardIdObject.isEmpty()){
            ToolsHelper.throwException("该申办流程对应的card_id不存在");
        }
        Integer cardId = cardIdObject.getInteger(applyConfigJsonObject.getInteger("issuer_id").toString());
        if(cardId < 1){
            ToolsHelper.throwException("该申办流程对应的issuer_id不存在");
        }
        //apply_config
        JSONObject newObject = new JSONObject(new LinkedHashMap<>());
        newObject.put("flow_type",productPackageApplyModifyDTO.getFlowType());
        newObject.put("payment_mode",productPackageApplyModifyDTO.getPaymentMode());
        newObject.put("sign_bank",productPackageApplyModifyDTO.getSignBank());
        newObject.put("use_deposit",productPackageApplyModifyDTO.getUseDeposit());
        for(String key:flowTypeObject.keySet()){
            switch (key){
                case "purchase_type":
                case "purchase_party":
                    newObject.put(key,flowTypeObject.getInteger(key));
                    break;
                case "card_id":
                    newObject.put(key,cardId);
                    break;
            }

        }
        applyConfigJsonObject.getInnerMap().putAll(newObject.getInnerMap());
        JSONObject packageInfo = JSON.parseObject(productPackageTmpEntity.getPackageInfo());
        packageInfo.getInnerMap().putAll(applyConfigJsonObject.getInnerMap());

        //记录日志
        PackageChangeLogEntity packageChangeLogEntity = new PackageChangeLogEntity();
        packageChangeLogEntity.setPackageSn(productPackageTmpEntity.getPackageSn());
        packageChangeLogEntity.setChangeBefore(productPackageTmpEntity.getApplyConfig());
        packageChangeLogEntity.setChangeAfter(applyConfigJsonObject.toJSONString());
        packageChangeLogEntity.setRemark("变更申办方式内容："+newObject.toJSONString());
        packageChangeLogEntity.setOperater(loginCode);

        LambdaUpdateWrapper<ProductPackageTmpEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ProductPackageTmpEntity::getPackageSn, productPackageTmpEntity.getPackageSn())
            .set(ProductPackageTmpEntity::getApplyConfig, applyConfigJsonObject.toJSONString())
            .set(ProductPackageTmpEntity::getPackageInfo, packageInfo.toJSONString());
        productPackageTmpService.updateByWrapper(wrapper);

        packageChangeLogService.create(packageChangeLogEntity);
        return true;
    }

    /*
     *  前端内容修改
     */
    public Boolean frontedConfigModify(ProductPackageFrontedModifyDTO productPackageFrontedModifyDTO, String loginCode){
        //查找产品包tmp
        ProductPackageTmpEntity productPackageTmpEntity = productPackageTmpService.getBySn(productPackageFrontedModifyDTO.getPackageSn());

        JSONObject newConfig = JSON.parseObject(productPackageFrontedModifyDTO.getFrontedConfig());
        JSONObject diffConfig = new JSONObject();
        Set<String> key2 = newConfig.keySet();
        if(productPackageTmpEntity.getFrontedConfig() == null){
            for(String key:key2){
                diffConfig.put(key,"{\"old\":\"\",\"new\":"+newConfig.get(key)+"}");
            }
        }else{
            // 配置申办指引样式
            JSONObject oldConfig = JSON.parseObject(productPackageTmpEntity.getFrontedConfig());
            if (oldConfig.containsKey("applyGuideInfo") && !newConfig.containsKey("applyGuideInfo")) {
                // 前端没传 但是包原有配置 要保留
                newConfig.put("applyGuideInfo", oldConfig.getJSONObject("applyGuideInfo"));
            }
            diffConfig = compareDiffConfig(oldConfig,newConfig);
            if(diffConfig.size() == 0){
                ToolsHelper.throwException("前端内容没有修改更新");
            }
        }
        //针对其他配置的key检查，不可重复
        JSONObject applyConfig = JSON.parseObject(productPackageTmpEntity.getApplyConfig());
        String repeatKey = checkRepeatKey(applyConfig.keySet(),key2);
        if(repeatKey != null){
            ToolsHelper.throwException("字段"+repeatKey+"与申办配置重复");
        }
        //记录日志
        PackageChangeLogEntity packageChangeLogEntity = new PackageChangeLogEntity();
        packageChangeLogEntity.setPackageSn(productPackageTmpEntity.getPackageSn());
        packageChangeLogEntity.setChangeBefore(productPackageTmpEntity.getFrontedConfig());
        packageChangeLogEntity.setChangeAfter(productPackageFrontedModifyDTO.getFrontedConfig());
        packageChangeLogEntity.setRemark("变更前端内容："+diffConfig.toJSONString());
        packageChangeLogEntity.setOperater(loginCode);

        JSONObject packageInfo = JSON.parseObject(productPackageTmpEntity.getPackageInfo());
        packageInfo.getInnerMap().putAll(newConfig.getInnerMap());
        LambdaUpdateWrapper<ProductPackageTmpEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ProductPackageTmpEntity::getPackageSn, productPackageTmpEntity.getPackageSn())
            .set(ProductPackageTmpEntity::getFrontedConfig, productPackageFrontedModifyDTO.getFrontedConfig())
            .set(ProductPackageTmpEntity::getReleaseStatus, ReleaseStatusEnum.WAIT.getValue())
            .set(ProductPackageTmpEntity::getPackageInfo, packageInfo.toJSONString());
        productPackageTmpService.updateByWrapper(wrapper);

        packageChangeLogService.create(packageChangeLogEntity);

        //更新映射产品包缓存
        callPhpApplyFeign.delCacheByPackageSn(new MapDelCacheByPackageSnDTO(MapSetCacheEnum.MAP_SET_CACHE_TYPE_PREVIEW.getCode(), productPackageTmpEntity.getPackageSn()));

        return true;
    }
    /*
     * 检查 是否存在重复的key
     */
    public String checkRepeatKey(Set<String>key1,Set<String>key2){
        for(String key:key1){
            if(key2.contains(key)){
               return key;
            }
        }
        return null;
    }

    /*
     *  发布产品包到生产
     */
    @CacheEvict(value = CacheKeyConstant.PRODUCT_PACKAGE_ENTITY_CACHE,key = "#productPackageReleaseDTO.getPackageSn()")
    public Boolean releaseTmpToProd(ProductPackageReleaseDTO productPackageReleaseDTO, String loginCode){
        //查找产品包tmp
        ProductPackageTmpEntity productPackageTmpEntity = productPackageTmpService.getBySn(productPackageReleaseDTO.getPackageSn());
        //发布前参数检查
        if(productPackageTmpEntity.getApplyConfig() == null || productPackageTmpEntity.getApplyConfig().equals("[]")){
            ToolsHelper.throwException("申办内容不可为空");
        }
        if(productPackageTmpEntity.getFrontedConfig() == null || productPackageTmpEntity.getFrontedConfig().equals("[]")){
            ToolsHelper.throwException("前端样式内容不可为空");
        }
        //生产产品包
        ProductPackageEntity productPackageEntity = productPackageService.getBySn(productPackageReleaseDTO.getPackageSn());
        //检查key是否重复
        String repeatKey;
        //新增产品包
        if(productPackageEntity == null){
            //针对其他配置的key检查，不可重复
            JSONObject frontedConfigTmp = JSON.parseObject(productPackageTmpEntity.getFrontedConfig());
            //比较申办配置
            repeatKey = checkRepeatKey(JSON.parseObject(productPackageTmpEntity.getApplyConfig()).keySet(),frontedConfigTmp.keySet());
            if(repeatKey != null){
                ToolsHelper.throwException("字段"+repeatKey+"与申办配置重复");
            }
            //检查是否有限制发布
            checkCanProdApply(JSON.parseObject(productPackageTmpEntity.getApplyConfig()));

            //比较售后配置
            if(productPackageTmpEntity.getAftersaleConfig() != null &&  !productPackageTmpEntity.getAftersaleConfig().equals("[]") ){
                repeatKey = checkRepeatKey(JSON.parseObject(productPackageTmpEntity.getAftersaleConfig()).keySet(),frontedConfigTmp.keySet());
                if(repeatKey != null){
                    ToolsHelper.throwException("字段"+repeatKey+"与售后配置重复");
                }
            }
            //比较营销配置
            if(productPackageTmpEntity.getMarketConfig() != null &&  !productPackageTmpEntity.getMarketConfig().equals("[]") ){
                repeatKey = checkRepeatKey(JSON.parseObject(productPackageTmpEntity.getMarketConfig()).keySet(),frontedConfigTmp.keySet());
                if(repeatKey != null){
                    ToolsHelper.throwException("字段"+repeatKey+"与营销配置重复");
                }
            }
            //只更新前端配置
            JSONObject packageInfo = JSON.parseObject(productPackageTmpEntity.getPackageInfo());
            packageInfo.getInnerMap().putAll(frontedConfigTmp.getInnerMap());

            ProductPackageEntity productPackageEntityNew = new ProductPackageEntity();
            productPackageEntityNew.setPackageSn(productPackageTmpEntity.getPackageSn());
            productPackageEntityNew.setPackageName(productPackageTmpEntity.getPackageName());
            productPackageEntityNew.setPackageFee(productPackageTmpEntity.getPackageFee());
            productPackageEntityNew.setStatus(0);
            productPackageEntityNew.setIsTruck(productPackageTmpEntity.getIsTruck());
            productPackageEntityNew.setIsBase(productPackageTmpEntity.getIsBase());
            productPackageEntityNew.setDeliveryType(productPackageTmpEntity.getDeliveryType());
            productPackageEntityNew.setOfflineAutoDelivery(productPackageTmpEntity.getOfflineAutoDelivery());
            productPackageEntityNew.setApplyConfig(productPackageTmpEntity.getApplyConfig());
            productPackageEntityNew.setPageConfig(productPackageTmpEntity.getPageConfig());
            productPackageEntityNew.setPackageInfo(packageInfo.toJSONString());
            productPackageEntityNew.setBizType(productPackageTmpEntity.getBizType());
            productPackageEntityNew.setSource(productPackageTmpEntity.getSource());
            productPackageEntityNew.setFrontedConfig(productPackageTmpEntity.getFrontedConfig());
            productPackageEntityNew.setAftersaleConfig(productPackageTmpEntity.getAftersaleConfig());
            productPackageEntityNew.setMarketConfig(productPackageTmpEntity.getMarketConfig());
            productPackageEntityNew.setExpireDate(productPackageTmpEntity.getExpireDate());
            // 设置默认库存
            productPackageEntityNew.setPackageStock(productPackageConfig.getDefaultStock());
            productPackageService.create(productPackageEntityNew);
            //记录日志
            PackageChangeLogEntity packageChangeLogEntity = new PackageChangeLogEntity();
            packageChangeLogEntity.setPackageSn(productPackageEntityNew.getPackageSn());
            packageChangeLogEntity.setVersionDesc(productPackageReleaseDTO.getVersionDesc());
            packageChangeLogEntity.setRemark("新发布产品包："+productPackageEntityNew.getPackageSn());
            packageChangeLogEntity.setOperater(loginCode);
            packageChangeLogService.create(packageChangeLogEntity);
        }else{
            JSONObject diffConfig = compareDiffConfig(JSON.parseObject(productPackageEntity.getFrontedConfig())
                    , JSON.parseObject(productPackageTmpEntity.getFrontedConfig()));

            //针对其他配置的key检查，不可重复
            JSONObject frontedConfigTmp = JSON.parseObject(productPackageTmpEntity.getFrontedConfig());
            //比较申办配置

            if (productPackageEntity.getApplyConfig() != null) {
                repeatKey = checkRepeatKey(JSON.parseObject(productPackageEntity.getApplyConfig()).keySet(),
                        frontedConfigTmp.keySet());
                if (repeatKey != null) {
                    ToolsHelper.throwException("字段" + repeatKey + "与申办配置重复");
                }
            }
            //检查是否有限制发布
            checkCanProdApply(JSON.parseObject(productPackageTmpEntity.getApplyConfig()));

            //比较售后配置
            if (productPackageEntity.getAftersaleConfig() != null && !productPackageTmpEntity.getAftersaleConfig().equals("[]")) {
                repeatKey = checkRepeatKey(JSON.parseObject(productPackageEntity.getAftersaleConfig()).keySet()
                        , frontedConfigTmp.keySet());
                if (repeatKey != null) {
                    ToolsHelper.throwException("字段" + repeatKey + "与售后配置重复");
                }
            }
            //比较营销配置
            if (productPackageEntity.getMarketConfig() != null && !productPackageTmpEntity.getMarketConfig().equals(
                    "[]")) {
                repeatKey = checkRepeatKey(JSON.parseObject(productPackageEntity.getMarketConfig()).keySet(),
                        frontedConfigTmp.keySet());
                if (repeatKey != null) {
                    ToolsHelper.throwException("字段" + repeatKey + "与营销配置重复");
                }
            }
            HashMap<String, JSONObject> changeableKeyMap = new HashMap<>();
            changeableKeyMap.put("aftersaleConfig",
                    compareAndCheckDiffConfig(JSON.parseObject(productPackageEntity.getAftersaleConfig()),
                            JSON.parseObject(productPackageTmpEntity.getAftersaleConfig()),
                            productPackageConfig.getChangeableKeys().get("aftersaleConfig")));
            changeableKeyMap.put("marketConfig",
                    compareAndCheckDiffConfig(JSON.parseObject(productPackageEntity.getMarketConfig()),
                            JSON.parseObject(productPackageTmpEntity.getMarketConfig()),
                            productPackageConfig.getChangeableKeys().get("marketConfig")));
            changeableKeyMap.put("applyConfig",
                    compareAndCheckDiffConfig(JSON.parseObject(productPackageEntity.getApplyConfig()),
                            JSON.parseObject(productPackageTmpEntity.getApplyConfig()),
                            productPackageConfig.getChangeableKeys().get("applyConfig")));
            // 处理主配置 修改比对, 仅支持修改的字段, 新增字段需要在这里添加
            changeableKeyMap.put("mainConfig",
                    compareAndCheckDiffConfig(getFieldValues(JSON.parseObject(JSONUtil.toJsonStr(productPackageEntity))
                                    , productPackageConfig.getMainConfigCheckKeys()),
                            getFieldValues(JSON.parseObject(JSONUtil.toJsonStr(productPackageTmpEntity)),
                                    productPackageConfig.getMainConfigCheckKeys()),
                            productPackageConfig.getChangeableKeys().get("mainConfig")));

            JSONObject packageInfo = JSON.parseObject(productPackageTmpEntity.getPackageInfo());
            packageInfo.getInnerMap().putAll(frontedConfigTmp.getInnerMap());
            LambdaUpdateWrapper<ProductPackageEntity> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(ProductPackageEntity::getPackageSn, productPackageTmpEntity.getPackageSn())
                    .set(ProductPackageEntity::getFrontedConfig, productPackageTmpEntity.getFrontedConfig())
                    .set(ProductPackageEntity::getPackageInfo, packageInfo.toJSONString())
                    .set(ProductPackageEntity::getApplyConfig, productPackageTmpEntity.getApplyConfig())
                    .set(ProductPackageEntity::getPageConfig, productPackageTmpEntity.getPageConfig())
                    .set(ProductPackageEntity::getAftersaleConfig, productPackageTmpEntity.getAftersaleConfig())
                    .set(ProductPackageEntity::getMarketConfig, productPackageTmpEntity.getMarketConfig())
            ;
            // 增加处理允许修改的字段
            if (!productPackageEntity.getPackageName().equals(productPackageTmpEntity.getPackageName())) {
                wrapper.set(ProductPackageEntity::getPackageName, productPackageTmpEntity.getPackageName());
            }
            if (!productPackageEntity.getBizType().equals(productPackageTmpEntity.getBizType())) {
                wrapper.set(ProductPackageEntity::getBizType, productPackageTmpEntity.getBizType());
            }
            if (!productPackageEntity.getAddressConfigId().equals(productPackageTmpEntity.getAddressConfigId())) {
                wrapper.set(ProductPackageEntity::getAddressConfigId, productPackageTmpEntity.getAddressConfigId());
            }

            productPackageService.updateByWrapper(wrapper);

            JSONObject diffPageConfig = compareDiffConfig(JSON.parseObject(productPackageEntity.getPageConfig())
                    , JSON.parseObject(productPackageTmpEntity.getPageConfig()));
            //记录日志
            PackageChangeLogEntity packageChangeLogEntity = new PackageChangeLogEntity();
            packageChangeLogEntity.setPackageSn(productPackageEntity.getPackageSn());
            packageChangeLogEntity.setVersionDesc(productPackageReleaseDTO.getVersionDesc());
            packageChangeLogEntity.setRemark("变更内容："+JSONUtil.toJsonStr(changeableKeyMap) + "前端配置："+diffConfig.toJSONString()+ "页面配置："+diffPageConfig.toJSONString());
            packageChangeLogEntity.setOperater(loginCode);
            packageChangeLogService.create(packageChangeLogEntity);

            //如果是基础包更新，则需要更新子包
            if(productPackageEntity.getIsBase() == 1){
                this.updatePackageByParent(productPackageEntity.getPackageSn());
            }
        }

        LambdaUpdateWrapper<ProductPackageTmpEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ProductPackageTmpEntity::getPackageSn, productPackageTmpEntity.getPackageSn())
            .set(ProductPackageTmpEntity::getAllowModify, 0)
            .set(ProductPackageTmpEntity::getReleaseStatus, ReleaseStatusEnum.PROD.getValue());
        productPackageTmpService.updateByWrapper(wrapper);

        //更新映射产品包缓存
        callPhpApplyFeign.delCacheByPackageSn(new MapDelCacheByPackageSnDTO(MapSetCacheEnum.MAP_SET_CACHE_TYPE_PROD.getCode(), productPackageTmpEntity.getPackageSn()));
        
        return true;
    }
    /*
     *  比较新旧两个配置
     */
    public JSONObject compareDiffConfig(JSONObject oldConfig,JSONObject newConfig) {
        JSONObject diffConfig = new JSONObject();
        if(newConfig == null){
            return diffConfig;
        }
        Set<String> key2 = newConfig.keySet();
        if (oldConfig == null) {
            for (String key : key2) {
                diffConfig.put(key, "{\"old\":\"\",\"new\":" + newConfig.get(key) + "}");
            }
        } else {
            Set<String> key1 = oldConfig.keySet();
            for (String key : key1) {
                if(key2.contains(key)){
                    if(!oldConfig.get(key).equals(newConfig.get(key))){
                        diffConfig.put(key, "{\"old\":" + oldConfig.get(key) + ",\"new\":" + newConfig.get(key) + "}");
                    }
                }else{
                    diffConfig.put(key, "{\"old\":" + oldConfig.get(key) + ",\"new\":\"\"}");
                }
            }
            for (String key : key2) {
                if (!key1.contains(key)) {
                    diffConfig.put(key, "{\"old\":\"\",\"new\":" + newConfig.get(key) + "}");
                }
            }
        }
        return diffConfig;
    }

    /*
     * 获取全部产品包列表
     */
    public IPage<PackageChangeLogEntity> getAllLogList(PackageLogGetListDTO dto) {
        // 分页设置
        IPage<PackageChangeLogEntity> oPage = new Page<>(dto.getPageNum(), dto.getPageSize(), true);

        // 查询条件设置
        LambdaQueryWrapper<PackageChangeLogEntity> wrapper = new LambdaQueryWrapper<>();

        wrapper.eq(PackageChangeLogEntity::getPackageSn, dto.getPackageSn())
            .orderByDesc(PackageChangeLogEntity::getCreatedAt);

        IPage<PackageChangeLogEntity> pageList = packageChangeLogService.getPageListByWrapper(oPage, wrapper);
        return pageList;
    }

    /*
     * 获取全部产品包列表
     */
    public JSONObject getAllConfig() {
        //匹配sku
        ProductPackageConfigEntity productPackageConfigEntity = productPackageConfigService.getByKey("adminConfig");
        JSONObject jsonObject = JSON.parseObject(productPackageConfigEntity.getConfigValues());

        //读取配置字段的值
        //获取biz_type的数据
//        LambdaQueryWrapper<ConfigBizFieldValuesEntity> wrapper = new LambdaQueryWrapper<>();
//        wrapper.select(ConfigBizFieldValuesEntity::getFieldKey,ConfigBizFieldValuesEntity::getName)
//               .eq(ConfigBizFieldValuesEntity::getBizField, "biz_type")
//               .eq(ConfigBizFieldValuesEntity::getStatus, 1)
//               .orderByAsc(ConfigBizFieldValuesEntity::getSort);
//        List<ConfigBizFieldValuesEntity> list =  configBizFieldValuesService.getListByWrapper(wrapper);
//        List<ConfigNameKeyIntegerBO> bizTypeList = new ArrayList<>();
//        if(list != null){
//            for (ConfigBizFieldValuesEntity configBizFieldValues : list) {
//                ConfigNameKeyIntegerBO configNameKeyInteger = new ConfigNameKeyIntegerBO();
//                configNameKeyInteger.setKey(Integer.parseInt(configBizFieldValues.getFieldKey()));
//                configNameKeyInteger.setName(configBizFieldValues.getName()+"【"+configBizFieldValues.getFieldKey()+"】");
//                bizTypeList.add(configNameKeyInteger);
//            }
//        }

        //jsonObject.put("biz_type", bizTypeList);
        //log.info(productPackageConfig.toString());
        if(productPackageConfig.getConfigBizFieldIntegerKeys().size() > 0){
            //整型数据
            productPackageConfig.getConfigBizFieldIntegerKeys().forEach(key -> {
                jsonObject.put(key, configBizFieldBusiness.getIntegerBizField(key));
            });
        }
        if(productPackageConfig.getConfigBizFieldStringKeys().size() > 0){
            //字符串数据
            productPackageConfig.getConfigBizFieldStringKeys().forEach(key -> {
                jsonObject.put(key, configBizFieldBusiness.getStringBizField(key));
            });
        }

        return jsonObject;
    }

    /*
     * 获取全部产品包列表
     */
    public PackageTmpInfoVo getTmpInfo(PackageTmpGetInfoDTO dto) {
        //查找产品包tmp
        ProductPackageTmpEntity productPackageTmpEntity = productPackageTmpService.getBySn(dto.getPackageSn());
        if(productPackageTmpEntity == null){
            ToolsHelper.throwException("产品包"+dto.getPackageSn()+"不存在");
        }
        PackageTmpInfoVo vo = new PackageTmpInfoVo();
        BeanUtils.copyProperties(productPackageTmpEntity, vo);
        vo.setStatusStr(ProductPackageStatusEnum.getDescByCode(productPackageTmpEntity.getStatus()));
        vo.setReleaseStatusStr(ReleaseStatusEnum.getDescByCode(productPackageTmpEntity.getReleaseStatus()));
        vo.setApplyConfig(JSON.parseObject(productPackageTmpEntity.getApplyConfig()));
        vo.setFrontedConfig(JSON.parseObject(productPackageTmpEntity.getFrontedConfig()));
        if(productPackageTmpEntity.getAftersaleConfig()!= null && !productPackageTmpEntity.getAftersaleConfig().equals("[]")){
            vo.setAftersaleConfig(JSON.parseObject(productPackageTmpEntity.getAftersaleConfig()));
        }
        vo.setMarketConfig(JSON.parseObject(productPackageTmpEntity.getMarketConfig()));
        if(productPackageTmpEntity.getPageConfig()!= null && !productPackageTmpEntity.getPageConfig().equals("[]")){
            vo.setPageConfig(JSON.parseObject(productPackageTmpEntity.getPageConfig()));
        }

        return vo;
    }

    /*
     * 获取全部产品包列表
     */
    public JSONObject getFrontedConfigDiff(PackageTmpGetInfoDTO dto) {
        //查找产品包tmp
        ProductPackageTmpEntity productPackageTmpEntity = productPackageTmpService.getBySn(dto.getPackageSn());
        //生产产品包
        ProductPackageEntity productPackageEntity = productPackageService.getBySn(dto.getPackageSn());
        if(productPackageEntity == null){
            return new JSONObject();
        }
        return compareDiffConfig(JSON.parseObject(productPackageEntity.getFrontedConfig()),JSON.parseObject(productPackageTmpEntity.getFrontedConfig()));
    }


    /*
     * 获取全部产品包列表
     */
    public Boolean updateStock(PackageUpdateStockDTO dto) {
        //生产产品包
        ProductPackageEntity productPackageEntity = productPackageService.getBySn(dto.getPackageSn());
        if(productPackageEntity == null){
            ToolsHelper.throwException("产品包"+dto.getPackageSn()+"不存在或者未发布");
        }
        ProductPackageStockEntity productPackageStockEntity = new ProductPackageStockEntity();
        switch (dto.getOperate()){
            //加库存
            case "incr":
                //数据库加库存
                productPackageService.updateStock(dto.getPackageSn(),dto.getStock());
                //增加记录
                productPackageStockEntity.setPackageSn(productPackageEntity.getPackageSn());
                productPackageStockEntity.setStockSn(ToolsHelper.genNum(redisPermanentTemplate, "ProductPackageStock", "prod", 8));
                productPackageStockEntity.setPreStock(productPackageEntity.getPackageStock());
                productPackageStockEntity.setOperateType(dto.getOperate());
                productPackageStockEntity.setOperateStock(dto.getStock());
                productPackageStockEntity.setRemark("后台增加库存，"+dto.getMsg());
                productPackageStockService.create(productPackageStockEntity);
                break;
            //加库存
            case "decr":
                //数据库加库存
                productPackageService.updateStock(dto.getPackageSn(),-dto.getStock());
                //增加记录
                productPackageStockEntity.setPackageSn(productPackageEntity.getPackageSn());
                productPackageStockEntity.setStockSn(ToolsHelper.genNum(redisPermanentTemplate, "ProductPackageStock", "prod", 8));
                productPackageStockEntity.setPreStock(productPackageEntity.getPackageStock());
                productPackageStockEntity.setOperateType(dto.getOperate());
                productPackageStockEntity.setOperateStock(-dto.getStock());
                productPackageStockEntity.setRemark("后台减库存，"+dto.getMsg());
                productPackageStockService.create(productPackageStockEntity);
                break;
            default:
                ToolsHelper.throwException("操作方式"+dto.getOperate()+"不存在");
                break;
        }

        return true;
    }


    /*
     * 获取全部产品包列表
     */
    public IPage<GetListByAddressConfigIdVo> getListByAddressConfigId(GetListByAddressConfigIdDTO dto) {
        // 分页设置
        IPage<ProductPackageEntity> oPage = new Page<>(dto.getPageNum(), dto.getPageSize(), true);
        // 查询条件设置
        LambdaQueryWrapper<ProductPackageEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(NumberUtil.isPositive(dto.getAddressConfigId()), ProductPackageEntity::getAddressConfigId, dto.getAddressConfigId())
            .orderByDesc(ProductPackageEntity::getCreatedAt);
        IPage<ProductPackageEntity> pageList = productPackageService.getPageListByWrapper(oPage, wrapper);
        return pageList.convert(record -> {
            GetListByAddressConfigIdVo vo = new GetListByAddressConfigIdVo();
            BeanUtils.copyProperties(record, vo);
            return vo;
        });
    }

    /*
     * 通过flow_type获取其他申办参数
     */
    public JSONObject getNeedParamsByFlowType(Integer flowType){
        JSONObject needParams = null;
        //检查flow_type是否配置固定的参数need_params
        ProductPackageConfigEntity productPackageConfigEntity = productPackageConfigService.getByKey("adminProdConfig");
        JSONObject jsonObject = JSON.parseObject(productPackageConfigEntity.getConfigValues());
        JSONObject needParamsObject = jsonObject.getJSONObject("need_params");
        needParams = needParamsObject.getJSONObject(flowType.toString());
        return needParams;
    }

    /*
     * 获取全部产品包列表
     */
    public ProductPackageInfoVo getInfoByPackageSn(GetInfoByPackageSnDTO dto) {

        ProductPackageEntity productPackage = productPackageService.getBySn(dto.getPackageSn());
        if( productPackage == null){
            ToolsHelper.throwException(dto.getPackageSn()+"不存在");
        }
        ProductPackageInfoVo vo = new ProductPackageInfoVo();
        BeanUtils.copyProperties(productPackage, vo);
        JSONObject jsonObject = JSON.parseObject(productPackage.getPackageInfo());
        if (ObjectUtils.isNotEmpty(jsonObject)) {
            Integer manufacturer = jsonObject.getInteger("manufacturer");
            if (ObjectUtils.isNotEmpty(manufacturer)) {
                vo.setManufacturer(manufacturer);
            }
            Integer deviceType = jsonObject.getInteger("device_type");
            if (ObjectUtils.isNotEmpty(deviceType)) {
                vo.setDeviceType(deviceType);
            }
            Integer deviceVersion = jsonObject.getInteger("device_version");
            if (ObjectUtils.isNotEmpty(deviceVersion)) {
                vo.setDeviceVersion(deviceVersion);
            }

            String title = jsonObject.getString("title");
            if (ObjectUtils.isNotEmpty(title)) {
                vo.setTitle(title);
            }
            String subTitle = jsonObject.getString("sub_title");
            if (ObjectUtils.isNotEmpty(subTitle)) {
                vo.setSubTitle(subTitle);
            }
            String oriFee = jsonObject.getString("ori_fee");
            if (ObjectUtils.isNotEmpty(oriFee)) {
                vo.setOriFee(oriFee);
            }
            String productImg = jsonObject.getString("product_img");
            if (ObjectUtils.isNotEmpty(productImg)) {
                vo.setProductImg(productImg);
            }

            Integer term = jsonObject.getInteger("term");
            if (ObjectUtils.isNotEmpty(term)) {
                vo.setTerm(term);
            }
            Integer cardId = jsonObject.getInteger("card_id");
            if (ObjectUtils.isNotEmpty(cardId)) {
                vo.setCardId(cardId);
            }
            Integer issuerId = jsonObject.getInteger("issuer_id");
            if (ObjectUtils.isEmpty(issuerId) && ObjectUtils.isNotEmpty(cardId)) {
                CardsEntity cardsEntity = cardsService.getByCardId(cardId);
                issuerId = cardsEntity.getIssuerId();
            }
            vo.setIssuerId(issuerId);

            if(ObjectUtils.isNotEmpty(issuerId)){
                vo.setIssuerServiceName(getIssuerServiceName(issuerId,dto.getPlateNoPre()));
            }
            String protocol = jsonObject.getString("protocol");
            if (ObjectUtils.isNotEmpty(protocol)) {
                vo.setProtocol(protocol);
            }
            Boolean hasCoupon = jsonObject.getBoolean("has_coupon");
            if (ObjectUtils.isNotEmpty(hasCoupon)) {
                vo.setHasCoupon(hasCoupon);
            }
            JSONArray productTags = jsonObject.getJSONArray("product_tags");
            if (ObjectUtils.isNotEmpty(productTags)) {
                vo.setProductTabs(productTags);
            }
            JSONObject aftersales = jsonObject.getJSONObject("aftersales");
            if (ObjectUtils.isNotEmpty(aftersales)) {
                ProductPackageInfoVo.AfterSales afterSalesVo = new ProductPackageInfoVo.AfterSales();
                Integer showReapply = aftersales.getInteger("is_show_reapply");
                if (ObjectUtils.isNotEmpty(showReapply)) {
                    afterSalesVo.setShowReapply(showReapply);
                }
                Integer onlyOfflineExchange = aftersales.getInteger("is_only_offline_exchange");
                if (ObjectUtils.isNotEmpty(onlyOfflineExchange)) {
                    afterSalesVo.setOnlyOfflineExchange(onlyOfflineExchange);
                }
                Integer revokeNeedPay = aftersales.getInteger("revoke_need_pay");
                if (ObjectUtils.isNotEmpty(revokeNeedPay)) {
                    afterSalesVo.setRevokeNeedPay(revokeNeedPay);
                }
                Integer warrantyTime = aftersales.getInteger("warranty_time");
                if (ObjectUtils.isNotEmpty(warrantyTime)) {
                    afterSalesVo.setWarrantyTime(warrantyTime);
                }

                vo.setAfterSales(afterSalesVo);
            }
        }

        if (StringUtils.isNotEmpty(productPackage.getApplyConfig())) {
            JSONObject applyConfig = JSONObject.parseObject(productPackage.getApplyConfig());
            vo.setDeviceColor(applyConfig.getInteger("device_color"));
            if (vo.getDeviceColor() == null) {
                // 兼容旧包，默认返回0
                vo.setDeviceColor(0);
            }
            vo.setSkuSn(applyConfig.getString("goods_sku"));
            vo.setDeviceName(applyConfig.getString("device_name"));
        }
        if (NumberUtil.isPositive(productPackage.getBizType())) {
            ConfigBizFieldValuesEntity bizEntity = configBizFieldValuesService.getInfoByBizFieldAndKey("biz_type", String.valueOf(productPackage.getBizType()));
            if (bizEntity != null) {
                vo.setBizTypeName(bizEntity.getName());
            }
        }

        return vo;

    }

    /*
     * 获取全部产品包列表
     */
    public String getIssuerServiceName(Integer issuerId,String plateNoPre) {
        String defaultName = "";
        if( issuerId == IssuerIdEnum.WANGLUZL.getCode()){
            if(ObjectUtils.isNotEmpty(plateNoPre)){
                defaultName = IssuerServiceCodeMap.getNameByPlateNoPre(plateNoPre);
            }

        }else{
            defaultName = IssuerIdServiceCodeMap.getNameByIssuerId(issuerId);
        }
        return defaultName;
    }


    /**
     * 修改排序
     * @param dto
     * @param loginCode
     */
    public void updateSort(ProductPackageUpdateSortDTO dto,String loginCode) {
        ProductPackageEntity productPackageEntity = productPackageService.getBySn(dto.getPackageSn());
        if(Objects.isNull(productPackageEntity)){
            ToolsHelper.throwException("产品包不存在");
        }

        // 如果设置的值和数据库中的值一样，则不做处理
        if(productPackageEntity.getSort().equals(dto.getSort())){
            return;
        }
        ProductPackageEntity oldProductPackageEntity = productPackageService.getBySortAndSource(dto.getSort(),
                productPackageEntity.getSource());
        if (ObjectUtil.isNotEmpty(oldProductPackageEntity)) {
            ToolsHelper.throwException("排序值已存在:" + oldProductPackageEntity.getPackageSn() + ",请重新设置");
        }
        LambdaUpdateWrapper<ProductPackageEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ProductPackageEntity::getPackageSn, dto.getPackageSn())
                .set(ProductPackageEntity::getSort, dto.getSort());
        productPackageService.updateByWrapper(wrapper);

        // 记录修改日志
        PackageChangeLogEntity packageChangeLogEntity = new PackageChangeLogEntity();
        packageChangeLogEntity.setPackageSn(productPackageEntity.getPackageSn());
        packageChangeLogEntity.setChangeBefore("{sort:\""+productPackageEntity.getSort().toString()+"\"}");
        packageChangeLogEntity.setChangeAfter(JSONObject.toJSONString(dto));
        packageChangeLogEntity.setRemark("变更排序："+dto.getSort());
        packageChangeLogEntity.setOperater(loginCode);
        packageChangeLogService.create(packageChangeLogEntity);
    }

    /**
     * 商城产品包设置是否允许创建订单
     *
     */
    public void updateIsShowAdd(ProductPackageSetIsShowAddDTO dto, String loginCode) {
        ProductPackageEntity productPackageEntity = productPackageService.getBySn(dto.getPackageSn());
        if (Objects.isNull(productPackageEntity)) {
            ToolsHelper.throwException("产品包不存在");
        }

        // 如果设置的值和数据库中的值一样，则不做处理
        if(productPackageEntity.getIsShowAdd().equals(dto.getIsShowAdd())){
           return;
        }
        LambdaUpdateWrapper<ProductPackageEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ProductPackageEntity::getPackageSn, dto.getPackageSn())
                .set(ProductPackageEntity::getIsShowAdd, dto.getIsShowAdd());
        productPackageService.updateByWrapper(wrapper);

        // 记录修改日志
        PackageChangeLogEntity packageChangeLogEntity = new PackageChangeLogEntity();
        packageChangeLogEntity.setPackageSn(productPackageEntity.getPackageSn());
        packageChangeLogEntity.setChangeBefore("{isShowAdd:\""+productPackageEntity.getIsShowAdd().toString()+"\"}");
        packageChangeLogEntity.setChangeAfter(JSONObject.toJSONString(dto));
        packageChangeLogEntity.setRemark("变更是否允许创建商城订单：" + dto);
        packageChangeLogEntity.setOperater(loginCode);
        packageChangeLogService.create(packageChangeLogEntity);
    }

    public HashMap<String, Object> getPackageSelectOptions() {
        HashMap<String, Object> options = new HashMap<>();
        List<SourceCategoryBO> sourceCategory = sourceBusiness.getSourceCategory(new ArrayList<>());
        options.put("source", sourceCategory);
        options.put("isShowAdd", ProductPackageIsShowAddEnum.getLabelList());
        options.put("status", ProductPackageStatusEnum.getLabelList());
        options.put("marketSource", MarketSourceEnum.getLabelList());
        return options;
    }


    public void changeableKeyCheck(HashMap<String,JSONObject> changeableKeyMap) {

        if (ObjectUtil.isNotEmpty(changeableKeyMap)) {
            JSONObject changeableKeyConfig = getChangeableKeyConfig();
            if (ObjectUtil.isEmpty(changeableKeyConfig)) {
                ToolsHelper.throwException("可修改key配置文件为空,不允许修改");
            }
            for (Map.Entry<String, JSONObject> entry : changeableKeyMap.entrySet()) {
                if(ObjectUtil.isEmpty(entry.getValue())){
                    continue;
                }
                String key = entry.getKey();
                if (!changeableKeyConfig.containsKey(key)) {
                    ToolsHelper.throwException("可修改信息不包含" + key + "的配置，不允许修改");
                }
                Set<String> keySet = entry.getValue().keySet();
                JSONArray jsonArrayConfig= changeableKeyConfig.getJSONArray(key);
                for (String k : keySet) {
                    if (!jsonArrayConfig.contains(k)) {
                        ToolsHelper.throwException("可修改信息不包含" + key + "的" + k + "的配置，不允许修改");
                    }
                }

            }
        }
    }

    public void changeableKeyCheck(String key,JSONObject compareJson) {

        if (ObjectUtil.isNotEmpty(compareJson)) {
            JSONObject changeableKeyConfig = getChangeableKeyConfig();
            if (ObjectUtil.isEmpty(changeableKeyConfig)) {
                ToolsHelper.throwException("可修改key配置文件为空,不允许修改");
            }

            if (!changeableKeyConfig.containsKey(key)) {
                ToolsHelper.throwException("可修改信息不包含" + key + "的配置，不允许修改");
            }
            Set<String> keySet = compareJson.keySet();
            JSONArray jsonArrayConfig= changeableKeyConfig.getJSONArray(key);
            for (String k : keySet) {
                if (!jsonArrayConfig.contains(k)) {
                    ToolsHelper.throwException("可修改信息不包含" + key + "的" + k + "的配置，不允许修改");
                }
            }
        }
    }

    /**
     * 获取可修改的key配置
     * @return
     */
    public JSONObject getChangeableKeyConfig() {
        //匹配sku
        ProductPackageConfigEntity productPackageConfigEntity = productPackageConfigService.getByKey("changeableKeyConfig");
        return JSON.parseObject(productPackageConfigEntity.getConfigValues());
    }


    public JSONObject compareAndCheckDiffConfig(JSONObject oldConfig, JSONObject newConfig, Set<String> checkDiffKeys) {
        JSONObject diffConfig = new JSONObject();
        if (newConfig == null) {
            return diffConfig;
        }
        Set<String> key2 = newConfig.keySet();
        if (oldConfig == null) {
            for (String key : key2) {
                diffConfig.put(key, "{\"old\":\"\",\"new\":" + newConfig.get(key) + "}");
//                if (!checkDiffKeys.contains(key)) {
//                    ToolsHelper.throwException("不允许修改[新增]" + key + "的值" + newConfig.get(key) +"于"+newConfig.toJSONString());
//                }
            }
        } else {
            Set<String> key1 = oldConfig.keySet();
            for (String key : key1) {
                if (key2.contains(key)) {
                    if (!oldConfig.get(key).equals(newConfig.get(key))) {
                        diffConfig.put(key, "{\"old\":" + oldConfig.get(key) + ",\"new\":" + newConfig.get(key) + "}");
                        if (!checkDiffKeys.contains(key)) {
                            ToolsHelper.throwException("不允许修改[修改]" + key + "的值" + newConfig.get(key)+ "，原值为"+oldConfig.get(key));
                        }
                    }
                } else {
                    diffConfig.put(key, "{\"old\":" + oldConfig.get(key) + ",\"new\":\"\"}");
                    if (!checkDiffKeys.contains(key)) {
                        ToolsHelper.throwException("不允许修改[减少]" + key + "的值" + oldConfig.get(key));
                    }
                }
            }
            for (String key : key2) {
                if (!key1.contains(key)) {
                    diffConfig.put(key, "{\"old\":\"\",\"new\":" + newConfig.get(key) + "}");
                    if (!checkDiffKeys.contains(key)) {
                        ToolsHelper.throwException("不允许修改[新增]" + key + "的值" + newConfig.get(key)+ "于"+oldConfig.toJSONString());
                    }
                }
            }
        }
        return diffConfig;
    }



    /*
     * 通过flow_type获取其他申办参数
     */
    public WecarProductVo getWecarProductVoByPackageSn(String packageSn){
        WecarProductVo vo = new WecarProductVo();
        ProductPackageEntity productPackage = productPackageService.getBySn(packageSn);
        if( productPackage == null){
            ToolsHelper.throwException(packageSn+"不存在");
        }

        JSONObject jsonObject = JSON.parseObject(productPackage.getPackageInfo());
        if (ObjectUtils.isNotEmpty(jsonObject)) {
            Integer cardId = jsonObject.getInteger("card_id");
            Integer issuerId = jsonObject.getInteger("issuer_id");
            String aliasName = jsonObject.getString("title");
            Integer deviceType = jsonObject.getInteger("device_type");
            if (ObjectUtils.isEmpty(issuerId) && ObjectUtils.isNotEmpty(cardId)) {
                CardsEntity cardsEntity = cardsService.getByCardId(cardId);
                if(cardsEntity != null){
                    issuerId = cardsEntity.getIssuerId();
                }
            }
            if(ObjectUtils.isNotEmpty(issuerId)){
                CardsIssuersEntity cardsIssuers =  cardsIssuersService.getByIssuerId(issuerId);
                if(cardsIssuers != null){
                    vo.setCardProviderId(cardsIssuers.getCode());
                    vo.setCardProviderName(cardsIssuers.getName());
                }
            }
            if(ObjectUtils.isEmpty(aliasName)){
                aliasName = productPackage.getPackageName();
            }
            vo.setAliasName(aliasName);

            JSONObject bindShowInfo = jsonObject.getJSONObject("bindShowInfo");
            if(ObjectUtil.isNotEmpty(bindShowInfo)){
                vo.setProductImg(bindShowInfo.getString("productImg"));
            }

            if(ObjectUtils.isNotEmpty(deviceType) && deviceType.equals(2)){
                vo.setObuType("SINGLE");
            }
        }
        vo.setId(packageSn);
        return vo;
    }

    /**
     * 获取产品包标题-有缓存
     */
    public String getPackageTitle(String packageSn) {
        String productPackageInfo = productPackageService.getPackageInfoBySnWithCache(packageSn);
        if (ObjectUtil.isEmpty(productPackageInfo)) {
            return "";
        }
        JSONObject jsonObject = JSON.parseObject(productPackageInfo);
        if (ObjectUtil.isEmpty(jsonObject)) {
            return "";
        }
        return jsonObject.getString("title");
    }


    /*
     *  更新子包的数据
     */
    public void updatePackageByParent(String parentPackageSn) {
        ProductPackageEntity productPackageBase = productPackageService.getBySn(parentPackageSn);
        if (Objects.isNull(productPackageBase)) {
            ToolsHelper.throwException("产品包不存在");
        }
        List<ProductPackageEntity> productPackageSubList = productPackageService.getListByParentPackageSn(parentPackageSn);

        for(ProductPackageEntity productPackageEntitySub : productPackageSubList){
            LambdaUpdateWrapper<ProductPackageEntity> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(ProductPackageEntity::getPackageSn, productPackageEntitySub.getPackageSn())
                    .set(ProductPackageEntity::getFrontedConfig, productPackageBase.getFrontedConfig())
                    .set(ProductPackageEntity::getPackageInfo, productPackageBase.getPackageInfo())
                    .set(ProductPackageEntity::getApplyConfig, productPackageBase.getApplyConfig())
                    .set(ProductPackageEntity::getAftersaleConfig, productPackageBase.getAftersaleConfig())
                    .set(ProductPackageEntity::getMarketConfig, productPackageBase.getMarketConfig())
                    .set(ProductPackageEntity::getPackageName, productPackageBase.getPackageName())
                    .set(ProductPackageEntity::getAddressConfigId, productPackageBase.getAddressConfigId())
                    .set(ProductPackageEntity::getBizType, productPackageBase.getBizType())
            ;
            productPackageService.updateByWrapper(wrapper);
        }


    }

    public HashMap<String, String> getPackageNameBySn(List<String> packageSnList) {
        HashMap<String, String> map = new HashMap<>();
        if (ObjectUtils.isEmpty(packageSnList)) {
            return map;
        }
        List<ProductPackageEntity> productPackageEntityList = productPackageService.getPackageNameBySns(packageSnList);
        if (CollUtil.isEmpty(productPackageEntityList)) {
            return map;
        }
        for (ProductPackageEntity productPackageEntity : productPackageEntityList) {
            map.put(productPackageEntity.getPackageSn(), productPackageEntity.getPackageName());
        }
        return map;
    }


    /*
     *  前端内容修改
     */
    public Boolean pageConfigModify(ProductPackagePageModifyDTO dto, String loginCode){
        //查找产品包tmp
        ProductPackageTmpEntity productPackageTmpEntity = productPackageTmpService.getBySn(dto.getPackageSn());

        JSONObject newConfig = JSON.parseObject(dto.getPageConfig());
        JSONObject diffConfig = new JSONObject();
        Set<String> key2 = newConfig.keySet();
        if(productPackageTmpEntity.getPageConfig() == null){
            for(String key:key2){
                diffConfig.put(key,"{\"old\":\"\",\"new\":"+newConfig.get(key)+"}");
            }
        }else{
            diffConfig = compareDiffConfig(JSON.parseObject(productPackageTmpEntity.getPageConfig()),newConfig);
            if(diffConfig.size() == 0){
                ToolsHelper.throwException("页面内容没有修改更新");
            }
        }
        //记录日志
        PackageChangeLogEntity packageChangeLogEntity = new PackageChangeLogEntity();
        packageChangeLogEntity.setPackageSn(productPackageTmpEntity.getPackageSn());
        packageChangeLogEntity.setChangeBefore(productPackageTmpEntity.getPageConfig());
        packageChangeLogEntity.setChangeAfter(dto.getPageConfig());
        packageChangeLogEntity.setRemark("变更页面内容："+diffConfig.toJSONString());
        packageChangeLogEntity.setOperater(loginCode);

        JSONObject packageInfo = JSON.parseObject(productPackageTmpEntity.getPackageInfo());
        packageInfo.getInnerMap().putAll(newConfig.getInnerMap());
        LambdaUpdateWrapper<ProductPackageTmpEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ProductPackageTmpEntity::getPackageSn, productPackageTmpEntity.getPackageSn())
                .set(ProductPackageTmpEntity::getPageConfig, dto.getPageConfig())
                .set(ProductPackageTmpEntity::getReleaseStatus, ReleaseStatusEnum.WAIT.getValue())
                .set(ProductPackageTmpEntity::getPackageInfo, packageInfo.toJSONString());
        productPackageTmpService.updateByWrapper(wrapper);

        packageChangeLogService.create(packageChangeLogEntity);

        //更新映射产品包缓存
        callPhpApplyFeign.delCacheByPackageSn(new MapDelCacheByPackageSnDTO(MapSetCacheEnum.MAP_SET_CACHE_TYPE_PREVIEW.getCode(), productPackageTmpEntity.getPackageSn()));

        return true;
    }


    /*
     *  检查apply内容是否可以发布
     */
    public Boolean checkCanProdApply(JSONObject applyConfig){
        //针对其他配置的key检查，不可重复
        ProductPackageConfigEntity productPackageConfigEntity = productPackageConfigService.getByKey("adminProdConfig");
        JSONObject jsonObject = JSON.parseObject(productPackageConfigEntity.getConfigValues());

        for(String key:jsonObject.keySet()){
            if(applyConfig.getInteger(key) != null){
                log.info(jsonObject.getJSONArray(key).toString());
               //对比值是否存在
                if(jsonObject.getJSONArray(key).contains(applyConfig.getInteger(key))){
                        ToolsHelper.throwException("apply配置中"+key+"的值:"+applyConfig.getInteger(key)+",不允许发布使用");
                }
               //存在则报错
            }
        }
        return true;
    }

    /**
     * 根据packageSn获取产品包信息
     * @param dto
     * @return
     */
    public List<ProductPackageGetInfoByPackageSnsVo> getInfoByPackageSns(GetInfoByPackageSnsDTO dto){
        List<ProductPackageEntity> productPackageEntityList =
                productPackageService.getPackageBySns(dto.getPackageSns());
        if(CollectionUtil.isEmpty(productPackageEntityList)){
            return new LinkedList<>();
        }
        // 把数据按GetInfoByPackageSnsDTO.getPackageSns 原顺序排序返回
        return productPackageEntityList.stream().
                sorted(Comparator.comparingInt(productPackageEntity -> dto.getPackageSns().indexOf(productPackageEntity.getPackageSn())))
                .map(productPackageEntity -> {
            ProductPackageGetInfoByPackageSnsVo productPackageGetInfoByPackageSnsVo = new ProductPackageGetInfoByPackageSnsVo();
            BeanUtils.copyProperties(productPackageEntity, productPackageGetInfoByPackageSnsVo);
            productPackageGetInfoByPackageSnsVo.setPackageInfo(productPackageEntity.getPackageInfoObj());
            if(ObjectUtil.isNotEmpty(productPackageEntity.getPageConfig())){
                productPackageGetInfoByPackageSnsVo.setPageConfig( JSON.parseObject(productPackageEntity.getPageConfig()));
            }
            return productPackageGetInfoByPackageSnsVo;
        }).collect(Collectors.toList());
    }

    /**
     * 通过属性获取sku
     * @return skuSn
     */
    public String getSkuByAttr(GetSkuByAttrBO bo) {
        String skuSn = null;
        try {
            // 调用goods服务接口
            JSONObject result = callGoodsApplication.getSkuByAttr(bo).getDataWithCheckError();
            if(result != null){
                skuSn = result.getString("skuSn");
            }
        } catch (BizException e) {
            log.error("调用goods服务通过属性获取sku失败",e.getMessage());
        }
        return skuSn;
    }

    public void checkStallSkuAttr(CheckStallSkuAttrDTO dto) {
        JsonResult<CheckStallSkuAttrVO> result = callGoodsApplication.checkStallSkuAttr(dto);
        result.checkError();
        if (ObjectUtil.isEmpty(result.getData())) {
            ToolsHelper.throwException("调用goods服务校验sku接口失败:无成功返回");
        }
        if (!result.getData().getResult()) {
            ToolsHelper.throwException(result.getData().getMessage());
        }
    }


    /*
     *  通用检查
     */
    public Boolean checkIssuerIdAndCardId(Integer issuerId,Integer cardId,Integer isTruck){
        CardsEntity cardsEntity = cardsService.getByCardId(cardId);
        if(cardsEntity == null){
            ToolsHelper.throwException("cardId不存在");
        }
        if(!Objects.equals(cardsEntity.getIssuerId(), issuerId)){
            ToolsHelper.throwException("issuerId与cardId不匹配");
        }
        if(!Objects.equals(cardsEntity.getIsTruck(), isTruck)){
            ToolsHelper.throwException("isTruck与cardId不匹配");
        }
        return true;
    }

    public JSONObject getPackageInfoPopup(JSONObject packageInfo) {
        if (ObjectUtil.isEmpty(packageInfo.get("packagePopup"))) {
            return packageInfo;
        }

        // 检查 packagePopup 是否存在且非空
        Object popupObj = packageInfo.get("packagePopup");
        if (ObjectUtil.isEmpty(popupObj) || !(popupObj instanceof JSONObject)) {
            return packageInfo;
        }

        JSONObject popupJson = (JSONObject) popupObj;

        try {
            // 直接解析 JSONObject 到 HashMap，无需转为字符串
            HashMap<String, HashMap<String, String>> packagePopup = JSON.parseObject(
                    popupJson.toJSONString(), // 实际上也可以直接传 popupJson
                    new TypeReference<HashMap<String, HashMap<String, String>>>() {
                    }
            );
            // 获取popup 对应的值信息
            List<ProductPackagePopupEntity> popupList = popupService.getAll();
            // packagePopup 的值进行遍历处理
            for (Map.Entry<String, HashMap<String, String>> entry : packagePopup.entrySet()) {
                // entry 的popupSn == popupList 中某个 popupSn
                for (ProductPackagePopupEntity popup : popupList) {
                    if (popup.getPopupSn().equals(entry.getValue().get("popupSn"))) {
                        packageInfo.put(entry.getKey(), popup.getUrl());
                    }
                }
            }

        } catch (Exception e) {
            // 记录日志或处理异常
            log.error("解析 packagePopup 失败：" + e.getMessage());
        }
        return packageInfo;
    }
    /*
     *  检查sku和属性是否匹配
     */
    public Boolean checkSkuByAttr(String skuSnCheck,Integer issuerId,Integer manufacturer,Integer deviceType,Integer deviceColor,Integer deviceVersion){
        //查找sku
        String skuSn = getSkuByAttr(
                new GetSkuByAttrBO(
                        ChannelTypeEnum.getStallCodeByValue(1),
                        issuerId,
                        manufacturer,
                        deviceType,
                        deviceColor,
                        deviceVersion
                )
        );
        if(StringUtils.isEmpty(skuSn)){
            ToolsHelper.throwException("商品sku查找为空");
        }
        if(!Objects.equals(skuSn, skuSnCheck)){
            ToolsHelper.throwException("skuSn不匹配,查找的sku为："+skuSn);
        }
        return true;
    }

}
