package com.ets.apply.application.app.job;

import com.alibaba.fastjson.JSON;
import com.ets.apply.application.app.business.unicom.UnicomApplyBusiness;
import com.ets.apply.application.common.dto.unicom.UnicomSyncOrderStateDTO;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class UnicomApplyJob {

    @Autowired
    private UnicomApplyBusiness unicomApplyBusiness;

    /*
     *  轮询
     */
    @XxlJob("syncUnicomOrderHandler")
    public ReturnT<String> syncUnicomOrderHandler(String params){

        unicomApplyBusiness.syncUnicomOrderOfUnFinished();

        return ReturnT.SUCCESS;
    }

    /*
     *  刷新号码
     */
    @XxlJob("autoRefreshNumbers")
    public ReturnT<String> autoRefreshNumbers(String params){

        int pageCount = 5;
        if (StringUtils.isNotEmpty(params)) {
            pageCount = Integer.parseInt(params);
            pageCount = Math.min(pageCount, 20);
            pageCount = Math.max(pageCount, 1);
        }

        unicomApplyBusiness.autoRefreshNumbers(10, pageCount);

        return ReturnT.SUCCESS;
    }

    @XxlJob("syncOrderState")
    public ReturnT<String> syncOrderState(String params) {

        params = params.trim().replace("\r\n", "").replace("\n", "");

        if (StringUtils.isEmpty(params)) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "参数不能为空");
        }

        UnicomSyncOrderStateDTO dto = JSON.parseObject(params, UnicomSyncOrderStateDTO.class);

        if (dto.getSyncUnicom()) {
            unicomApplyBusiness.doSyncOrder(dto.getUnicomApplySn());
        } else {
            unicomApplyBusiness.syncOrderByData(dto.getUnicomApplySn(), dto.getRow());
        }

        return ReturnT.SUCCESS;
    }
}
