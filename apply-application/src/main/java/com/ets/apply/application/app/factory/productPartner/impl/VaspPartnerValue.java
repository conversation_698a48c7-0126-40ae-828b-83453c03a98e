package com.ets.apply.application.app.factory.productPartner.impl;

import com.ets.apply.application.app.service.thirdPartner.VaspService;
import com.ets.apply.application.common.bo.productOrder.ProductOrderRefundBO;
import com.ets.apply.application.common.bo.productOrder.ProductOrderShipBO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class VaspPartnerValue extends ProductPartnerBase {

    @Autowired
    private VaspService vaspService;

    /**
     * 调用vasp 通知取消退款
     */
    public String refund(ProductOrderRefundBO productOrderRefundBO) {
        return vaspService.refund(productOrderRefundBO);
    }


    /**
     * 通知vasp 发货
     *
     * @param productOrderShipBO
     */
    public void ship(ProductOrderShipBO productOrderShipBO) {
        vaspService.ship(productOrderShipBO);
    }


}
