package com.ets.apply.application.common.consts.xconnect;

import com.ets.common.ToolsHelper;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum XconnectStatusEnum {

    WAIT_PAY(1,10, "待支付"),
    IS_PAID(2, 20, "待发货"),
    SHIPPED(3, 30, "待收货"),
    ACTIVED(4, 70, "已完成"),
    REFUNDING(5, 90, "已取消"),
    REFUNDED(6, 90, "已退款"),
    REFUNDED_NEW(6, 62, "已退款"),//退款完成
    ;

    private final Integer status;
    private final Integer productOrderStatus;
    private final String description;

    public static Integer getByProductOrderStatus(Integer status,String refundSn) {
        for (XconnectStatusEnum node : XconnectStatusEnum.values()) {
            if (Objects.equals(node.getProductOrderStatus(), status)) {
                if(status.equals(90) && StringUtils.isNotEmpty(refundSn)){
                    return XconnectStatusEnum.REFUNDED.getStatus();
                }else{
                    return node.getStatus();
                }
            }
        }
        ToolsHelper.throwException("未找到对应的Xconnect订单状态");
        return null;
    }
}
