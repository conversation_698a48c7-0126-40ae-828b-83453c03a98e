package com.ets.apply.application.controller.frontend;


import com.ets.apply.application.app.business.orderAftersales.OrderAftersalesBusiness;
import com.ets.apply.application.common.dto.request.orderAftersales.OrderAftersalesApplyDTO;
import com.ets.apply.application.common.dto.request.orderAftersales.OrderAftersalesApplyInitDTO;
import com.ets.apply.application.common.dto.request.orderAftersales.OrderAftersalesApplyWithCodeDTO;
import com.ets.apply.application.common.dto.request.orderAftersales.OrderAftersalesGetInfoDTO;
import com.ets.apply.application.common.vo.orderAftersales.OrderAftersalesGetInfoVO;
import com.ets.apply.application.common.vo.response.issuer.IssuerAftersalesApplyInitVO;
import com.ets.apply.application.controller.BaseController;
import com.ets.apply.application.infra.entity.OrderAftersalesApplyEntity;
import com.ets.common.JsonResult;
import com.ets.common.util.UserUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import jakarta.validation.Valid;

/*
 * 申办订单售后申请相关
 */
@RestController
@RequestMapping("/frontend/orderAftersales")
public class OrderAftersalesController extends BaseController {

    @Autowired
    OrderAftersalesBusiness orderAftersalesBusiness;

    /*
     * 售后申请-初始化（预处理）
     *
     */
    @RequestMapping("/applyInit")
    public JsonResult<IssuerAftersalesApplyInitVO> applyInit(@RequestBody(required = false) @Valid OrderAftersalesApplyInitDTO dto) {
        return JsonResult.ok(orderAftersalesBusiness.applyInit(dto));
    }

    /*
     * 申请售后
     */
    @RequestMapping("/apply")
    public JsonResult<OrderAftersalesApplyEntity> apply(@RequestBody(required = false) @Valid OrderAftersalesApplyDTO dto) {
        return JsonResult.ok(orderAftersalesBusiness.apply(dto,UserUtil.getUid()));
    }

    @RequestMapping("/getInfo")
    public JsonResult<OrderAftersalesGetInfoVO> getInfo(@RequestBody(required = false) @Valid OrderAftersalesGetInfoDTO dto) {
        return JsonResult.ok(orderAftersalesBusiness.getInfo(dto));
    }

    /*
     * 需要验证码 触发发卡方售后申请
     */
    @RequestMapping("/applyWithCode")
    public JsonResult<OrderAftersalesApplyEntity> applyWithCode(@RequestBody(required = false) @Valid OrderAftersalesApplyWithCodeDTO dto) {
        return JsonResult.ok(orderAftersalesBusiness.applyWithCode(dto,UserUtil.getUid()));
    }
}
