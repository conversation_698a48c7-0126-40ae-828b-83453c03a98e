package com.ets.apply.application.common.dto.request.creditCard;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class CreditCardAuditNotifyDTO {

    /**
     * uid
     */
    private Long uid;

    /**
     * 银行
     */
    @JsonProperty(value = "which_bank")
    private Integer whichBank;

    @JsonProperty(value = "apply_number")
    private String applyNumber;

    @JsonProperty(value = "audit_time")
    private String auditTime;

    private Integer classify;

    private Integer status;

}
