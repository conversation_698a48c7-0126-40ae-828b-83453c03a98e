package com.ets.apply.application.common.consts.productOrder;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ChannelTypeEnum {

    CHANNEL_TYPE_MALL(1, "电商渠道", "APPLY_SEARCH_SKU_MALL"),
    CHANNEL_TYPE_WECHAT(2, "微信渠道","APPLY_SEARCH_SKU");
    private final Integer value;
    private final String description;
    private final String stallCode;

    public static String getStallCodeByValue(Integer value) {
        for (ChannelTypeEnum node : ChannelTypeEnum.values()) {
            if (node.getValue().equals(value)) {
                return node.getStallCode();
            }
        }
        return "";
    }
}
