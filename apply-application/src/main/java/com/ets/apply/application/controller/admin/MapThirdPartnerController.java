package com.ets.apply.application.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.apply.application.app.business.map.MapThirdPartnerBusiness;
import com.ets.apply.application.common.dto.map.*;
import com.ets.apply.application.common.dto.request.MapCompareDiffDTO;
import com.ets.apply.application.common.vo.map.MapModulePackageListVO;
import com.ets.apply.application.common.vo.map.MapThirdPartnerListVO;
import com.ets.apply.application.common.vo.map.MapThirdPartnerPackageListVO;
import com.ets.apply.application.controller.BaseController;
import com.ets.apply.application.infra.entity.map.MapThirdPartnerPackageEntity;
import com.ets.common.JsonResult;
import com.ets.common.RequestHelper;
import jakarta.validation.Valid;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/admin/mapThirdPartner")
public class MapThirdPartnerController extends BaseController {
    @Autowired
    private MapThirdPartnerBusiness mapThirdPartnerBusiness;

    /**
     * 模块列表查询
     * @return
     */
    @RequestMapping("/getList")
    @ResponseBody
    public JsonResult<IPage<MapThirdPartnerListVO>> getList() {
        return JsonResult.ok(mapThirdPartnerBusiness.getList());
    }
    /**
     * 获取产品包列表
     * @return
     */
    @RequestMapping("/getPackageList")
    @ResponseBody
    public JsonResult<IPage<MapThirdPartnerPackageListVO>> getPackageList(@RequestBody(required = false) @Valid MapThirdPartnerPackageDTO dto) {
        return JsonResult.ok(mapThirdPartnerBusiness.getPackageList(dto));
    }
    /**
     *  添加产品包
     * @return
     */
    @RequestMapping("/addPackage")
    @ResponseBody
    public JsonResult<Boolean> addPackage(@RequestBody(required = false) @Valid MapThirdPartnerAddPackageDTO dto) {
        mapThirdPartnerBusiness.addPackage(dto, RequestHelper.getHttpServletRequest().getHeader("loginCode"));
        return JsonResult.ok(true);
    }
    /**
     *  修改产品包
     * @return
     */
    @RequestMapping("/modifyPackage")
    @ResponseBody
    public JsonResult<Boolean> modifyPackage(@RequestBody(required = false) @Valid MapThirdPartnerAddPackageDTO dto) {
        mapThirdPartnerBusiness.updatePackage(dto, RequestHelper.getHttpServletRequest().getHeader("loginCode"));
        return JsonResult.ok(true);
    }

    /**
     *  修改产品包
     * @return
     */
    @RequestMapping("/delPackage")
    @ResponseBody
    public JsonResult<Boolean> delPackage(@RequestBody(required = false) @Valid MapThirdPartnerAddPackageDTO dto) {
        mapThirdPartnerBusiness.delPackage(dto, RequestHelper.getHttpServletRequest().getHeader("loginCode"));
        return JsonResult.ok(true);
    }

    /**
     *
     * @return
     */
    @RequestMapping("/prodMap")
    @ResponseBody
    public JsonResult<Boolean> prodMap(@RequestBody(required = false) @Valid MapThirdPartnerProdDTO dto) {
        mapThirdPartnerBusiness.updateProdCache(dto,RequestHelper.getHttpServletRequest().getHeader("loginCode"));
        return JsonResult.ok();
    }
}
