package com.ets.apply.application.app.business;

import com.ets.apply.application.app.service.bigdata.BigDataService;
import com.ets.apply.application.app.thirdservice.feign.UserApplicationFeign;
import com.ets.apply.application.app.thirdservice.request.order.OrderFindDTO;
import com.ets.apply.application.app.thirdservice.request.user.UpdateDepositRecordStatusDTO;
import com.ets.apply.application.app.thirdservice.request.user.UpdateUserDepositDTO;
import com.ets.apply.application.app.thirdservice.response.bigData.BigDataOrderSettlementVO;
import com.ets.apply.application.app.thirdservice.response.order.GetRefundInfoByOrderSnVo;
import com.ets.apply.application.app.thirdservice.response.user.UserDepositVO;
import com.ets.apply.application.app.thirdservice.response.user.UsersDepositRecordVO;
import com.ets.apply.application.common.config.TruckConfig;
import com.ets.apply.application.common.consts.deposit.DepositRecordStatusEnum;
import com.ets.apply.application.common.consts.deposit.DepositStatusEnum;
import com.ets.common.JsonResult;
import com.ets.common.ToolsHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class TruckBusiness {

    private static final int REFUND_STATUS_SUCCESS = 1;

    @Autowired
    private BigDataService bigDataService;

    @Autowired
    private TruckConfig truckConfig;

    @Autowired
    private UserApplicationFeign userApplicationFeign;

    @Autowired
    private OrderBusiness orderBusiness;

    public boolean checkTruckOrderMatch(String orderSn) {

        // 测试订单不查大数据
        if (ObjectUtils.isNotEmpty(truckConfig.getTestTruckRefundOrderSns())
                && truckConfig.getTestTruckRefundOrderSns().contains(orderSn)) {
            log.info("测试订单不查大数据：{}", orderSn);
            return true;
        }

        // 查询大数据是否符合条件
        BigDataOrderSettlementVO orderSettlement = bigDataService.orderSettlement(orderSn);
        if (ObjectUtils.isEmpty(orderSettlement)) {
            ToolsHelper.throwException("获取大数据退还状态结果为空");
        }

        // 符合条件
        return orderSettlement.getReturnFlag().equals("0") && orderSettlement.getStandardFlag().equals("1");
    }

    /**
     * 处理单个保证金记录
     */
    public boolean processDepositRecord(UsersDepositRecordVO record) {
        // 只处理有保证金账户编号的记录
        if (StringUtils.isEmpty(record.getUserDepositSn())) {
            log.warn("保证金记录缺少账户编号，ID：{}", record.getId());
            return false;
        }

        // 一次性获取保证金账户信息
        UserDepositVO userDeposit = getUserDepositInfo(record);
        if (userDeposit == null) {
            return false;
        }

        // 查询退款状态
        if (!isRefundSuccessful(userDeposit)) {
            return false;
        }

        // 更新保证金记录状态
        if (!updateDepositRecordStatus(record)) {
            return false;
        }

        // 更新保证金账户状态
        return updateDepositAccountStatus(userDeposit);
    }

    /**
     * 获取保证金账户信息
     */
    private UserDepositVO getUserDepositInfo(UsersDepositRecordVO record) {
        try {
            JsonResult<UserDepositVO> depositResult = userApplicationFeign.getUserDepositByUserDepositSn(record.getUserDepositSn());
            if (!depositResult.isSuccess() || depositResult.getData() == null) {
                log.error("查询货车保证金账户失败，账户编号：{}，错误信息：{}", record.getUserDepositSn(), depositResult.getMsg());
                return null;
            }
            return depositResult.getData();
        } catch (Exception e) {
            log.error("查询货车保证金账户异常，账户编号：{}，异常：{}", record.getUserDepositSn(), e.getMessage());
            return null;
        }
    }

    /**
     * 检查退款是否成功
     * 使用OrderBusiness的getRefundInfoByOrderSn方法来判断退款状态
     */
    private boolean isRefundSuccessful(UserDepositVO userDeposit) {
        try {
            if (StringUtils.isEmpty(userDeposit.getOrderSn())) {
                log.warn("保证金账户缺少订单号，账户编号：{}", userDeposit.getUserDepositSn());
                return false;
            }

            // 使用OrderBusiness查询退款信息
            OrderFindDTO orderFindDTO = new OrderFindDTO();
            orderFindDTO.setOrderSn(userDeposit.getOrderSn());

            GetRefundInfoByOrderSnVo refundInfo = orderBusiness.getRefundInfoByOrderSn(orderFindDTO);
            if (refundInfo == null) {
                log.warn("未查询到退款信息，订单号：{}", userDeposit.getOrderSn());
                return false;
            }

            // 检查退款状态：1表示退款成功
            boolean isRefundSuccess = refundInfo.getStatus() != null && refundInfo.getStatus() == REFUND_STATUS_SUCCESS;

            if (isRefundSuccess) {
                log.info("订单退款成功，订单号：{}，退款单号：{}", userDeposit.getOrderSn(), refundInfo.getRefundSn());
            } else {
                log.debug("订单退款未成功，订单号：{}，退款状态：{}", userDeposit.getOrderSn(), refundInfo.getStatus());
            }

            return isRefundSuccess;
        } catch (Exception e) {
            log.error("查询退款状态异常，账户编号：{}，异常：{}", userDeposit.getUserDepositSn(), e.getMessage());
            return false;
        }
    }

    /**
     * 更新保证金记录状态
     */
    private boolean updateDepositRecordStatus(UsersDepositRecordVO record) {
        try {
            UpdateDepositRecordStatusDTO updateDto = new UpdateDepositRecordStatusDTO();
            updateDto.setId(record.getId());
            updateDto.setStatus(DepositRecordStatusEnum.SUCCESS.getValue());

            JsonResult<Void> updateResult = userApplicationFeign.updateDepositRecordStatus(updateDto);
            if (!updateResult.isSuccess()) {
                log.error("更新货车保证金记录状态失败，ID：{}，错误信息：{}", record.getId(), updateResult.getMsg());
                return false;
            }
            return true;
        } catch (Exception e) {
            log.error("更新货车保证金记录状态异常，ID：{}，异常：{}", record.getId(), e.getMessage());
            return false;
        }
    }

    /**
     * 更新保证金账户状态
     */
    private boolean updateDepositAccountStatus(UserDepositVO userDeposit) {
        try {
            // 更新账户状态为退款状态
            UpdateUserDepositDTO updateUserDepositDTO = new UpdateUserDepositDTO();
            updateUserDepositDTO.setUserDepositSn(userDeposit.getUserDepositSn());
            updateUserDepositDTO.setStatus(DepositStatusEnum.REFUNDED.getValue());
            updateUserDepositDTO.setExpendAmount(userDeposit.getDepositAmount());

            JsonResult<Void> updateDepositResult = userApplicationFeign.updateUserDeposit(updateUserDepositDTO);
            if (!updateDepositResult.isSuccess()) {
                log.error("更新货车保证金账户失败，账户ID：{}，错误信息：{}", userDeposit.getId(), updateDepositResult.getMsg());
                return false;
            }

            log.info("成功更新货车保证金账户状态，账户ID：{}，账户编号：{}", userDeposit.getId(), userDeposit.getUserDepositSn());
            return true;
        } catch (Exception e) {
            log.error("更新货车保证金账户异常，账户编号：{}，异常：{}", userDeposit.getUserDepositSn(), e.getMessage());
            return false;
        }
    }
}
