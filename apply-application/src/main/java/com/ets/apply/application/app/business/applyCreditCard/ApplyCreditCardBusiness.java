package com.ets.apply.application.app.business.applyCreditCard;

import cn.hutool.core.util.ArrayUtil;
import com.ets.apply.application.app.business.creditCard.CreditCardBusiness;
import com.ets.apply.application.common.config.creditBank.CommonCreditBankConfig;
import com.ets.apply.application.common.consts.activityCreditCardUsersInfo.*;
import com.ets.apply.application.common.consts.service.ServerErrorCodeEnum;
import com.ets.apply.application.common.dto.request.creditCard.ApplyOrderDTO;
import com.ets.apply.application.common.vo.creditCard.CreditCardApplyOrderVO;
import com.ets.apply.application.infra.entity.ActivityCreditCardUsersInfoEntity;
import com.ets.apply.application.infra.service.ActivityCreditCardUsersInfoService;
import com.ets.common.BizException;
import com.ets.common.ToolsHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;

@Component
@Slf4j
public class ApplyCreditCardBusiness  {
    @Autowired
    private ActivityCreditCardUsersInfoService activityCreditCardUsersInfoService;
    @Autowired
    private CreditCardBusiness creditCardBusiness;
    /**
     * 信用卡申请
     *
     * @param applyOrderDTO
     * @return
     */
    public CreditCardApplyOrderVO applyOrder(ApplyOrderDTO applyOrderDTO) {
        CreditCardApplyOrderVO creditCardApplyOrderVO = null;
        try {
            //查找是否存在正常申请当中的记录
            ActivityCreditCardUsersInfoEntity processingApplyEntity = activityCreditCardUsersInfoService.getProcessingApply(
                applyOrderDTO.getUid(),
                applyOrderDTO.getWhichBank(),
                applyOrderDTO.getClassify(),
                applyOrderDTO.getReferType(),
                applyOrderDTO.getReferSn(),
                applyOrderDTO.getVersion()
            );
            //存在进行中的申请
            if(processingApplyEntity != null){
                //非待定状态，报错提示
                if(!processingApplyEntity.getStatus().equals(ActivityCreditCardUserInfoStatusEnum.STATUS_WAIT.getCode())){
                    ToolsHelper.throwException("已申请过信用卡，请刷新页面查看办理进度", ServerErrorCodeEnum.SERVICE_ERROR_CODE_CREDIT_CARD_APPLIED.getCode());
                }
                //待定状态
                applyOrderDTO.setBankApplyNumber(processingApplyEntity.getOrderSn());
            }
            //申办类型的，检查是否可绑定旧申请单号
            if(applyOrderDTO.getReferType().equals(ActivityCreditCardReferTypeConstant.TYPE_NEW_APPLY)){
                //存在其他进行中的申请
                if(activityCreditCardUsersInfoService.checkProcessingApply(
                    applyOrderDTO.getUid(),
                    applyOrderDTO.getWhichBank(),
                    applyOrderDTO.getClassify(),
                    applyOrderDTO.getReferSn()
                )){
                    ToolsHelper.throwException("你已有1个进行中的" + ActivityCreditCardUserInfoWhichBankEnum.getDescByCode(applyOrderDTO.getWhichBank()) + "银行补贴套餐订单，请先完成该订单",ServerErrorCodeEnum.SERVICE_ERROR_CODE_CREDIT_CARD_APPLIED.getCode());
                }
                //查找是否存在正常申请当中的记录
                ActivityCreditCardUsersInfoEntity processingApplyOldEntity = activityCreditCardUsersInfoService.getProcessingApply(
                    applyOrderDTO.getUid(),
                    applyOrderDTO.getWhichBank(),
                    applyOrderDTO.getClassify(),
                    applyOrderDTO.getReferType(),
                    "",
                    applyOrderDTO.getVersion()
                );
                //继承银行申请记录
                if(processingApplyOldEntity != null){
                    processingApplyOldEntity.setReferSn(applyOrderDTO.getReferSn());
                    processingApplyOldEntity.setPlateNo(applyOrderDTO.getPlateNo());
                    activityCreditCardUsersInfoService.updateById(processingApplyOldEntity);
                    //非待定状态，报错提示
                    if(!processingApplyOldEntity.getStatus().equals(ActivityCreditCardUserInfoStatusEnum.STATUS_WAIT.getCode())){
                        ToolsHelper.throwException("已申请过信用卡，请刷新页面查看办理进度", ServerErrorCodeEnum.SERVICE_ERROR_CODE_CREDIT_CARD_APPLIED.getCode());
                    }
                    //待定状态
                    applyOrderDTO.setBankApplyNumber(processingApplyOldEntity.getOrderSn());
                }
            }

            creditCardApplyOrderVO = creditCardBusiness.applyOrder(applyOrderDTO);
        } catch (BizException e){
            if (
                Arrays.asList(
                    ServerErrorCodeEnum.SERVICE_ERROR_CODE_CREDIT_CARD_APPLIED.getCode(),
                    ServerErrorCodeEnum.SERVICE_ERROR_CODE_CREDIT_CARD_SUBMITTED.getCode()
                ).contains(e.getErrorCode())
            ) {
                ToolsHelper.throwException(e.getMessage(), e.getErrorCode());
            } else if ( e.getErrorCode().equals(ServerErrorCodeEnum.SERVICE_ERROR_CODE_CREDIT_CARD_NOT_ALLOW.getCode())) {
                // 需要原样输出提示语的情况
                ToolsHelper.throwException(e.getMessage());
            } else {
                log.error(applyOrderDTO.toString()+",msg:"+e.getMessage());
                ToolsHelper.throwException("申请信用卡失败，稍后再试");
            }
        }
        return creditCardApplyOrderVO;
    }
}
