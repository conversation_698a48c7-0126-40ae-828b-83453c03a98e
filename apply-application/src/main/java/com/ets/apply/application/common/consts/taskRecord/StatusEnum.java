package com.ets.apply.application.common.consts.taskRecord;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;

@Getter
@AllArgsConstructor
public enum StatusEnum {

    TASK_STATUS_WAIT(0, "待处理"),
    TASK_STATUS_PROCESS(1, "处理中"),
    TASK_STATUS_FINISH(2, "处理完成"),
    TASK_STATUS_FAIL(3, "处理失败"),
    TASK_STATUS_STOP(4, "暂停处理");

    private final int code;
    private final String description;

    public static String getDescByCode(int code) {
        for (StatusEnum node : StatusEnum.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }

    public static HashMap<Integer,String> getMap() {
        //创建map对象
        HashMap<Integer,String> statusMap = new HashMap<>();
        for (StatusEnum value : StatusEnum.values()) {
            statusMap.put(value.getCode(),value.getDescription());
        }
        return statusMap;
    }
}
