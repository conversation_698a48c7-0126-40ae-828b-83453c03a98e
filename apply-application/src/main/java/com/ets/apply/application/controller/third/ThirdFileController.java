package com.ets.apply.application.controller.third;

import com.ets.apply.application.app.business.file.FileBusiness;
import com.ets.apply.application.common.dto.file.Base64ImageTransferDTO;
import com.ets.apply.application.common.dto.file.ImageDownloadAndTransferDTO;
import com.ets.apply.application.common.vo.file.ImageDownloadAndTransferVO;
import com.ets.apply.application.common.vo.file.ImageTransferVO;
import com.ets.common.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

/**
 * <AUTHOR>
 */
@RequestMapping("/third/file")
@RefreshScope
@RestController
@Slf4j
public class ThirdFileController {

    @Autowired
    private FileBusiness fileBusiness;
    /**
     * 文件下载并上传cos
     */
    @PostMapping("/image-download-and-transfer")

    public JsonResult<ImageDownloadAndTransferVO> imageDownloadAndTransfer(@RequestBody @Valid ImageDownloadAndTransferDTO dto){

        return JsonResult.ok(fileBusiness.imageDownloadAndTransfer(dto));

    }

    /**
     * base 64 图片处理上传cos
     */
    @PostMapping("/base64-image-transfer")
    public JsonResult<ImageTransferVO> base64ImageTransfer(@RequestBody @Valid Base64ImageTransferDTO dto){
        return JsonResult.ok(fileBusiness.base64ImageTransfer(dto));
    }
}
