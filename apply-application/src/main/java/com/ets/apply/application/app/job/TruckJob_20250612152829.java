package com.ets.apply.application.app.job;

import com.ets.apply.application.app.business.TruckBusiness;
import com.ets.apply.application.app.factory.benefit.BenefitFactory;
import com.ets.apply.application.common.consts.orderBenefitRecord.OrderBenefitRecordStatusEnum;
import com.ets.apply.application.common.consts.segmentBenefit.BenefitTypeEnum;
import com.ets.apply.application.infra.entity.OrderBenefitRecord;
import com.ets.apply.application.infra.entity.OrderOrderEntity;
import com.ets.apply.application.infra.service.OrderBenefitRecordService;
import com.ets.apply.application.infra.service.OrderLogService;
import com.ets.apply.application.infra.service.OrderOrderService;
import com.ets.common.ToolsHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Component
public class TruckJob {

    @Autowired
    private OrderBenefitRecordService orderBenefitRecordService;

    @Autowired
    private OrderOrderService orderOrderService;

    @Autowired
    private OrderLogService orderLogService;

    @Autowired
    private TruckBusiness truckBusiness;

    @XxlJob("truckPayOrderRefundHandler")
    public ReturnT<String> truckPayOrderRefundHandler(String params) {
        log.info("开始执行货车付费订单退款任务");
        int limit = Integer.parseInt(params);

        try {
            // 查询待处理的记录
            List<OrderBenefitRecord> records = orderBenefitRecordService.getNeedSendRecordList(
                    BenefitTypeEnum.TRUCK_REFUND.getType(),
                    limit
            );

            if (ObjectUtils.isEmpty(records)) {
                log.info("没有待处理的货车付费订单退款任务记录");
                return ReturnT.SUCCESS;
            }

            for (OrderBenefitRecord record : records) {
                try {
                    // 查询订单
                    OrderOrderEntity order = orderOrderService.getByOrderSn(record.getOrderSn());
                    if (ObjectUtils.isEmpty(order)) {
                        ToolsHelper.throwException("订单不存在");
                    }

                    if (truckBusiness.checkTruckOrderMatch(record.getOrderSn())) {
                        // 发放权益
                        BenefitFactory.create(record.getBenefitType()).send(record.getId());

                        // 更新状态为发放成功
                        record.setStatus(OrderBenefitRecordStatusEnum.SUCCESS.getValue());
                        record.setErrorMsg("");
                        record.setUpdatedAt(LocalDateTime.now());
                        orderBenefitRecordService.updateById(record);

                        // 订单记录日志
                        orderLogService.addLog(order, "货车用户满足活动条件退款");
                    }
                } catch (Exception e) {
                    // 更新状态为发放失败
                    record.setStatus(OrderBenefitRecordStatusEnum.FAIL.getValue());
                    record.setErrorMsg(e.getMessage());
                    record.setUpdatedAt(LocalDateTime.now());
                    orderBenefitRecordService.updateById(record);

                    log.error("货车付费订单退款失败，记录ID：{}，错误信息：{}", record.getId(), e.getMessage(), e);
                }
            }
        } catch (Exception e) {
            log.error("货车付费订单任务执行异常", e);
            throw e;
        }

        return ReturnT.SUCCESS;
    }


    @XxlJob("truckTermCouponSendHandler")
    public ReturnT<String> truckTermCouponSend(String params) {
        log.info("开始执行货车券包发放任务");
        int limit = Integer.parseInt(params);

        try {
            // 查询待处理的记录
            List<OrderBenefitRecord> records = orderBenefitRecordService.getNeedSendRecordList(
                    BenefitTypeEnum.TRUCK_TERM_COUPON.getType(),
                    limit
            );

            if (ObjectUtils.isEmpty(records)) {
                log.info("没有待处理的货车券包记录");
                return ReturnT.SUCCESS;
            }

            for (OrderBenefitRecord record : records) {
                try {
                    // 查询订单
                    OrderOrderEntity order = orderOrderService.getByOrderSn(record.getOrderSn());
                    if (ObjectUtils.isEmpty(order)) {
                        ToolsHelper.throwException("订单不存在");
                    }

                    if (truckBusiness.checkTruckOrderMatch(record.getOrderSn())) {
                        // 发放权益
                        BenefitFactory.create(record.getBenefitType()).send(record.getId());

                        // 更新状态为发放成功
                        record.setStatus(OrderBenefitRecordStatusEnum.SUCCESS.getValue());
                        record.setErrorMsg("");
                        record.setUpdatedAt(LocalDateTime.now());
                        orderBenefitRecordService.updateById(record);

                        // 订单记录日志
                        orderLogService.addLog(order, "货车用户账期达标，发放权益券包至车牌");
                    }
                } catch (Exception e) {
                    // 更新状态为发放失败
                    record.setStatus(OrderBenefitRecordStatusEnum.FAIL.getValue());
                    record.setErrorMsg(e.getMessage());
                    record.setUpdatedAt(LocalDateTime.now());
                    orderBenefitRecordService.updateById(record);

                    log.error("货车券包发放失败，记录ID：{}，错误信息：{}", record.getId(), e.getMessage(), e);
                }
            }
        } catch (Exception e) {
            log.error("货车券包发放任务执行异常", e);
            throw e;
        }
        return ReturnT.SUCCESS;
    }
}
