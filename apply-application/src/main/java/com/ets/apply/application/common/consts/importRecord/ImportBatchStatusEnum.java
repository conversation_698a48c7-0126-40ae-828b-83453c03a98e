package com.ets.apply.application.common.consts.importRecord;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ImportBatchStatusEnum {

    DEFAULT(0, "待处理"),

    DOING(1, "处理中"),

    ALL_SUCCESS(2, "全部处理成功"),

    PART_FAILED(3, "部分失败");

    private final int code;
    private final String description;

    public static String getDescByCode(int code) {
        for (ImportBatchStatusEnum node : ImportBatchStatusEnum.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }
}
