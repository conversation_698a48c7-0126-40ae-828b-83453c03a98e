package com.ets.apply.feign.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class ProductOrderResponse {

    /**
     * 商品订单号--唯一
     */
    private String productOrderSn;

    /**
     * 第三方订单号--可能重复
     */
    private String thirdOrderSn;

    /**
     * 支付时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime paidTime;

    /**
     * 支付金额
     */
    private BigDecimal paidAmount;

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 车牌颜色
     */
    private Integer plateColor;


    private Long uid;
}
