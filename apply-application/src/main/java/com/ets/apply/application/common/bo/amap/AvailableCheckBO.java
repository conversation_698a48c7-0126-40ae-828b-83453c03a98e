package com.ets.apply.application.common.bo.amap;

import lombok.Data;

@Data
public class AvailableCheckBO {

    private Fulfillment fulfillment;

    @Data
    public static class Fulfillment {
        private FulfillmentTarget fulfillmentTarget;
    }

    @Data
    public static class FulfillmentTarget {
        private String targetType;
        private Identifier identifier;
        private Verification verification;
    }

    @Data
    public static class Identifier {
        private String type;
        private String primaryIdentifier;
        private String secondIdentifier;
    }

    @Data
    public static class Verification {
        private Boolean required;
    }
}
