package com.ets.apply.application.controller.third;

import com.ets.apply.application.app.business.plateNo.PlateNoBusiness;
import com.ets.apply.application.common.dto.request.plateNo.PlateNoCheckUniqueDTO;
import com.ets.apply.application.common.vo.plateNo.PlateNoCheckUniqueVO;
import com.ets.common.JsonResult;
import com.ets.common.annotation.RateLimiterAnnotation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

@RequestMapping("/third/plate-no")
@RefreshScope
@RestController
@Slf4j
public class PlateNoController {

    @Autowired
    private PlateNoBusiness plateNoBusiness;

    /**
     * 请求卡方车牌校验接口，设置qps 4
     * @param checkUniqueDTO
     * @return
     */
    @RateLimiterAnnotation(qps = 4,msg = "系统繁忙，请稍后再试")
    @PostMapping("/check-unique")
    public JsonResult<PlateNoCheckUniqueVO> checkUnique(@RequestBody @Valid PlateNoCheckUniqueDTO checkUniqueDTO) {

        return JsonResult.ok(plateNoBusiness.plateNoCheck(checkUniqueDTO));

    }

}
