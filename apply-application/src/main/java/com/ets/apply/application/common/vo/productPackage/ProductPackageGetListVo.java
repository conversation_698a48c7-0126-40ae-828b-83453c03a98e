package com.ets.apply.application.common.vo.productPackage;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 商品套餐配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-19
 */
@Data
public class ProductPackageGetListVo {


    /**
     * 商品套餐流水号
     */
    private String packageSn;


    /**
     * 商品套餐名称
     */
    private String packageName;


    /**
     * 设备费（元）
     */
    private BigDecimal packageFee;

    /**
     * 库存
     */
    private Integer packageStock;

    /**
     * 订单数量
     */
    private Integer orderNums;

    /**
     * 渠道码
     */
    private String source;

    /*
     * 状态 1-有效 2-无效
     */
    private Integer status;

    private String statusStr;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    private String issuerName;

    private String flowType;
    /**
     *
     */
    private String purchaseType;

    private String purchaseParty;
    /**
     *
     */
    private Integer cardId;
    /**
     *设备厂家
     */
    private String manufacturer;
    /**
     *设备类型
     */
    private String deviceType;

    private Integer term = 0;

    /**
     * 可用券批次
     */
    private List<String> canUseCouponBatchNo;

    /*
     * 发布状态：1待发布2已发布
     */
    private Integer releaseStatus;

    private String releaseStatusStr;

    private Integer allowModify;

    private String bizTypeStr;
}
