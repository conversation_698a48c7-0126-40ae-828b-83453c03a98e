package com.ets.apply.application.app.service.thirdPartner;

import com.ets.apply.application.app.business.ThirdInterfaceLogBusiness;
import com.ets.apply.application.app.thirdservice.feign.CallWecarFeign;
import com.ets.apply.application.common.config.WecarConfig;
import com.ets.apply.application.common.dto.request.ThirdInterfaceLogDTO;
import com.ets.apply.application.common.dto.request.wecar.WecarNotifyStatusDTO;
import com.ets.apply.application.common.vo.wecar.WecarNotifyStatusVo;
import com.ets.common.BizException;
import com.ets.common.ToolsHelper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.security.MessageDigest;
import java.util.*;

@Slf4j
@Component(value = "WecarService")
public class WecarService {

    @Autowired
    private WecarConfig wecarConfig;

    @Autowired
    private CallWecarFeign callWecarFeign;
    @Autowired
    private ThirdInterfaceLogBusiness thirdInterfaceLogBusiness;
    /**
     *   订单状态同步 腾讯出行
     */
    public WecarNotifyStatusVo notifyStatus(WecarNotifyStatusDTO dto){
        //补充日志记录
        ThirdInterfaceLogDTO thirdInterfaceLogDTO = new ThirdInterfaceLogDTO();
        thirdInterfaceLogDTO.setLogMethod("WecarService:notifyStatus");
        thirdInterfaceLogDTO.setLogRequest(wecarConfig.getNotifyUrl());

        try {
            int ts = (int)(System.currentTimeMillis()/1000);
            String seqId = ToolsHelper.createRandomStr(30);
            Map<String,Object> macMap = new HashMap<>();
            macMap.put("api_key",wecarConfig.getApiKey());
            macMap.put("seq_id",seqId);
            macMap.put("timestamp",ts);
            macMap.put("nonce",ToolsHelper.createRandomStr(10));
            macMap.put("seqId",seqId);
            macMap.put("userCode",dto.getUserCode());
            macMap.put("outOrderId",dto.getOutOrderId());
            macMap.put("status",dto.getStatus());
            macMap.put("logisticsStatus",dto.getLogisticsStatus());
            macMap.put("actionTime",ts);
            macMap.put("vehicleNo",dto.getVehicleNo());
            macMap.put("spOrderStatus",dto.getSpOrderStatus());
            macMap.put("spId",1);//区分标识
            String signStr = "/etcOrder/statusSync?"+ksortAndBuild(macMap)+wecarConfig.getSecretKey();
            macMap.put("sig",md5(signStr).toUpperCase());


            thirdInterfaceLogDTO.setLogParams(macMap.toString());

            String jsonResult = callWecarFeign.statusSync(URI.create(wecarConfig.getNotifyUrl()),macMap);

            thirdInterfaceLogDTO.setLogRespone(jsonResult);
            thirdInterfaceLogBusiness.addLog(thirdInterfaceLogDTO);

            WecarNotifyStatusVo wecarNotifyStatusVo = new ObjectMapper().readValue(jsonResult, WecarNotifyStatusVo.class);
            if(!wecarNotifyStatusVo.getStatus().equals(0)){
                ToolsHelper.throwException(wecarNotifyStatusVo.getMessage());
            }
            return new ObjectMapper().readValue(jsonResult, WecarNotifyStatusVo.class);
        } catch (JsonProcessingException e) {
            //log.info(e.toString());
            thirdInterfaceLogDTO.setLogRespone(e.toString());
            thirdInterfaceLogBusiness.addLog(thirdInterfaceLogDTO);
            ToolsHelper.throwException(e.getMessage());
        }catch (BizException e) {
            //log.info(e.toString());
            thirdInterfaceLogDTO.setLogRespone(e.toString());
            thirdInterfaceLogBusiness.addLog(thirdInterfaceLogDTO);
            ToolsHelper.throwException(e.getMessage());
        }
        return null;
    }

    /*
     *  排序并且构造签名字符串
     */
    public static String ksortAndBuild(Map<String,Object> macMap) throws BizException {
        Set<String> keySet = macMap.keySet();
        String[] keyArray = keySet.toArray(new String[0]);
        Arrays.sort(keyArray);
        StringBuilder sb = new StringBuilder();
        for(int i =0; i<keyArray.length;i++){
            if(String.valueOf(macMap.get(keyArray[i])).length()>0){
                //String values = URLEncoder.encode(macMap.get(keyArray[i]).toString(),"UTF-8");
                String values = macMap.get(keyArray[i]).toString();
                if((i+1) == keyArray.length ){
                    sb.append(keyArray[i]).append("=").append(values);
                }else{
                    sb.append(keyArray[i]).append("=").append(values).append("&");
                }

            }
        }
        return sb.toString();
    }

    public static String md5(String source) {
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            byte[] md5byte = md5.digest(source.getBytes("UTF-8"));
            StringBuffer strHexString = new StringBuffer();
            for (int i = 0; i < md5byte.length; i++) {
                String hex = Integer.toHexString(0xff & md5byte[i]);
                if (hex.length() == 1) {
                    strHexString.append('0');
                }
                strHexString.append(hex);
            }
            return strHexString.toString();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

    }


}
