package com.ets.apply.application.common.consts.issueService;

import com.ets.apply.application.common.vo.SelectOptionsVO;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum IssueServiceRefundStatusEnum {

    UN_REFUND(0, "未退款"),

    REFUND_SUCCESS(1, "已退款");

    private final int code;
    private final String description;

    public static String getDescByCode(int code) {
        for (IssueServiceRefundStatusEnum node : IssueServiceRefundStatusEnum.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }

    public static List<SelectOptionsVO> getSelectOptions() {

        return Stream.of(IssueServiceRefundStatusEnum.values())
                .map(item -> new SelectOptionsVO(String.valueOf(item.getCode()), item.getDescription()))
                .collect(Collectors.toList());
    }
}
