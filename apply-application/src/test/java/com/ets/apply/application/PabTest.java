package com.ets.apply.application;

import com.ets.apply.application.app.business.creditCard.CreditCardBusiness;
import com.ets.apply.application.app.service.bank.PabService;
import com.ets.apply.application.app.thirdservice.feign.CallPabFeign;
import com.ets.apply.application.common.dto.request.creditCard.ApplyOrderDTO;
import com.ets.apply.application.common.vo.creditCard.CreditCardApplyOrderVO;
import com.ets.apply.application.common.vo.creditCard.pab.PabResponseVO;
import com.ets.common.ToolsHelper;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URLEncoder;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;

@SpringBootTest(classes = ApplyApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringRunner.class)
@ActiveProfiles(value = "dev")
@Slf4j
public class PabTest {
    @Autowired
    private CallPabFeign callPabFeign;
    @Autowired
    PabService pabService;
    @Autowired
    private CreditCardBusiness creditCardBusiness;
    @Test
    public void verify() {
        try {
            pabService.checkIsNewByPhone("***********");

        } catch (Exception e) {
            log.info("CreditCardSpd 光大银行申请异常：" + e.getMessage());
            ToolsHelper.throwException("当前办理方式不可用，请稍后再试");
        }

        System.out.println(999);
    }

    @Test
    public void sign() {
        try {
            Map<String,String> macMap = new HashMap<>();
            macMap.put("versionNo","R10310");
            macMap.put("scc","232100338");
            macMap.put("onlineSQFlag","N");
            macMap.put("channel","WXHZF");
            macMap.put("ccp","tcg2");
            macMap.put("partnerSeqId","m1906269623000001000");
            macMap.put("templateNo","s1907183PR");
            macMap.put("cardCatenaNo","18");
            macMap.put("mt","su6");
            macMap.put("successUrl","https://h5-pub.etczs.net/some/closeWV/closeWV.html?which=30&navWay=reLaunch&path=/pages/apply/page/creditProgress");
            Set<String> keySet = macMap.keySet();
            String[] keyArray = keySet.toArray(new String[0]);
            Arrays.sort(keyArray);
            StringBuilder sb = new StringBuilder();
            for(int i =0; i<keyArray.length;i++){
                if(String.valueOf(macMap.get(keyArray[i])).length()>0){
                    String values = URLEncoder.encode(macMap.get(keyArray[i]),"UTF-8");
                    if((i+1) == keyArray.length ){
                        sb.append(keyArray[i]).append("=").append(values);
                    }else{
                        sb.append(keyArray[i]).append("=").append(values).append("&");
                    }

                }
            }
            System.out.println(sb);
            System.out.println(sb.toString());
            String secret = "YLPPA-WANGSHEN-595284-SECAP-@#$*^";
              byte[] hmacSha256Bytes = calculateHmacSHA256(sb.toString(),secret);
              String hmacSha256Hex = bytesToHex(hmacSha256Bytes);
              System.out.println("hmacSha256:"+hmacSha256Hex);

        } catch (Exception e) {
            log.info("CreditCardSpd 光大银行申请异常：" + e.getMessage());
            ToolsHelper.throwException("当前办理方式不可用，请稍后再试");
        }

    }
    private static String byteArrayToHexString(byte[] b) {
        StringBuilder hs = new StringBuilder();
        String stmp;
        for (int n = 0; b != null && n < b.length; n++) {
            stmp = Integer.toHexString(b[n] & 0XFF);
            if (stmp.length() == 1)
                hs.append('0');
            hs.append(stmp);
        }
        return hs.toString().toUpperCase();
    }

    private static byte[] calculateHmacSHA256(String message, String secretKey) throws NoSuchAlgorithmException, InvalidKeyException, UnsupportedEncodingException {
        Mac mac = Mac.getInstance("HmacSHA256");
        SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey.getBytes("UTF-8"), "HmacSHA256");
        mac.init(secretKeySpec);
        return mac.doFinal(message.getBytes("UTF-8"));
    }
    private static String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }
    @Test
    public void getApplyUrl() {
        try {
            ApplyOrderDTO applyOrderDTO = new ApplyOrderDTO();
            applyOrderDTO.setClassify(2);
            applyOrderDTO.setReferSn("230721164100000625A1");
            applyOrderDTO.setPlateNo("粤A7NN10");
            applyOrderDTO.setVersion("unify");
            applyOrderDTO.setWhichBank(30);
            applyOrderDTO.setReferType(1);
            applyOrderDTO.setUid(10L);
            CreditCardApplyOrderVO creditCardApplyOrderVO = creditCardBusiness.applyOrder(applyOrderDTO);
            System.out.println("getApplyUrl:"+creditCardApplyOrderVO.toString());
        }catch (Exception e) {
            System.out.println("getApplyUrl:"+e.getMessage());
            //ToolsHelper.throwException("当前办理方式不可用，请稍后再试");
        }

    }

    @Test
    public void queryOrder() {
        try {
//            ApplyOrderDTO applyOrderDTO = new ApplyOrderDTO();
//            applyOrderDTO.setClassify(2);
//            applyOrderDTO.setReferSn("230721164100000625A1");
//            applyOrderDTO.setPlateNo("粤A7NN10");
//            applyOrderDTO.setVersion("unify");
//            applyOrderDTO.setWhichBank(30);
//            applyOrderDTO.setReferType(1);
//            applyOrderDTO.setUid(10L);
//            CreditCardApplyOrderVO creditCardApplyOrderVO = creditCardBusiness.applyOrder(applyOrderDTO);
//            System.out.println("getApplyUrl:"+creditCardApplyOrderVO.toString());

            PabResponseVO pabResponseVO = pabService.queryOrder("m190626962300000202310121148412984613");
            System.out.println("queryOrder:"+pabResponseVO.toString());
        }catch (Exception e) {
            System.out.println("getApplyUrl:"+e.getMessage());
            //ToolsHelper.throwException("当前办理方式不可用，请稍后再试");
        }

    }
    /*
     *  排序并且构造签名字符串
     */
    private static String ksortAndBuild(Map<String,String> macMap) throws UnsupportedEncodingException{
        Set<String> keySet = macMap.keySet();
        String[] keyArray = keySet.toArray(new String[0]);
        Arrays.sort(keyArray);
        StringBuilder sb = new StringBuilder();
        for(int i =0; i<keyArray.length;i++){
            if(String.valueOf(macMap.get(keyArray[i])).length()>0){
                String values = URLEncoder.encode(macMap.get(keyArray[i]),"UTF-8");
                if((i+1) == keyArray.length ){
                    sb.append(keyArray[i]).append("=").append(values);
                }else{
                    sb.append(keyArray[i]).append("=").append(values).append("&");
                }

            }
        }
        return sb.toString();
    }



    public static String getSHA256String(String str) throws NoSuchAlgorithmException, UnsupportedEncodingException {
        MessageDigest messageDigest;
        messageDigest = MessageDigest.getInstance("SHA-256");
        messageDigest.update(str.getBytes("UTF-8"));
        String encodeStr = byteArrayToHexString(messageDigest.digest());
        return encodeStr;
    }
}
