package com.ets.apply.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.apply.application.common.dto.aftersalesreviews.AdminAfterSalesReviewsGetListDTO;
import com.ets.apply.application.infra.entity.aftersalesreview.AftersalesReviews;
import com.ets.apply.application.infra.mapper.AftersalesReviewsMapper;

import java.util.List;

import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Service
@DS("db-issuer-admin-proxy")
public class AftersalesReviewsService extends BaseService<AftersalesReviewsMapper, AftersalesReviews> {

    public IPage<AftersalesReviews> getList(AdminAfterSalesReviewsGetListDTO dto) {
        // 构建查询条件
        LambdaQueryWrapper<AftersalesReviews> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(dto.getReviewSn()), AftersalesReviews::getReviewSn, dto.getReviewSn())
                .eq(dto.getReviewStatus() != null, AftersalesReviews::getReviewStatus, dto.getReviewStatus())
                .ge(dto.getApplyTimeStart() != null, AftersalesReviews::getApplyTime, dto.getApplyTimeStart())
                .le(dto.getApplyTimeEnd() != null, AftersalesReviews::getApplyTime, dto.getApplyTimeEnd())
                .orderByDesc(AftersalesReviews::getCreatedAt);

        return this.page(new Page<>(dto.getPageNum(), dto.getPageSize()), null)
    }

    public AftersalesReviews getByOrderSn(String orderSn) {
        Wrapper<AftersalesReviews> wrapper = Wrappers.<AftersalesReviews>lambdaQuery()
                .eq(AftersalesReviews::getOrderSn, orderSn)
                .orderByDesc(AftersalesReviews::getCreatedAt)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }

    public AftersalesReviews getByReviewSn(String reviewSn) {
        Wrapper<AftersalesReviews> wrapper = Wrappers.<AftersalesReviews>lambdaQuery()
                .eq(AftersalesReviews::getReviewSn, reviewSn)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }
}
