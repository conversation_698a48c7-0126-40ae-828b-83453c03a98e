package com.ets.apply.application.common.dto.external;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ExternalOrderCreateDTO {

    @NotBlank(message = "外部订单号不能为空")
    private String thirdOrderSn;

    private String userCode;

    private String plateNo;

    @NotBlank(message = "收件人姓名不能为空")
    private String receiver;

    @NotBlank(message = "收件人手机号不能为空")
    private String phone;

    @NotBlank(message = "收件人地址不能为空")
    private String address;

    @NotBlank(message = "产品编号不能为空")
    private String productId;

    // 总价-- 基于第三方可信数据，不能C 端传输
    private BigDecimal totalAmount;

    // 支付金额
    private BigDecimal paidAmount;
}
