package com.ets.apply.application.common.vo.adminSalesUpgrade;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class SalesUpgradeListVO {
    private Integer id;
    private String planName;
    private Integer priority;
    private String ruleConditions;
    private Integer status;
    private String statusStr;
    private Integer releaseStatus;
    private String releaseStatusStr;
    private List<String> packageSns;
    private String operator;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

}
