package com.ets.apply.application.controller.internal;

import com.ets.apply.application.app.factory.flow.FlowFactory;
import com.ets.apply.application.common.dto.request.flowType.IsAllowDTO;
import com.ets.apply.application.controller.BaseController;
import com.ets.apply.application.infra.entity.OrderOrderEntity;
import com.ets.apply.application.infra.relation.OrderBindCardRelation;
import com.ets.apply.application.infra.relation.OrderBindReviewOrderRelation;
import com.ets.apply.application.infra.service.CardsService;
import com.ets.apply.application.infra.service.OrderOrderService;
import com.ets.apply.application.infra.service.ReviewOrderService;
import com.ets.common.JsonResult;
import com.ets.common.ToolsHelper;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping("/flowType")
@RefreshScope
@RestController
@Slf4j
public class FlowTypeController extends BaseController {

    @Autowired
    private OrderOrderService orderService;
    @Autowired
    private CardsService cardsService;
    @Autowired
    private ReviewOrderService reviewOrderService;
    @PostMapping("/isAllow")
    public JsonResult<Object> isAllow(@RequestBody @Valid IsAllowDTO dto) {
        OrderOrderEntity order = orderService.findByOrderSn(dto.getOrderSn());
        switch (dto.getFlow()){
            case "PushLogisticOrder":
                FlowFactory.create(order.getFlowType()).isAllowPushLogisticOrder(order, null);
                break;
            default:
                ToolsHelper.throwException("未实现");
        }

        return JsonResult.ok();
    }

    @PostMapping("/getStepList")
    public JsonResult<Object> getStepList(@RequestBody @Valid IsAllowDTO dto) {
        OrderOrderEntity order = orderService.findByOrderSn(dto.getOrderSn());
        cardsService.bindToMasterEntity(order, OrderBindCardRelation.class);
        reviewOrderService.bindToMasterEntity(order, OrderBindReviewOrderRelation.class);
        return JsonResult.ok(FlowFactory.create(order.getFlowType()).getFlowStepList(order,null));
    }
}
