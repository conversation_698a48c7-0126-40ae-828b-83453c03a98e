package com.ets.apply.application.common.consts.productOrder;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ReferValueEnum {

    DEFAULT(0, "", "默认无渠道"),
    PARTNER_VALUE_VASP(1, "vasp", "人寿财险"),
    PARTNER_VALUE_WECAR(2, "wecar", "腾讯出行"),
    PARTNER_VALUE_GEELY(3, "geely", "吉利");
    private final Integer value;
    private final String companyId;
    private final String description;


    public static String getCompanyIdByReferValue(Integer value) {
        for (ReferValueEnum referValueEnum : ReferValueEnum.values()) {
            if (referValueEnum.getValue().equals(value)) {
                return referValueEnum.getCompanyId();
            }
        }
        return null;
    }
}
