package com.ets.apply.application.controller.internal;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.apply.application.app.business.review.ReviewsBusiness;
import com.ets.apply.application.common.dto.reviews.OfflineReviewListDTO;
import com.ets.apply.application.common.dto.reviews.ReviewListDTO;
import com.ets.apply.application.common.dto.reviews.TruckReviewListDTO;
import com.ets.apply.application.common.vo.reviews.OfflineReviewListVO;
import com.ets.apply.application.common.vo.reviews.ReviewListVO;
import com.ets.apply.application.common.vo.reviews.TruckReviewListVO;
import com.ets.common.JsonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

@RestController
@RequestMapping("/reviews")
public class ReviewsController {

    @Autowired
    private ReviewsBusiness reviewsBusiness;

    @PostMapping("/getReviewList")
    public JsonResult<IPage<ReviewListVO>> getReviewList(@RequestBody @Valid ReviewListDTO listDTO) {
        return JsonResult.ok(reviewsBusiness.getReviewList(listDTO));
    }
    
        @PostMapping("/getTruckReviewList")
    public JsonResult<IPage<TruckReviewListVO>> getTruckReviewList(@RequestBody @Valid TruckReviewListDTO listDTO) {
        return JsonResult.ok(reviewsBusiness.getTruckReviewList(listDTO));
    }
    
    @PostMapping("/getOfflineReviewList")
    public JsonResult<IPage<OfflineReviewListVO>> getOfflineReviewList(@RequestBody @Valid OfflineReviewListDTO listDTO) {
        return JsonResult.ok(reviewsBusiness.getOfflineReviewList(listDTO));
    }
}
