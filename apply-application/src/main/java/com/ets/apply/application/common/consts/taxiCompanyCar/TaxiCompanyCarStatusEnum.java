package com.ets.apply.application.common.consts.taxiCompanyCar;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TaxiCompanyCarStatusEnum {

    TAXI_COMPANY_CAR_STATUS_DEFAULT(0, "未处理"),
    TAXI_COMPANY_CAR_STATUS_CREATED_ORDER(1, "创建订单成功"),
    TAXI_COMPANY_CAR_STATUS_UPLOADED(2, "成功上传文件"),
    TAXI_COMPANY_CAR_STATUS_BIND(3, "绑定成功"),
    TAXI_COMPANY_CAR_STATUS_CANCEL(4, "取消订单");

    private final Integer status;
    private final String desc;

}
