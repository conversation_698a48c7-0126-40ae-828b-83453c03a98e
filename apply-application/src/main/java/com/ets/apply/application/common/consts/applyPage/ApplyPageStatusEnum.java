package com.ets.apply.application.common.consts.applyPage;

import com.ets.apply.application.common.consts.productOrder.ProductPackageEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ApplyPageStatusEnum {

    LISTING(1,"上架"),
    DELIST(2,"下架");

    private final Integer status;

    private final String description;

    public static String getDescByCode(Integer code) {
        for (ApplyPageStatusEnum node : ApplyPageStatusEnum.values()) {
            if (node.getStatus().equals(code)) {
                return node.getDescription();
            }
        }

        return "";
    }
}
