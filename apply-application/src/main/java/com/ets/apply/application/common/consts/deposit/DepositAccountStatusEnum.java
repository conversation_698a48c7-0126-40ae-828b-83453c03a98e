package com.ets.apply.application.common.consts.deposit;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 保证金账户状态枚举
 */
@Getter
@AllArgsConstructor
public enum DepositAccountStatusEnum {

    NORMAL(1, "正常"),
    FROZEN(2, "冻结"),
    CANCELED(3, "注销"),
    REFUNDED(4, "退款");

    private final Integer code;
    private final String description;

    /**
     * 根据状态码获取描述
     *
     * @param code 状态码
     * @return 状态描述
     */
    public static String getDescByCode(Integer code) {
        if (code == null) {
            return "";
        }
        
        for (DepositAccountStatusEnum status : DepositAccountStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status.getDescription();
            }
        }
        return "";
    }

    /**
     * 根据状态码获取枚举对象
     *
     * @param code 状态码
     * @return 枚举对象，未找到返回null
     */
    public static DepositAccountStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        
        for (DepositAccountStatusEnum status : DepositAccountStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 判断是否为正常状态
     *
     * @param code 状态码
     * @return true-正常状态，false-非正常状态
     */
    public static boolean isNormal(Integer code) {
        return NORMAL.getCode().equals(code);
    }

    /**
     * 判断是否为冻结状态
     *
     * @param code 状态码
     * @return true-冻结状态，false-非冻结状态
     */
    public static boolean isFrozen(Integer code) {
        return FROZEN.getCode().equals(code);
    }

    /**
     * 判断是否为注销状态
     *
     * @param code 状态码
     * @return true-注销状态，false-非注销状态
     */
    public static boolean isCanceled(Integer code) {
        return CANCELED.getCode().equals(code);
    }

    /**
     * 判断是否为退款状态
     *
     * @param code 状态码
     * @return true-退款状态，false-非退款状态
     */
    public static boolean isRefunded(Integer code) {
        return REFUNDED.getCode().equals(code);
    }
}
