package com.ets.apply.application.common.consts.activityCreditCardBankUser;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ActivityCreditCardBankUserIsNewUserBackMoneyEnum {

    DEFAULT(0, "默认"),
    NEW_USE_BACK_MONEY_YES(1, "新用户"),
    NEW_USE_BACK_MONEY_NOT(2, "非新户"),
    NEW_USE_BACK_MONEY_UNKNOWN(3, "未知是否新户返佣");

    private final int code;
    private final String description;

    public static String getDescByCode(int code) {
        for (ActivityCreditCardBankUserIsNewUserBackMoneyEnum node : ActivityCreditCardBankUserIsNewUserBackMoneyEnum.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }

}
