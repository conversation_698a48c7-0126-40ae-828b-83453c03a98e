package com.ets.apply.application.common.consts.qualify;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ObjectTypeEnum {

    OBJECT_TYPE_RIGHTS("rights", "权益");

    private final String code;
    private final String description;

    public static String getDescByCode(String code) {
        for (ObjectTypeEnum node : ObjectTypeEnum.values()) {
            if (node.getCode().equals(code)) {
                return node.getDescription();
            }
        }

        return "";
    }
}
