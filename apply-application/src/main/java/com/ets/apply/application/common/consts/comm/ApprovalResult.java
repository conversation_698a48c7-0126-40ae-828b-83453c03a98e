package com.ets.apply.application.common.consts.comm;

import lombok.AllArgsConstructor;
import lombok.Getter;


@Getter
@AllArgsConstructor
public enum ApprovalResult {
    APPROVED("1", "审核通过"),
    REJECTED("2", "审核拒绝");

    private final String status;
    private final String description;

    public static ApprovalResult getApplyStatusEnumByStatus(String status) {
        for (ApprovalResult applyStatusEnum : ApprovalResult.values()) {
            if (applyStatusEnum.getStatus().equals(status)) {
                return applyStatusEnum;
            }
        }
        return null;
    }
}
