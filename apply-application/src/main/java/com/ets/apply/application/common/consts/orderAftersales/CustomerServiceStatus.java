package com.ets.apply.application.common.consts.orderAftersales;


import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CustomerServiceStatus {

    CREATED(0, "已创建"),

    FAILED(3, "失败"),

    FINISHED(4, "已完成"),

    CANCELED(9, "已取消");

    // 6 请求业务退款

    private final int code;
    private final String description;
    public static CustomerServiceStatus getByCode(int code) {
        for (CustomerServiceStatus node : CustomerServiceStatus.values()) {
            if (node.getCode() == code) {
                return node;
            }
        }
        return null;
    }
}
