package com.ets.apply.application.app.thirdservice.fallback;


import com.alibaba.fastjson.JSONObject;
import com.ets.apply.application.app.thirdservice.feign.CallPhpPayFeign;
import com.ets.apply.application.app.thirdservice.request.pay.AnyCouponDTO;
import com.ets.apply.application.app.thirdservice.request.pay.QueryRefundResultDTO;
import com.ets.apply.application.common.dto.order.GetInfoByPaymentSnDTO;
import com.ets.apply.application.common.dto.payment.PhpRefundDTO;
import com.ets.apply.application.common.dto.request.entrust.EntrustPayParamsDTO;
import com.ets.apply.application.common.dto.request.entrust.GetBindBankAndVerifyDTO;
import com.ets.apply.application.common.dto.request.productOrder.PaymentRefundDTO;
import com.ets.common.JsonResult;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

@Component
public class CallPhpPayFallbackFactory implements FallbackFactory<CallPhpPayFeign> {

    @Override
    public CallPhpPayFeign create(Throwable cause) {
        return new CallPhpPayFeign() {

            @Override
            public String refund(PaymentRefundDTO dto) {

                return JsonResult.error("请求PHP pay 服务失败: " + cause.getMessage()).toString();
            }

            @Override
            public String anyCoupon(AnyCouponDTO anyCouponDTO) {
                return JsonResult.error("请求PHP pay 服务 anyCoupon失败: " + cause.getMessage()).toString();
            }

            @Override
            public JsonResult<JSONObject> manualRefund(PhpRefundDTO paymentRefundDTO) {
                return JsonResult.error("请求PHP pay 服务失败: " + cause.getMessage());
            }

            @Override
            public JsonResult<JSONObject> entrustCheck(GetBindBankAndVerifyDTO dto) {
                return JsonResult.error("请求PHP pay  entrustCheck服务失败: " + cause.getMessage());
            }
            @Override
            public JsonResult<JSONObject> entrustPay(EntrustPayParamsDTO dto) {
                return JsonResult.error("请求PHP pay  entrustPay服务失败: " + cause.getMessage());
            }
            @Override
            public JsonResult<JSONObject> manualRefundQueryByPaymentSn(GetInfoByPaymentSnDTO dto) {
                return JsonResult.error("请求PHP pay  manualRefundQueryByPaymentSn: " + cause.getMessage());
            }

            @Override
            public JsonResult<JSONObject> queryRefundResult(QueryRefundResultDTO dto) {
                return null;
            }
        };
    }
}