package com.ets.apply.application.infra.entity.sales;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ets.apply.application.infra.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 营销计划相关操作日志
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("etc_sales_log")
public class SalesLogEntity extends BaseEntity<SalesLogEntity> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 对象类型
     */
    private String objectType;

    /**
     * 对象id
     */
    private Integer objectId;

    /**
     * 操作类型
     */
    private String type;

    /**
     * 操作前数据
     */
    private String preOperate;

    /**
     * 操作后数据
     */
    private String afterOperate;

    /**
     * 操作描述
     */
    private String msg;

    /**
     * 操作人
     */
    private String operateUser;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
