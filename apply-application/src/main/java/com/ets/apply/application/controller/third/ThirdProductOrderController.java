package com.ets.apply.application.controller.third;

import com.ets.apply.application.app.business.ProductOrderBusiness;
import com.ets.apply.application.app.business.VaspBusiness;
import com.ets.apply.application.common.dto.request.productOrder.*;
import com.ets.apply.application.common.dto.request.vasp.VaspCancelDto;
import com.ets.apply.application.common.dto.request.vasp.VaspSubmitDto;
import com.ets.apply.application.common.dto.request.wecar.*;
import com.ets.apply.application.common.vo.productOrder.ProductOrderThirdInfoVO;
import com.ets.apply.application.common.vo.productOrder.ProductOrderThirdListVO;
import com.ets.apply.application.common.vo.wecar.*;
import com.ets.apply.feign.feign.ProductOrderFeign;
import com.ets.apply.feign.request.GetFinishOrdersDTO;
import com.ets.apply.feign.request.ProductOrderExternalCreateDTO;
import com.ets.apply.feign.response.ProductOrderExternalCreateVO;
import com.ets.apply.feign.response.ProductOrderResponse;
import com.ets.common.BizException;
import com.ets.common.JsonResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

@RequestMapping("/third/productOrder")
@RefreshScope
@RestController
@Slf4j
public class ThirdProductOrderController implements ProductOrderFeign {


    @Autowired
    VaspBusiness vaspBusiness;

    @Autowired
    ProductOrderBusiness productOrderBusiness;

    /**
     * 人寿订单提交
     * @param vaspSubmitDto
     * @return
     */
    @PostMapping("/vaspSubmit")
    public JsonResult<?> vaspSubmit(@RequestBody @Valid VaspSubmitDto vaspSubmitDto) {
        return JsonResult.ok(vaspBusiness.vaspSubmit(vaspSubmitDto));
    }

    @PostMapping("/vaspCancel")
    public JsonResult<?> vaspCancel(@RequestBody @Valid VaspCancelDto vaspCancelDto) {
        vaspBusiness.vaspCancel(vaspCancelDto);
        return JsonResult.ok();
    }

    @PostMapping("ship")
    public JsonResult<?> ship(@RequestBody @Valid ProductOrderShipDTO shipDTO) {
        productOrderBusiness.ship(shipDTO);
        return JsonResult.ok();
    }
    /*
     * 接收商品订单发货通知，回调的参数不一样
     */
    @PostMapping("/goodsShip")
    public JsonResult<?> goodsShip(@RequestBody @Valid ProductOrderGoodsShipDTO dto) {
        //转化dto
        ProductOrderShipDTO shipDTO = new ProductOrderShipDTO();
        shipDTO.setOrderSn(dto.getBusinessOrderSn());
        shipDTO.setExpressCorp(dto.getExpressCompany());
        shipDTO.setExpressNumber(dto.getExpressNumber());
        shipDTO.setExpressTime(dto.getExpressTime());
        productOrderBusiness.ship(shipDTO);
        return JsonResult.ok();
    }


    @Override
    @RequestMapping("/getFinishedOrders")
    public JsonResult<List<ProductOrderResponse>> getFinishedOrders(@RequestBody @Valid GetFinishOrdersDTO dto) {
        return JsonResult.ok(productOrderBusiness.getFinishedOrders(dto));
    }

    @Override
    @RequestMapping("/getProductOrder")
    public JsonResult<ProductOrderResponse> getProductOrder(@RequestParam(value = "productOrderSn") String productOrderSn, @RequestParam(value = "uid") Long uid) {
        return JsonResult.ok(productOrderBusiness.getByProductOrderSnAndUid(productOrderSn,uid));
    }

    @RequestMapping("/createOrderFromExternal")
    public JsonResult<ProductOrderExternalCreateVO> createOrderFromExternal(@Valid @RequestBody ProductOrderExternalCreateDTO dto) {

        return JsonResult.ok(productOrderBusiness.createOrderFromExternal(dto));
    }

    @PostMapping("/getProductList")
    public JsonResult<List<WecarProductVo>> getProductList(@RequestBody @Valid GetProductListDTO dto){
        return JsonResult.ok(productOrderBusiness.getProductListByCompanyId(dto.getCompanyId()));
    }
    /*
     * 创建商城订单
     */
    @PostMapping("/addOrder")
    public JsonResult<WecarAddOrderVo> addOrder(@RequestBody @Valid ThirdAddOrderDTO dto){
        return JsonResult.ok(productOrderBusiness.addOrder(dto));
    }

    @PostMapping("/prepay")
    @ResponseBody
    public JsonResult<Object> prepay(@RequestBody(required = false) @Valid ThirdOrderPrePayDTO request) {

        return JsonResult.ok(productOrderBusiness.prepay(request));
    }

    /*
     * 创建商城订单
     */
    @PostMapping("/getOrderList")
    public JsonResult<List<ProductOrderThirdListVO>> getOrderList(@RequestBody @Valid WecarGetOrderListDTO dto){
        return JsonResult.ok(productOrderBusiness.getOrderList(dto));
    }

    /*
     * 获取订单详细
     */
    @PostMapping("/getOrderInfo")
    public JsonResult<ProductOrderThirdInfoVO> getOrderInfo(@RequestBody @Valid WecarGetOrderInfoDTO dto){
        return JsonResult.ok(productOrderBusiness.getOrderInfo(dto));
    }

    /*
     * 取消商城订单
     */
    @PostMapping("/cancelOrder")
    public JsonResult<Boolean> cancelOrder(@RequestBody @Valid WecarCancelOrderDTO dto) throws BizException, JsonProcessingException {
        return JsonResult.ok(productOrderBusiness.thridOrderCancelOrder(dto));
    }
    /*
     * 修改商城订单
     */
    @PostMapping("/modifyOrder")
    public JsonResult<Boolean> modifyOrder(@RequestBody @Valid WecarModifyOrderDTO dto){
        return JsonResult.ok(productOrderBusiness.modifyOrder(dto));
    }

    /*
     * 同步状态通知
     */
    @PostMapping("/notify")
    public JsonResult<Boolean> notify(@RequestBody @Valid ThirdOrderNotifyDTO dto){
        return JsonResult.ok(productOrderBusiness.notify(dto));
    }
}
