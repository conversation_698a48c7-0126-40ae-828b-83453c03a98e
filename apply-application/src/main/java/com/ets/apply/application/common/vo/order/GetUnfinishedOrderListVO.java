package com.ets.apply.application.common.vo.order;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class GetUnfinishedOrderListVO {
    private String orderSn;
    private Integer status;
    private BigDecimal needPay;
    private Integer canContinuePayApply;
    private ProductPackage productPackage;
    @Data
    public static class ProductPackage {
        /**
         * 产品包号
         */
        private String packageSn;
        /**
         * 产品包名称
         */
        private String packageName;

        /**
         * 产品包前端配置信息
         */
        private Object frontendConfig;

    }
}
