package com.ets.apply.application.common.consts.orderAftersales;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum OrderAftersalesErrorCodeEnum {
    AFTERSALES_ERROR_CODE_ORDER_STATUS_INCORRECT(208001, "订单状态不对"),
    AFTERSALES_ERROR_CODE_NOT_ALLOWED_TO_APPLY(208002, "不允许申请售后"),
    AFTERSALES_ERROR_CODE_ALREADY_COMPLETED(208003, "已完成售后"),
    ;

    private final Integer code;
    private final String desc;

}
