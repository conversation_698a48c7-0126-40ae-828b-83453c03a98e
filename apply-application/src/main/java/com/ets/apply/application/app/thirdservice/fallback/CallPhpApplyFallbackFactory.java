package com.ets.apply.application.app.thirdservice.fallback;


import com.ets.apply.application.app.thirdservice.feign.CallPhpApplyFeign;
import com.ets.apply.application.app.thirdservice.request.sales.SalesProdDTO;
import com.ets.apply.application.common.bo.map.MapSetCacheByAssignIdBO;
import com.ets.apply.application.common.bo.flowType.ReadyForPushLogisticOrderBO;
import com.ets.apply.application.common.bo.flowType.ReadyForPushReviewOrderBO;
import com.ets.apply.application.common.dto.OrderSnDTO;
import com.ets.apply.application.common.dto.adminSalesUpgrade.SalesUpgradeProdDTO;
import com.ets.apply.application.common.dto.map.MapDelCacheByPackageSnDTO;
import com.ets.apply.application.common.dto.order.CancelOrderDTO;
import com.ets.apply.application.common.dto.request.*;
import com.ets.apply.application.common.dto.request.admin.orderOrder.AdminOrderCancelDTO;
import com.ets.apply.application.common.dto.request.admin.task.AdminTaskExecDTO;
import com.ets.apply.application.common.dto.request.creditCard.CreditCardChangeNotifyDTO;
import com.ets.apply.application.common.dto.request.creditCard.CreditCardFirstAuditNotifyDTO;
import com.ets.apply.application.common.dto.request.creditCard.CreditCardFirstUsedNotifyDTO;
import com.ets.apply.application.common.dto.request.orderAftersales.OrderAftersalesChangeDTO;
import com.ets.apply.application.common.dto.request.productOrder.CheckPlateNoDTO;
import com.ets.apply.application.infra.entity.TaskRecordEntity;
import com.ets.common.JsonResult;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@Component
public class CallPhpApplyFallbackFactory implements FallbackFactory<CallPhpApplyFeign> {

    @Override
    public CallPhpApplyFeign create(Throwable cause) {
        return new CallPhpApplyFeign() {

            @Override
            public String checkPlateNo(CheckPlateNoDTO params) {

                return JsonResult.error("请求PHP Apply 服务失败: " + cause.getMessage()).toString();
            }

            @Override
            public String creditCardActivateNotify(CreditCardChangeNotifyDTO params){
                return JsonResult.error("请求PHP Apply 服务失败: " + cause.getMessage()).toString();
            }

            @Override
            public String creditCardSubmittedNotify(@RequestBody CreditCardChangeNotifyDTO params) {
                return JsonResult.error("请求PHP Apply 服务失败: " + cause.getMessage()).toString();
            }

            @Override
            public String creditCardFirstAuditNotify(CreditCardFirstAuditNotifyDTO params) {
                return JsonResult.error("请求PHP Apply 服务失败: " + cause.getMessage()).toString();
            }

            @Override
            public String creditCardAuditNotify(@RequestBody CreditCardChangeNotifyDTO params) {
                return JsonResult.error("请求PHP Apply 服务失败: " + cause.getMessage()).toString();
            }

            @Override
            public String creditCardAuditFailedNotify(@RequestBody CreditCardChangeNotifyDTO params) {
                return JsonResult.error("请求PHP Apply 服务失败: " + cause.getMessage()).toString();
            }

            @Override
            public String creditCardFirstUsedNotify(CreditCardChangeNotifyDTO params) {
                return JsonResult.error("请求PHP Apply 服务失败: " + cause.getMessage()).toString();
            }

            @Override
            public String checkPlateUnique(CheckPlateNoDTO params) {
                return JsonResult.error("请求PHP Apply 服务失败: " + cause.getMessage()).toString();
            }

            @Override
            public String getProductPackageList(GetProductPackageListDTO params) {

                return JsonResult.error("请求PHP Apply 服务失败: " + cause.getMessage()).toString();
            }

            @Override
            public String mapSetCacheByModule(MapSetCacheByModuleDTO params) {

                return JsonResult.error("请求PHP Apply 服务失败: " + cause.getMessage()).toString();
            }

            public String mapSetCacheByAssignId(MapSetCacheByAssignIdBO params) {

                return JsonResult.error("请求PHP Apply 服务失败: " + cause.getMessage()).toString();
            }

            @Override
            public String mapSetCacheByKey(MapSetCacheByKeyDTO params) {

                return JsonResult.error("请求PHP Apply 服务失败: " + cause.getMessage()).toString();
            }
            @Override
            public String mapCompareDiff(MapCompareDiffDTO params) {

                return JsonResult.error("请求PHP Apply 服务失败: " + cause.getMessage()).toString();
            }
            @Override
            public String getProductsByProvince(GetProductByProvinceDTO params) {

                return JsonResult.error("请求PHP Apply 服务失败: " + cause.getMessage()).toString();
            }
            @Override
            public String getIssuerByPlateNo(GetIssuerByPlateNoDTO params) {

                return JsonResult.error("请求PHP Apply 服务失败: " + cause.getMessage()).toString();
            }

            @Override
            public String changeNotify(CreditCardChangeNotifyDTO params) {
                return JsonResult.error("请求PHP Apply 服务失败: " + cause.getMessage()).toString();
            }
            public String delCacheByPackageSn(MapDelCacheByPackageSnDTO params) {
                return JsonResult.error("请求PHP Apply 服务失败: " + cause.getMessage()).toString();
            }
            public String orderCancel(AdminOrderCancelDTO params) {
                return JsonResult.error("请求PHP Apply 服务失败: " + cause.getMessage()).toString();
            }
            public String readyForPushReviewOrder(ReadyForPushReviewOrderBO params) {
                return JsonResult.error("请求PHP Apply 服务失败: " + cause.getMessage()).toString();
            }
            public String readyForPushLogisticOrder(ReadyForPushLogisticOrderBO params) {
                return JsonResult.error("请求PHP Apply 服务失败: " + cause.getMessage()).toString();
            }
            public String offlineDelivery(OrderSnDTO params) {
                return JsonResult.error("请求PHP Apply 服务失败: " + cause.getMessage()).toString();
            }


            @Override
            public String goodsOrderAfterSaleChange(OrderAftersalesChangeDTO dto) {
                return JsonResult.error("请求PHP Apply 服务失败: " + cause.getMessage()).toString();
            }

            @Override
            public String taskExec(@RequestBody AdminTaskExecDTO dto) {
                return JsonResult.error("请求PHP Apply taskExec 服务失败: " + cause.getMessage()).toString();
            }
            @Override
            public String salesCouponProd(@RequestBody SalesProdDTO dto) {
                return JsonResult.error("请求PHP Apply salesCouponProd 服务失败: " + cause.getMessage()).toString();
            }

            @Override
            public String salesUpgradeProd(@RequestBody SalesUpgradeProdDTO params) {
                return JsonResult.error("请求PHP Apply salesUpgradeProd 服务失败: " + cause.getMessage()).toString();
            }

            @Override
            public JsonResult<Object> cancelOrder(CancelOrderDTO dto) {
                return JsonResult.error("请求PHP Apply cancelOrder 服务失败: " + cause.getMessage());
            }
        };
    }
}