package com.ets.apply.application.controller.frontend;

import com.ets.apply.application.app.business.ApplyGuideBusiness;
import com.ets.apply.application.common.vo.applyGuide.ApplyGuideMapVO;
import com.ets.apply.application.common.vo.applyGuide.ApplyGuideResultVO;
import com.ets.common.JsonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/frontend/apply-guide")
public class ApplyGuideController {

    @Autowired
    private ApplyGuideBusiness applyGuideBusiness;

    @PostMapping("/get-question-map")
    public JsonResult<List<ApplyGuideMapVO>> getQuestionMap(@RequestParam(required = false) Integer mapType) {
        return JsonResult.ok(applyGuideBusiness.getQuestionMap(mapType));
    }

    @PostMapping("/get-result-map")
    public JsonResult<List<ApplyGuideResultVO>> getResultMap(@RequestParam(required = false) Integer mapType) {
        return JsonResult.ok(applyGuideBusiness.getResultMap(mapType));
    }

    // get-question-group-list
    @PostMapping("/get-question-group-list")
    public JsonResult<List<ApplyGuideQuestionGroupVO>> getQuestionGroupList(
            @RequestParam Integer mapType,
            @RequestParam(required = false) Integer questionType) {
        return JsonResult.ok(applyGuideBusiness.getQuestionGroupList(mapType, questionType));
    }

    // get-result-by-map

}
