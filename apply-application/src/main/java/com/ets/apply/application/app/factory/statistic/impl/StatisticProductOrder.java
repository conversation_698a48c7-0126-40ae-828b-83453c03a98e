package com.ets.apply.application.app.factory.statistic.impl;
import com.ets.apply.application.common.bo.admin.StatisticDataDataSourceBO;
import com.ets.apply.application.infra.entity.StatisticDataLogEntity;
import com.ets.apply.application.infra.service.StatisticDataLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalTime;


@Component
@Repository
public class StatisticProductOrder extends StatisticBase {
    @Autowired
    private StatisticDataLogService statisticDataLogService;
    /*
     *  获取
     *      今天
     *      昨天
     *      7天前
     */
    @Override
    public StatisticDataDataSourceBO getABCValues(String name,Integer module,String type) {
        //今天
        LocalDate today = LocalDate.now();
        StatisticDataLogEntity lastRecord = statisticDataLogService.getLastRecord(module,type,today.atStartOfDay(),today.atTime(LocalTime.MAX));
        return new StatisticDataDataSourceBO(
                module,
                type,
                lastRecord == null ? null : lastRecord.getUpdatedAt(),
                name,
                lastRecord == null ? 0 : lastRecord.getNums(),
                statisticDataLogService.getLastNums(module,type,today.minusDays(1).atStartOfDay(),today.minusDays(1).atTime(LocalTime.MAX)),
                statisticDataLogService.getLastNums(module,type,today.minusDays(7).atStartOfDay(),today.minusDays(7).atTime(LocalTime.MAX)),
                0,
                0,
                0
        );
    }

}
