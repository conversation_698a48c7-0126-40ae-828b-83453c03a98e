package com.ets.apply.application.controller.admin;

import com.ets.apply.application.app.business.ProductPackageTmpBusiness;
import com.ets.apply.application.common.dto.ProductPackageTmp.ProductPackageTmpDTO;
import com.ets.apply.application.controller.BaseController;
import com.ets.common.JsonResult;
import com.ets.common.RequestHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Controller
@RequestMapping("/admin/product-package-tmp")
@RestController
public class ProductPackageTmpController extends BaseController {

    @Autowired
    private ProductPackageTmpBusiness productPackageTmpBusiness;

    /**
     * 产品包复制
     * <a href="https://yapi.etczs.net/project/312/interface/api/28614">...</a>
     * @param dto
     * @return
     */
    @RequestMapping("/copy")
    public JsonResult<?> copy(@RequestBody ProductPackageTmpDTO dto){
        // 获取操作用户
        String loginCode = RequestHelper.getHttpServletRequest().getHeader("loginCode");
        productPackageTmpBusiness.copy(dto.getPackageSn(),loginCode);
        return JsonResult.ok();
    }


}
