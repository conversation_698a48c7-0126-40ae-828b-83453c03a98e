package com.ets.apply.application.common.utils.bank;

import cfca.sadk.algorithm.sm2.SM2PrivateKey;
import cfca.sadk.algorithm.sm2.SM2PublicKey;
import cfca.sadk.cgb.toolkit.BASE64Toolkit;
import cfca.sadk.cgb.toolkit.SM2Toolkit;
import cn.hutool.core.io.IoUtil;
import com.ets.common.ToolsHelper;
import org.springframework.stereotype.Component;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;

@Component
public class SM2SignUtil {


	public SM2SignUtil() {

	}

	public static String signString(String srcStr, String pvkName, String encoding) throws Exception {
		String signValue;
		try {
			SM2Toolkit sm2Tool = new SM2Toolkit();
			// 这里在win系统能够正常获取，但是在linux系统就会找不到文件
//			String path = Objects.requireNonNull(SM2SignUtil.class.getClassLoader().getResource("")).getPath();
//			byte[] privateByte = read(path + pvkName);
//			SM2PrivateKey sm2PrivateKey = (SM2PrivateKey) sm2Tool.SM2BuildPrivateKey(BASE64Toolkit.encode(privateByte));
			InputStream is = SM2SignUtil.class.getResourceAsStream("/" + pvkName);
			if (is == null) {
				ToolsHelper.throwException("数据流为空。");
			}
			byte[] isByte = IoUtil.readBytes(is);
			SM2PrivateKey sm2PrivateKey = (SM2PrivateKey) sm2Tool.SM2BuildPrivateKey(BASE64Toolkit.encode(isByte));
			signValue = BASE64Toolkit.encode(sm2Tool.SM2Sign(sm2PrivateKey, srcStr.getBytes(encoding)));
			System.out.println("签名原文:" + srcStr);
			return signValue;
		} catch (Exception var8) {
			throw var8;
		}
	}

	public static boolean verifySignString(String pukName, String signValue, String srcString, String encoding) throws Exception {
		boolean result;

		try {
			SM2Toolkit sm2Tool = new SM2Toolkit();
			InputStream is = SM2SignUtil.class.getResourceAsStream("/" + pukName);
			if (is == null) {
				ToolsHelper.throwException("数据流为空。");
			}
			byte[] publicByte = IoUtil.readBytes(is);
			SM2PublicKey sm2MerPublicKey = (SM2PublicKey)sm2Tool.SM2BuildPublicKey(BASE64Toolkit.encode(publicByte));
			result = sm2Tool.SM2Verify(sm2MerPublicKey, srcString.getBytes(encoding), BASE64Toolkit.decode(signValue));
			return result;
		} catch (Exception var9) {
			throw var9;
		}
	}

	public static String encryptString(String pukName, String needEncryptData, String encoding) throws Exception {
		String encryptResult;

		try {
			SM2Toolkit sm2Tool = new SM2Toolkit();
			InputStream is = SM2SignUtil.class.getResourceAsStream("/" + pukName);
			if (is == null) {
				ToolsHelper.throwException("数据流为空。");
			}
			byte[] publicByte = IoUtil.readBytes(is);
			SM2PublicKey sm2MerPublicKey = (SM2PublicKey)sm2Tool.SM2BuildPublicKey(BASE64Toolkit.encode(publicByte));
			encryptResult = BASE64Toolkit.encode(sm2Tool.SM2EncryptData(sm2MerPublicKey, needEncryptData.getBytes(encoding)));
			return encryptResult;
		} catch (Exception var8) {
			System.out.println("公钥加密失败");
			throw var8;
		}
	}

	public static String decryptString(String pvkName, String encString, String encoding) throws Exception {
		String decryptResult;

		try {
			SM2Toolkit sm2Tool = new SM2Toolkit();
			InputStream is = SM2SignUtil.class.getResourceAsStream("/" + pvkName);
			if (is == null) {
				ToolsHelper.throwException("数据流为空。");
			}
			byte[] privateByte = IoUtil.readBytes(is);
			SM2PrivateKey sm2PrivateKey = (SM2PrivateKey)sm2Tool.SM2BuildPrivateKey(BASE64Toolkit.encode(privateByte));
			decryptResult = new String(sm2Tool.SM2DecryptData(sm2PrivateKey, BASE64Toolkit.decode(encString)), encoding);
			return decryptResult;
		} catch (Exception var8) {
			System.out.println("私钥解密失败");
			throw var8;
		}
	}

	public static byte[] read(String filePath) throws IOException {
		if (filePath == null) {
			throw new IllegalArgumentException("Illegal Argument: filePath");
		} else {
			FileInputStream crls = null;

			try {
				crls = new FileInputStream(filePath);
				byte[] out = new byte[crls.available()];
				byte[] buffer = new byte[65536];

				int rLength;
				for(int offset = 0; (rLength = crls.read(buffer, 0, buffer.length)) != -1; offset += rLength) {
					System.arraycopy(buffer, 0, out, offset, rLength);
				}

				byte[] abyte0 = out;
				return abyte0;
			} catch (IOException var14) {
				throw var14;
			} finally {
				if (crls != null) {
					try {
						crls.close();
					} catch (Exception var13) {
					}
				}

			}
		}
	}
}
