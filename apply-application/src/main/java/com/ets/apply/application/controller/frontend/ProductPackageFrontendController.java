package com.ets.apply.application.controller.frontend;

import com.ets.apply.application.app.business.ProductPackageBusiness;
import com.ets.apply.application.common.dto.productPackage.GetInfoByPackageSnsDTO;
import com.ets.apply.application.common.vo.productPackage.ProductPackageGetInfoByPackageSnsVo;
import com.ets.common.JsonResult;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/frontend/product-package")
public class ProductPackageFrontendController {

    @Autowired
    private ProductPackageBusiness productPackageBusiness;
    @RequestMapping("/get-info-by-package-sns")
    public JsonResult<List<ProductPackageGetInfoByPackageSnsVo>> getInfoByPackageSns(@RequestBody @Valid GetInfoByPackageSnsDTO dto) {
        return JsonResult.ok(productPackageBusiness.getInfoByPackageSns(dto));
    }

}
