package com.ets.apply.application.common.bo.monitor;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ApplyOrderMonitorBO {

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    private Integer lastDays;

    private String sendKey;

    /**
     * 平均取数天数
     */
    private Integer averageDays;

    private LocalDateTime averageStartTime;

    private LocalDateTime averageEndTime;

}
