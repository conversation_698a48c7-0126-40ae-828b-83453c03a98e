package com.ets.apply.application.common.dto.request.splitFlow;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import jakarta.validation.constraints.NotBlank;
import java.time.LocalDateTime;

@Data
public class AddSplitFlowConfigDTO {

    /**
     * 分流名称
     */
    @Length(min = 1, max = 20, message = "分流名称请输入50个以内字符")
    private String splitName;

    /**
     * 分流类型[etc_2.0_before_pay-2.0支付前]
     */
    @Length(min = 1, max = 20, message = "分流类型请输入20个以内字符")
    private String splitType;

    /**
     * 分流的周期：20210420
     */
    @Length(min = 1, max = 20, message = "分流周期请输入20个以内字符")
    private String splitTerm;

    /**
     * 读取结果需要传入的参数 [uid]
     */
    @NotBlank(message = "传入的参数不可为空")
    private String getResultParams;

    /**
     * 分流规则，白名单，正常分流
     */
    @NotBlank(message = "分流规则不可为空")
    private String splitRule;

    /**
     * 默认结果
     */
    private String defaultResult;

    /**
     * 分流结果返回附带参数
     */
    private String resultReturnParams;

    /**
     * 过期时间
     */
    private LocalDateTime expireDate;


    /**
     * 描述
     */
    private String splitDesc;


    private String operator;
}
