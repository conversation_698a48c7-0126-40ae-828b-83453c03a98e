package com.ets.apply.application.app.thirdservice.feign;

import com.ets.apply.application.common.vo.adminOrder.MemberPhoneInfoVO;
import com.ets.common.JsonResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 会员
 */
@FeignClient(
        url = "${microUrls.member:http://etc-member-pro-core:22010}",
        name = "etc-member-pro-core", contextId = "CallMemberFeign")
public interface CallMemberFeign {

    /**
     * 根据手机号获取用户信息
     * @param phone 手机号
     * @return 用户信息列表
     */
    @GetMapping("/user/memberInfoByPhone")
    JsonResult<List<MemberPhoneInfoVO>> getMemberInfoByPhone(@RequestParam(value = "phone") String phone);

}
