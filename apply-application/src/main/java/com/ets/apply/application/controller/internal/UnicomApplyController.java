package com.ets.apply.application.controller.internal;

import com.ets.apply.application.app.business.unicom.UnicomApplyBusiness;
import com.ets.apply.application.common.dto.unicom.UnicomBusinessOrderCancelDTO;
import com.ets.apply.application.common.dto.unicom.UnicomChooseNumberDTO;
import com.ets.apply.application.common.dto.unicom.UnicomSyncOrderStateDTO;
import com.ets.apply.application.common.vo.unicom.UnicomChooseNumberVO;
import com.ets.apply.application.controller.BaseController;
import com.ets.common.JsonResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/unicomApply")
public class UnicomApplyController extends BaseController {

    @Autowired
    private UnicomApplyBusiness unicomApplyBusiness;

    @PostMapping("/chooseNumberTest")
    public JsonResult<UnicomChooseNumberVO> chooseNumberTest(
            @RequestParam(value = "phone") String phone,
            @RequestParam(value = "rule") String rule,
            @RequestParam(value = "isNoFour") Boolean isNoFour,
            @RequestParam(value = "isGoodEnd") Boolean isGoodEnd,
            @RequestParam(value = "searchNumber") String searchNumber
    ) {

        UnicomChooseNumberDTO dto = new UnicomChooseNumberDTO();
        dto.setRule(rule);
        dto.setIsGoodEnd(isGoodEnd);
        dto.setIsNoFour(isNoFour);
        dto.setSearchNumber(searchNumber);

        return JsonResult.ok(unicomApplyBusiness.getNumberVO(phone, dto));
    }

    @PostMapping("/businessOrderCancel")
    public JsonResult<Object> businessOrderCancel(@RequestBody @Valid UnicomBusinessOrderCancelDTO dto) {

        unicomApplyBusiness.businessOrderCancel(dto);

        return JsonResult.ok();
    }

    @PostMapping("/syncOrderState")
    public JsonResult<Object> syncOrderState(@RequestBody @Valid UnicomSyncOrderStateDTO dto) {

        if (dto.getSyncUnicom()) {
            unicomApplyBusiness.doSyncOrder(dto.getUnicomApplySn());
        } else {
            unicomApplyBusiness.syncOrderByData(dto.getUnicomApplySn(), dto.getRow());
        }

        return JsonResult.ok();
    }

    @PostMapping("/asyncSelectSumber")
    public JsonResult<Object> asyncSelectSumber(
            @RequestParam(value = "keyIndex") String keyIndex,
            @RequestParam(value = "pageCount") Integer pageCount
    ) {
        unicomApplyBusiness.asyncSelectNumber(keyIndex, pageCount);

        return JsonResult.ok();
    }

    @PostMapping("/getListFromRedis")
    public JsonResult<List<String>> getListFromRedis(
            @RequestParam(value = "keyIndex") String keyIndex
    ) {
        List<String> list;
        if (StringUtils.isNotEmpty(keyIndex)) {
            list = unicomApplyBusiness.getListFromRedisByIndex(keyIndex);
        } else {
            list = unicomApplyBusiness.getListFromRedis();
        }

        return JsonResult.ok(list);
    }

}
