package com.ets.apply.application.common.dto.external;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class ExternalBindDTO {

    /**
     * 验证码
     */
    @NotBlank(message = "验证码不能为空")
    private String verifyCode;

    @NotBlank(message = "手机号不能为空")
    private String phone;

    @NotBlank(message = "车牌号不能为空")
    private String plateNo;

    private Integer plateColor;

}
