package com.ets.apply.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ets.apply.application.infra.entity.splitFlow.SplitFlowEntity;
import com.ets.apply.application.infra.entity.splitFlow.SplitFlowLogEntity;
import com.ets.apply.application.infra.mapper.SplitFlowLogMapper;
import com.ets.apply.application.infra.mapper.SplitFlowMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 映射组合规则 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-27
 */
@Service
@DS("db-apply")
public class SplitFlowLogService extends BaseService<SplitFlowLogMapper, SplitFlowLogEntity> {

    public void addLog(Integer splitFlowId,String type,String content,String operator) {
        SplitFlowLogEntity splitFlowLog = new SplitFlowLogEntity();
        splitFlowLog.setSplitFlowId(splitFlowId);
        splitFlowLog.setType(type);
        splitFlowLog.setContent(content);
        splitFlowLog.setOperator(operator);
        this.create(splitFlowLog);
    }
}
