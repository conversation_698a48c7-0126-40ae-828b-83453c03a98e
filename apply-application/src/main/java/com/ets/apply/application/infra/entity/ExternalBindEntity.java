package com.ets.apply.application.infra.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 外部渠道订单绑定表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-02
 */
@Getter
@Setter
@TableName("etc_external_bind")
public class ExternalBindEntity extends BaseEntity<ExternalBindEntity> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 渠道类型：50行致传祺,同users表
     */
    private Integer thirdType;

    /**
     * 绑定编号
     */
    private String bindSn;

    /**
     * 绑定订单号（商城订单/商品订单）
     */
    private String bindOrderSn;

    /**
     * 绑定uid，冗余字段
     */
    private Long bindUid;

    /**
     * 外部用户唯一标识
     */
    private String externalUnionId;

    /**
     * 状态 1正常，9取消
     */
    private Integer status;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
}
