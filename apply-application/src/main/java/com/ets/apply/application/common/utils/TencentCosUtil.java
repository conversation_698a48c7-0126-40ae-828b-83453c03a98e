package com.ets.apply.application.common.utils;


import com.ets.apply.application.common.config.CosFrontendConfig;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.model.ObjectMetadata;
import com.qcloud.cos.model.PutObjectRequest;
import com.qcloud.cos.model.PutObjectResult;
import com.qcloud.cos.model.StorageClass;
import com.qcloud.cos.region.Region;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.io.*;


@Data
@Component
@Slf4j
@RefreshScope
public class TencentCosUtil {

    @Autowired
    private CosFrontendConfig cosFrontendConfig;
//    //腾讯云的SecretId
//    public String secretId;
//    //腾讯云的SecretKey
//    public String secretKey;
//    //腾讯云的bucket (存储桶)
//    public String bucket;
//    //腾讯云的region(bucket所在地区)
//    public String region;
//    //腾讯云的allowPrefix(允许上传的路径)
//    public String allowPrefix;
//    //腾讯云的临时密钥时长(单位秒)
//    public String durationSeconds;
//    //腾讯云的访问基础链接:
//    public String baseUrl;
//    //腾讯云上传后的读取地址(CDN加速)
//    public String readUrl;
//    //根目录
//    public String rootDir;
//    //视频根目录
//    public String rootVideoDir;
//    //视频格式
//    private List<String> videoFormat;

    /**
     * 上传腾讯云
     *
     * @param bytes    文件字节
     * @param filePath 文件路径
     * @return 腾讯云访问路径
     */
    public String upload(byte[] bytes, String filePath) {
        // 1 初始化秘钥信息
        COSCredentials cred = new BasicCOSCredentials(cosFrontendConfig.getSecretId(), cosFrontendConfig.getSecretKey());
        // 2 设置bucket的区域
        ClientConfig clientConfig = new ClientConfig(new Region(cosFrontendConfig.getRegion()));
        // 3 生成cos客户端
        COSClient cosClient = new COSClient(cred, clientConfig);

        // 处理文件路径
        filePath = filePath.startsWith("/") ? filePath : "/" + filePath;

        // 判断文件大小（小文件上传建议不超过20M）
        int length = bytes.length;
        // 获取文件流
        InputStream byteArrayInputStream = new ByteArrayInputStream(bytes);
        ObjectMetadata objectMetadata = new ObjectMetadata();
        // 从输入流上传必须制定content length, 否则http客户端可能会缓存所有数据，存在内存OOM的情况
        objectMetadata.setContentLength(length);
        // 默认下载时根据cos路径key的后缀返回响应的contenttype, 上传时设置contenttype会覆盖默认值
        // objectMetadata.setContentType("image/jpeg");

        PutObjectRequest putObjectRequest = new PutObjectRequest(cosFrontendConfig.getBucket(), filePath, byteArrayInputStream, objectMetadata);
        // 设置 Content type, 默认是 application/octet-stream
        putObjectRequest.setMetadata(objectMetadata);
        // 设置存储类型, 默认是标准(Standard), 低频(standard_ia)
        putObjectRequest.setStorageClass(StorageClass.Standard);
        PutObjectResult putObjectResult = cosClient.putObject(putObjectRequest);

        String eTag = putObjectResult.getETag();
        System.out.println(eTag);
        // 关闭客户端
        cosClient.shutdown();
        // http://{buckname}-{appid}.cosgz.myqcloud.com/image/1545012027692.jpg
        return cosFrontendConfig.getReadUrl() + filePath;
    }

    /**
     * 上传腾讯云
     *
     * @param file     文件
     * @param filePath 文件路径
     * @return 腾讯云访问路径
     */
    public String upload(File file, String filePath) {
        // 1 初始化秘钥信息
        COSCredentials cred = new BasicCOSCredentials(cosFrontendConfig.getSecretId(),cosFrontendConfig.getSecretKey());
        // 2 设置bucket的区域, COS地域的简称请参照 https://www.qcloud.com/document/product/436/6224
        ClientConfig clientConfig = new ClientConfig(new Region(cosFrontendConfig.getRegion()));
        // 3 生成cos客户端
        COSClient cosClient = new COSClient(cred, clientConfig);

        // 处理文件路径
        filePath = filePath.startsWith("/") ? filePath : "/" + filePath;

        // 判断文件大小（小文件上传建议不超过20M）
        byte[] bytes = File2byte(file);
        int length = bytes.length;
        // 获取文件流
        InputStream byteArrayInputStream = new ByteArrayInputStream(bytes);
        ObjectMetadata objectMetadata = new ObjectMetadata();
        // 从输入流上传必须制定content length, 否则http客户端可能会缓存所有数据，存在内存OOM的情况
        objectMetadata.setContentLength(length);
        // 默认下载时根据cos路径key的后缀返回响应的contenttype, 上传时设置contenttype会覆盖默认值
        // objectMetadata.setContentType("image/jpeg");

        PutObjectRequest putObjectRequest = new PutObjectRequest(cosFrontendConfig.getBucket(), filePath, byteArrayInputStream, objectMetadata);
        // 设置 Content type, 默认是 application/octet-stream
        putObjectRequest.setMetadata(objectMetadata);
        // 设置存储类型, 默认是标准(Standard), 低频(standard_ia)
        putObjectRequest.setStorageClass(StorageClass.Standard);
        PutObjectResult putObjectResult = cosClient.putObject(putObjectRequest);

        String eTag = putObjectResult.getETag();
        System.out.println(eTag);
        // 关闭客户端
        cosClient.shutdown();
        // http://{buckname}-{appid}.cosgz.myqcloud.com/image/1545012027692.jpg
        return cosFrontendConfig.getReadUrl() + filePath;
    }

    public byte[] File2byte(File tradeFile) {
        byte[] buffer = null;
        try {
            FileInputStream fis = new FileInputStream(tradeFile);
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            byte[] b = new byte[1024];
            int n;
            while ((n = fis.read(b)) != -1) {
                bos.write(b, 0, n);
            }
            fis.close();
            bos.close();
            buffer = bos.toByteArray();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return buffer;
    }


}
