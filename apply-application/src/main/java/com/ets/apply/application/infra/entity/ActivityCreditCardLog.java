package com.ets.apply.application.infra.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 信用卡申办记录操作日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("etc_activity_credit_card_log")
public class ActivityCreditCardLog extends BaseEntity<ActivityCreditCardLog> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 银行流水号
     */
    private String orderSn;

    /**
     * 操作类型
     */
    private String operateType;

    /**
     * 操作内容
     */
    private String operateContent;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;


}
