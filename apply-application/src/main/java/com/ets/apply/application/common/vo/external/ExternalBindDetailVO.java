package com.ets.apply.application.common.vo.external;

import lombok.Data;

@Data
public class ExternalBindDetailVO {

    // 绑定编号
    private String bindSn;

    // 请求地址前缀
    private String urlPrefix;

    // 车牌号
    private String plateNo;

    private Integer plateColor;

    // 申办订单号
    private String applyOrderSn;

    // 是否有usercard信息
    private Boolean hasUserCard;

    /**
     * 卡号
     */
    private String cardNo;

    /**
     * 发行方
     */
    private String issuerName;

    /**
     * 质保期
     */
    private String warrantyExpire;

    /**
     * issuer的省份名称
     */
    private String province;

    /**
     * 是否已激活
     */
    private Boolean isActivated;


}
