package com.ets.apply.application.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Map;

@RefreshScope
@Configuration
@Data
@ConfigurationProperties(prefix = "params")
public class LoginCodeLimitsConfig {
    //市场部受限账号
    private List<String> limitUserLoginCodes;

    //市场部受限账号对应sources值,暂时只有一个渠道
    private String limitUserSource;

    // 渠道source：[受限用户]
    private Map<String, List<String>> limitSourceUserMap;
}
