package com.ets.apply.application.controller.internal;

import com.ets.apply.application.app.business.TaskRecordBusiness;
import com.ets.apply.application.app.factory.task.TaskFactory;
import com.ets.apply.application.common.consts.taskRecord.ReferTypeEnum;
import com.ets.apply.application.common.consts.taskRecord.StatusEnum;
import com.ets.apply.application.common.dto.task.AddTaskDTO;
import com.ets.apply.application.common.dto.task.TaskRecordDTO;
import com.ets.apply.application.controller.BaseController;
import com.ets.apply.application.infra.entity.TaskRecordEntity;
import com.ets.apply.application.infra.service.TaskRecordService;
import com.ets.apply.application.common.dto.request.TaskRecordExecDto;
import com.ets.common.BizException;
import com.ets.common.JsonResult;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.web.bind.annotation.*;
import com.ets.apply.application.common.dto.request.TaskRecordQueryDto;
import com.ets.apply.application.common.vo.TaskRecordQueryVO;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;


/**
 * <p>
 * 任务管理列表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-29
 */
@RequestMapping("/taskRecord")
@RefreshScope
@RestController
@Slf4j
public class TaskRecordController extends BaseController {

    @Autowired
    private TaskRecordBusiness taskRecordBusiness;

    @Autowired
    private TaskRecordService taskRecordService;

    /**
     * 根据任务流水号,任务类型,关联sn,状态获取task列表
     * @param taskRecordQueryDto 查询条件
     * @return JsonResult
     * @throws BizException 异常
     */
    @RequestMapping("/getList")
    public JsonResult<IPage<TaskRecordQueryVO>> getList(@RequestBody TaskRecordQueryDto taskRecordQueryDto) throws BizException {

        IPage<TaskRecordQueryVO> page = taskRecordBusiness.getTaskRecordList2(taskRecordQueryDto);
        return JsonResult.ok(page);
    }

    /**
     * 获取筛选项数组
     * @return JsonResult
     * @throws BizException 异常
     */
    @RequestMapping("/getSelectOptions")
    public JsonResult getSelectOptions() throws BizException {
        Map<Integer,String> statusMap = new HashMap<Integer,String>();
        for (StatusEnum value : StatusEnum.values()) {
            statusMap.put(value.getCode(),value.getDescription());
        }
        //创建map对象
        Map<String,Object> map = new HashMap<String,Object>();
        //给Map中添加元素
        map.put("refer_type",ReferTypeEnum.getMap());
        map.put("status",statusMap);
        return JsonResult.ok(map);
    }
    /*
        直接执行task
     */
    @RequestMapping("/exec")
    public JsonResult<Boolean> exec(@RequestBody TaskRecordExecDto dto) throws BizException {

        // 执行任务
        TaskFactory.create(dto.getReferType()).execute(dto.getTaskSn(),true);

        return JsonResult.ok();
    }

    @PostMapping("/execute")
    public JsonResult<Object> execute(
            @RequestParam(value = "taskSn") String taskSn
    ) {
        log.info("execute taskSn:{}", taskSn);
        TaskRecordEntity taskRecord = taskRecordService.getOneByTaskSn(taskSn);
        // 执行任务
        TaskFactory.create(taskRecord.getReferType()).execute(taskRecord.getTaskSn(),true);

        return JsonResult.ok();
    }
    @PostMapping("/addTask")
    public JsonResult<Object> addTask(@RequestBody @Valid AddTaskDTO dto) {

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime nextExecTime = LocalDateTime.parse(dto.getNextExecTime(), formatter);

        TaskRecordDTO taskRecordDTO = new TaskRecordDTO();
        taskRecordDTO.setReferSn(dto.getReferSn());
        taskRecordDTO.setReferType(dto.getReferType());
        taskRecordDTO.setNextExecTime(nextExecTime);
        taskRecordDTO.setNotifyContent(dto.getNotifyContent());
        TaskFactory.create(dto.getReferType()).addAndPush(taskRecordDTO);
        return JsonResult.ok();
    }
}

