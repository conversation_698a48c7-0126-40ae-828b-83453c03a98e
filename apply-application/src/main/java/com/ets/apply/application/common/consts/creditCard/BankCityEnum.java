package com.ets.apply.application.common.consts.creditCard;

import com.ets.apply.application.common.consts.activityCreditCardUsersInfo.ActivityCreditCardUserInfoWhichBankConstant;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

@Getter
@AllArgsConstructor
public enum BankCityEnum {
    PING_AN_CITY(ActivityCreditCardUserInfoWhichBankConstant.PING_AN_INTERFACE, CityConstant.CITY_PAB_LIST, "平安银行办理地址"),
    GUANG_FA_CITY(ActivityCreditCardUserInfoWhichBankConstant.GUANG_FA_INTERFACE, CityConstant.CITY_GDB_LIST,
            "广发银行办理地址"),
    JIAO_TONG_CITY(ActivityCreditCardUserInfoWhichBankConstant.JIAO_TONG, CityConstant.CITY_COMM_LIST, "交通银行办理地址"),
    CITIC_CITY(ActivityCreditCardUserInfoWhichBankConstant.CITIC, CityConstant.CITY_CITIC_LIST, "中信银行办理地址"),
    CMBC_CITY(ActivityCreditCardUserInfoWhichBankConstant.CMBC, CityConstant.CITY_CMBC_LIST, "中国民生银行办理地址"),
    SPD_CITY(ActivityCreditCardUserInfoWhichBankConstant.SPD, CityConstant.CITY_SPD_LIST, "浦发银行办理地址"),
    CEB_CITY(ActivityCreditCardUserInfoWhichBankConstant.CEB, CityConstant.CITY_CEB_LIST, "中国光大银行办理地址"),
    ;
    private final int code;
    private final List<String> cityList;
    private final String name;

    public static BankCityEnum getByCode(int code) {
        for (BankCityEnum bankCityEnum : BankCityEnum.values()) {
            if (bankCityEnum.code == code) {
                return bankCityEnum;
            }
        }
        return null;
    }

}
