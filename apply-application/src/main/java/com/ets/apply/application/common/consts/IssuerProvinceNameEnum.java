package com.ets.apply.application.common.consts;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


@Getter
@AllArgsConstructor
public enum IssuerProvinceNameEnum {
    //  广东；北京；江苏；陕西；河南；上海；福建；湖南；云南；浙江；四川；河北；贵州；湖北；天津；重庆；山西；宁夏；甘肃；青海；西藏；辽宁；内蒙古；广西；安徽；吉林；江西；黑龙江；新疆；山东
    GUANGDONG("广东"),
    BEIJING("北京"),
    JIANGSU("江苏"),
    SHANXI("陕西"),
    HENAN("河南"),
    SHANGHAI("上海"),
    FUJIAN("福建"),
    HUNAN("湖南"),
    YUNNAN("云南"),
    ZHEJIANG("浙江"),
    SICHUAN("四川"),
    HEBEI("河北"),
    GUIZHOU("贵州"),
    HUBEI("湖北"),
    TIANJIN("天津"),
    CHONGQING("重庆"),
    SHANXI2("山西"),
    NINGXIA("宁夏"),
    GANSU("甘肃"),
    QINGHAI("青海"),
    XIZANG("西藏"),
    LIAONING("辽宁"),
    NEIMENGGU("内蒙古"),
    GUANGXI("广西"),
    ANHUI("安徽"),
    JILIN("吉林"),
    JIANGXI("江西"),
    HEILONGJIANG("黑龙江"),
    XINJIANG("新疆"),
    SHANDONG("山东"),
    ;

    private final String value;

    public static final List<String> list;
    static {
        list = Arrays.stream(IssuerProvinceNameEnum.values()).map(IssuerProvinceNameEnum::getValue).collect(Collectors.toList());
    }

}
