package com.ets.apply.application.app.service.bank;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.ets.apply.application.common.config.creditBank.CommCreditBankConfig;
import com.ets.apply.application.common.dto.request.bank.comm.CommApplyOrderDto;
import com.ets.common.BizException;
import com.ets.common.ToolsHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.MessageDigest;
import java.util.*;


@Component
@Slf4j
public class CommService {
    @Autowired
    private CommCreditBankConfig commBankConfig;

    private static final String ALGORITHM_MD5 = "MD5";
    private static final String CHARSET_UTF_8 = "UTF-8";

    /**
     * 获取交通银行申请链接
     *
     * @return String
     */
    public String getApplyUrl(CommApplyOrderDto commApplyOrderDto) throws BizException {
        try {
            Map<String,Object> macMapAttach = new HashMap<>();
            macMapAttach.put("applyEmail","");
            Map<String,Object> macMap = new LinkedHashMap<>();
            macMap.put("requestId",commApplyOrderDto.getRequestId());
            macMap.put("requestTime",System.currentTimeMillis()/1000);
            macMap.put("serialNum",commApplyOrderDto.getRequestId());
            macMap.put("callBackURL",commApplyOrderDto.getCallBackURL());
            macMap.put("notifyURL",commBankConfig.getNotifyUrl());
            macMap.put("attach",macMapAttach);
            String content = URLEncoder.encode(JSON.toJSONString(macMap, SerializerFeature.SortField),"UTF-8");
            log.info(macMap.toString());
            log.info(content);

            String data = Encrypt(content,commBankConfig.getAesKey());
            String sign = md5(data+commBankConfig.getMd5KEY());
            Map<String,String> paramsMap = new HashMap<>();
            paramsMap.put("clientId",commBankConfig.getClientId());
            paramsMap.put("trackCode",commBankConfig.getTrackCode());
            paramsMap.put("data",data);
            paramsMap.put("sign",sign);

            log.info(paramsMap.toString());

            String url = commBankConfig.getApplyUrl()+"?"+ksortAndBuild(paramsMap);
            return url;

        } catch (Exception e) {
            ToolsHelper.throwException("当前办理方式不可用，请稍后再试");
        }
        return null;
    }


    // 加密
    public static String Encrypt(String sSrc, String sKey) throws Exception {
        if (sKey == null) {
            System.out.print("Key为空null");
            return null;
        }
        // 判断Key是否为16位
        if (sKey.length() != 16) {
            System.out.print("Key长度不是16位");
            return null;
        }
        byte[] raw = sKey.getBytes("utf-8");
        SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");// "算法/模式/补码方式"
        cipher.init(Cipher.ENCRYPT_MODE, skeySpec);
        byte[] encrypted = cipher.doFinal(sSrc.getBytes("utf-8"));

        return bytesToHexString(encrypted);
    }

    // 解密
    public static String Decrypt(String sSrc, String sKey) throws Exception {
        try {
            // 判断Key是否正确
            if (sKey == null) {
                System.out.print("Key为空null");
                return null;
            }
            // 判断Key是否为16位
            if (sKey.length() != 16) {
                System.out.print("Key长度不是16位");
                return null;
            }
            byte[] raw = sKey.getBytes("utf-8");
            SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");

            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE, skeySpec);

            try {
                byte[] encrypted = cipher.doFinal(hexStringToBytes(sSrc));

                String originalString = new String(encrypted);
                return originalString;
            } catch (Exception e) {
                System.out.println(e.toString());
                return null;
            }
        } catch (Exception ex) {
            System.out.println(ex.toString());
            return null;
        }
    }

    public static String bytesToHexString(byte[] src) {
        StringBuilder stringBuilder = new StringBuilder("");
        if (src == null || src.length <= 0) {
            return null;
        }
        for (int i = 0; i < src.length; i++) {
            int v = src[i] & 0xFF;
            String hv = Integer.toHexString(v);
            if (hv.length() < 2) {
                stringBuilder.append(0);
            }
            stringBuilder.append(hv);
        }
        return stringBuilder.toString();
    }

    public static byte[] hexStringToBytes(String hexString) {
        if (hexString == null || hexString.equals("")) {
            return null;
        }
        hexString = hexString.toUpperCase();
        int length = hexString.length() / 2;
        char[] hexChars = hexString.toCharArray();
        byte[] d = new byte[length];
        for (int i = 0; i < length; i++) {
            int pos = i * 2;
            d[i] = (byte) (charToByte(hexChars[pos]) << 4 | charToByte(hexChars[pos + 1]));
        }
        return d;
    }

    private static byte charToByte(char c) {
        return (byte) "0123456789ABCDEF".indexOf(c);
    }

    public static String md5(String source) {
        try {
            MessageDigest md5 = MessageDigest.getInstance(ALGORITHM_MD5);
            byte[] md5byte = md5.digest(source.getBytes(CHARSET_UTF_8));
            StringBuffer strHexString = new StringBuffer();
            for (int i = 0; i < md5byte.length; i++) {
                String hex = Integer.toHexString(0xff & md5byte[i]);
                if (hex.length() == 1) {
                    strHexString.append('0');
                }
                strHexString.append(hex);
            }
            return strHexString.toString();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

    }


    /*
     *  排序并且构造签名字符串
     */
    private static String ksortAndBuild(Map<String,String> macMap) throws UnsupportedEncodingException {
        Set<String> keySet = macMap.keySet();
        String[] keyArray = keySet.toArray(new String[0]);
        //Arrays.sort(keyArray);
        StringBuilder sb = new StringBuilder();
        for(int i =0; i<keyArray.length;i++){
            if(String.valueOf(macMap.get(keyArray[i])).length()>0){
                String values = URLEncoder.encode(macMap.get(keyArray[i]),"UTF-8");
                if((i+1) == keyArray.length ){
                    sb.append(keyArray[i]).append("=").append(values);
                }else{
                    sb.append(keyArray[i]).append("=").append(values).append("&");
                }

            }
        }
        return sb.toString();
    }


}
