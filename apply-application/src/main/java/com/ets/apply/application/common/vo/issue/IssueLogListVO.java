package com.ets.apply.application.common.vo.issue;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class IssueLogListVO {

    private Integer id;

    /**
     * 商品订单号
     */
    private String serviceSn;

    /**
     * 日志内容
     */
    private String content;

    private String logTypeStr;

    /**
     * 操作人
     */
    private String operateUser;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

}
