package com.ets.apply.application.infra.entity;

import java.io.Serializable;
import java.math.BigDecimal;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import java.util.LinkedHashMap;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>
 * 商品套餐配置表待发布
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("product_package_tmp")
public class ProductPackageTmpEntity extends BaseEntity<ProductPackageTmpEntity> {

    private static final long serialVersionUID = 1L;
    @Override
    public Serializable pkVal() {
        return this.packageSn;
    }
    /**
     * 商品套餐流水号
     */
    private String packageSn;

    /**
     * 商品套餐名称
     */
    private String packageName;

    /**
     * 商品套餐信息,json格式
     */
    private String packageInfo;

    /**
     * 发布状态：1待发布2已发布
     */
    private Integer releaseStatus;

    /**
     * 商品套餐㽽[1-有效 2-无效]
     */
    private Integer status;

    /**
     * 设备费（元）
     */
    private BigDecimal packageFee;

    /**
     * 库存预警值
     */
    private Integer alarmStock;

    /**
     * 有效期
     */
    private LocalDateTime expireDate;

    /**
     * 是否货车[0-客车 1-货车]
     */
    private Integer isTruck;

    /**
     * 发货方式[1-线上发货 2-网点自提 3-线上发货，网点自提]
     */
    private Integer deliveryType;

    /**
     * 自提是否自动发货[0-手动发货 1-自动发货]
     */
    private Integer offlineAutoDelivery;

    /**
     * 备注
     */
    private String remark;

    /**
     * 来源流水号
     */
    private String sourceSn;

    /**
     * 是否基础包[0-不是 1-是]
     */
    private Integer isBase;

    /**
     * 产品渠道
     */
    private String source;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    /**
     * 第三方数据字段
     */
    private String thirdData;

    /**
     * 是否展示“新增”功能，主要用于后台控制输出
     */
    private Integer isShowAdd;

    /**
     * 排序值
     */
    private Double sort;

    /**
     * 销售状态[0-不能销售 1-允许销售]
     */
    private Integer saleStatus;

    /**
     * 业务类型 [1-客车新办 2-客车重办 3-货车新办 4-地推 5-企业服务 6-广告 7-前装 8-电商 9-设备升级-注销重办 10-换车换牌-注销重办 11-非我司设备升级-注销重办 12-设备保障]
     */
    private Integer bizType;

    /**
     * 申办配置
     */
    private String applyConfig;

    /**
     * 售后配置
     */
    private String aftersaleConfig;

    /**
     * 前端配置
     */
    private String frontedConfig;

    /**
     * 前端页面配置
     */
    private String pageConfig;

    /**
     * 营销配置
     */
    private String marketConfig;

    /**
     * 发布上线后除了前端样式其他不允许修改
     */
    private Integer allowModify;

    /**
     * 发货地址映射规则
     */
    private Integer addressConfigId;

    private Integer templateId;

    public JSONObject getMarketConfigObj() {

        if (StringUtils.isEmpty(marketConfig)) {
            return new JSONObject(new LinkedHashMap<>());
        } else {
            return JSON.parseObject(marketConfig, Feature.OrderedField);
        }
    }
}
