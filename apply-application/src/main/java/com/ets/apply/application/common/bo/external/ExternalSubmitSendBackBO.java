package com.ets.apply.application.common.bo.external;

import com.ets.common.annotation.PhoneAnnotation;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

@Data
public class ExternalSubmitSendBackBO {

    @NotBlank(message = "业务订单号不能为空")
    private String businessSn;

    private Integer businessType = 2;

    /**
     * 寄回方式
     */
    @NotNull(message = "请选择寄回方式")
    private Integer sendbackType;

    private Long uid;

    /* 寄回件相关参数 ****/

    /**
     * 回寄快递单号
     */
    @Length(max = 50, message = "快递单号长度不能超过50个字符")
    private String sendbackExpressNumber;

    /**
     * 回寄快递公司
     */
    @Length(max = 10, message = "快递公司长度不能超过10个字符")
    private String sendbackExpressCompany = "默认";

    /**
     * 寄件人联系电话
     */
    @PhoneAnnotation
    private String sendbackPhone;


}
