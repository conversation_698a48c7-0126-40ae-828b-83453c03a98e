package com.ets.apply.application.common.consts.productPackageResource;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum ResourceStatusEnum {

    LISTING(1,"上架"),
    DELIST(2,"下架");

    private final Integer status;

    private final String description;

    public static String getDescByCode(Integer code) {
        for (ResourceStatusEnum node : ResourceStatusEnum.values()) {
            if (node.getStatus().equals(code)) {
                return node.getDescription();
            }
        }

        return "";
    }
}
