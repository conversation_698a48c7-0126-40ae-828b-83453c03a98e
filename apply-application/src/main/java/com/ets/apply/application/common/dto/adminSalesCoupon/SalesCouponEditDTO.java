package com.ets.apply.application.common.dto.adminSalesCoupon;


import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;


@Data
public class SalesCouponEditDTO {
    private Integer id;
    @NotNull(message = "planName不可为空")
    private String planName = "";
    private Integer department;
    private Integer type;
    private List<Object> typeValues;
    private List<Object> assignPackageSn;
    private List<Object> excludePackageSn;
    private List<Object> canUseCouponBatchNo;



}
