package com.ets.apply.application.app.job;

import com.alibaba.fastjson.JSONObject;
import com.ets.apply.application.app.business.creditCard.CreditCardRewardBusiness;
import com.ets.apply.application.common.bo.creditCard.CreditCardRewardBO;
import com.qcloud.cos.utils.StringUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Component
public class CreditCardRewardJob {

    @Autowired
    CreditCardRewardBusiness creditCardRewardBusiness;

    /**
     * http://wiki.golcer.cn/pages/viewpage.action?pageId=39584237
     * @param param
     * @return
     */
    @XxlJob("creditCardRewardCoupon")
    public ReturnT<String> creditCardRewardCouponHandler(String param) {
        CreditCardRewardBO creditCardRewardBO = JSONObject.parseObject(param, CreditCardRewardBO.class);
        if (StringUtils.isNullOrEmpty(creditCardRewardBO.getStartTime()) || StringUtils.isNullOrEmpty(creditCardRewardBO.getEndTime())) {
            LocalDateTime beginDate = LocalDateTime.now().minusHours(creditCardRewardBO.getBeforeHours());
            LocalDateTime endDate = LocalDateTime.now();
            creditCardRewardBO.setStartTime(beginDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            creditCardRewardBO.setEndTime(endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        creditCardRewardBusiness.reward(creditCardRewardBO);
        return ReturnT.SUCCESS;
    }
}
