package com.ets.apply.feign.feign;

import com.ets.apply.feign.fallback.OrderFeignFallback;
import com.ets.apply.feign.request.FindOneOrderReq;
import com.ets.apply.feign.request.OrderListDTO;
import com.ets.apply.feign.response.OrderAddressResponse;
import com.ets.apply.feign.response.OrderPackageInfoResponse;
import com.ets.apply.feign.response.OrderResponse;
import com.ets.common.JsonResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;


@FeignClient(
        url = "${microUrls.apply:http://apply-application:20070}",
        name = "applyOrderFeign", contextId = "applyOrderFeign", path = "/order", fallback = OrderFeignFallback.class)
public interface OrderFeign {

    @RequestMapping("/getUserValidCarOrdersByUid")
    JsonResult<?> getUserValidCarOrdersByUid(@NotNull(message = "用户Uid不能为空") @RequestParam(value = "uid", required = false) Long uid);

    @RequestMapping("/findOneByPlate")
    JsonResult<OrderResponse> findOneByPlate(@Valid @RequestBody FindOneOrderReq req);

    @RequestMapping("/getOrderByOrderSn")
    JsonResult<OrderResponse> getOrderByOrderSn(@NotBlank(message = "订单号不能为空") @RequestParam(value = "orderSn", required = false) String orderSn);

    @RequestMapping("/getOrderListByPlate")
    JsonResult<List<OrderResponse>> getOrderListByPlate(@Valid @RequestBody OrderListDTO orderListDTO);

    @RequestMapping("/getOrderAddressByOrderSn")
    JsonResult<OrderAddressResponse> getOrderAddressByOrderSn(@NotBlank(message = "订单号不能为空") @RequestParam(value = "orderSn", required = false) String orderSn);

    @PostMapping("/getOrderPackageInfoByOrderSn")
    JsonResult<OrderPackageInfoResponse> getOrderPackageInfoByOrderSn(@NotBlank(message = "订单号不能为空") @RequestParam(value = "orderSn") String orderSn);

}
