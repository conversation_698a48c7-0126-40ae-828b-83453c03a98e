package com.ets.apply.application.common.vo.creditCard;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class CreditCardApplyStatusVO {


    /**
     * 信用卡申请单号
     */
    private String orderSn;

    /**
     * 关联单号
     */
    private String referSn;

    /**
     * 是否首刷
     */
    private Integer isFirstUse;


    /**
     * 是否激活
     */
    private Integer activated;

    /**
     * 我方申请状态
     */
    private Integer status;

    /**
     * 申请信息
     */
    private String message;

    /**
     * 版本号
     */
    private String version;

    /**
     * 银行审核状态
     */
    private Integer bankStatus;

    /**
     * 进件状态
     */
    private Integer applyCompleted;

    /**
     * 审核新户
     */
    private Integer auditNewUser;

    /**
     * 激活新户
     */
    private Integer isNewUser;
}
