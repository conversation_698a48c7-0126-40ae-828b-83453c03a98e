package com.ets.apply.application.controller.frontend;

import com.ets.apply.application.app.business.SplitFlowBusiness;
import com.ets.apply.application.common.dto.request.splitFlow.SplitFlowGetResultDTO;
import com.ets.apply.application.common.utils.UserUtil;
import com.ets.apply.application.common.vo.splitFlow.SplitFlowGetResultVo;
import com.ets.apply.application.controller.BaseController;
import com.ets.common.JsonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

@RestController
@RequestMapping("/frontend/splitFlow")
public class SplitFlowController extends BaseController {

    @Autowired
    SplitFlowBusiness splitFlowBusiness;

    @RequestMapping("/getResult")
    public JsonResult<SplitFlowGetResultVo> getResult(@RequestBody @Valid SplitFlowGetResultDTO dto) {
        dto.setUid(UserUtil.getUid());
        return JsonResult.ok(splitFlowBusiness.getResult(dto));
    }


}
