package com.ets.apply.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.apply.application.infra.entity.ProductPackageTemplateEntity;
import com.ets.apply.application.infra.mapper.ProductPackageTemplateMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 产品包模板 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Service
@DS("db-apply")
public class ProductPackageTemplateService extends BaseService<ProductPackageTemplateMapper, ProductPackageTemplateEntity> {
    /*
     * 检查name是否唯一
     */
    public Boolean checkNameIsValid(String name,Integer id) {
        Wrapper<ProductPackageTemplateEntity> wrapper = Wrappers.<ProductPackageTemplateEntity>lambdaQuery()
                .eq(ProductPackageTemplateEntity::getName, name)
                .eq(ProductPackageTemplateEntity::getStatus, 2)
                .ne(ProductPackageTemplateEntity::getId, id)
                .last("LIMIT 1");
        if(super.baseMapper.selectOne(wrapper) == null){
            return true;
        }
        return false;
    }

}
