package com.ets.apply.application.common.consts.comm;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum ApplyStatusEnum {

    APPLY_STATUS_ZERO("0", "其他"),
    APPLY_STATUS_ONE("1", "申请提交成功"),
    APPLY_STATUS_TWO("2", "未提交"),
    APPLY_STATUS_THREE("3", "系统异常提交失败"),
    APPLY_STATUS_FOUR("4", "已持卡客户"),
    APPLY_STATUS_FIVE("5", "1个月内线上重复申请客户");

    private final String status;
    private final String description;

    public static String getDescByCode(String status) {
        for (ApplyStatusEnum node : ApplyStatusEnum.values()) {
            if (Objects.equals(node.getStatus(), status)) {
                return node.getDescription();
            }
        }

        return "";
    }

    /**
     * 返回对应值的枚举
     */
    public static ApplyStatusEnum getApplyStatusEnumByStatus(String status){
        for(ApplyStatusEnum applyStatusEnum :ApplyStatusEnum.values() ){
            if(applyStatusEnum.getStatus().equals(status)){
                return applyStatusEnum;
            }
        }
        return null;
    }
}
