package com.ets.apply.application.common.consts.salesUpgrade;

import com.ets.apply.application.common.consts.productPackage.ManufacturerEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum SaleUpgradeStatusEnum {
    //状态：1-待上架 2-上架，3-下架
    STATUS_DEFAULT(1, "待上架"),
    STATUS_UP(2, "上架"),
    STATUS_DOWN(3, "下架");

    private final Integer value;
    private final String desc;
    public static String getDescByStatus(int status) {
        for (SaleUpgradeStatusEnum node : SaleUpgradeStatusEnum.values()) {
            if (node.getValue().equals(status)) {
                return node.getDesc();
            }
        }
        return "";
    }
}
