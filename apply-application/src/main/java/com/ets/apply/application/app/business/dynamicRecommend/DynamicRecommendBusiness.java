package com.ets.apply.application.app.business.dynamicRecommend;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSON;
import com.ets.apply.application.app.business.OrderBusiness;
import com.ets.apply.application.app.factory.dynamicRecommend.DynamicRecommendStrategyFactory;
import com.ets.apply.application.app.factory.dynamicRecommend.RecommendStrategy;
import com.ets.apply.application.app.thirdservice.feign.BigDataApiFeign;
import com.ets.apply.application.app.thirdservice.request.bigData.DynamicRecommendDTO;
import com.ets.apply.application.app.thirdservice.response.bigData.BigDataDynamicRecommendVO;
import com.ets.apply.application.common.config.AllowApplyProductPackageRule;
import com.ets.apply.application.common.config.DynamicRecommendConfig;
import com.ets.apply.application.common.consts.activityCreditCardUsersInfo.ActivityCreditCardUserInfoStatusEnum;
import com.ets.apply.application.common.consts.common.YesOrNoEnum;
import com.ets.apply.application.common.consts.dynamicRecomend.DynamicRecommendTypeEnum;
import com.ets.apply.application.common.consts.dynamicRecomend.DynamicRecommendVersion;
import com.ets.apply.application.common.dto.dynamicRecommend.RecommendDTO;
import com.ets.apply.application.common.vo.dynamicRecommend.DynamicRecommendVO;
import com.ets.apply.application.common.vo.order.GetUnpaidOrderInfoVO;
import com.ets.apply.application.infra.entity.OrderOrderEntity;
import com.ets.apply.application.infra.entity.ProductPackageEntity;
import com.ets.apply.application.infra.relation.order.OrderOrderBindCreditCardUsersInfoRelation;
import com.ets.apply.application.infra.relation.order.OrderOrderBindProductPackageRelation;
import com.ets.apply.application.infra.service.ActivityCreditCardUsersInfoService;
import com.ets.apply.application.infra.service.OrderOrderService;
import com.ets.apply.application.infra.service.ProductPackageService;
import com.ets.common.JsonResult;
import com.ets.common.ToolsHelper;
import feign.FeignException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class DynamicRecommendBusiness {

    @Autowired
    private OrderBusiness orderBusiness;

    @Autowired
    private DynamicRecommendConfig dynamicRecommendConfig;

    @Autowired
    private BigDataApiFeign bigDataApiFeign;

    @Autowired
    private OrderOrderService orderOrderService;

    @Autowired
    private ActivityCreditCardUsersInfoService activityCreditCardUsersInfoService;

    @Autowired
    private ProductPackageService productPackageService;

    @Autowired
    private AllowApplyProductPackageRule allowApplyProductPackageRule;


    /**
     * @deprecated 旧版本，后续迭代不维护，待确认前端版本已无调用去除代码
     */
    public DynamicRecommendVO recommend(Long uid) {
        try {
            // 优先处理返回未支付订单
            DynamicRecommendVO unpaidOrder = recommendUnpaidOrder(uid);
            if (ObjectUtil.isNotEmpty(unpaidOrder)) {
                return unpaidOrder;
            }
            return recommendByBiData(uid);
        } catch (Exception e) {
            log.error("DynamicRecommendBusiness.recommend 请求异常：{}", e.getMessage());
        }
        return null;
    }

    /**
     * 根据类型推荐
     */
    public DynamicRecommendVO recommendV2(Long uid, RecommendDTO recommendDTO) {
        try {
            List<Integer> recommendTypes = recommendDTO.getRecommendType();
            if (ObjectUtil.isEmpty(recommendTypes)) {
                return null;
            }

            for (Integer recommendType : recommendTypes) {
                DynamicRecommendTypeEnum recommendTypeEnum = DynamicRecommendTypeEnum.getEnumByType(recommendType);
                if (recommendTypeEnum == null) {
                    continue;
                }

                RecommendStrategy strategy = DynamicRecommendStrategyFactory.getRecommendStrategy(recommendTypeEnum);
                if (strategy != null) {
                    DynamicRecommendVO result = strategy.recommend(uid,recommendDTO);
                    if (ObjectUtil.isNotEmpty(result)) {
                        return result;
                    }
                }
            }
            return null;
        } catch (Exception e) {
            log.error("DynamicRecommendBusiness.recommend 请求异常：{}", e.getMessage(), e);
        }
        return null;
    }


    public DynamicRecommendVO recommendUnpaidOrder(Long uid) {
        GetUnpaidOrderInfoVO unpaidOrderInfoVO = orderBusiness.getUnpaidOrderInfo(uid);
        // 返回数据不是空的话，组装返回数据
        if (unpaidOrderInfoVO != null) {
            if(!YesOrNoEnum.YES.getCode().equals(unpaidOrderInfoVO.getCanContinueApply())){
                return null;
            }
            String description = dynamicRecommendConfig.getUnpaidOrderRecommendDescription();
            // 从产品包获取描述，无则获取配置默认描述信息
            if (ObjectUtil.isNotEmpty(unpaidOrderInfoVO.getProductPackage())) {
                if (ObjectUtil.isNotEmpty(unpaidOrderInfoVO.getProductPackage().getPackageInfo())) {
                    // 获取object 中的字段值
                    JSONObject packageInfoObject =
                            new JSONObject(unpaidOrderInfoVO.getProductPackage().getPackageInfo());
                    if (packageInfoObject.containsKey("indexUnpaidOrderDesc") && ObjectUtil.isNotEmpty(packageInfoObject.getStr("indexUnpaidOrderDesc"))) {
                        description = packageInfoObject.getStr("indexUnpaidOrderDesc");
                    }
                }
            }
            HashMap<String, Object> params = dynamicRecommendConfig.getUnpaidOrderRecommendParams();
            params.put("orderSn", unpaidOrderInfoVO.getOrderSn());

            return createDynamicRecommendVO(
                    DynamicRecommendTypeEnum.RECOMMEND_BY_UNPAID_ORDER.getType(),
                    dynamicRecommendConfig.getUnpaidOrderRecommendPath(),
                    dynamicRecommendConfig.getUnpaidOrderRecommendTitle(),
                    description,
                    params
            );
        }
        return null;
    }

    public DynamicRecommendVO recommendUnpaidOrder(Long uid, RecommendDTO recommendDTO) {
        GetUnpaidOrderInfoVO unpaidOrderInfoVO = orderBusiness.getUnpaidOrderInfo(uid);
        // 返回数据不是空的话，组装返回数据
        if (unpaidOrderInfoVO != null) {
            if (!DynamicRecommendVersion.VERSION_1.getVersion().equals(recommendDTO.getVersion()) && !YesOrNoEnum.YES.getCode().equals(unpaidOrderInfoVO.getCanContinueApply())) {
                return null;
            }
            String description = dynamicRecommendConfig.getUnpaidOrderRecommendDescription();
            // 从产品包获取描述，无则获取配置默认描述信息
            if (ObjectUtil.isNotEmpty(unpaidOrderInfoVO.getProductPackage())) {
                if (ObjectUtil.isNotEmpty(unpaidOrderInfoVO.getProductPackage().getPackageInfo())) {
                    // 获取object 中的字段值
                    JSONObject packageInfoObject =
                            new JSONObject(unpaidOrderInfoVO.getProductPackage().getPackageInfo());
                    if (packageInfoObject.containsKey("indexUnpaidOrderDesc") && ObjectUtil.isNotEmpty(packageInfoObject.getStr("indexUnpaidOrderDesc"))) {
                        description = packageInfoObject.getStr("indexUnpaidOrderDesc");
                    }
                }
            }
            HashMap<String, Object> params = dynamicRecommendConfig.getUnpaidOrderRecommendParams();
            params.put("orderSn", unpaidOrderInfoVO.getOrderSn());
            if (ObjectUtil.isNotEmpty(recommendDTO.getTab()) && ObjectUtil.isNotEmpty(dynamicRecommendConfig.getUnpaidOrderTabSourceMap())) {
                Object tabSource = dynamicRecommendConfig.getUnpaidOrderTabSourceMap().get(recommendDTO.getTab());
                if (ObjectUtil.isNotEmpty(tabSource)) {
                    params.put("orderSource", tabSource);
                }
            }

            return createDynamicRecommendVO(
                    DynamicRecommendTypeEnum.RECOMMEND_BY_UNPAID_ORDER.getType(),
                    dynamicRecommendConfig.getUnpaidOrderRecommendPath(),
                    dynamicRecommendConfig.getUnpaidOrderRecommendTitle(),
                    description,
                    params
            );
        }
        return null;
    }

    public DynamicRecommendVO recommendByBiData(Long uid) {
        try {
            // 从大数据中获取用户的
            DynamicRecommendDTO dynamicRecommendDTO = new DynamicRecommendDTO();
            dynamicRecommendDTO.setUid(uid);

            JsonResult<BigDataDynamicRecommendVO> bigDataRecommend = bigDataApiFeign.recommendInfo(dynamicRecommendDTO);
            if (bigDataRecommend.getCode() != 200) {
                log.error("请求big-data 服务失败: " + bigDataRecommend.getMsg());
                return null;
            }
            // 判定大数据返回的数据，有对应的数据则处理对应的返回逻辑
            BigDataDynamicRecommendVO data = bigDataRecommend.getData();
            if (data == null || data.getUid() == null) {
                return null;
            }

            // 判断是否有有效订单
            boolean hasEffectOrder = Objects.equals(data.getHasEffectOrder(),
                    YesOrNoEnum.YES.getCode());
            // 有有效订单，则不返回推荐
            if(hasEffectOrder){
                return null;
            }
            
            // 如果有取消时间，并且取消时间在最近30天内，则返回取消订单推荐
            Integer cancelDayDiff = getDayDiffFromNow(data.getTheLastOrderCancelTime());
            boolean cancelWithinCertainDays = ObjectUtil.isNotEmpty(cancelDayDiff) && cancelDayDiff <= 30;

            if (ObjectUtil.isNotEmpty(dynamicRecommendConfig.getOrderCancelRecommendPath()) &&
                    cancelWithinCertainDays) {
                return createDynamicRecommendVO(
                        DynamicRecommendTypeEnum.RECOMMEND_BY_ORDER_CANCEL.getType(),
                        dynamicRecommendConfig.getOrderCancelRecommendPath(),
                        dynamicRecommendConfig.getOrderCancelRecommendTitle(),
                        dynamicRecommendConfig.getOrderCancelRecommendDescription(),
                        dynamicRecommendConfig.getOrderCancelRecommendParams()
                );
            }


            Integer lastDetailPageDayDiff = getDayDiffFromNow(data.getTheLastDetailPageTime());
            boolean lastDetailPageTimeWithin7Days =
                    ObjectUtil.isNotEmpty(lastDetailPageDayDiff) && lastDetailPageDayDiff <= 7;

            boolean detailPageRecommendPathNotEmpty =
                    ObjectUtil.isNotEmpty(data.getTheLastDetailPage());

            Integer paidDayDiff = getDayDiffFromNow(data.getTheLastOrderPaidTime());
            boolean paidBeyondCertainDays = ObjectUtil.isEmpty(paidDayDiff) ||  paidDayDiff > 7;

            Integer lastOrderCreateDayDiff = getDayDiffFromNow(data.getTheLastOrderCreateTime());
            boolean createBeyondCertainDays = ObjectUtil.isEmpty(lastOrderCreateDayDiff) || lastOrderCreateDayDiff > 7;

            // 没有有效订单 如果有访问落地页，并且访问的时间在7天内
            if (!hasEffectOrder && lastDetailPageTimeWithin7Days && detailPageRecommendPathNotEmpty) {

                // 还没有创建或支付的客货车申办订单:订单创建时间为空或者大于7天 并且 创建时间支付空或者时间大于7天
                if (createBeyondCertainDays && paidBeyondCertainDays) {
                    // 如果有返回参数，将参数转化为hashmap
                    HashMap<String, Object> params = dynamicRecommendConfig.getDetailPageRecommendParams();
                    if (ObjectUtil.isNotEmpty(data.getPathQuery())) {
                        // 强制类型转换，不进行ide告警提示
                        @SuppressWarnings("unchecked")
                        HashMap<String, Object> biParams = JSON.parseObject(data.getPathQuery(),
                                HashMap.class);
                        biParams.putAll(params);
                        params = biParams;
                    }
                    return createDynamicRecommendVO(
                            DynamicRecommendTypeEnum.RECOMMEND_BY_USER_ACCESS_PAGE.getType(),
                            data.getTheLastDetailPage(),
                            dynamicRecommendConfig.getDetailPageRecommendTitle(),
                            dynamicRecommendConfig.getDetailPageRecommendDescription(),
                            params
                    );
                }
            }
            return null;
        } catch (FeignException fe) {
            log.error("Feign请求big-data 服务失败: {}", fe.getMessage());
            return null;
        } catch (Exception e) {
            log.error("recommendByBiData 异常：{} 错误信息:{}", e.getMessage(), e.getStackTrace());
            return null;
        }

    }

    private DynamicRecommendVO createDynamicRecommendVO(
            int recommendType, String path, String title, String description, HashMap<String, Object> params) {
        DynamicRecommendVO vo = new DynamicRecommendVO();
        vo.setRecommendType(recommendType);
        vo.setPath(path);
        vo.setTitle(title);
        vo.setDescription(description);
        vo.setParams(params);
        return vo;
    }

    private Integer getDayDiffFromNow(LocalDate dateTime) {
        if (dateTime == null) {
            return null;
        }
        return ToolsHelper.dayDiffFromNow(dateTime.atStartOfDay());
    }


    /**
     * 推荐信用卡办理
     *
     */
    public DynamicRecommendVO recommendCreditCardOrder(Long uid) {

        // 获取用户办理的最新一条已支付信用卡订单
        OrderOrderEntity latestPaidCreditOrder = orderOrderService.getLatestPaidCreditOrderByUidAndBizType(uid,
                dynamicRecommendConfig.getCreditCardOrderRecommendBizType());
        if (ObjectUtil.isEmpty(latestPaidCreditOrder)) {
            return null;
        }

        // 检查产品包
        productPackageService.bindToMasterEntity(latestPaidCreditOrder, OrderOrderBindProductPackageRelation.class);

        if(ObjectUtil.isEmpty(latestPaidCreditOrder.getProductPackageEntity())){
            return null;
        }
        ProductPackageEntity productPackageEntity = latestPaidCreditOrder.getProductPackageEntity();
        if (!ObjectUtil.equals(productPackageEntity.getPackageFee(), BigDecimal.ZERO) ||
                !orderBusiness.checkAllowProductPackage(productPackageEntity,
                        allowApplyProductPackageRule.getAllowCreditRule())) {
            return null;

        }
        
        // 判定是否有信用卡及是否已进件，如果订单未关联信用卡或者未进件，返回到推荐信息
        activityCreditCardUsersInfoService.bindToMasterEntity(latestPaidCreditOrder,
                OrderOrderBindCreditCardUsersInfoRelation.class);

        // 有信用卡关联单并且信用卡状态不是待处理状态，则不进行推荐，否则组装推荐信息
        if (ObjectUtil.isNotEmpty(latestPaidCreditOrder.getActivityCreditCardUsersInfoEntity())
                && !Objects.equals(latestPaidCreditOrder.getActivityCreditCardUsersInfoEntity().getStatus(),
                ActivityCreditCardUserInfoStatusEnum.STATUS_WAIT.getCode())) {
            return null;
        }
        HashMap<String, Object> params = dynamicRecommendConfig.getCreditCardOrderRecommendParams();
        params.put("orderSn", latestPaidCreditOrder.getOrderSn());
        // 组装推荐信息
        return createDynamicRecommendVO(
                DynamicRecommendTypeEnum.RECOMMEND_BY_CREDIT_CARD.getType(),
                dynamicRecommendConfig.getCreditCardOrderRecommendPath(),
                dynamicRecommendConfig.getCreditCardOrderRecommendTitle(),
                dynamicRecommendConfig.getCreditCardOrderRecommendDescription(),
                params
        );
    }


}

