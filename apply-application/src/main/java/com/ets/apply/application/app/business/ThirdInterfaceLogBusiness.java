package com.ets.apply.application.app.business;
import com.ets.apply.application.common.dto.request.ThirdInterfaceLogDTO;
import com.ets.apply.application.infra.entity.ThirdInterfaceLogEntity;
import com.ets.apply.application.infra.service.ThirdInterfaceLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ThirdInterfaceLogBusiness extends BaseBusiness {
    @Autowired
    private ThirdInterfaceLogService thirdInterfaceLogService;

    /*
     * 新增日志
     */
    public Boolean addLog(ThirdInterfaceLogDTO dto){
        try {
            ThirdInterfaceLogEntity thirdInterfaceLogEntity = new ThirdInterfaceLogEntity();
            thirdInterfaceLogEntity.setLogMethod(dto.getLogMethod());
            thirdInterfaceLogEntity.setLogParams(dto.getLogParams());
            thirdInterfaceLogEntity.setLogRequest(dto.getLogRequest());
            thirdInterfaceLogEntity.setLogRespone(dto.getLogRespone());
            thirdInterfaceLogEntity.setStatus(dto.getStatus());
            thirdInterfaceLogService.create(thirdInterfaceLogEntity);
        }catch (Exception e) {
            log.info("新增日志异常：" + e.getMessage());
        }
        return true;
    }
}
