package com.ets.apply.application.common.bo.orderCenter;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class OrderCenterApplyBO {
    //用户id
    private Long uid;
    //业务类型
    private Integer productType;
    //业务单号
    private String productSn;
    //订单创建时间
    private String createTime;
    //产品名称
    private String productName = "一代-经典ETC";
    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 车牌颜色
     */
    private Integer plateColor;
    //订单金额
    private BigDecimal amount;
    //通用订单状态
    private Integer status;
    //默认true显示
    private Boolean isShow = true;

    //是否主订单
    private Boolean isMain = true;

    private Extra extra;
    //状态顺序是否检查
    private Boolean checkSequence = true;

    @Data
    public static class Extra {

        private Integer businessType;
        private String businessTypeStr;

        /**
         * 订单状态
         */
        private Integer orderStatus;
        /**
         * 车牌号
         */
        private String plateNo;

        /**
         * 车牌颜色
         */
        private Integer plateColor;
        /**
         * 商品图片
         */
        private String goodsImgUrl = "https://wecos.etczs.net/apply/progress/obu-img-first-normal-0721.png";

        /**
         * 是否显示回收按钮
         */
        private Boolean showRecovery = false;

        /**
         * 售后状态 0:未售后,1:申请退货退款中,2已退货退款,3:部分退货退款，9:取消售后申请
         */
        Integer aftersaleStatus;

        /**
         * 激活状态 0未激活，1已激活，2已注销
         */
        Integer activatedStatus;

    }
}
