package com.ets.apply.application.common.consts.taxiImport;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
@AllArgsConstructor
public enum ImportStatusEnum {

    DEFAULT(0, "未处理"),
    IMPORT_ORDER_SUCCEED(1, "导入成功"),
    IMPORT_ORDER_FAILED(2, "导入失败"),
    IMPORT_REVIEW_ORDER_SUCCEED(3, "审核单提交成功"),
    IMPORT_REVIEW_ORDER_FAILED(4, "审核单提交失败"),
    IMPORT_ORDER_AUDIT_SUCCESS(5, "审核通过"),
    IMPORT_ORDER_AUDIT_FAILED(6, "审核异常"),
    IMPORT_ORDER_ACTIVATED(7, "激活成功");


    private final Integer code;

    private final String description;

    public static final Map<String, String> map;

    static {
        ImportStatusEnum[] enums = ImportStatusEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(
                LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getCode().toString(), enums[index].getDescription()),
                Map::putAll
        );
    }

    public static String getDescByCode(Integer code) {
        for (ImportStatusEnum node : ImportStatusEnum.values()) {
            if (node.getCode().equals(code)) {
                return node.getDescription();
            }
        }

        return "";
    }

    public static List<Map<String, String>> getLabelList() {
        List<Map<String, String>> list = new ArrayList<>();

        for (ImportStatusEnum node : ImportStatusEnum.values()) {
            Map<String, String> row = new LinkedHashMap<>();
            row.put("label", node.getDescription());
            row.put("value", node.getCode().toString());

            list.add(row);
        }

        return list;
    }


}
