package com.ets.apply.application.common.dto.request.deviceValuation;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class UseValuationDTO {
    @NotBlank(message = "valuationSn不能为空")
    private String valuationSn;

    @NotNull(message = "referSn不能为null")
    private String referSn;

    @NotNull(message = "uid不能为null")
    private Long uid;
}
