package com.ets.apply.application.app.thirdservice.feign;

import com.ets.apply.application.app.thirdservice.fallback.BigDataUserProfileApiFallbackFactory;
import com.ets.apply.application.app.thirdservice.request.bigData.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(url = "${baseData.bigDataUserProfile.url}", name = "BigDataUserProfileApiFeign", fallbackFactory = BigDataUserProfileApiFallbackFactory.class)
public interface BigDataUserProfileApiFeign {

    @PostMapping("/atomcmps/tag/list")
    String atomcmpsTagList(@RequestBody AtomcmpsTagListDTO dto);
}
