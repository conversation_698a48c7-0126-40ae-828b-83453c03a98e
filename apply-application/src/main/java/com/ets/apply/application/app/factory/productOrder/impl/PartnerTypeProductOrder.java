package com.ets.apply.application.app.factory.productOrder.impl;

import cn.hutool.core.util.ObjectUtil;
import com.ets.apply.application.common.bo.productOrder.ProductOrderRefundBO;
import com.ets.apply.application.common.bo.productOrder.ProductOrderShipBO;
import com.ets.common.ToolsHelper;
import org.springframework.stereotype.Component;
import com.ets.apply.application.app.factory.productPartner.ProductPartnerFactory;

import java.math.BigDecimal;

@Component
public class PartnerTypeProductOrder extends ProductOrderBase {

    /**
     * 通知退款
     */
    public String refund(ProductOrderRefundBO productOrderRefundBO) {
        // 实际支付金额大于0 才进行退款
        if (ObjectUtil.isNull(productOrderRefundBO.getReferValue())) {
            ToolsHelper.throwException("退款参数referValue异常，请确认");
        }
        if (productOrderRefundBO.getPaidAmount().compareTo(BigDecimal.ZERO) <= 0) {
            return "";
        }
        return ProductPartnerFactory.create(productOrderRefundBO.getReferValue()).refund(productOrderRefundBO);
    }


    /**
     * 通知 发货
     *
     * @param productOrderShipBO
     */
    public void ship(ProductOrderShipBO productOrderShipBO) {
        //塞队列进行发货操作
        ProductPartnerFactory.create(productOrderShipBO.getReferValue()).ship(productOrderShipBO);
    }


}
