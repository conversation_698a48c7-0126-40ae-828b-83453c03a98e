package com.ets.apply.application.common.bo.amap;

import lombok.Data;

@Data
public class AmapCommonBO {
    /**
     * utc_timestampString是当前时间戳(毫秒)
     * versionString是1.0版本号
     * charsetString是UTF-8字符串编码
     * methodString是接口全限定名，即云店的SPI
     * signString是签名
     * sign_typeString是RSA2签名类型
     * app_idString是应用id
     * biz_contentString是请求业务体
     */
    private String utcTimestamp;
    private String version;
    private String charset;
    private String method;
    private String sign;
    private String signType;
    private String appId;
    private String bizContent;
}
