package com.ets.apply.application.common.consts.orderAftersales;

import com.ets.apply.application.common.consts.order.StatusEnum;
import com.ets.common.ToolsHelper;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

@Getter
@AllArgsConstructor
public enum OrderAftersalesTypeEnum {
    TYPE_REISSUE_GOODS(1, "设备补办",6),
    TYPE_EXCHANGE_GOODS(2, "设备更换",2),
    TYPE_UNBIND_REFUND(3, "退货退款",1),
    TYPE_UNBIND_ONLY(4, "仅取消",0),
    TYPE_UNBIND_CANCEL(5, "仅退款",3);

    private final Integer type;
    private final String name;
    //商品售后类型
    private final Integer serviceType;

    //支付售后的类型
    public static final List<Integer> ORDER_AFTERSALES_TYPE_MAIN_LIST = Arrays.asList(
            OrderAftersalesTypeEnum.TYPE_REISSUE_GOODS.getType(),
            OrderAftersalesTypeEnum.TYPE_UNBIND_REFUND.getType(),
            OrderAftersalesTypeEnum.TYPE_UNBIND_ONLY.getType(),
            OrderAftersalesTypeEnum.TYPE_EXCHANGE_GOODS.getType()
    );

    public static OrderAftersalesTypeEnum getByType(int type) {
        for (OrderAftersalesTypeEnum node : OrderAftersalesTypeEnum.values()) {
            if (node.getType() == type) {
                return node;
            }
        }
        ToolsHelper.throwException("商品售后类型异常："+type);
        return null;
    }

    public static Integer getTypeByServiceType(int serviceType) {
        for (OrderAftersalesTypeEnum node : OrderAftersalesTypeEnum.values()) {
            if (node.getServiceType() == serviceType) {
                return node.getType();
            }
        }
        ToolsHelper.throwException("商品售后类型异常："+serviceType);
        return 0;
    }

    //支付售后的类型
    public static Boolean checkSupportType(int type) {
        if(Arrays.asList(
                OrderAftersalesTypeEnum.TYPE_UNBIND_REFUND.getType(),
                OrderAftersalesTypeEnum.TYPE_UNBIND_ONLY.getType(),
                OrderAftersalesTypeEnum.TYPE_EXCHANGE_GOODS.getType(),
                OrderAftersalesTypeEnum.TYPE_REISSUE_GOODS.getType(),
                OrderAftersalesTypeEnum.TYPE_UNBIND_CANCEL.getType()
        ).contains(type)){
            return true;
        }
        return false;
    }

    public static String getDescByType(int type) {
        for (OrderAftersalesTypeEnum node : OrderAftersalesTypeEnum.values()) {
            if (node.getType().equals(type)) {
                return node.getName();
            }
        }
        return "";
    }
}
