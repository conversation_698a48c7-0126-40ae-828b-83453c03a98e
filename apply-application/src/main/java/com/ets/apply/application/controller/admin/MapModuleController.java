package com.ets.apply.application.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.apply.application.app.business.map.MapModuleBusiness;
import com.ets.apply.application.common.dto.map.*;
import com.ets.apply.application.common.vo.map.MapModuleDetailVO;
import com.ets.apply.application.common.vo.map.MapModuleListVO;
import com.ets.apply.application.common.vo.map.MapModulePackageListVO;
import com.ets.apply.application.controller.BaseController;
import com.ets.apply.application.infra.entity.map.MapLogEntity;
import com.ets.common.JsonResult;
import com.ets.common.RequestHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import jakarta.validation.Valid;

@Controller
@RequestMapping("/admin/mapModule")
public class MapModuleController extends BaseController {
    @Autowired
    private MapModuleBusiness mapModuleBusiness;

    /**
     * 模块列表查询
     * @return
     */
    @RequestMapping("/get-list")
    @ResponseBody
    public JsonResult<IPage<MapModuleListVO>> getList(@RequestBody(required = false) @Valid MapModuleDTO mapModuleDTO) {
        return JsonResult.ok(mapModuleBusiness.getAllList(mapModuleDTO));
    }


    /**
     * 获取产品包列表
     * @return
     */
    @RequestMapping("/get-package-list")
    @ResponseBody
    public JsonResult<IPage<MapModulePackageListVO>> getPackageList(@RequestBody(required = false) @Valid MapPackageDTO mapPackageDTO) {
        return JsonResult.ok(mapModuleBusiness.getPackageList(mapPackageDTO));
    }

    /**
     *  添加产品包
     * @return
     */
    @RequestMapping("/add-package")
    @ResponseBody
    public JsonResult<Boolean> addPackage(@RequestBody(required = false) @Valid MapModuleAddPackageDTO mapModuleAddPackageDTO) {
        mapModuleBusiness.addPackage(mapModuleAddPackageDTO, RequestHelper.getHttpServletRequest().getHeader("loginCode"));
        return JsonResult.ok(true);
    }
    /**
     *  修改产品包
     * @return
     */
    @RequestMapping("/modify-package")
    @ResponseBody
    public JsonResult<Boolean> modifyPackage(@RequestBody(required = false) @Valid MapModuleAddPackageDTO mapModuleAddPackageDTO) {
        mapModuleBusiness.updatePackage(mapModuleAddPackageDTO, RequestHelper.getHttpServletRequest().getHeader("loginCode"));
        return JsonResult.ok(true);
    }

    /**
     *  修改产品包
     * @return
     */
    @RequestMapping("/delPackage")
    @ResponseBody
    public JsonResult<Boolean> delPackage(@RequestBody(required = false) @Valid MapModuleAddPackageDTO mapModuleAddPackageDTO) {
        mapModuleBusiness.delPackage(mapModuleAddPackageDTO, RequestHelper.getHttpServletRequest().getHeader("loginCode"));
        return JsonResult.ok(true);
    }

    /**
     * 产品包列表查询
     * @return
     */
    @RequestMapping("/getLoglist")
    @ResponseBody
    public JsonResult<IPage<MapLogEntity>> getLoglist(@RequestBody(required = false) @Valid MapLogGetListDTO dto) {
        return JsonResult.ok(mapModuleBusiness.getLoglist(dto));
    }


    @RequestMapping("/get-module-detail")
    @ResponseBody
    public JsonResult<MapModuleDetailVO> getModuleDetail(@RequestBody(required = false) @Valid MapModuleDetailDTO dto) {
        return JsonResult.ok(mapModuleBusiness.getModuleDetail(dto));
    }

    @RequestMapping("/modify-decoration-type")
    @ResponseBody
    public JsonResult<?> modifyDecorationType(@RequestBody(required = false) @Valid MapModuleModifyDecorationTypeDTO dto) {
        mapModuleBusiness.modifyDecorationType(dto);
        return JsonResult.ok();
    }
}
