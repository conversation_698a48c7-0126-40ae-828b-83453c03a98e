package com.ets.apply.application.common.dto.request.order;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class OrderRiskCheckNotifyDTO {
    @NotBlank(message = "订单号不能为空")
    private String orderSn;
    @NotNull(message = "风控类型不能为空")
    private Integer riskType;
    @NotNull(message = "风控通知消息不能为空")
    private String msg;
}
