package com.ets.apply.application.common.consts.productPackageTmp;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AllowModifyEnum {
    ALLOW_MODIFY_ENUM(1, "允许修改"),
    NOT_ALLOW_MODIFY_ENUM(0, "不允许修改");

    private final Integer value;
    private final String desc;

    public static String getDescByCode(int code) {
        for (AllowModifyEnum node : AllowModifyEnum.values()) {
            if (node.getValue().equals(code)) {
                return node.getDesc();
            }
        }
        return "";
    }
}
