package com.ets.apply.application.common.consts.unicom;

import com.ets.apply.application.common.vo.SelectOptionsVO;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum UnicomOrderStateEnum {

    DEFAULT("0", "默认"),

    ACTIVATED("1", "激活"),

    CANCELED("2", "退单"),

    VOIDED("4", "销户"),

    CHARGED("6", "首充"),

    UN_REGISTER("CA", "待开户");


    private final String code;
    private final String description;

    public static String getDescByCode(String code) {
        for (UnicomOrderStateEnum node : UnicomOrderStateEnum.values()) {
            if (node.getCode().equals(code)) {
                return node.getDescription();
            }
        }

        return "";
    }

    public UnicomOrderStateEnum getByCode(String code) {
        for (UnicomOrderStateEnum node : UnicomOrderStateEnum.values()) {
            if (node.getCode().equals(code)) {
                return node;
            }
        }

        return null;
    }

    public static List<SelectOptionsVO> getSelectOptions() {

        return Stream.of(UnicomOrderStateEnum.values())
                .map(item -> new SelectOptionsVO(String.valueOf(item.getCode()), item.getDescription()))
                .collect(Collectors.toList());
    }
}
