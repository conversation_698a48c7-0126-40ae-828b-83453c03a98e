package com.ets.apply.application.common.consts.entrust;

import com.ets.apply.application.common.consts.payment.PaymentModeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum EntrustModeEnum {
    ENTRUST_MODE_WEIXIN(1, "普通签约", PaymentModeEnum.PAYMENT_MODE_WEIXIN.getCode()),
    ENTRUST_MODE_VEHICLE(2, "车主平台签约", PaymentModeEnum.PAYMENT_MODE_VEHICLE.getCode()),
    ENTRUST_MODE_CMB(5, "招行签约", PaymentModeEnum.PAYMENT_MODE_CMB.getCode()),
    ENTRUST_MODE_CCB(6, "建行签约", PaymentModeEnum.PAYMENT_MODE_CCB.getCode()),
    ENTRUST_MODE_BIND_BANK(9, "指定卡签约", PaymentModeEnum.PAYMENT_MODE_VEHICLE_BIND_BANK.getCode());

    private final int code;
    private final String description;
    private final int paymentMode;

    public static int getPaymentModeByCode(int code) {
        for (EntrustModeEnum node : EntrustModeEnum.values()) {
            if (node.getCode() == code) {
                return node.getPaymentMode();
            }
        }
        return 0;
    }

}
