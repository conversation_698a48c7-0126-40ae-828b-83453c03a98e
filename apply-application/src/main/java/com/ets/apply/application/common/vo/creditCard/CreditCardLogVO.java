package com.ets.apply.application.common.vo.creditCard;

import com.ets.apply.application.common.consts.activityCreditCardOperateLog.CreditCardOperateLogTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class CreditCardLogVO {
    /**
     * 自增id
     */
    private Integer id;

    /**
     * 银行流水号
     */
    private String orderSn;

    /**
     * 操作类型
     */
    private String operateType;

    private String operateTypeStr;

    /**
     * 操作内容
     */
    private String operateContent;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdAt;

    public String getOperateTypeStr() {
        return CreditCardOperateLogTypeEnum.map.getOrDefault(operateType, "未知");
    }
}
