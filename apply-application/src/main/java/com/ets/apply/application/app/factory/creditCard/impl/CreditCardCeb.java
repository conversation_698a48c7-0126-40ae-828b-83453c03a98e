package com.ets.apply.application.app.factory.creditCard.impl;

import cn.hutool.core.util.ObjectUtil;
import com.ets.apply.application.app.business.creditCard.CreditCardBusiness;
import com.ets.apply.application.app.service.bank.CebService;
import com.ets.apply.application.app.service.bank.SpdService;
import com.ets.apply.application.common.config.creditBank.CebCreditBankConfig;
import com.ets.apply.application.common.consts.activityCreditCardUsersInfo.ActivityCreditCardUserInfoReferTypeEnum;
import com.ets.apply.application.common.dto.request.creditCard.ApplyOrderDTO;
import com.ets.apply.application.common.vo.creditCard.CreditCardApplyOrderVO;
import com.ets.common.ToolsHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class CreditCardCeb extends CreditCardBase {
    @Autowired
    private CreditCardBusiness creditCardBusiness;

    @Autowired
    private CebService cebService;

    @Autowired
    private CebCreditBankConfig cebCreditBankConfig;
    public CreditCardApplyOrderVO applyOrder(ApplyOrderDTO applyOrderDTO) {
        try {
            //银行申办流水号
            if(applyOrderDTO.getBankApplyNumber().length() < 1) {
                if(ObjectUtil.isNull(applyOrderDTO.getReferSn())){
                    applyOrderDTO.setReferSn("");
                }
                if(ObjectUtil.isNull(applyOrderDTO.getReferType())){
                    applyOrderDTO.setReferType(ActivityCreditCardUserInfoReferTypeEnum.TYPE_UNKNOWN.getCode());
                }
                applyOrderDTO.setInnerVersion(cebCreditBankConfig.getInnerVersion());
                applyOrderDTO.setBankApplyNumber(creditCardBusiness.applyCreditCard(applyOrderDTO));
            }

            CreditCardApplyOrderVO creditCardApplyOrderVO = new CreditCardApplyOrderVO();
            creditCardApplyOrderVO.setUid(applyOrderDTO.getUid());
            creditCardApplyOrderVO.setUrl(cebService.getApplyUrl(applyOrderDTO.getBankApplyNumber(),applyOrderDTO.getUid()));
            return creditCardApplyOrderVO;
        } catch (Exception e) {
            log.info("CreditCardSpd 光大银行申请异常：" + e.getMessage());
            ToolsHelper.throwException("当前办理方式不可用，请稍后再试");
        }
        return null;
    }
}
