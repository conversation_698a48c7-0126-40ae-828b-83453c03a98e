package com.ets.apply.application.controller.frontend;

import cn.hutool.core.util.ObjectUtil;
import com.ets.apply.application.app.business.applyCreditCard.ApplyCreditCardBusiness;
import com.ets.apply.application.app.business.creditCard.CreditCardBusiness;
import com.ets.apply.application.common.config.creditBank.CommonCreditBankConfig;
import com.ets.apply.application.common.dto.request.creditCard.*;
import com.ets.apply.application.common.utils.UserUtil;
import com.ets.apply.application.common.vo.creditCard.CreditCardApplyOrderVO;
import com.ets.apply.application.common.vo.creditCard.CreditCardApplyStatusVO;
import com.ets.apply.application.common.vo.creditCard.CreditCardCallBackVO;
import com.ets.apply.application.controller.BaseController;
import com.ets.common.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * <AUTHOR>
 */
@RequestMapping("/frontend/creditCard")
@RestController
@Slf4j
public class CreditCardFrontendController extends BaseController {

    @Autowired
    private CreditCardBusiness creditCardBusiness;

    @Autowired
    private ApplyCreditCardBusiness applyCreditCardBusiness;

    @Autowired
    private CommonCreditBankConfig commonCreditBankConfig;

    @PostMapping("/checkQualification")
    @ResponseBody
    public JsonResult<Object> checkQualification(@RequestBody(required = false) @Valid CreditCardCheckQualificationDTO request) {
        return JsonResult.ok(creditCardBusiness.checkQualification(request,UserUtil.getUid()));
    }


    /**
     * 前端查询信用卡申请状态接口
     * wiki <a href="http://yapi.etczs.net/project/312/interface/api/25158">前端查询信用卡申请状态接口</a>
     */
    @PostMapping("/applyStatus")
    @ResponseBody
    public JsonResult<CreditCardApplyStatusVO> applyStatus(@RequestBody(required = false) @Valid CreditCardApplyStatusDTO dto) {
        return JsonResult.ok(creditCardBusiness.applyStatus(UserUtil.getUid(), dto));
    }

    @PostMapping("/coopStatus")
    @ResponseBody
    public JsonResult<Object> coopStatus(@RequestBody(required = false) @Valid CreditCardCoopStatusDTO dto) {
        return JsonResult.ok(creditCardBusiness.coopStatus(dto,UserUtil.getUid()));
    }

    /**
     * <a href="https://yapi.etczs.net/project/312/interface/api/28128">进件结果前端回跳通知</a>
     */
    @PostMapping("/callback")
    public JsonResult<CreditCardCallBackVO> callBack(@RequestBody @Valid CreditCardCallBackDTO dto){
        return JsonResult.ok(creditCardBusiness.callback(dto));
    }


    /**
     * 申请信用卡
     */
    @PostMapping("/applyOrder")
    public JsonResult<CreditCardApplyOrderVO> applyOrder(@RequestBody @Valid ApplyCreditCardDTO applyCreditCardDTO) {
        ApplyOrderDTO applyOrderDTO = new ApplyOrderDTO();
        BeanUtils.copyProperties(applyCreditCardDTO, applyOrderDTO);
        applyOrderDTO.setUid(UserUtil.getUid());
        // classify 字段后续废弃，以referType为准
        applyOrderDTO.setClassify(applyCreditCardDTO.getReferType());

        // 映射subReferType ,解决产品需要渠道可配置的问题
        if (applyCreditCardDTO.getSubReferType().equals(0) && ObjectUtil.isNotEmpty(commonCreditBankConfig.getSourceSubReferTypeMap().get(applyCreditCardDTO.getSource()))) {
            applyOrderDTO.setSubReferType(commonCreditBankConfig.getSourceSubReferTypeMap().get(applyCreditCardDTO.getSource()));
        }
        // 版本号
        return JsonResult.ok(applyCreditCardBusiness.applyOrder(applyOrderDTO));
    }


}
