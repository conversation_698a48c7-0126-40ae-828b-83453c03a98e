package com.ets.apply.application.controller.frontend;

import com.ets.apply.application.app.business.CouponBusiness;
import com.ets.apply.application.common.utils.UserUtil;
import com.ets.apply.application.common.vo.coupon.ValidCouponVO;
import com.ets.apply.application.controller.BaseController;
import com.ets.common.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RequestMapping("/frontend/coupon")
@RefreshScope
@RestController
@Slf4j
public class CouponController extends BaseController {

    @Autowired
    private CouponBusiness couponBusiness;

    @RequestMapping("/get-valid-coupon")
    JsonResult<List<ValidCouponVO>> getValidCoupon() {
        return JsonResult.ok(couponBusiness.getUserValidCoupon(UserUtil.getUid()));
    }
}
