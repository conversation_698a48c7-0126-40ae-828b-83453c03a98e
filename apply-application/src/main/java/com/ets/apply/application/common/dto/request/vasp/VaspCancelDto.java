package com.ets.apply.application.common.dto.request.vasp;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.util.List;

@Data
public class VaspCancelDto {

    /**
     * vasp 订单编号
     */
    @NotBlank(message = "订单号不能为空，请确认")
    private String orderId;

    /**
     * 取消原因
     */
    private String cancelDesc;
    /**
     * 取消时间
     */
    private String cancelDtime;

    /**
     * 是否退款-非必传字段
     */
    private String isRefund;

    /**
     * 退款详情，非必传字段
     */
//    @Size(min = 1, max = 1, message = "一个订单只有购买一个商品可以退款，请确认订单信息")
//    List<VaspOrderRefundDetailDto> refundDetails;
    @NotBlank(message = "退款信息不能为空")
    private String refundDetails;

}
