package com.ets.apply.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ets.apply.application.common.bo.admin.ConfigNameKeyStringBO;
import com.ets.apply.application.common.dto.request.splitFlow.EditSplitFlowConfigDTO;
import com.ets.apply.application.infra.entity.splitFlow.SplitFlowConfigEntity;
import com.ets.apply.application.infra.mapper.SplitFlowConfigMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 映射组合规则 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-27
 */
@Service
@DS("db-apply")
public class SplitFlowConfigService extends BaseService<SplitFlowConfigMapper, SplitFlowConfigEntity> {
    @Autowired
    private SplitFlowLogService splitFlowLogService;
    public void addNew(SplitFlowConfigEntity splitFlowConfigEntity) {
        this.create(splitFlowConfigEntity);
        splitFlowLogService.addLog(splitFlowConfigEntity.getId(),"add","新增配置:"+ splitFlowConfigEntity,splitFlowConfigEntity.getOperator());
    }

    public void edit(EditSplitFlowConfigDTO dto) {

        LambdaUpdateWrapper<SplitFlowConfigEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(SplitFlowConfigEntity::getId, dto.getId())
                .set(SplitFlowConfigEntity::getSplitName, dto.getSplitName())
                .set(SplitFlowConfigEntity::getSplitDesc, dto.getSplitDesc())
                .set(SplitFlowConfigEntity::getSplitType, dto.getSplitType())
                .set(SplitFlowConfigEntity::getSplitTerm, dto.getSplitTerm())
                .set(SplitFlowConfigEntity::getGetResultParams, dto.getGetResultParams())
                .set(SplitFlowConfigEntity::getSplitRule, dto.getSplitRule())
                .set(SplitFlowConfigEntity::getDefaultResult, dto.getDefaultResult())
                .set(SplitFlowConfigEntity::getResultReturnParams, dto.getResultReturnParams())
                .set(SplitFlowConfigEntity::getExpireDate, dto.getExpireDate());

        this.updateByWrapper(wrapper);

        splitFlowLogService.addLog(dto.getId(),"edit","修改配置"+ dto,dto.getOperator());
    }

    /*
     * 通过biz_field获取信息，包含子集
     */
    public List<ConfigNameKeyStringBO> getSelectOptionsList(){
        //获取biz_type的数据
        LambdaQueryWrapper<SplitFlowConfigEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(SplitFlowConfigEntity::getSplitType,SplitFlowConfigEntity::getSplitName)
                .orderByAsc(SplitFlowConfigEntity::getCreatedAt);
        List<SplitFlowConfigEntity> list = this.getListByWrapper(wrapper);
        List<ConfigNameKeyStringBO> boList = new ArrayList<>();
        if(list != null){
            for (SplitFlowConfigEntity splitFlowConfig : list) {
                ConfigNameKeyStringBO bo = new ConfigNameKeyStringBO();
                bo.setKey(splitFlowConfig.getSplitType());
                bo.setName(splitFlowConfig.getSplitName());
                boList.add(bo);
            }
        }
        return boList;
    }
}
