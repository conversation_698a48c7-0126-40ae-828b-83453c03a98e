package com.ets.apply.application.common.vo.map;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class MapAssignListVO {
    private Integer id;
    /**
     * 模块
     */
    private String module;

    private String assignKey;
    private String assignKeyName;
    /**
     * 模块名称
     */
    private String message;

    private Integer recommendType;

    private String recommendTypeText;

    /*
     * 状态 1-有效 2-无效
     */
    private Integer status;

    private String statusStr;

    private Integer sort;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 装饰类型 0：无 1：高价值城市推荐
     */
    private Integer decorationType;

    private String operator;
}
