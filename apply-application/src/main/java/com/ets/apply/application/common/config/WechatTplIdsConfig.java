package com.ets.apply.application.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

@RefreshScope
@Configuration
@Data
@ConfigurationProperties(prefix = "template-msg-ids.wechat")
public class WechatTplIdsConfig {

    private Map<String, String> noticeIdsMap;
}
