package com.ets.apply.application.app.thirdservice.feign;

import com.ets.apply.application.app.thirdservice.fallback.CallPhpIssuerAdminFallbackFactory;
import com.ets.apply.application.common.dto.request.orderAftersales.OrderAftersalesOrderChangeDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(
        url = "${params.microUrls.etc-micro-issuer-admin}",
        name = "CallPhpIssuerAdminFeign",
        fallbackFactory = CallPhpIssuerAdminFallbackFactory.class
)
public interface CallPhpIssuerAdminFeign {

    @PostMapping(value = "/order/order-change")
    String orderChange(@RequestHeader(name = "ISSUER-CODE") String issuerCode, @RequestBody OrderAftersalesOrderChangeDTO dto);


}
