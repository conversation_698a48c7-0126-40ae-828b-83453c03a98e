package com.ets.apply.application.infra.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 行驶证信息审核资料
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("review_order_vehicle")
public class ReviewOrderVehicleEntity extends BaseEntity<ReviewOrderVehicleEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private String reviewOrderVehicleSn;

    /**
     * 审核单主键
     */
    private String reviewOrderSn;

    private String draftSn;

    /**
     * 用户uid
     */
    private Long uid;

    /**
     * 正页照片url
     */
    private String frontImgUrl;

    /**
     * 副页照片url
     */
    private String backImgUrl;

    /**
     * 车头照片url
     */
    private String carImgUrl;

    /**
     * 补充照片url
     */
    private String supplementImgUrl;

    /**
     * 道路运输证url
     */
    private String roadTransportImgUrl;

    /**
     * 激活前车头照片url
     */
    private String carHeaderActivateImgUrl;

    /**
     * 车前挡照片url（激活前车内照）
     */
    private String gearActivateImgUrl;

    private String frontCarImgUrl;

    private String backWmImgUrl;

    private String carWmImgUrl;

    private String supplementWmImgUrl;

    private String roadTransportWmImgUrl;

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * ；0: 蓝色 ；1: 黄色 ；2: 黑色 ；3: 白色 ；4: 渐变绿色 ；5: 黄绿双拼色 ；6: 蓝白渐变色 ；7: 临时牌照 ；9: 未确定 1；1: 绿色 1；2: 红色 
     */
    private Integer plateColor;

    /**
     * 车身颜色
     */
    private String bodyColor;

    /**
     * 车辆类型
     */
    private String type;

    /**
     * 所有人
     */
    private String owner;

    /**
     * 住址
     */
    private String address;

    /**
     * 使用性质
     */
    private String useCharacter;

    /**
     * 品牌型号
     */
    private String model;

    /**
     * 车辆识别代码
     */
    private String vin;

    /**
     * 发动机号码
     */
    private String engineNo;

    /**
     * 注册日期
     */
    private String registerDate;

    /**
     * 发证日期
     */
    private String issueDate;

    /**
     * 核定载人数
     */
    private String passengers;

    /**
     * 外廓尺寸
     */
    private String overallSize;

    /**
     * 红章
     */
    private String redChapter;

    /**
     * 档案编号
     */
    private String fileNumber;

    /**
     * 整备质量
     */
    private String weight;

    /**
     * 总质量
     */
    private String totalWeight;

    /**
     * 备注
     */
    private String remark;

    /**
     * 检验记录
     */
    private String record;

    /**
     * 核定载质量
     */
    private String carryWeight;

    /**
     * 准牵引总质量
     */
    private String tractionWeight;

    /**
     * 是否实名认证过
     */
    private Integer isReal;

    /**
     * 发行方审核通过
     */
    private Integer isVerified;

    /**
     * 车轴数
     */
    private Integer axles;

    /**
     * 关联的ocr识别日志记录表（ocr_result_record）的 record_sn
     */
    private String frontOcrRecordSn;

    /**
     * 关联的ocr识别日志记录表（ocr_result_record）的 record_sn
     */
    private String backOcrRecordSn;

    /**
     * 用于后续数据同步，业务中禁止使用
     */
    private Integer id;

    /**
     * 货车类型：0非货车1普通货车2牵引车集装箱
     */
    private Integer truckType;

    /**
     * 经营范围:0无经营范围给1非货物专用运输车辆 2专用集装箱运输车辆 3混用集装箱运输车辆
     */
    private Integer businessScope;

    /**
     * 业务类型：0默认为空1货车运政车
     */
    private Integer businessType;

    /**
     * 使用性质枚举值[1-营运 2-非营运 3-普通货运（江苏） 4-专用集装箱（江苏） 5-混用集装箱（江苏）]
     */
    private Integer useCharacterType;

    /**
     * 车型[1-一型客车 2-二型客车 3-三型客车 4-四型客车 11-一型货车 12-二型货车 13-三型货车 14-四型货车 15-五型货车 16-六型货车 21-一型专项 22-二型专项 23-三型专项 24-四型专项 25-五型专项 26-六型专项]
     */
    private Integer vehicleType;

    private String powerOfAttorneyImgUrl;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;


}
