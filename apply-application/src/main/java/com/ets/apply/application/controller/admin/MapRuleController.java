package com.ets.apply.application.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.apply.application.app.business.map.MapRuleBusiness;
import com.ets.apply.application.common.dto.map.*;
import com.ets.apply.application.common.vo.map.MapRuleCombineListVO;
import com.ets.apply.application.common.vo.map.MapRuleItemListVO;
import com.ets.apply.application.controller.BaseController;
import com.ets.common.JsonResult;
import com.ets.common.RequestHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import jakarta.validation.Valid;

@Controller
@RequestMapping("/admin/mapRule")
public class MapRuleController extends BaseController {
    @Autowired
    private MapRuleBusiness mapRuleBusiness;

    /**
     * 模块列表查询
     * @return
     */
    @RequestMapping("/get-combine-list")
    @ResponseBody
    public JsonResult<IPage<MapRuleCombineListVO>> getCombineList(@RequestBody(required = false) @Valid MapRuleCombineDTO dto) {
        return JsonResult.ok(mapRuleBusiness.getCombineList(dto));
    }


    /**
     * 获取产品包列表
     * @return
     */
    @RequestMapping("/get-item-list")
    @ResponseBody
    public JsonResult<IPage<MapRuleItemListVO>> getItemList(@RequestBody(required = false) @Valid MapRuleItemDTO mapRuleItemDTO) {
        return JsonResult.ok(mapRuleBusiness.getItemList(mapRuleItemDTO));
    }

    /**
     *  添加产品包
     * @return
     */
    @RequestMapping("/add-combine")
    @ResponseBody
    public JsonResult<Boolean> addCombine(@RequestBody(required = false) @Valid MapRuleAddCombineDTO dto) {
        mapRuleBusiness.addCombine(dto,RequestHelper.getHttpServletRequest().getHeader("loginCode"));
        return JsonResult.ok(true);
    }
    /**
     *  修改产品包
     * @return
     */
    @RequestMapping("/modify-combine")
    @ResponseBody
    public JsonResult<Boolean> modifyCombine(@RequestBody(required = false) @Valid MapRuleEditCombineDTO dto) {
        mapRuleBusiness.updateCombine(dto,RequestHelper.getHttpServletRequest().getHeader("loginCode"));
        return JsonResult.ok(true);
    }

    @RequestMapping("/add-item")
    @ResponseBody
    public JsonResult<Boolean> addItem(@RequestBody(required = false) @Valid MapRuleAddItemDTO dto) {
        mapRuleBusiness.addItem(dto,RequestHelper.getHttpServletRequest().getHeader("loginCode"));
        return JsonResult.ok(true);
    }
    /**
     *  修改产品包
     * @return
     */
    @RequestMapping("/modify-item")
    @ResponseBody
    public JsonResult<Boolean> modifyItem(@RequestBody(required = false) @Valid MapRuleEditItemDTO dto) {
        mapRuleBusiness.updateItem(dto,RequestHelper.getHttpServletRequest().getHeader("loginCode"));
        return JsonResult.ok(true);
    }
}
