package com.ets.apply.application.common.consts.reviewOrder;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ThirdReviewStatus {

    notPush(1, "未推送"),
    pushing(2, "推送中"),
    wait(3, "待审核"),
    pass(4, "审核通过"),
    fail(5, "审核失败"),
    reject(6, "");

    private final int code;
    private final String description;

    public static String getDescByCode(int code) {
        for (ThirdReviewStatus node : ThirdReviewStatus.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }
}
