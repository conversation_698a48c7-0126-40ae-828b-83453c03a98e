package com.ets.apply.application.app.business.creditCard;

import cfca.sadk.algorithm.sm2.SM2PublicKey;
import cfca.sadk.cgb.toolkit.BASE64Toolkit;
import cfca.sadk.cgb.toolkit.SM2Toolkit;
import cfca.sadk.cgb.toolkit.SM3Toolkit;
import cfca.sadk.cgb.toolkit.SM4Toolkit;
import cn.hutool.core.io.IoUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.ets.apply.application.app.service.bank.CgbService;
import com.ets.apply.application.common.config.CgbBankConfig;
import com.ets.apply.application.common.dto.bank.*;
import com.ets.apply.application.common.utils.bank.SM2SignUtil;
import com.ets.apply.application.common.utils.bank.SM4Util;
import com.ets.apply.application.common.vo.bank.CgbOpenQueryVO;
import com.ets.apply.application.common.vo.bank.CgbSm2VO;
import com.ets.apply.application.common.vo.bank.CgbVerifyCodeVO;
import com.ets.apply.application.common.vo.bank.CgbVo;
import com.ets.common.ToolsHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class CgbBusiness {

    @Autowired
    private CgbBankConfig cgbBankConfig;

    @Autowired
    private CgbService cgbService;

    private static final String DEFAULT_CHAR = "123457890abcdefghijklmnopqrstuvwxyz";

    /**
     * 获取签名
     *
     * @param dto:参数
     * @return
     */
    public CgbVo sign(CgbSmDto dto) {
        if (StringUtils.isEmpty(dto.getReqStr().getBody())) {
            ToolsHelper.throwException("加密参数不能为空。");
        }
        String signature = "";
        try {
            signature = SM2SignUtil.signString(JSONUtil.toJsonStr(dto.getReqStr()),
                    "bank/" + cgbBankConfig.getCgbToGolden(),
                    "UTF-8");
        } catch (Exception e) {
            ToolsHelper.throwException("获取签免失败:" + e.getMessage());
        }
        CgbVo vo = new CgbVo();
        vo.setEncryptKey(dto.getEncryptKey());
        vo.setMsg("签名成功。");
        vo.setFlag(1);
        vo.setSignature(signature);
        return vo;

    }

    /**
     * Sm2加密
     *
     * @param dto:
     * @return: vo
     */
    public CgbVo sm2Encrypt(CgbSmDto dto) {
        if (StringUtils.isEmpty(dto.getReqStr().getBody())) {
            ToolsHelper.throwException("加密参数不能为空。");
        }
        CgbVo vo = new CgbVo();
        try {
            log.info("报文加密前：=== " + JSONUtil.toJsonStr(dto.getReqStr()) + " ===");
            String encryptKey = RandomStringUtils.random(16, DEFAULT_CHAR).toUpperCase();
            String reqStr = SM4Util.SM4EncryptData(encryptKey, JSONUtil.toJsonStr(dto.getReqStr()), "UTF-8");
            log.info("请求报文加密结果：\n" + reqStr);
            String encryptKey1 = SM2SignUtil.encryptString(
                    "bank/" + cgbBankConfig.getGoldenToCgb(),
                    encryptKey, "UTF-8");
            log.info("encryptKey1：\n" + encryptKey1);
            vo.setFlag(1);
            vo.setEncryptKey(encryptKey);
            vo.setEncryptKeyvalue(encryptKey1);
            vo.setReqString(reqStr);
        } catch (Exception e) {
            ToolsHelper.throwException("加密失败: " + e.getMessage());
        }
        return vo;
    }

    /**
     * SM4解密方法
     *
     * @param dto:
     * @return: 返回解密结果
     */
    public CgbVo sm4Decrypt(CgbSm4Dto dto) {
        if (StringUtils.isEmpty(dto.getReqStr())) {
            ToolsHelper.throwException("解密参数不能为空。");
        }

        if (StringUtils.isEmpty(dto.getEncryptKey())) {
            ToolsHelper.throwException("解密密钥不能为空。");
        }

        String response = "";
        try {
            response = SM4Util.SM4DecryptData(dto.getEncryptKey(), JSONUtil.toJsonStr(dto.getReqStr()), "UTF-8");
            log.info("解密报文:" + response);
        } catch (Exception e) {
            ToolsHelper.throwException("解密失败:" + e.getMessage());
        }

        CgbVo vo = new CgbVo();
        vo.setEncryptKey(dto.getEncryptKey());
        vo.setMsg("解密成功");
        vo.setFlag(1);
        vo.setReqString(response);
        return vo;
    }

    public String verify(CgbVerifyDTO dto) {
        log.info("##广发银行开始解密2: {}##", dto);
        try {
            InputStream inputStream = this.getClass().getResourceAsStream("/bank/dppaykey.puk");
            if (ObjectUtils.isEmpty(inputStream)) {
                log.error("公钥文件不存在：/bank/dppaykey.puk");
                ToolsHelper.throwException("公钥不存在");
            }

            SM2Toolkit tool = new SM2Toolkit();
            byte[] arrB = dto.getDecryptData().getBytes();
            int iLen2 = arrB.length;
            byte[] arrOut = new byte[iLen2 / 2];

            byte[] publicByte = IoUtil.readBytes(inputStream);
            log.info("##广发银行解密publicByte={}##", Arrays.toString(publicByte));

            SM2PublicKey sm2PublicKey = (SM2PublicKey) tool.SM2BuildPublicKey(BASE64Toolkit.encode(publicByte));
            // 两个字符表示一个字节，所以字节数组长度是字符串长度除以2
            for (int i = 0; i < iLen2; i = i + 2) {
                String strTmp = new String(arrB, i, 2);
                arrOut[i / 2] = (byte) Integer.parseInt(strTmp, 16);
            }
            log.info("##广发银行解密arrOut={}##", Arrays.toString(arrOut));
            byte[] sm3HashData = SM3Toolkit.SM3HashData(sm2PublicKey, arrOut);
            log.info("##广发银行解密sm3HashData={}##", Arrays.toString(sm3HashData));
            boolean result = tool.SM2VerifyHash(sm2PublicKey, sm3HashData, BASE64Toolkit.decode(dto.getFileSignedValue()));
            log.info("广发银行解密SM2VerifyHash=" + result);
            if (!result) {
                ToolsHelper.throwException("解密结果为空");
            }

            SM4Toolkit toolkit = new SM4Toolkit();
            toolkit.SM4Init(dto.getKey().getBytes(), dto.getKey().getBytes());
            byte[] sm4DecryptData = toolkit.SM4DecryptData(arrOut);
            String fileContent = new String(sm4DecryptData, StandardCharsets.UTF_8);
            log.info("广发银行加密fileContent:" + fileContent);

            return fileContent;
        } catch (Throwable e) {
            log.error("##广发银行解密异常: {}##", e.getLocalizedMessage());
            ToolsHelper.throwException("签名不通过");
        }
        return null;
    }

    public CgbSm2VO sm2EncryptStr(CgbSm2DTO dto) {
        CgbSm2VO cgbSm2VO = new CgbSm2VO();
        try {
            String CGB_KEY = "bank/" + cgbBankConfig.getSm2PublicKey();
            String encodeStr = SM2SignUtil.encryptString(CGB_KEY, dto.getUserData(), "UTF-8");
            cgbSm2VO.setEncryptvalue(encodeStr);
        } catch (Throwable e) {
            log.error("广发银行加密字符串异常：{}", e.getLocalizedMessage());
            ToolsHelper.throwException("");
        }
        return cgbSm2VO;
    }

    public CgbOpenQueryVO openQuery(CgbOpenQueryDTO openQueryDTO) {
        String res = cgbService.custInfoVerifyMergeSendSms(openQueryDTO.getCertNo(), openQueryDTO.getCardNo(), openQueryDTO.getUserName());
        return JSON.parseObject(res, CgbOpenQueryVO.class);
    }

    public CgbVerifyCodeVO verifyCode(CgbVerifyCodeDTO verifyCodeDTO) {
        String res = cgbService.intnPntsOrdrPlace(verifyCodeDTO.getBusinessType(),
                verifyCodeDTO.getSubBusinessType(),
                verifyCodeDTO.getCardNo(),
                verifyCodeDTO.getOrderNo(),
                verifyCodeDTO.getMsgContent(),
                verifyCodeDTO.getMsgNo());
        return JSON.parseObject(res, CgbVerifyCodeVO.class);
    }
}
