package com.ets.apply.application.app.service.bank;

import cn.com.infosec.isfj.Isec;
import cn.com.infosec.isfj.cryptoutil.SM2AlgoUtil;
import cn.com.infosec.isfj.cryptoutil.SymKeyUtil;
import cn.com.infosec.isfj.enums.SymmetricAlgorithm;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.ets.apply.application.app.business.ThirdInterfaceLogBusiness;
import com.ets.apply.application.app.thirdservice.feign.CgbFeign;
import com.ets.apply.application.common.config.CgbBankConfig;
import com.ets.apply.application.common.config.creditBank.CgbCreditBankConfig;
import com.ets.apply.application.common.dto.request.ThirdInterfaceLogDTO;
import com.ets.apply.application.common.dto.request.bank.cgb.CgbReceiveDataDto;
import com.ets.apply.application.common.utils.bank.SM2SignUtil;
import com.ets.apply.application.common.utils.bank.SM4Util;
import com.ets.apply.application.common.utils.bank.cgb.Md5Utils;
import com.ets.apply.application.common.utils.bank.cgb.Sm4Utils;
import com.ets.apply.application.common.vo.bank.CgbVo;
import com.ets.common.ToolsHelper;
import com.ets.starter.config.AppConfig;
import com.google.common.collect.ImmutableMap;
import feign.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.lang.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Component
@Slf4j
public class CgbService {
    static {
        Isec.initialize();
    }

    @Value("${spring.profiles.active}")
    private String ACTIVE;

    @Resource(name = "redisPermanentTemplate")
    private StringRedisTemplate redisPermanentTemplate;

    @Autowired
    private CgbCreditBankConfig cgbCreditBankConfig;

    @Autowired
    private AppConfig appConfig;

    @Autowired
    private CgbBankConfig cgbBankConfig;

    @Autowired
    private CgbFeign cgbFeign;

    @Autowired
    private ThirdInterfaceLogBusiness thirdInterfaceLogBusiness;

    private static final String DEFAULT_CHAR = "123457890abcdefghijklmnopqrstuvwxyz";

    public String decryptData(CgbReceiveDataDto data) throws Exception {
        String sm4Key = SM2AlgoUtil.sm2Decrypt(cgbCreditBankConfig.getEtcSm2PrivateKey(), data.getEncryptKey());
        //SM4解密请求体密文 cbc模式 根据实际要求选择cbc或者ecb模式
        String decryptData = Sm4Utils.symmetricCryptToCbc(sm4Key, data.getData(), false);
        String requestData = new String(Base64.getDecoder().decode(decryptData), StandardCharsets.UTF_8);
        log.info("广发银行请求报文解密结果：{}", requestData);
        //广发公钥验签请求报文
        boolean result = SM2AlgoUtil.sm2RawVerify(cgbCreditBankConfig.getCgbSm2PublicKey(), decryptData, data.getSignature());

        if (!result && !Arrays.asList("dev", "test","pre").contains(appConfig.getEnv())) {
            throw new Exception("验签失败");
        }
        return requestData;
    }

    public Map<String, Object> formatResponse(String code, String message) throws Exception {
        Map<String, Object> dataMap = ImmutableMap.of("respCode", code, "respMsg", message);
        //请求数据域转为json字符串后采用RSA加密
        String respData = JSONObject.toJSONString(dataMap);
        String signature = SM2AlgoUtil.sm2RawSign(cgbCreditBankConfig.getEtcSm2PrivateKey(), Base64.getEncoder().encodeToString(respData.getBytes(StandardCharsets.UTF_8)));
        String sm4key = SymKeyUtil.genSymmetricKey(SymmetricAlgorithm.SM4, 128);
        String encryptData = Sm4Utils.symmetricCryptToCbc(sm4key, Base64.getEncoder().encodeToString(respData.getBytes(StandardCharsets.UTF_8)), true);
        String encryptKey = SM2AlgoUtil.sm2Encrypt(cgbCreditBankConfig.getCgbSm2PublicKey(), sm4key);

        Map<String, Object> param = new HashMap<>();
        param.put("retcode", "SUCCESS");
        param.put("channel", cgbCreditBankConfig.getChannel());

        long timestamp = System.currentTimeMillis();
        param.put("timestamp", timestamp);
        param.put("data", encryptData);
        //仅针对corp_id、timestamp、retcode、data参数进行RSA加签
        param.put("encryptKey", encryptKey);
        param.put("signature", signature);
        return param;
    }

    /**
     * 模拟广发请求ETC
     * @param data
     * @return
     * @throws Exception
     */
    public Map<String, Object> formatRequest(String data)  {
        Map<String, Object> param = new HashMap<>();
        try{
            //请求数据域转为json字符串后采用RSA加密
            //        String privateKey = "q0L0bKYqbc1RprBV4x0vQzC+OjFDHQhZxDGTqqaDSMk=";
            String signature = SM2AlgoUtil.sm2RawSign(cgbCreditBankConfig.getEtcSm2PrivateKey(), Base64.getEncoder().encodeToString(data.getBytes(StandardCharsets.UTF_8)));
            String sm4key = SymKeyUtil.genSymmetricKey(SymmetricAlgorithm.SM4, 128);
            String encryptData = Sm4Utils.symmetricCryptToCbc(sm4key, Base64.getEncoder().encodeToString(data.getBytes(StandardCharsets.UTF_8)), true);
            String encryptKey = SM2AlgoUtil.sm2Encrypt(cgbCreditBankConfig.getEtcSm2PublicKey(), sm4key);

            String certId = Md5Utils.getMd5Hex(cgbCreditBankConfig.getCgbSm2PublicKey());

            param.put("certId", certId);
            param.put("channel", cgbCreditBankConfig.getChannel());

            long timestamp = System.currentTimeMillis();
            param.put("timestamp", timestamp);
            param.put("data", encryptData);
            //仅针对corp_id、timestamp、retcode、data参数进行RSA加签
            param.put("encryptKey", encryptKey);
            param.put("signature", signature);
            return param;
        }catch (Exception e){
            log.info("模拟广发请求ETC异常：",e);
            ToolsHelper.throwException("模拟广发请求ETC异常");
        }
        return param;

    }

    public String getApplyUrl(String orderSn, String redirectUrl) throws Exception {
        // 没有文档，保证跟原PHP 接口数据一致，数据处理方式
        String userData = "productId=" + cgbCreditBankConfig.getProductId() + "&highroadCode=" + cgbCreditBankConfig.getHighroadCode() + "&GDflowNo=" + orderSn + "&GDbackUrl=" + java.net.URLEncoder.encode(redirectUrl, "utf-8") + "&thirdChannel=" + cgbCreditBankConfig.getThirdChannel();
        String sm2EncryptStr = this.sm2EncryptStr(userData);
        String url = UrlBuilder.create()
                .setScheme(cgbCreditBankConfig.getScheme())
                .setHost(cgbCreditBankConfig.getCgbUrl())
                .setPort(cgbCreditBankConfig.getPort())
                .addPath(cgbCreditBankConfig.getApplyOrderUrl())
                .addQuery("encryptvalue", sm2EncryptStr)
                .addQuery("thirdChannel", cgbCreditBankConfig.getThirdChannel())
                .build();
        String query = UrlBuilder.of(url).getQuery().toString();
        return cgbCreditBankConfig.getCgbHostUrl() + cgbCreditBankConfig.getApplyOrderUrl() + "?" + query;
    }

    /**
     * 广发银行加密字符串 --旧的申请接口地址加密数据
     * @param userData
     * @return
     */
    public String sm2EncryptStr(String userData) {
        String encodeStr = "";
        try {
            String cbgKey = "bank/" + cgbBankConfig.getSm2PublicKey();
            encodeStr = SM2SignUtil.encryptString(cbgKey, userData, "UTF-8");
            return encodeStr;
        } catch (Throwable e) {
            log.error("广发银行加密字符串异常：{}", e.getLocalizedMessage());
            ToolsHelper.throwException("");
        }
        return encodeStr;
    }

    public String custInfoVerifyMergeSendSms(String certNo, String cardNo, String userName) {
        String tradeCode = "custInfoVerifyMergeSendSms";

        // 请求参数
        Map<String, String> bodyHeader = getBodyHeaders(tradeCode, cardNo);

        Map<String, String> body = new HashMap<>();
        body.put("merchantNum", cgbBankConfig.getMerchantNum());
        body.put("busiType", "003");
        body.put("custName", userName);
        body.put("certNo", certNo);
        body.put("certType", "0");
        body.put("cardNo", cardNo);
        body.put("orderNo", ToolsHelper.genNum(redisPermanentTemplate, "GuangFa", ACTIVE, 8));
        body.put("tranChannel", cgbBankConfig.getTranChannel());

        return postRequest(tradeCode, bodyHeader, body);
    }

    public String intnPntsOrdrPlace(String businessType, String subBusinessType, String cardNo, String orderNo, String msgContent, String msgNo) {
        String tradeCode = "intnPntsOrdrPlace";

        // 请求参数
        Map<String, String> bodyHeader = getBodyHeaders(tradeCode, cardNo);

        Map<String, String> body = new HashMap<>();
        body.put("toiSubBusinessType", subBusinessType);
        body.put("merchantNum", cgbBankConfig.getMerchantNum());
        body.put("busiType", businessType);
        body.put("orderNo", orderNo);
        body.put("cardNo", cardNo);
        body.put("msgContent", msgContent);
        body.put("msgNo", msgNo);
        body.put("tranChannel", cgbBankConfig.getTranChannel());

        return postRequest(tradeCode, bodyHeader, body);
    }

    private Map<String, String> getHeaders(String sign, String encryptKey) {
        Map<String, String> headers = new HashMap<>();
        headers.put("certId", cgbBankConfig.getCertId());
        headers.put("signType", "SM2");
        headers.put("signature", sign);
        headers.put("encryptType", "SM4");
        headers.put("encryptKey", encryptKey);
        return headers;
    }

    private Map<String, String> getBodyHeaders(String tradeCode, String cardNo) {
        Map<String, String> bodyHeader = new HashMap<>();
        bodyHeader.put("version", cgbBankConfig.getVersion());
        bodyHeader.put("instId", cgbBankConfig.getInstId());
        bodyHeader.put("appId", cgbBankConfig.getAppId());
        bodyHeader.put("productCode", cgbBankConfig.getProductCode());
        bodyHeader.put("tradeCode", tradeCode);
        bodyHeader.put("requestTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
        bodyHeader.put("senderSN", cardNo + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
        return bodyHeader;
    }

    private String postRequest(String tradeCode, Map<String, String> bodyHeader, Map<String, String> body) {
        ThirdInterfaceLogDTO logDTO = new ThirdInterfaceLogDTO();
        logDTO.setLogMethod("cgb:" + tradeCode);
        try {
            // body参数排序
            Map<String, String> sortedBody = new HashMap<>();
            body.entrySet().stream()
                    .filter(Objects::nonNull)
                    .sorted(Map.Entry.comparingByKey())
                    .forEachOrdered(res->sortedBody.put(res.getKey(), res.getValue()));

            Map<String, Object> reqBody = new HashMap<>();
            reqBody.put("header", bodyHeader);
            reqBody.put("body", sortedBody);

            // 请求内容加密、签名
            CgbVo cgbVo = sm4Encrypt(reqBody);
            String sign = getSign(reqBody);

            // 请求头
            Map<String, String> headers = getHeaders(sign, cgbVo.getEncryptKeyvalue());
            log.info("【广发积分兑换】加密前请求体：{}", reqBody);
            logDTO.setLogParams(JSONUtil.toJsonStr(reqBody));

            String url = cgbBankConfig.getCgbHost() +
                    "/gateway/API-2.0/" +
                    cgbBankConfig.getAppId() + "/" +
                    cgbBankConfig.getProductCode() + "/" +
                    tradeCode + "/" +
                    cgbBankConfig.getVersion() + "/" +
                    cgbBankConfig.getPipeline();
            logDTO.setLogRequest(url);

            log.info("【广发积分兑换】URL：{}, headers：{}, body：{}", url, headers, cgbVo.getReqString());
            Response response = cgbFeign.postReq(new URI(url), cgbVo.getReqString(), headers);
            Map<String, Collection<String>> respHeaders = response.headers();
            Response.Body respBody = response.body();

            // 解密返回结果
            String resp = IoUtil.read(respBody.asReader(StandardCharsets.UTF_8));
            String decryptResp = sm4Decrypt(IterableUtils.first(respHeaders.get("encryptkey")), resp);
            log.info("【广发积分兑换】解密返回结果：{}", decryptResp);
            logDTO.setLogRespone(decryptResp);

            return decryptResp;
        } catch (URISyntaxException e) {
            logDTO.setStatus(0);
            logDTO.setLogRespone(e.getLocalizedMessage());
            log.error("构建请求URL异常：" + e.getLocalizedMessage());
            ToolsHelper.throwException("请求地址异常");
        } catch (Throwable e) {
            logDTO.setStatus(0);
            logDTO.setLogRespone(e.getLocalizedMessage());
            log.error("请求广发接口异常：" + e.getLocalizedMessage());
            ToolsHelper.throwException("系统异常，请稍后重试");
        } finally {
            thirdInterfaceLogBusiness.addLog(logDTO);
        }
        return null;
    }

    private CgbVo sm4Encrypt(Map<String, Object> map) {
        CgbVo vo = new CgbVo();
        try {
            String key = RandomStringUtils.random(16, DEFAULT_CHAR).toUpperCase();
            String encodeStr = SM4Util.SM4EncryptData(key, JSONUtil.toJsonStr(map), "UTF-8");
            String encryptKey = SM2SignUtil.encryptString(
                    "bank/" + cgbBankConfig.getGoldenToCgb(),
                    key, "UTF-8");
            vo.setEncryptKeyvalue(encryptKey);
            vo.setReqString(encodeStr);
        } catch (Throwable e) {
            ToolsHelper.throwException("广发银行加密字符串异常：{}" + e.getLocalizedMessage());
        }
        return vo;
    }

    private String sm4Decrypt(String encryptKey, String reqStr) {
        String response = "";
        try {
            String key = SM2SignUtil.decryptString(
                    "bank/" + cgbBankConfig.getCgbToGolden(),
                    encryptKey, "UTF-8");
            response = SM4Util.SM4DecryptData(key, JSONUtil.toJsonStr(reqStr), "UTF-8");
        } catch (Exception e) {
            ToolsHelper.throwException("广发银行解密字符串异常:" + e.getMessage());
        }
        return response;
    }

    private String getSign(Map<String, Object> map) {
        String signature = "";
        try {
            signature = SM2SignUtil.signString(JSONUtil.toJsonStr(map),
                    "bank/" + cgbBankConfig.getCgbToGolden(),
                    "UTF-8");
        } catch (Exception e) {
            ToolsHelper.throwException("广发请求获取签名失败:" + e.getMessage());
        }
        return signature;
    }
}
