package com.ets.apply.application;

import com.ets.apply.application.app.business.creditCard.CreditCardBusiness;
import com.ets.apply.application.app.service.bank.CiticService;
import com.ets.apply.application.app.service.bank.PabService;
import com.ets.apply.application.app.thirdservice.feign.CallCiticNewFeign;
import com.ets.apply.application.app.thirdservice.feign.CallPabFeign;
import com.ets.apply.application.common.config.creditBank.CiticCreditBankConfig;
import com.ets.apply.application.common.dto.request.creditCard.ApplyOrderDTO;
import com.ets.apply.application.common.vo.creditCard.CreditCardApplyOrderVO;
import com.ets.apply.application.common.vo.creditCard.citic.CiticResponseVO;
import com.ets.apply.application.common.vo.creditCard.pab.PabResponseVO;
import com.ets.common.ToolsHelper;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URLEncoder;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

@SpringBootTest(classes = ApplyApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringRunner.class)
@ActiveProfiles(value = "dev")
@Slf4j
public class CiticTest {
    @Autowired
    private CiticService citicService;

    @Test
    public void queryOrder() {
        try {
            //查询一下申请流水的状态
            CiticResponseVO ret = citicService.queryOrder("123456789");
            System.out.println(ret.toString());

        } catch (Exception e) {
            log.info("CreditCardSpd 光大银行申请异常：" + e.getMessage());
            ToolsHelper.throwException("当前办理方式不可用，请稍后再试");
        }


    }


}
