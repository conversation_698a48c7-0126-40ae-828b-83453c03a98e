package com.ets.apply.application.common.consts.unicom;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum UnicomApplyReferTypeEnum {

    APPLY(1, "申办订单");

    private final int code;
    private final String description;

    public static String getDescByCode(int code) {
        for (UnicomApplyReferTypeEnum node : UnicomApplyReferTypeEnum.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }
}
