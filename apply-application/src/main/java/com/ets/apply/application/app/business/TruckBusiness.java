package com.ets.apply.application.app.business;

import com.ets.apply.application.app.service.bigdata.BigDataService;
import com.ets.apply.application.app.thirdservice.response.bigData.BigDataOrderSettlementVO;
import com.ets.apply.application.common.config.TruckConfig;
import com.ets.common.ToolsHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class TruckBusiness {

    @Autowired
    private BigDataService bigDataService;

    @Autowired
    private TruckConfig truckConfig;

    public boolean checkTruckOrderMatch(String orderSn) {

        // 测试订单不查大数据
        if (ObjectUtils.isNotEmpty(truckConfig.getTestTruckRefundOrderSns())
                && truckConfig.getTestTruckRefundOrderSns().contains(orderSn)) {
            log.info("测试订单不查大数据：{}", orderSn);
            return true;
        }

        // 查询大数据是否符合条件
        BigDataOrderSettlementVO orderSettlement = bigDataService.orderSettlement(orderSn);
        if (ObjectUtils.isEmpty(orderSettlement)) {
            ToolsHelper.throwException("获取大数据退还状态结果为空");
        }

        // 符合条件
        return orderSettlement.getReturnFlag().equals("0") && orderSettlement.getStandardFlag().equals("1");
    }
}
