package com.ets.apply.application.common.dto.request.admin.orderOrder;

import jakarta.validation.constraints.Min;
import lombok.Data;

@Data
public class AdminOrderOrderGetListDTO {
    private String orderSn = "";
    private String plateNo = "";
    private String plateColor = "";
    private Long uid = 0L;
    private String sendPhone = "";
    private String sendName = "";
    private String packageSn = "";
    /**
     * 1、待支付 2 、待发货 3、待签收 4、已完成 5、已取消
     */
    private Integer status;
    private String createTimeBegin;

    private String createTimeEnd;

    @Min(1)
    private Integer pageNum = 1;

    @Min(1)
    private Integer pageSize = 20;

}
