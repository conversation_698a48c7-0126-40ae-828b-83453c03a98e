package com.ets.apply.application.common.consts.productOrder;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
@AllArgsConstructor
public enum ProductOrderStatusEnum {

    DEFAULT(10, "未支付"),
    PAID(20, "待发货"),
    SHIPPED(30, "已发货"),
    RECEIVED(40, "已收货"),
    AFTER_SALE(50, "售后中"),
    REFUNDING(60, "待退款"),
    ACTIVATED(70, "已完成"),
    CLOSED(90, "已关闭");


    private  final Integer code;

    private  final String description;

    public static final Map<String, String> map;

    static {
        ProductOrderStatusEnum[] enums = ProductOrderStatusEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(
                LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getCode().toString(), enums[index].getDescription()),
                Map::putAll
        );
    }

    public static String getDescByCode(Integer code) {
        for (ProductOrderStatusEnum node : ProductOrderStatusEnum.values()) {
            if (node.getCode().equals(code)) {
                return node.getDescription();
            }
        }
        //兼容我方收钱的订单状态
        return switch (code) {
            case 61 -> "待退款";
            case 62 -> "已关闭";
            default -> "";
        };
    }

    public static List<Map<String, String>> getLabelList() {
        List<Map<String, String>> list = new ArrayList<>();

        for (ProductOrderStatusEnum node : ProductOrderStatusEnum.values()) {
            Map<String, String> row = new LinkedHashMap<>();
            row.put("label", node.getDescription());
            row.put("value", node.getCode().toString());

            list.add(row);
        }

        return list;
    }

    public static ProductOrderStatusEnum getByCode(Integer code) {
        for (ProductOrderStatusEnum node : ProductOrderStatusEnum.values()) {
            if (node.getCode().equals(code)) {
                return node;
            }
        }
        return null;
    }
}
