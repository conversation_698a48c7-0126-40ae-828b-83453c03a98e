package com.ets.apply.application.common.vo.adminOrder;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class ChooseUserInfoVO {

    /**
     * 下单人uid
     */
    private Long uid;

    /**
     * 下单人昵称
     */
    private String nickname;

    /**
     * 车牌号列表
     */
    private List<PlateNoInfo> plateNoList;

    /**
     * 手机 脱敏
     */
    private String phone;

    /**
     * 注册时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 手机号来源
     */
    private String channel;

    /**
     * 姓名 脱敏
     */
    private String sendName;

    /**
     * 当前车牌信息
     */
    private String currentPlateNoInfo;

    @Data
    public static class PlateNoInfo {

        /**
         * 车牌号列表
         */
        private String plateNo;

        /**
         * 0: 蓝色 ；1: 黄色 ；2: 黑色 ；3: 白色 ；4: 渐变绿色 ；5: 黄绿双拼色 ；6: 蓝白渐变色 ；7: 临时牌照 ；9: 未确定 11: 绿色【不用】 12: 红色
         */
        private Integer plateColor;
    }

}
