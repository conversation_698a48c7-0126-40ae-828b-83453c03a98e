package com.ets.apply.application.app.thirdservice.feign;
import com.ets.apply.application.app.thirdservice.request.user.GetDepositRecordListDTO;
import com.ets.apply.application.app.thirdservice.request.user.UpdateDepositRecordStatusDTO;
import com.ets.apply.application.app.thirdservice.request.user.UpdateUserDepositDTO;
import com.ets.apply.application.app.thirdservice.response.user.UserDepositVO;
import com.ets.apply.application.app.thirdservice.response.user.UsersDepositRecordVO;
import com.ets.apply.application.common.dto.user.GetListByApplyOrderSnDTO;
import com.ets.apply.application.common.vo.user.UsersCardsEntityVO;
import com.ets.apply.application.common.vo.user.UsersEntityVO;
import com.ets.common.JsonResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

import com.alibaba.fastjson.JSONObject;

@FeignClient(
        url = "${microUrls.user:http://user-application:20050}",
        name = "UserApplicationFeign"
)
public interface UserApplicationFeign {

    @RequestMapping("/users/getById")
    JsonResult<UsersEntityVO> getById(@RequestParam(value = "uid") Long uid);

    @PostMapping("/usersCards/getListByApplyOrderSn")
    JsonResult<List<UsersCardsEntityVO>> getListByApplyOrderSn(@RequestBody GetListByApplyOrderSnDTO dto);

    @PostMapping("/users/getRealNameByUid")
    JsonResult<JSONObject> getRealNameByUid(@RequestParam(value = "uid") Long uid);

    @PostMapping("/user-deposit-record/get-list")
    JsonResult<List<UsersDepositRecordVO>> getDepositRecordList(@RequestBody GetDepositRecordListDTO dto);

    /**
     * 更新货车保证金记录状态
     * @param dto 更新参数
     * @return 更新结果
     */
    @PostMapping("/user-deposit-record/update-status")
    JsonResult<Void> updateDepositRecordStatus(@RequestBody UpdateDepositRecordStatusDTO dto);

    @RequestMapping("/userDeposit/get-one-by-user-deposit-sn")
    JsonResult<UserDepositVO> getUserDepositByUserDepositSn(@RequestParam(value = "userDepositSn") String userDepositSn);

    /**
     * 更新货车保证金账户信息（状态、支出金额等）
     * @param dto 更新参数
     * @return 更新结果
     */
    @PostMapping("/userDeposit/update")
    JsonResult<Void> updateUserDeposit(@RequestBody UpdateUserDepositDTO dto);
}
