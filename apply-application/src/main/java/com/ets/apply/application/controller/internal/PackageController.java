package com.ets.apply.application.controller.internal;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.apply.application.app.business.ProductPackageBusiness;
import com.ets.apply.application.common.dto.productPackage.GetInfoByPackageSnDTO;
import com.ets.apply.application.common.dto.request.productPackage.*;
import com.ets.apply.application.common.vo.productPackage.GetListByAddressConfigIdVo;
import com.ets.apply.application.common.vo.productPackage.ProductPackageInfoVo;
import com.ets.apply.application.controller.BaseController;
import com.ets.common.JsonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;


/**
 * <p>
 * 商品套餐配置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-24
 */
@Controller
@RefreshScope
@RestController
@RequestMapping("/package")
public class PackageController extends BaseController {
    @Autowired
    private ProductPackageBusiness productPackageBusiness;


    /**
     * 通过地址配置id查找
     * @return
     */
    @RequestMapping("/getListByAddressConfigId")
    @ResponseBody
    public JsonResult<IPage<GetListByAddressConfigIdVo>> getListByAddressConfigId(@RequestBody(required = false) @Valid GetListByAddressConfigIdDTO dto) {
        return JsonResult.ok(productPackageBusiness.getListByAddressConfigId(dto));
    }
    @RequestMapping("/getInfoByPackageSn")
    public JsonResult<ProductPackageInfoVo> getInfoByPackageSn(@RequestBody @Valid GetInfoByPackageSnDTO dto) {
        return JsonResult.ok(productPackageBusiness.getInfoByPackageSn(dto));
    }

}

