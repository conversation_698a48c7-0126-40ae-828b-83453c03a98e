package com.ets.apply.application.common.dto.external;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

@Data
public class ExternalSubmitSendBackDTO {

    @NotBlank(message = "外部订单号不能为空")
    private String thirdOrderSn;

    /* 寄回件相关参数 ****/

    /**
     * 回寄快递单号
     */
    @Length(max = 50, message = "快递单号长度不能超过50个字符")
    private String sendbackExpressNumber;

    /**
     * 回寄快递公司
     */
    @Length(max = 10, message = "快递公司长度不能超过10个字符")
    private String sendbackExpressCompany = "默认";

    private Integer serviceType = 1;

}
