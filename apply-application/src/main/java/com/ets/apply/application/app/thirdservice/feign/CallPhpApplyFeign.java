package com.ets.apply.application.app.thirdservice.feign;

import com.ets.apply.application.app.thirdservice.fallback.CallPhpApplyFallbackFactory;
import com.ets.apply.application.app.thirdservice.request.sales.SalesProdDTO;
import com.ets.apply.application.common.bo.flowType.ReadyForPushLogisticOrderBO;
import com.ets.apply.application.common.bo.flowType.ReadyForPushReviewOrderBO;
import com.ets.apply.application.common.dto.OrderSnDTO;
import com.ets.apply.application.common.bo.map.MapSetCacheByAssignIdBO;
import com.ets.apply.application.common.dto.adminSalesUpgrade.SalesUpgradeProdDTO;
import com.ets.apply.application.common.dto.map.MapDelCacheByPackageSnDTO;
import com.ets.apply.application.common.dto.order.CancelOrderDTO;
import com.ets.apply.application.common.dto.request.*;
import com.ets.apply.application.common.dto.request.admin.orderOrder.AdminOrderCancelDTO;
import com.ets.apply.application.common.dto.request.admin.task.AdminTaskExecDTO;
import com.ets.apply.application.common.dto.request.creditCard.CreditCardChangeNotifyDTO;
import com.ets.apply.application.common.dto.request.creditCard.CreditCardFirstAuditNotifyDTO;
import com.ets.apply.application.common.dto.request.orderAftersales.OrderAftersalesChangeDTO;
import com.ets.apply.application.common.dto.request.productOrder.CheckPlateNoDTO;
import com.ets.common.JsonResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 调用PHP队列
 */
@FeignClient(
        url = "${params.microUrls.etc-apply}",
        name = "CallPhpApplyFeign",
        fallbackFactory = CallPhpApplyFallbackFactory.class
)
public interface CallPhpApplyFeign {

    @PostMapping(
            value = "/admincpApi/apply-order/check-can-apply"
    )
    String checkPlateNo(@RequestBody CheckPlateNoDTO params);

    @PostMapping(value = "/notify/credit-card/credit-card-activate-notify")
    String creditCardActivateNotify(@RequestBody CreditCardChangeNotifyDTO params);

    @PostMapping(value = "/notify/credit-card/credit-card-submitted-notify")
    String creditCardSubmittedNotify(@RequestBody CreditCardChangeNotifyDTO params);

    @PostMapping(value = "/notify/credit-card/credit-card-first-audit-notify")
    String creditCardFirstAuditNotify(@RequestBody CreditCardFirstAuditNotifyDTO params);

    @PostMapping(value = "/notify/credit-card/credit-card-audit-notify")
    String creditCardAuditNotify(@RequestBody CreditCardChangeNotifyDTO params);
    @PostMapping(value = "/notify/credit-card/credit-card-audit-failed-notify")
    String creditCardAuditFailedNotify(@RequestBody CreditCardChangeNotifyDTO params);

    @PostMapping(value = "/notify/credit-card/credit-card-first-used-notify")
    String creditCardFirstUsedNotify(@RequestBody CreditCardChangeNotifyDTO params);


    @PostMapping(
            value = "/admincpApi/apply-order/check-plate-unique"
    )
    String checkPlateUnique(@RequestBody CheckPlateNoDTO params);

    @PostMapping(value = "/admincpApi/apply-order/get-product-package-list")
    String getProductPackageList(@RequestBody GetProductPackageListDTO params);

    @PostMapping(value = "/admincpApi/apply-order/get-show-info-by-province")
    String getProductsByProvince(@RequestBody GetProductByProvinceDTO params);

    @PostMapping(value = "/admincpApi/apply-order/submit-plate")
    String getIssuerByPlateNo(@RequestBody GetIssuerByPlateNoDTO params);


    @PostMapping(value = "/admincpApi/map/set-cache-by-module")
    String mapSetCacheByModule(@RequestBody MapSetCacheByModuleDTO params);




    @PostMapping(value = "/admincpApi/map/set-cache-by-assign-id")
    String mapSetCacheByAssignId(@RequestBody MapSetCacheByAssignIdBO params);

    @PostMapping(value = "/admincpApi/map/set-cache-by-key")
    String mapSetCacheByKey(@RequestBody MapSetCacheByKeyDTO params);

    @PostMapping(value = "/admincpApi/map/compare-diff")
    String mapCompareDiff(@RequestBody MapCompareDiffDTO params);

    @PostMapping(value = "/notify/credit-card/change-notify")
    String changeNotify(CreditCardChangeNotifyDTO params);

    @PostMapping(value = "/admincpApi/map/del-cache-by-package-sn")
    String delCacheByPackageSn(@RequestBody MapDelCacheByPackageSnDTO params);

    @PostMapping(value = "/admincpApi/apply-order/cancel")
    String orderCancel(@RequestBody AdminOrderCancelDTO params);

    @PostMapping(value = "/admincpApi/apply-order/ready-for-push-review-order")
    String readyForPushReviewOrder(@RequestBody ReadyForPushReviewOrderBO params);
    @PostMapping(value = "/admincpApi/apply-order/ready-for-push-logistic-order")
    String readyForPushLogisticOrder(@RequestBody ReadyForPushLogisticOrderBO params);
    @PostMapping(value = "/admincpApi/apply-order/offline-delivery")
    String offlineDelivery(@RequestBody OrderSnDTO params);

    @PostMapping(value = "/notify/goods-order/after-sale-change")
    String goodsOrderAfterSaleChange(OrderAftersalesChangeDTO dto);

    @PostMapping(value = "/admincpApi/task/exec")
    String taskExec(@RequestBody AdminTaskExecDTO dto);

    @PostMapping(value = "/admincpApi/sales/coupon-prod")
    String salesCouponProd(@RequestBody SalesProdDTO dto);

    @PostMapping(value = "/admincpApi/sales-upgrade/prod")
    String salesUpgradeProd(@RequestBody SalesUpgradeProdDTO params);

    @PostMapping(value = "/notify/order/cancel")
    JsonResult<Object> cancelOrder(@RequestBody CancelOrderDTO dto);
}
