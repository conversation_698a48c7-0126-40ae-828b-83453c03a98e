package com.ets.apply.application.common.consts.issueService;

import com.ets.apply.application.common.vo.SelectOptionsVO;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum IssueServiceLogTypeEnum {

    TYPE_CREATE(1, "创建"),
    TYPE_PAY(2, "支付"),
    TYPE_CANCEL(3, "取消"),
    TYPE_VOID(4, "注销"),
    TYPE_FINISH(5, "完成"),
    TYPE_BIND_USER(6, "绑定用户"),
    TYPE_REFUND(7, "退款"),
    TYPE_BIND_VEHICLE(8, "车辆绑定"),
    TYPE_ACTIVATED(9, "激活成功");

    private final int code;
    private final String description;

    public static String getDescByCode(int code) {
        for (IssueServiceLogTypeEnum node : IssueServiceLogTypeEnum.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }

    public static List<SelectOptionsVO> getSelectOptions() {

        return Stream.of(IssueServiceLogTypeEnum.values())
                .map(item -> new SelectOptionsVO(String.valueOf(item.getCode()), item.getDescription()))
                .collect(Collectors.toList());
    }
}
