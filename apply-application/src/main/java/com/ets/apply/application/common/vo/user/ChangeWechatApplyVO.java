package com.ets.apply.application.common.vo.user;

import lombok.Data;

import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * <AUTHOR>
 */
@Data
public class ChangeWechatApplyVO {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 申请用户
     */
    private Long uid;

    /**
     * 发起更换申请的微信的unionid
     */
    private String applyUnionId;

    /**
     * 被更换的用户
     */
    private Integer changedUid;

    /**
     * 申请车牌
     */
    private String plateNo;

    /**
     * 申请身份证
     */
    private String idNumber;

    /**
     * 联系手机号
     */
    private String phone;

    /**
     * 0 未验证 1 已验证
     */
    private Integer phoneVerifyStatus;

    /**
     * 版本号，用于区分不同的功能版本
     */
    private Integer version;

    /**
     * 卡种（对应 etc_cards.id）
     */
    private Integer cardId;

    /**
     * 用户卡ID
     */
    private Integer userCardId;

    /**
     * 申请状态：0提交申请；10已签约；20审核通过；25更换处理中；30审核未通过；40已更换
     */
    private Integer status;

    /**
     * 被更换微信用户信息
     */
    private String changedUser;

    /**
     * 拒绝原因
     */
    private String rejectReason;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

}
