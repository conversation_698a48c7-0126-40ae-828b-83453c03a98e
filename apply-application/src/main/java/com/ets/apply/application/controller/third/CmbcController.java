package com.ets.apply.application.controller.third;

import com.ets.apply.application.app.business.creditCard.CmbcBusiness;
import com.ets.common.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;

@RequestMapping("/third/cmbc")
@RefreshScope
@RestController
@Slf4j
public class CmbcController {


    @Autowired
    CmbcBusiness cmbcBusiness;
    @PostMapping("/receiveOrder")
    public JsonResult<?> receiveOrder(@RequestBody String params) throws ParseException {
        cmbcBusiness.receiveOrder(params);
        return JsonResult.ok();
    }
}
