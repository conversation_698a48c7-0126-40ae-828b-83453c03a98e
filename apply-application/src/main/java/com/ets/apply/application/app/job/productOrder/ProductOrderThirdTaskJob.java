package com.ets.apply.application.app.job.productOrder;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ets.apply.application.app.business.ProductOrderBusiness;
import com.ets.apply.application.app.business.productOrder.ProductOrderThirdTaskBusiness;
import com.ets.apply.application.common.bo.productOrder.ProductOrderThirdRefundIngBO;
import com.ets.apply.application.common.bo.productOrderThirdTask.ProductOrderThirdTaskNotifyHandlerBO;
import com.ets.apply.application.common.consts.productOrder.ProductOrderLogTypeEnum;
import com.ets.apply.application.common.consts.productOrder.ProductOrderStatusEnum;
import com.ets.apply.application.infra.entity.ProductOrderEntity;
import com.ets.apply.application.infra.service.ProductOrderService;
import com.ets.common.ToolsHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Arrays;

@Component
public class ProductOrderThirdTaskJob {

    @Autowired
    private ProductOrderThirdTaskBusiness productOrderThirdTaskBusiness;
    @Autowired
    private ProductOrderService productOrderService;
    @Autowired
    private ProductOrderBusiness productOrderBusiness;


    /**
     * 处理14 天内未通知完成的订单，并通知第三方
     */
    @XxlJob("thirdTaskNotifyHandler")

    public ReturnT<String> thirdTaskNotifyHandler(String params) {

        ProductOrderThirdTaskNotifyHandlerBO thirdTaskNotifyHandlerBO = JSONObject.parseObject(params,
                ProductOrderThirdTaskNotifyHandlerBO.class);
        if (ObjectUtil.isEmpty(thirdTaskNotifyHandlerBO.getStartTime()) || ObjectUtil.isEmpty(thirdTaskNotifyHandlerBO.getEndTime())) {
            // 获取开始时间和结束时间 默认间隔14 天的数据都获取
            LocalDateTime beginDateTime = LocalDateTime.now().minusDays(thirdTaskNotifyHandlerBO.getGapDays());
            LocalDateTime endDateTime = LocalDateTime.now();
            thirdTaskNotifyHandlerBO.setStartTime(beginDateTime);
            thirdTaskNotifyHandlerBO.setEndTime(endDateTime);
        }
        productOrderThirdTaskBusiness.syncUnfinishedTask(thirdTaskNotifyHandlerBO);
        return ReturnT.SUCCESS;

    }

    /**
     * 大于14 天到1个月的数据再捞取进行通知
     */

    @XxlJob("thirdTaskNotifyGapDaysHandler")
    public ReturnT<String> thirdTaskNotifyGapDaysHandler(String params) {

        ProductOrderThirdTaskNotifyHandlerBO thirdTaskNotifyHandlerBO = JSONObject.parseObject(params,
                ProductOrderThirdTaskNotifyHandlerBO.class);

        LocalDateTime startTime = LocalDateTime.now().minusDays(thirdTaskNotifyHandlerBO.getGapDays() + 30);
        LocalDateTime endTime = LocalDateTime.now().minusDays(thirdTaskNotifyHandlerBO.getGapDays());

        thirdTaskNotifyHandlerBO.setStartTime(startTime);
        thirdTaskNotifyHandlerBO.setEndTime(endTime);
        productOrderThirdTaskBusiness.syncUnfinishedTask(thirdTaskNotifyHandlerBO);
        return ReturnT.SUCCESS;

    }

    /**
     * 客服确认要取消退款的第三方订单，修改成待退款状态，然后后台取消
     */

    @XxlJob("thirdOrderToRefundIngHandler")
    public ReturnT<String> thirdOrderToRefundIngHandler(String params) {
        ProductOrderThirdRefundIngBO refundIngBO = JSONObject.parseObject(params, ProductOrderThirdRefundIngBO.class);
        ProductOrderEntity entity = productOrderService.getOneByColumn(refundIngBO.getProductOrderSn(), ProductOrderEntity::getProductOrderSn);
        if (entity == null) {
            ToolsHelper.throwException("记录不存在");
        }
        if (!Arrays.asList(
                ProductOrderStatusEnum.PAID.getCode(),
                ProductOrderStatusEnum.DEFAULT.getCode(),
                ProductOrderStatusEnum.SHIPPED.getCode(),
                ProductOrderStatusEnum.RECEIVED.getCode()
        ).contains(entity.getOrderStatus())
        ) {
            ToolsHelper.throwException("不能进行该操作");
        }

        LambdaUpdateWrapper<ProductOrderEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ProductOrderEntity::getProductOrderSn, refundIngBO.getProductOrderSn())
                .set(ProductOrderEntity::getCancelReason, refundIngBO.getReason())
                .set(ProductOrderEntity::getOrderStatus, ProductOrderStatusEnum.REFUNDING.getCode());
        productOrderService.updateByWrapper(wrapper);

        // 记录日志
        productOrderBusiness.createLog(
                ProductOrderLogTypeEnum.FINISH_AFTER_SALE.getCode(),
                entity,
                ProductOrderStatusEnum.REFUNDING.getCode(),
                "订单修改成待退款:"+refundIngBO.getReason(),
                "system"
        );
        return ReturnT.SUCCESS;

    }
}
