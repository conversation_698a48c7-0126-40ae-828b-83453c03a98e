package com.ets.apply.application.common.consts.productPackage;

import com.ets.apply.application.common.consts.productOrder.ProductOrderStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum ProductPackageIsShowAddEnum {

    SHOW_ADD_NO(0, "否"),
    SHOW_ADD_YES(1, "是");

    private final Integer value;
    private final String desc;

    public static String getDescByCode(int code) {
        for (ProductPackageIsShowAddEnum node : ProductPackageIsShowAddEnum.values()) {
            if (node.getValue().equals(code)) {
                return node.getDesc();
            }
        }
        return "";
    }

    public static List<Map<String,String>> getLabelList() {
        List<Map<String, String>> list = new ArrayList<>();

        for (ProductPackageIsShowAddEnum node : ProductPackageIsShowAddEnum.values()) {
            Map<String, String> row = new LinkedHashMap<>();
            row.put("label", node.getDesc());
            row.put("value", node.getValue().toString());

            list.add(row);
        }
        return list;
    }
}
