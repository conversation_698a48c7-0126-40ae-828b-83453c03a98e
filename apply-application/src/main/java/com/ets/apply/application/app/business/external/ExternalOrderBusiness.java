package com.ets.apply.application.app.business.external;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ets.apply.application.app.business.ProductOrderBusiness;
import com.ets.apply.application.app.business.orderAftersales.OrderAftersalesBusiness;
import com.ets.apply.application.app.factory.productPartner.ProductPartnerFactory;
import com.ets.apply.application.app.thirdservice.feign.CallGoodsApplication;
import com.ets.apply.application.app.thirdservice.feign.CallPhpApplyFeign;
import com.ets.apply.application.common.bo.external.*;
import com.ets.apply.application.common.bo.productOrder.ProductOrderShipBO;
import com.ets.apply.application.common.config.MicroUrlsConfig;
import com.ets.apply.application.common.consts.productOrder.*;
import com.ets.apply.application.common.dto.external.*;
import com.ets.apply.application.common.dto.order.CancelOrderDTO;
import com.ets.apply.application.common.dto.request.orderAftersales.OrderAftersalesCheckDTO;
import com.ets.apply.application.common.dto.request.productOrder.ThirdAddOrderDTO;
import com.ets.apply.application.common.vo.wecar.WecarAddOrderVo;
import com.ets.apply.application.infra.entity.ProductOrderEntity;
import com.ets.apply.application.infra.entity.ProductPackageEntity;
import com.ets.apply.application.infra.service.ProductOrderService;
import com.ets.apply.application.infra.service.ProductPackageService;
import com.ets.common.ToolsHelper;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;

@Component
public class ExternalOrderBusiness {


    @Autowired
    private ProductOrderBusiness productOrderBusiness;

    @Resource(name = "defaultRedisTemplate")
    protected StringRedisTemplate defaultRedisTemplate;

    @Autowired
    private CallGoodsApplication callGoodsApplication;

    @Autowired
    private ProductOrderService productOrderService;

    @Autowired
    private ProductPackageService productPackageService;

    @Autowired
    private MicroUrlsConfig microUrlsConfig;

    @Autowired
    private OrderAftersalesBusiness orderAftersalesBusiness;

    @Autowired
    private CallPhpApplyFeign callPhpApplyFeign;

    public  HashMap<String, String> create(ExternalOrderCreateDTO dto, String externalName) {

        HashMap<String, String> result = new HashMap<>();

        // 加锁，防并发创建订单
        String lockKey = "lock:external-order-create:" + externalName + ":" + dto.getThirdOrderSn();
        if (! ToolsHelper.addLock(defaultRedisTemplate, lockKey, 5)) {
            ToolsHelper.throwException("请求过于频繁，请稍后再试");
        }

        // 幂等兼容
        ProductOrderEntity existOrder = productOrderBusiness.getThirdOrderExist(externalName, dto.getThirdOrderSn());
        if (existOrder != null) {
            result.put("spOrderId", existOrder.getProductOrderSn());
            return result;
        }

        ThirdAddOrderDTO addOrderDTO = new ThirdAddOrderDTO();
        addOrderDTO.setCompanyId(externalName);
        addOrderDTO.setUserCode(dto.getUserCode());
        addOrderDTO.setApplyOrderNo(dto.getThirdOrderSn());
        addOrderDTO.setVehicleNo(dto.getPlateNo());
        addOrderDTO.setReceiver(dto.getReceiver());
        addOrderDTO.setReceiverAddress(dto.getAddress());
        addOrderDTO.setMobile(dto.getPhone());
        addOrderDTO.setApplyProductId(dto.getProductId());
        addOrderDTO.setTotalAmount(dto.getTotalAmount());
        addOrderDTO.setPaidAmount(dto.getPaidAmount());

        WecarAddOrderVo addOrderVo = productOrderBusiness.addOrderFromThird(addOrderDTO);

        productOrderBusiness.thirdHasPaid(addOrderVo.getSpOrderId());

        result.put("spOrderId", addOrderVo.getSpOrderId());

        return result;
    }

    public String getPackageSnByProductId(String productId, String externalName) {
        // 产品包编号映射
        HashMap<String, String> productMap = productOrderBusiness.getProductMapByCompanyId(externalName);
        if (productMap == null || productMap.isEmpty()) {
            ToolsHelper.throwException("外部商品ID和产品包的映射配置为空");
        }
        if (! productMap.containsKey(productId)) {
            ToolsHelper.throwException("外部商品ID和产品包映射不存在："  + productId);
        }

        return productMap.get(productId);
    }

    public ExternalNotifyShippedBO getNotifyShippedBO(ProductOrderShipBO shipBO) {

        ExternalNotifyShippedBO bo = new ExternalNotifyShippedBO();
        ProductOrderEntity productOrder = productOrderBusiness.getProductOrderBySn(shipBO.getProductOrderSn());
        bo.setProductOrder(productOrder);

        ReferValueEnum referValueEnum = ReferValueEnum.getByValue(productOrder.getReferValue());
        if (! productOrder.getReferType().equals(ReferTypeEnum.THIRD_PARTNER_TYPE.getCode())
                || referValueEnum == null) {
            // 不支持的合作商
            return null;
        }
        bo.setReferValueEnum(referValueEnum);

        bo.setSpOrderId(productOrder.getProductOrderSn());
        bo.setUserCode(productOrder.getReferUserCode());
        bo.setExpressNumber(shipBO.getLogisticNumber());
        bo.setExpressCompany(shipBO.getLogisticCompany());
        bo.setExpressTime(shipBO.getExpressTime());
        bo.setGoodsOrderStatus(6);

        return bo;
    }

    public ProductOrderEntity getOrderAndCheck(String externalName, String thirdOrderSn) {

        ProductOrderEntity entity = productOrderBusiness.getThirdOrderExist(externalName, thirdOrderSn);

        if (entity == null) {
            ToolsHelper.throwException("外部渠道有效订单不存在：" + thirdOrderSn);
        }

        return entity;
    }

    public void cancel(ExternalOrderCancelDTO dto, String externalName) {

        ProductOrderEntity entity = productOrderBusiness.getLastThirdOrderExist(externalName, dto.getThirdOrderNo());

        if (entity.getOrderStatus().equals(ProductOrderStatusEnum.CLOSED.getCode())) {
            return;
        }

        // 已创建发货单, 则尝试取消发货单
        productOrderBusiness.cancelDeliveryOrder(entity);

        productOrderBusiness.cancel(
                entity.getProductOrderSn(), dto.getReason(), "外部接口调用"
        );
    }

    public void afterSaleCreateCheck(ExternalOrderAfterSaleCreateDTO dto, String externalName) {

        ProductOrderEntity entity = getOrderAndCheck(externalName, dto.getThirdOrderSn());

        ExternalCreateCustomerBO bo = afterSaleCreateCheckCommon(dto, entity);
        bo.setPreCheck(true);
        // 检查是否可以创建售后单
        callGoodsApplication.customerServiceCreateOrder(bo).checkError();
    }

    protected ExternalCreateCustomerBO afterSaleCreateCheckCommon(ExternalOrderAfterSaleCreateDTO dto, ProductOrderEntity entity) {

        // 未发货则不能创建售后
        if (entity.getSendTime() == null) {
            ToolsHelper.throwException("商品还没有发货，不能申请售后");
        }

        if (! Arrays.asList(
                ProductOrderStatusEnum.RECEIVED.getCode(),
                ProductOrderStatusEnum.SHIPPED.getCode()
        ).contains(entity.getOrderStatus())
        ) {
            ToolsHelper.throwException("当前订单状态不支持售后：" + ProductOrderStatusEnum.getDescByCode(entity.getOrderStatus()));
        }

        LocalDateTime receivedTime = entity.getReceivedTime() != null ? entity.getReceivedTime() : entity.getSendTime();
        // 按快递签收时间+7天计算
        String beginTime = ToolsHelper.localDateTimeToString(receivedTime.plusDays(7));

        // check申办业务 是否可以申请售后
        afterSaleCreateCheckBusiness(entity, dto.getServiceType());

        // 获取产品包sku
        ProductPackageEntity productPackageEntity = productPackageService.getBySn(entity.getPackageSn());
        HashMap<String, Integer> skuInfoMap = new HashMap<>();
        skuInfoMap.put(productPackageEntity.getPackageInfoObj().getString("goods_sku"), 1);

        ExternalCreateCustomerBO bo = new ExternalCreateCustomerBO();
        bo.setServiceType(dto.getServiceType());
        bo.setUid(entity.getUid());

        bo.setBusinessType(2);
        bo.setSkuInfoMap(skuInfoMap);
        bo.setBusinessSn(entity.getProductOrderSn());
        bo.setBusinessBeginTime(beginTime);
        bo.setContactPhone(entity.getSendPhone());

        return bo;
    }

    public JSONObject afterSaleCreate(ExternalOrderAfterSaleCreateDTO dto, String externalName) {

        ProductOrderEntity entity = getOrderAndCheck(externalName, dto.getThirdOrderSn());

        ExternalCreateCustomerBO bo = afterSaleCreateCheckCommon(dto, entity);
        // 创建商品售后单
        bo.setApplyReason("外部合作商创建售后单");
        bo.setProblemDescription(dto.getProblemDescription());
        bo.setSendbackExpressNumber(dto.getSendbackExpressNumber());
        bo.setSendbackExpressCompany(dto.getSendbackExpressCompany());
        bo.setOperator("外部合作商");

        CustomerNotifyUrlBO notifyUrl = new CustomerNotifyUrlBO();
        notifyUrl.setStatusChange(microUrlsConfig.getApply() + "/externalOrderNotify/afterSaleStatusChange");
        bo.setNotifyUrl(notifyUrl);

        return callGoodsApplication.customerServiceCreateOrder(bo).getDataWithCheckError();
    }

    public void afterSaleCreateCheckBusiness(ProductOrderEntity entity, Integer serviceType) {

        if (StringUtils.isEmpty(entity.getApplyOrderSn())) {
            // 未绑定申办单时，允许做售后
            return;
        }

        // 查询申办
        OrderAftersalesCheckDTO dto = new OrderAftersalesCheckDTO();
        dto.setBusinessOrderSn(entity.getApplyOrderSn());
        dto.setUid(entity.getUid());
        dto.setServiceType(serviceType);

        orderAftersalesBusiness.check(dto);
    }

    public void submitSendBack(ExternalSubmitSendBackDTO dto, String externalName) {

        ProductOrderEntity entity = getOrderAndCheck(externalName, dto.getThirdOrderSn());

        // check申办业务 是否可以申请售后
        afterSaleCreateCheckBusiness(entity, dto.getServiceType());

        ExternalSubmitSendBackBO bo = new ExternalSubmitSendBackBO();
        bo.setBusinessType(2);
        bo.setBusinessSn(entity.getProductOrderSn());
        bo.setUid(entity.getUid());
        bo.setSendbackType(1);
        bo.setSendbackExpressNumber(dto.getSendbackExpressNumber());
        bo.setSendbackExpressCompany(dto.getSendbackExpressCompany());
        bo.setSendbackPhone(entity.getSendPhone());

        callGoodsApplication.submitSendBack(bo).checkError();
    }

    public void afterSaleFinish(ExternalOrderAfterSaleStatusChangeDTO dto) {
        // 接收到售后完成通知

        ProductOrderEntity productOrder = productOrderBusiness.getProductOrderBySn(dto.getBusinessOrderSn());

        ReferValueEnum referValueEnum = ReferValueEnum.getByValue(productOrder.getReferValue());
        if (! productOrder.getReferType().equals(ReferTypeEnum.THIRD_PARTNER_TYPE.getCode())
                || referValueEnum == null) {
            // 不支持的合作商
            return;
        }

        // 关闭订单， 申请时暂时不设置售后中状态
        if (! productOrder.getOrderStatus().equals(ProductOrderStatusEnum.CLOSED.getCode())) {

            // 取消关联的申办订单
            if (StringUtils.isNotEmpty(productOrder.getApplyOrderSn())) {
                CancelOrderDTO cancelOrderDTO = new CancelOrderDTO();
                cancelOrderDTO.setApplyOrderSn(productOrder.getApplyOrderSn());
                cancelOrderDTO.setReason("商城订单售后完成关闭，自动取消关联申办订单。相关参数：" + JSON.toJSONString(dto));

                callPhpApplyFeign.cancelOrder(cancelOrderDTO).checkError();
            }

            String reason = "售后退货退款完成";
            productOrderService.closeOrder(productOrder.getProductOrderSn(), reason);
            productOrderBusiness.createLog(
                    ProductOrderLogTypeEnum.CANCEL.getCode(),
                    productOrder,
                    ProductOrderStatusEnum.CLOSED.getCode(),
                    "订单取消(取消原因：" + reason + ")",
                    "系统"
            );
        }

        // 通知外部合作商 工厂模式
        ProductPartnerFactory.create(productOrder.getReferValue()).afterSaleStatusChange(productOrder, dto);

    }

}
