package com.ets.apply.application.infra.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ets.apply.application.infra.entity.sales.SalesCacheDatasEntity;
import com.ets.apply.application.infra.mapper.SalesCacheDatasMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 映射发布生产数据 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
@Service
@DS("db-apply")
public class SalesCacheDatasService extends BaseService<SalesCacheDatasMapper, SalesCacheDatasEntity> {
    /*
     * 获取数据
     */
    public SalesCacheDatasEntity getOneByParams(String cacheMainKey, String cacheKey, String type){
        // 查询条件设置
        LambdaQueryWrapper<SalesCacheDatasEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SalesCacheDatasEntity::getCacheMainKey, cacheMainKey)
                .eq(SalesCacheDatasEntity::getCacheKey, cacheKey)
                .eq(SalesCacheDatasEntity::getType, type);
        return this.getOne(wrapper);
    }
    /*
     * 通过planId获取产品包列表数据
     */
    public JSONObject getCacheValuesByParams(String cacheMainKey, String cacheKey, String type){
        // 查询条件设置
        LambdaQueryWrapper<SalesCacheDatasEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SalesCacheDatasEntity::getCacheMainKey, cacheMainKey)
                .eq(SalesCacheDatasEntity::getCacheKey, cacheKey)
                .eq(SalesCacheDatasEntity::getType, type);
        SalesCacheDatasEntity entity = this.getOne(wrapper);
        if (entity == null){
            return null;
        }
        return JSONObject.parseObject(entity.getCacheValues());
    }
}
