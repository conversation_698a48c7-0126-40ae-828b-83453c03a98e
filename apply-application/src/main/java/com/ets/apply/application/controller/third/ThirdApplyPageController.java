package com.ets.apply.application.controller.third;

import com.ets.apply.application.app.business.ApplyPageBusiness;
import com.ets.apply.application.common.dto.request.applyPage.ApplyPageDTO;
import com.ets.apply.application.common.vo.applyPage.ApplyPageVO;
import com.ets.common.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

@RequestMapping("/third/apply-page")
@RefreshScope
@RestController
@Slf4j
public class ThirdApplyPageController {

    @Autowired
    private ApplyPageBusiness applyPageBusiness;


    @RequestMapping("/get-by-page-sn-with-template")
    public JsonResult<ApplyPageVO> getByPageSnWithTemplate(@RequestBody @Valid ApplyPageDTO applyPageDTO){
        return JsonResult.ok(applyPageBusiness.getByPageSnWithTemplate(applyPageDTO.getPageSn(), applyPageDTO.getPageCategory()));
    }
}
