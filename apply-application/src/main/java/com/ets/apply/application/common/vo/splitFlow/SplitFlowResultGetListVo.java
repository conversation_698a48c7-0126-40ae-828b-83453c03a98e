package com.ets.apply.application.common.vo.splitFlow;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class SplitFlowResultGetListVo {

    private String splitSn;
    private String splitType;
    private String splitName;
    /**
     * 测试的周期：20210420
     */
    private String splitTerm;

    /**
     * 测试结果：A/B/C
     */
    private String splitResult;

    /**
     * 用户uid
     */
    private Long uid;

    /**
     * 关联order_sn，可为空
     */
    private String orderSn;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;


}
