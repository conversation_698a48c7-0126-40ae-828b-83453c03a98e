package com.ets.apply.application.controller.notify;

import com.ets.apply.application.app.business.nissan.NissanBusiness;
import com.ets.apply.application.common.dto.request.nissan.NissanEventLogDTO;
import com.ets.common.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RequestMapping("/nissan")
@RefreshScope
@RestController
@Slf4j
public class NissanController {

    @Autowired
    private NissanBusiness nissanBusiness;

    @RequestMapping("/event-log-notify")
    public JsonResult<?> eventLogNotify(@RequestBody NissanEventLogDTO dto) {
        nissanBusiness.notifyEventLog(dto);
        return JsonResult.ok();
    }
}
