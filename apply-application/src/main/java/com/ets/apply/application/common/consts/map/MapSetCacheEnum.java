package com.ets.apply.application.common.consts.map;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MapSetCacheEnum {
    MAP_SET_CACHE_TYPE_PROD("prod", "发布"),
    MAP_SET_CACHE_TYPE_PREVIEW("preview", "预览"),

    MAP_SET_CACHE_KEY_RULE_COMBINE("ruleCombine", "组合规则"),
    MAP_SET_CACHE_KEY_RULE_ITEM("ruleItem", "子规则"),

    MAP_SET_CACHE_KEY_PLATE_NO("plateNo", "车牌映射"),
    MAP_SET_CACHE_KEY_PROVINCE("province", "省份映射"),
    MAP_SET_CACHE_MODULE_UPGRADE("upgrade", "升级回购模块"),
    MAP_SET_CACHE_MODULE_NORMAL("normal", "普通模块"),
    MAP_SET_CACHE_MODULE_NORMAL_TRUCK("normalTruck", "普通货车模块"),
    MAP_SET_CACHE_MODULE_ASSIGN("assign", "指定模块"),
    MAP_SET_CACHE_MODULE_ASSIGN_PL("assign_pl", "指定pl模块"),
    MAP_SET_CACHE_MODULE_ASSIGN_COUPONID("assign_couponId", "指定优惠券模块"),
    MAP_SET_CACHE_MODULE_ASSIGN_PROMOTION("assign_promotion", "指定promotion模块"),
    MAP_SET_CACHE_MODULE_ASSIGN_COOPERATION("assign_cooperation", "指定cooperation模块"),
    MAP_SET_CACHE_MODULE_BACKUP("backup", "基础产品包"),
    ;


    private final String code;
    private final String description;

    public static String getDescByCode(String code) {
        for (MapSetCacheEnum node : MapSetCacheEnum.values()) {
            if (node.getCode().equals(code)) {
                return node.getDescription();
            }
        }

        return "";
    }

    public static MapSetCacheEnum getByCode(String code) {
        for (MapSetCacheEnum node : MapSetCacheEnum.values()) {
            if (node.getCode().equals(code)) {
                return node;
            }
        }
        return null;
    }
}
