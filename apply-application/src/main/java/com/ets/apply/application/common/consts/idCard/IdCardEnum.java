package com.ets.apply.application.common.consts.idCard;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum IdCardEnum {
    ID(1, "身份证"),

    <PERSON><PERSON><PERSON>(2, "港澳回乡证"),

    <PERSON><PERSON><PERSON>(3, "台胞证"),

    <PERSON><PERSON><PERSON><PERSON>(4, "外籍");

    private final int code;
    private final String description;

    public static String getDescByCode(int code) {
        for (IdCardEnum node : IdCardEnum.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }

}
