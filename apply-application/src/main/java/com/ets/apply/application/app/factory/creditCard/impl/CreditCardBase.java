package com.ets.apply.application.app.factory.creditCard.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.ets.apply.application.app.thirdservice.feign.CallPhpServerFeign;
import com.ets.apply.application.common.bo.creditCard.CreditCardProgressBO;
import com.ets.apply.application.common.bo.creditCard.CreditCardRewardBO;
import com.ets.apply.application.common.consts.activityCreditCardBankUser.ActivityCreditCardBankUserApplyCompleteEnum;
import com.ets.apply.application.common.consts.activityCreditCardBankUser.ActivityCreditCardBankUserStatusEnum;
import com.ets.apply.application.common.consts.activityCreditCardUsersInfo.ActivityCreditCardUserInfoStatusEnum;
import com.ets.apply.application.common.dto.request.bank.common.CreditCardApplyOrderDTO;
import com.ets.apply.application.common.dto.request.creditCard.ApplyOrderDTO;
import com.ets.apply.application.common.dto.request.creditCard.CheckByPhoneDTO;
import com.ets.apply.application.common.dto.request.creditCard.CreditCardCallBackDTO;
import com.ets.apply.application.common.vo.creditCard.CreditCardApplyOrderVO;
import com.ets.apply.application.common.vo.creditCard.CreditCardCallBackVO;
import com.ets.apply.application.common.vo.creditCard.pab.CheckNewUserByPhoneVO;
import com.ets.apply.application.infra.entity.ActivityCreditCardBankUsersEntity;
import com.ets.apply.application.infra.entity.ActivityCreditCardUsersInfoEntity;
import com.ets.apply.application.infra.service.ActivityCreditCardBankUsersService;
import com.ets.apply.application.infra.service.ActivityCreditCardUsersInfoService;
import com.ets.common.JsonResult;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;

public abstract class CreditCardBase implements ICreditCard {

    @Autowired
    private ActivityCreditCardUsersInfoService activityCreditCardUsersInfoService;
    @Autowired
    private ActivityCreditCardBankUsersService activityCreditCardBankUsersService;

    @Autowired
    private CallPhpServerFeign serverFeign;

    @Override
    public void batchReward(CreditCardRewardBO creditCardRewardBO) {

    }

    /**
     * 信用卡申请回调
     */
    public CreditCardCallBackVO callback(CreditCardCallBackDTO dto) {
        return null;
    }

    /**
     * 信用卡申请的基类方法，部分银行未重构，不强制要有申请
     */
    @Override
    public CreditCardApplyOrderVO applyOrder(ApplyOrderDTO applyOrderDTO) {
        return null;
    }

    /**
     * 信用卡申请的基类方法，部分银行未重构，不强制要有申请
     */
    @Override
    public CheckNewUserByPhoneVO checkNewUserByPhone(CheckByPhoneDTO dto) {
        return new CheckNewUserByPhoneVO();
    }

    public void saveBankOrder(ApplyOrderDTO applyOrderDTO, String orderSn) {
        // 活动信息表
        ActivityCreditCardUsersInfoEntity activityCreditCardInfoEntity = new ActivityCreditCardUsersInfoEntity();
        activityCreditCardInfoEntity.setOrderSn(orderSn);
        activityCreditCardInfoEntity.setPlateNo(applyOrderDTO.getPlateNo());
        activityCreditCardInfoEntity.setWhichBank(applyOrderDTO.getWhichBank());
        activityCreditCardInfoEntity.setUserName(applyOrderDTO.getUsername());
        activityCreditCardInfoEntity.setPhoneNumber(applyOrderDTO.getPhoneNumber());
        activityCreditCardInfoEntity.setClassify(applyOrderDTO.getClassify());
        activityCreditCardInfoEntity.setUpdatedAt(LocalDateTime.now());
        activityCreditCardInfoEntity.setCreatedAt(LocalDateTime.now());
        activityCreditCardInfoEntity.setStatus(ActivityCreditCardUserInfoStatusEnum.STATUS_WAIT.getCode());
        activityCreditCardInfoEntity.setUid(applyOrderDTO.getUid());
        activityCreditCardInfoEntity.setReferSn(applyOrderDTO.getReferSn());
        activityCreditCardInfoEntity.setVersion(applyOrderDTO.getVersion());
        activityCreditCardInfoEntity.setReferType(applyOrderDTO.getReferType());

        if(ObjectUtil.isNotNull(applyOrderDTO.getSubReferType())){
            activityCreditCardInfoEntity.setSubReferType(applyOrderDTO.getSubReferType());
        }
        // innerVersion 赋值
        if(ObjectUtil.isNotNull(applyOrderDTO.getInnerVersion())){
            activityCreditCardInfoEntity.setInnerVersion(applyOrderDTO.getInnerVersion());
        }

        if(ObjectUtil.isNotNull(applyOrderDTO.getCallback())){
            activityCreditCardInfoEntity.setCallbackUrl(applyOrderDTO.getCallback());
        }
        activityCreditCardUsersInfoService.save(activityCreditCardInfoEntity);

        // 银行信息表
        ActivityCreditCardBankUsersEntity activityCreditCardBankUsersEntity = new ActivityCreditCardBankUsersEntity();
        activityCreditCardBankUsersEntity.setApplyNumber(orderSn);
        activityCreditCardBankUsersEntity.setWhichBank(applyOrderDTO.getWhichBank());
        activityCreditCardBankUsersEntity.setClassify(applyOrderDTO.getClassify());
        activityCreditCardBankUsersEntity.setPhoneNumber(applyOrderDTO.getPhoneNumber());
        activityCreditCardBankUsersEntity.setUserName(applyOrderDTO.getUsername());
        activityCreditCardBankUsersEntity.setVersion(applyOrderDTO.getVersion());
        activityCreditCardBankUsersEntity.setCreatedAt(LocalDateTime.now());
        activityCreditCardBankUsersEntity.setUpdatedAt(LocalDateTime.now());
        activityCreditCardBankUsersService.save(activityCreditCardBankUsersEntity);
    }

    public CreditCardProgressBO markOrderOnProgress(String orderSn) {
        CreditCardProgressBO creditCardProgressBO= new CreditCardProgressBO();
        ActivityCreditCardUsersInfoEntity usersInfoEntity = activityCreditCardUsersInfoService.getOneByColumn(orderSn, ActivityCreditCardUsersInfoEntity::getOrderSn);
        if (ObjectUtil.isNotNull(usersInfoEntity) && usersInfoEntity.getStatus().equals(ActivityCreditCardUserInfoStatusEnum.STATUS_WAIT.getCode())) {
            usersInfoEntity.setStatus(ActivityCreditCardUserInfoStatusEnum.STATUS_NOT_AUDIT.getCode());
            activityCreditCardUsersInfoService.updateById(usersInfoEntity);
            creditCardProgressBO.setClassify(usersInfoEntity.getClassify());
        }

        ActivityCreditCardBankUsersEntity bankUsersEntity = activityCreditCardBankUsersService.getOneByColumn(orderSn, ActivityCreditCardBankUsersEntity::getApplyNumber);
        if (ObjectUtil.isNotNull(bankUsersEntity)) {
            bankUsersEntity.setApplyCompleted(ActivityCreditCardBankUserApplyCompleteEnum.APPLY_COMPLETED.getCode());
            bankUsersEntity.setSubmitTime(LocalDateTime.now());
            activityCreditCardBankUsersService.updateById(bankUsersEntity);
            creditCardProgressBO.setClassify(bankUsersEntity.getClassify());
        }
        return creditCardProgressBO;
    }


    /**
     * 第三方合作类型申请信用卡
     * @param applyOrderDTO
     * @return
     */
    public CreditCardApplyOrderVO applyThirdOrder(ApplyOrderDTO applyOrderDTO) {
        CreditCardApplyOrderDTO creditCardApplyOrderDTO = new CreditCardApplyOrderDTO();
        BeanUtils.copyProperties(applyOrderDTO,creditCardApplyOrderDTO);
        creditCardApplyOrderDTO.setCallbackUrl(applyOrderDTO.getCallback());
        String result = serverFeign.thirdApplyOrder(creditCardApplyOrderDTO);
        JsonResult<String> jsonResult = JsonResult.convertFromJsonStr(result, String.class);
        jsonResult.checkError();
        CreditCardApplyOrderVO applyOrderVO = new CreditCardApplyOrderVO();
        applyOrderVO.setUrl(JSON.parseObject(jsonResult.getData()).get("url").toString());
        applyOrderVO.setUid(applyOrderDTO.getUid());
        return applyOrderVO;
    }
}
