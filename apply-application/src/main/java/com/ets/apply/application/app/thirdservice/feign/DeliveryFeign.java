package com.ets.apply.application.app.thirdservice.feign;

import com.ets.apply.application.app.thirdservice.fallback.DeliveryFallbackFactory;
import com.ets.apply.application.app.thirdservice.request.LogisticsFindByOrderSnDTO;
import com.ets.apply.application.app.thirdservice.request.delivery.GetSkuSummaryDTO;
import com.ets.apply.application.app.thirdservice.request.delivery.RiskCheckInfoUploadDTO;
import com.ets.apply.application.app.thirdservice.response.LogisticsVO;
import com.ets.apply.application.app.thirdservice.response.delivery.GetSkuSummaryByExpressNumberVO;
import com.ets.common.JsonResult;
import com.ets.delivery.feign.request.logistics.LogisticsAttemptCancelDTO;
import com.ets.delivery.feign.response.logistics.AttemptCancelVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import jakarta.validation.Valid;

@FeignClient(
        url = "http://delivery-application:20130",
        name = "deliveryFeign",
        fallbackFactory = DeliveryFallbackFactory.class
)
public interface DeliveryFeign {

    @RequestMapping({"/no-login/logistics/findByOrderSn"})
    JsonResult<LogisticsVO> findByOrderSn(@RequestBody @Valid LogisticsFindByOrderSnDTO findByOrderSnDTO);

   @RequestMapping({"/no-login/logistics/get-sku-summary-by-express-number"})
    JsonResult<GetSkuSummaryByExpressNumberVO> getSkuSummaryByExpressNumber(@RequestBody @Valid GetSkuSummaryDTO getSkuSummaryDTO);

    @RequestMapping({"/risk-review/risk-info-upload"})
    JsonResult<?> riskInfoUpload(@RequestBody @Valid RiskCheckInfoUploadDTO riskCheckInfoU);

    @RequestMapping({"/no-login/logistics/attemptCancel"})
    JsonResult<AttemptCancelVO> attemptCancel(@RequestBody @Validated LogisticsAttemptCancelDTO logisticsAttemptCancelDTO);


}
