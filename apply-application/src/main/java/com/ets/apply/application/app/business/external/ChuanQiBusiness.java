package com.ets.apply.application.app.business.external;

import cn.hutool.core.net.URLEncodeUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson.JSON;
import com.ets.apply.application.app.thirdservice.feign.ExternalNotifyFeign;
import com.ets.apply.application.common.bo.external.ExternalNotifyShippedBO;
import com.ets.apply.application.common.bo.external.chuanqi.ChuanQiAfterSaleFinishNotifyBO;
import com.ets.apply.application.common.bo.external.chuanqi.ChuanQiShippedNotifyBO;
import com.ets.apply.application.common.bo.productOrder.ProductOrderShipBO;
import com.ets.apply.application.common.config.external.ChuanQiConfig;
import com.ets.apply.application.common.consts.external.chuanqi.ChuanQiExpressEnum;
import com.ets.apply.application.common.dto.external.ExternalOrderAfterSaleStatusChangeDTO;
import com.ets.apply.application.common.utils.SignUtil;
import com.ets.apply.application.infra.entity.ProductOrderEntity;
import com.ets.common.JsonResult;
import com.ets.common.ToolsHelper;
import com.ets.starter.component.LockComponent;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.util.HashMap;

@Component
public class ChuanQiBusiness {

    @Autowired
    private ChuanQiConfig config;

    @Autowired
    private ExternalOrderBusiness externalOrderBusiness;

    @Autowired
    private ExternalNotifyFeign externalNotifyFeign;

    @Autowired
    private LockComponent lockComponent;

    public void checkSign(HttpServletRequest request) {

        if (! config.getCheckSign()) {
            return;
        }

        String nonce = request.getHeader("nonce");
        String millisTime = request.getHeader("timestamp");
        String signFromHeader = request.getHeader("signature");
        if (
                StringUtils.isEmpty(nonce)
                        || StringUtils.isEmpty(millisTime)
                        || StringUtils.isEmpty(signFromHeader)
        ) {
            ToolsHelper.throwException("签名参数缺失");
        }

        if (Long.parseLong(millisTime) < System.currentTimeMillis() - 180 * 1000) {
            ToolsHelper.throwException("签名已过期");
        }

        String signStr = URLEncodeUtil.encode(nonce + millisTime + config.getKey()).toUpperCase();

        String signature = DigestUtil.md5Hex(signStr).toUpperCase();

        if (! signature.equals(signFromHeader)) {
            ToolsHelper.throwException("签名错误");
        }

        if (! lockComponent.addLock("ChuanQiSignLock:"+signFromHeader, 180)) {
            // 防止修改body
            ToolsHelper.throwException("签名已失效");
        }
    }

    public void shippedNotify(ProductOrderShipBO shipBO) {

        ExternalNotifyShippedBO bo = externalOrderBusiness.getNotifyShippedBO(shipBO);

        String secret = config.getKey();

        String url = config.getShippedNotifyUrl();
        if (StringUtils.isEmpty(url)) {
            return;
        }

        ChuanQiShippedNotifyBO notifyBO = new ChuanQiShippedNotifyBO();

        ChuanQiExpressEnum expressEnum = ChuanQiExpressEnum.getByName(shipBO.getLogisticCompany());
        notifyBO.setLogisticsCode(expressEnum.getCqCode());
        notifyBO.setLogisticsCompany(expressEnum.getCqName());
        notifyBO.setLogisticsNo(shipBO.getLogisticNumber());
        notifyBO.setOrderNo(bo.getProductOrder().getThirdOrderSn());
        notifyBO.setTime(shipBO.getExpressTime());

        String body = JSON.toJSONString(notifyBO);

        HashMap<String,String> signHeaders = SignUtil.getChuanQiSignInfo(secret);

        HashMap<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.putAll(signHeaders);

        String result = externalNotifyFeign.shippedNotify(URI.create(url), headers, body);
        JSON.parseObject(result, JsonResult.class).checkError();
    }

    public void afterSaleFinishNotify(ProductOrderEntity productOrder, ExternalOrderAfterSaleStatusChangeDTO dto) {

        ChuanQiAfterSaleFinishNotifyBO notifyBO = new ChuanQiAfterSaleFinishNotifyBO();
        notifyBO.setOrderNo(productOrder.getThirdOrderSn());

        String secret = config.getKey();

        String url = config.getAfterSaleFinishNotifyUrl();
        if (StringUtils.isEmpty(url)) {
            return;
        }

        String body = JSON.toJSONString(notifyBO);

        HashMap<String,String> signHeaders = SignUtil.getChuanQiSignInfo(secret);

        HashMap<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.putAll(signHeaders);

        String result = externalNotifyFeign.afterSaleFinishNotify(URI.create(url), headers, body);
        JSON.parseObject(result, JsonResult.class).checkError();
    }

}
