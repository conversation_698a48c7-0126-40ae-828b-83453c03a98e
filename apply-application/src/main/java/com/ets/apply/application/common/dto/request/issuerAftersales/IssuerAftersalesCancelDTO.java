package com.ets.apply.application.common.dto.request.issuerAftersales;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class IssuerAftersalesCancelDTO {
    @JsonProperty(value = "order_sn")
    private String orderSn;

    @JsonProperty(value = "after_sales_type")
    private Integer afterSalesType;

    @JsonProperty(value = "cancel_reason")
    private String cancelReason;

    @JsonProperty(value = "apply_order_sn")
    private String applyOrderSn;


    public IssuerAftersalesCancelDTO(String orderSn,Integer afterSalesType, String applyOrderSn, String cancelReason){
        this.orderSn = orderSn;
        this.afterSalesType = afterSalesType;
        this.applyOrderSn  = applyOrderSn;
        this.cancelReason = cancelReason;
    }
}
