package com.ets.apply.application.app.disposer;

import com.ets.apply.application.app.factory.task.TaskFactory;
import com.ets.apply.application.infra.entity.TaskRecordEntity;
import com.ets.common.queue.BaseDisposer;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;

@NoArgsConstructor
@Component(value = "TaskJobBean")
public class TaskDisposer extends BaseDisposer {
    public TaskDisposer(Object params) {
        super(params);
    }

    @Override
    public String getJobBeanName() {
        return "TaskJobBean";
    }

    @Override
    public void execute(Object content) {
        TaskRecordEntity taskRecord = super.getParamsObject(content, TaskRecordEntity.class);
        // 执行任务
        TaskFactory.create(taskRecord.getReferType()).execute(taskRecord.getTaskSn(),false);
    }
}
