package com.ets.apply.application.common.consts.external.chuanqi;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ChuanQiExpressEnum {

    UNKNOWN("未知", "未知", "unknown"),
    JD1("京东配送", "京东物流", "jd"),
    JD2("京东物流", "京东物流", "jd"),
    JD3("京东快递", "京东物流", "jd"),
    EMS("EMS", "EMS", "ems"),
    EMS1("邮政EMS", "EMS", "ems"),
    EMS2("EMS快递", "EMS", "ems"),
    EMS3("EMS", "EMS", "ems"),
    YZXB("邮政小包", "邮政快递包裹", "youzhengguonei"),
    SFSY("顺丰速运", "顺丰速运", "shunfeng"),
    SF("顺丰", "顺丰速运", "shunfeng"),
    SFKD("顺丰快递", "顺丰速运", "shunfeng"),
    ZTO("中通(ZTO)", "中通快递", "zhongtong"),
    ZT("中通快递", "中通快递", "zhongtong"),
    YD("韵达", "韵达快递", "yunda"),
    YDKD("韵达快递", "韵达快递", "yunda"),
    ST("申通快递", "申通快递", "shentong"),
    YT("圆通速递", "圆通速递", "yuantong"),
    ;

    private final String name;
    private final String cqName;
    private final String cqCode;

    public static ChuanQiExpressEnum getByName(String name) {

        for (ChuanQiExpressEnum node : ChuanQiExpressEnum.values()) {
            if (node.getName().equals( name)) {
                return node;
            }
        }

        return UNKNOWN;
    }
}
