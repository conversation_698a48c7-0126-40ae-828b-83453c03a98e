package com.ets.apply.application.common.dto.external;

import lombok.Data;

@Data
public class ExternalOrderAfterSaleStatusChangeDTO {

    // 售后服务单状态
    private Integer customerStatus;

    /**
     * 业务单号
     */
    private String businessOrderSn;

    /**
     * 用户id
     */
    private Long uid;

    /**
     * 售后服务类型
     */
    private Integer serviceType;

    /**
     * 售后服务单号
     */
    private String customerServiceSn;

    /**
     * 完成时间
     */
    private String finishTime;

    /**
     * 是否成功
     */
    private boolean isSuccess;

}
