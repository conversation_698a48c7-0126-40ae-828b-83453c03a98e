package com.ets.apply.application.common.consts.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum IsFrontEnum {
    //    是否前装[0-默认 1-前装]
    DEFAULT(0, "默认"),
    FRONT(1, "前装");

    private final int code;
    private final String desc;

    public static String getDescByCode(int code) {
        for (IsFrontEnum value : IsFrontEnum.values()) {
            if (value.getCode() == code) {
                return value.getDesc();
            }
        }
        return "";
    }
}
