package com.ets.apply.application.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.apply.application.app.business.issue.AdminIssueBusiness;
import com.ets.apply.application.common.dto.issue.IssueGetListDTO;
import com.ets.apply.application.common.vo.SelectOptionsVO;
import com.ets.apply.application.common.vo.issue.IssueLogListVO;
import com.ets.apply.application.common.vo.issue.IssuePageListVO;
import com.ets.common.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.HashMap;
import java.util.List;


@RestController
@RequestMapping("/admin/issueService")
@Slf4j
public class AdminIssueServiceController {

	@Autowired
	private AdminIssueBusiness adminIssueBusiness;

	@PostMapping("/getPageList")
	public JsonResult<IPage<IssuePageListVO>> getPageList(@Valid @RequestBody IssueGetListDTO dto) {

		return JsonResult.ok(adminIssueBusiness.getPageList(dto));
	}

	@PostMapping("/getSelectOptions")
	public JsonResult<HashMap<String, List<SelectOptionsVO>>> getSelectOptions()  {

		return JsonResult.ok(adminIssueBusiness.getSelectOptions());
	}

	@PostMapping("/getLogList")
	public JsonResult<List<IssueLogListVO>> getLogList(@RequestParam(name = "serviceSn") String serviceSn)  {

		return JsonResult.ok(adminIssueBusiness.getLogList(serviceSn));
	}


}
