package com.ets.apply.application.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.apply.application.app.business.SplitFlowConfigBusiness;
import com.ets.apply.application.common.dto.request.splitFlow.AddSplitFlowConfigDTO;
import com.ets.apply.application.common.dto.request.splitFlow.EditSplitFlowConfigDTO;
import com.ets.apply.application.common.dto.request.splitFlow.SplitFlowGetListDTO;
import com.ets.apply.application.common.vo.splitFlow.SplitFlowGetListVo;
import com.ets.apply.application.controller.BaseController;
import com.ets.common.JsonResult;
import com.ets.common.RequestHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import jakarta.validation.Valid;

@Controller
@RequestMapping("/admin/splitFlowConfig")
public class SplitFlowConfigController extends BaseController {

    @Autowired
    private SplitFlowConfigBusiness splitFlowConfigBusiness;
    /**
     * 设置
     * @return
     */
    @RequestMapping("/add")
    @ResponseBody
    public JsonResult<Boolean> add(@RequestBody @Valid AddSplitFlowConfigDTO dto) {
        dto.setOperator(RequestHelper.getHttpServletRequest().getHeader("loginCode"));
        return JsonResult.ok(splitFlowConfigBusiness.add(dto));
    }
    @RequestMapping("/edit")
    @ResponseBody
    public JsonResult<Boolean> edit(@RequestBody @Valid EditSplitFlowConfigDTO dto) {
        dto.setOperator(RequestHelper.getHttpServletRequest().getHeader("loginCode"));
        return JsonResult.ok(splitFlowConfigBusiness.edit(dto));
    }

    /**
     * 列表查询
     * @return
     */
    @RequestMapping("/getList")
    @ResponseBody
    public JsonResult<IPage<SplitFlowGetListVo>> getList(@RequestBody(required = false) @Valid SplitFlowGetListDTO dto) {
        return JsonResult.ok(splitFlowConfigBusiness.getList(dto));
    }
}

