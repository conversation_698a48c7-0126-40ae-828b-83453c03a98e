package com.ets.apply.application.controller.testing;
import com.ets.apply.application.app.factory.task.TaskFactory;
import com.ets.apply.application.common.dto.request.TaskExecTestDto;
import com.ets.common.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import jakarta.validation.Valid;


@RequestMapping("/testing/task")
@RefreshScope
@RestController
@Slf4j
public class TaskController {


    /*
     * 创建商城订单
     */
    @PostMapping("/exec")
    public JsonResult<Boolean> addOrder(@RequestBody @Valid TaskExecTestDto dto) throws Exception {

        // 执行任务
        TaskFactory.create(dto.getReferType()).execute(dto.getTaskSn(),true);

        return JsonResult.ok(true);
    }

}
