package com.ets.apply.application.app.factory.dynamicRecommend.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.ets.apply.application.app.factory.dynamicRecommend.RecommendStrategy;
import com.ets.apply.application.common.config.DynamicRecommendConfig;
import com.ets.apply.application.common.consts.dynamicRecomend.DynamicRecommendTypeEnum;
import com.ets.apply.application.common.consts.order.StatusEnum;
import com.ets.apply.application.common.dto.dynamicRecommend.RecommendDTO;
import com.ets.apply.application.common.vo.dynamicRecommend.DynamicRecommendVO;
import com.ets.apply.application.infra.entity.OrderOrderEntity;
import com.ets.apply.application.infra.entity.ProductPackageEntity;
import com.ets.apply.application.infra.service.CardsService;
import com.ets.apply.application.infra.service.OrderOrderService;
import com.ets.apply.application.infra.service.ProductPackageService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class PaidOrderRecommendStrategy implements RecommendStrategy {

    @Autowired
    private CardsService cardsService;

    @Autowired
    private OrderOrderService orderService;

    @Autowired
    private DynamicRecommendConfig dynamicRecommendConfig;

    @Autowired
    private ProductPackageService productPackageService;

    @Override
    public DynamicRecommendVO recommend(Long uid) {
        return recommendPaidOrder(uid);
    }

    @Override
    public DynamicRecommendVO recommend(Long uid, RecommendDTO recommendDTO) {
        return recommendPaidOrder(uid);
    }

    public DynamicRecommendVO recommendPaidOrder(Long uid) {
        List<Integer> truckIds = cardsService.getAllTruckIdsWithCache();
        // 限制一次最多返回10 个单
        List<OrderOrderEntity> unfinishedOrderList = orderService.getUnfinishedOrderListByConditions(uid,
                truckIds, 10);
        if (CollectionUtils.isEmpty(unfinishedOrderList)) {
            return null;
        }

        // 过滤掉待支付的单
        unfinishedOrderList = unfinishedOrderList.stream()
                .filter(order -> !Objects.equals(order.getStatus(), StatusEnum.WAIT_FOR_PAY.getCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(unfinishedOrderList)) {
            return null;
        }

        OrderOrderEntity firstOrder = unfinishedOrderList.stream().findFirst().orElse(null);
        if (ObjectUtil.isEmpty(firstOrder)) {
            return null;
        }

        // 返回数据不是空的话，组装返回数据

        String description = dynamicRecommendConfig.getPaidOrderRecommendDescription();
        if (firstOrder != null && StringUtils.isNotEmpty(firstOrder.getPackageSn())) {
            // 避免 NullPointerException
            ProductPackageEntity productPackage = productPackageService.getBySnWithCache(firstOrder.getPackageSn());
            if (ObjectUtil.isNotEmpty(productPackage)) {
                // 从产品包获取描述，无则获取配置默认描述信息
                if (ObjectUtil.isNotEmpty(productPackage.getPackageInfo())) {
                    JSONObject packageInfoObject = new JSONObject(productPackage.getPackageInfo());
                    if (packageInfoObject.containsKey("indexUnpaidOrderDesc") && ObjectUtil.isNotEmpty(packageInfoObject.getStr("indexUnpaidOrderDesc"))) {
                        description = packageInfoObject.getStr("indexUnpaidOrderDesc");
                    }
                }
            }
        }

        HashMap<String, Object> params = dynamicRecommendConfig.getPaidOrderRecommendParams();
        params.put("orderSn", Objects.requireNonNull(firstOrder).getOrderSn());
        DynamicRecommendVO vo = new DynamicRecommendVO();
        vo.setRecommendType(DynamicRecommendTypeEnum.RECOMMEND_BY_PAID_ORDER.getType());
        vo.setPath(dynamicRecommendConfig.getPaidOrderRecommendPath());
        vo.setTitle(dynamicRecommendConfig.getPaidOrderRecommendTitle());
        vo.setDescription(description);
        vo.setParams(params);
        vo.setGroupNums(unfinishedOrderList.size());
        return vo;
    }
}
