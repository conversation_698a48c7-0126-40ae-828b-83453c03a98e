package com.ets.apply.application.app.disposer;

import com.ets.apply.application.app.business.unicom.UnicomApplyBusiness;
import com.ets.apply.application.common.bo.unicom.UnicomSelectNumberDisposerBO;
import com.ets.common.queue.BaseDisposer;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@NoArgsConstructor
@Component(value = "UnicomSelectNumberJobBean")
public class UnicomSelectNumberDisposer extends BaseDisposer {

    @Autowired
    private UnicomApplyBusiness unicomApplyBusiness;

    public UnicomSelectNumberDisposer(Object params) {
        super(params);
    }

    @Override
    public String getJobBeanName() {
        return "UnicomSelectNumberJobBean";
    }

    @Override
    public void execute(Object content) {

        UnicomSelectNumberDisposerBO bo = getParamsObject(content, UnicomSelectNumberDisposerBO.class);

        unicomApplyBusiness.asyncSelectNumber(bo.getKeyIndex(), bo.getPageCount());
    }
}
