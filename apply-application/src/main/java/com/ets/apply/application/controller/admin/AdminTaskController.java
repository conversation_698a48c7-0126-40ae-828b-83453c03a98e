package com.ets.apply.application.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.apply.application.app.business.admin.AdminTaskBusiness;
import com.ets.apply.application.app.factory.task.TaskFactory;
import com.ets.apply.application.app.thirdservice.feign.CallPhpApplyFeign;
import com.ets.apply.application.common.dto.request.admin.task.AdminTaskExecDTO;
import com.ets.apply.application.common.dto.request.admin.task.AdminTaskGetListDTO;
import com.ets.apply.application.common.dto.request.admin.task.AdminTaskUpdateDTO;
import com.ets.apply.application.common.vo.admin.task.AdminTaskVO;
import com.ets.apply.application.controller.BaseController;
import com.ets.apply.application.infra.entity.TaskRecordEntity;
import com.ets.apply.application.infra.service.ConfigBizFieldValuesService;
import com.ets.apply.application.infra.service.TaskRecordService;
import com.ets.common.BizException;
import com.ets.common.JsonResult;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 *  申办售后列表
 * </p>
 */
@RequestMapping("/admin/task")
@RefreshScope
@RestController
@Slf4j
@Validated
public class AdminTaskController extends BaseController {
    @Autowired
    private AdminTaskBusiness adminTaskBusiness;
    @Autowired
    private ConfigBizFieldValuesService configBizFieldValuesService;
    @Autowired
    private TaskRecordService taskRecordService;
    @Autowired
    private CallPhpApplyFeign callPhpApplyFeign;


    @RequestMapping("/getList")
    @ResponseBody
    public JsonResult<IPage<AdminTaskVO>> getList(@RequestBody(required = false) @Valid AdminTaskGetListDTO dto) {
        return JsonResult.ok(adminTaskBusiness.getList(dto));
    }
    /**
     * 获取筛选项数组
     * @return JsonResult
     * @throws BizException 异常
     */
    @RequestMapping("/getSelectOptions")
    @ResponseBody
    public JsonResult getSelectOptions() throws BizException {
        //创建map对象
        Map<String,Object> map = new HashMap<String,Object>();
        //给Map中添加元素
        map.put("referType",configBizFieldValuesService.getListByBizFieldKey("task_refer_type",false));
        return JsonResult.ok(map);
    }

    @RequestMapping("/update")
    @ResponseBody
    public JsonResult<Boolean> update(@RequestBody(required = false) @Valid AdminTaskUpdateDTO dto) {
        return JsonResult.ok(adminTaskBusiness.updateTask(dto));
    }

    /*
     *  直接执行
     */
    @RequestMapping("/exec")
    @ResponseBody
    public JsonResult<Integer> exec(@RequestBody(required = false) @Valid AdminTaskUpdateDTO dto) {

        Integer flag;
        TaskRecordEntity taskRecord = taskRecordService.getOneByTaskSn(dto.getTaskSn());
        //区分是php还是java类型
        if(configBizFieldValuesService.checkTaskIsJava(taskRecord.getReferType())){
            // 执行任务
            TaskFactory.create(taskRecord.getReferType()).execute(taskRecord.getTaskSn(),true);
            flag = 1;
        }else{
            callPhpApplyFeign.taskExec(new AdminTaskExecDTO(dto.getTaskSn()));
            flag = 2;
        }

        return JsonResult.ok(flag);
    }
}