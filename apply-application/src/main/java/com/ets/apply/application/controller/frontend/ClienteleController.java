package com.ets.apply.application.controller.frontend;

import com.ets.apply.application.app.business.ClienteleBusiness;
import com.ets.apply.application.common.utils.UserUtil;
import com.ets.apply.application.common.vo.clientele.ClienteleNewUserSplitVO;
import com.ets.apply.application.controller.BaseController;
import com.ets.common.JsonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/frontend/clientele")
public class ClienteleController extends BaseController {

    @Autowired
    private ClienteleBusiness clienteleBusiness;

    @RequestMapping("/getNewUserSplitInfo")
    public JsonResult<ClienteleNewUserSplitVO> getNewUserSplitInfo() {

        return JsonResult.ok(clienteleBusiness.getNewUserSplitInfo(UserUtil.getUid()));
    }


}
