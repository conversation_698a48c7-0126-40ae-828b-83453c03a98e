package com.ets.apply.application.controller.testing;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ets.apply.application.app.event.OrderActivatedEvent;
import com.ets.apply.application.app.factory.productPartner.ProductPartnerFactory;
import com.ets.apply.application.common.bo.productOrder.ProductOrderShipBO;
import com.ets.apply.application.common.bo.task.OrderActivatedBO;
import com.ets.apply.application.infra.entity.ProductOrderEntity;
import com.ets.apply.application.infra.entity.TaskRecordEntity;
import com.ets.apply.application.infra.service.ProductOrderService;
import com.ets.common.JsonResult;
import com.ets.common.ToolsHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 测试 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-15
 */
@RestController
@RequestMapping("/testing/notify")
@Slf4j
public class NotifyController {

    @Autowired
    private ApplicationContext publisher;

    @Autowired
    private ProductOrderService productOrderService;

    @PostMapping("/statusChange")
    @ResponseBody
    public JsonResult<Object> statusChange(@RequestBody JSONObject dto) {

        log.info("订单状态变更：" + JSON.toJSONString(dto));

        return JsonResult.ok();
    }

    @PostMapping("/afterSaleChange")
    @ResponseBody
    public JsonResult<Object> afterSaleChange(@RequestBody JSONObject dto) {

        log.info("售后订单状态变更：" + JSON.toJSONString(dto));

        return JsonResult.ok();
    }

    @PostMapping("/shippedNotify")
    @ResponseBody
    public JsonResult<Object> shippedNotify(@RequestBody ProductOrderShipBO contentBO) {

        ProductPartnerFactory.create(contentBO.getReferValue()).ship(contentBO);

        return JsonResult.ok();
    }

    @PostMapping("/activatedEvent")
    @ResponseBody
    public JsonResult<Object> activatedEvent(@RequestBody JSONObject dto) {

        publisher.publishEvent(new OrderActivatedEvent(dto));

        return JsonResult.ok();
    }

    @PostMapping("/activatedNotify")
    @ResponseBody
    public JsonResult<Object> activatedNotify(@RequestBody OrderActivatedBO contentBO) {

        ProductOrderEntity productOrderEntity = productOrderService.findByApplyOrderSn(contentBO.getOrderSn());

        //ProductPartnerFactory.create(productOrderEntity.getReferValue()).activatedNotify(productOrderEntity, ToolsHelper.localDateTimeToString(contentBO.getActivatedTime()));

        return JsonResult.ok();
    }

}

