package com.ets.apply.application.common.vo.creditCard;

import cn.hutool.core.util.ObjectUtil;
import com.ets.apply.application.common.consts.activityCreditCardBankUser.ActivityCreditCardBankUserIsNewUserEnum;
import com.ets.apply.application.common.consts.activityCreditCardUsersInfo.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class CreditCardListVO {

    private Integer id;

    /**
     * 用户id
     */
    private Long uid;

    /**
     * 银行订单号，唯一标识
     */
    private String orderSn;

    /**
     * 申请类型 1、默认初始类型 2、不发券类型
     */
    private Integer classify;

    /**
     * 姓名
     */
    private String userName;

    /**
     * 哪个银行
     */
    private Integer whichBank;

    /**
     * 子类型-来源
     */
    private Integer subReferType;


    /**
     * 车牌
     */
    private String plateNo;

    /**
     * 状态 0 无需审核 1 未审核  2 审核不通过 3 审核通过 4 已发放，未领取 5 已领取 6 已退款 7 红包未领取，已退回 8 已发券
     */
    private Integer status;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime auditTime;

    /**
     * 激活时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime activeTime;

    /**
     * 首刷时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate firstUseDate;

    /**
     * 是否新用户
     */
    private Integer isNewUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdAt;

    /**
     * 是否显示置为激活按钮
     */
    private boolean showManualActivateBtn = false;

    /**
     * 关联单号
     */
    private String referSn;

    /**
     *
     */
    private Integer referType;
    /**
     * 状态
     *
     * @return
     */
    public String getStatusStr() {
        return ActivityCreditCardUserInfoStatusBriefEnum.getDescByCode(status);
    }

    public String getWhichBankStr() {
        return ActivityCreditCardUserInfoWhichBankEnum.getDescByCode(whichBank);
    }

    public String getNewUserStr() {
        if(isNewUser == null){
            return "";
        }else{
            return ActivityCreditCardBankUserIsNewUserEnum.getDescByCode(isNewUser);
        }
    }

    /**
     * 申请类型 --  classify 字段后续不继续使用，兼容
     * @return
     */
    public String getClassifyStr() {
        return ActivityCreditCardUserInfoReferTypeVOEnum.getDescByCode(referType);
    }


    public String getFirstUseDateStr(){
        if(ObjectUtil.isNotNull(firstUseDate)){
            return firstUseDate.toString().equals("1970-01-01") ? "" : firstUseDate.toString();
        }else{
            return "";
        }
    }

    /**
     * 来源
     * @return
     */
    public String getSubReferTypeStr(){
        return ActivityCreditCardUserInfoSubReferTypeEnum.getDescByCode(subReferType);
    }
}
