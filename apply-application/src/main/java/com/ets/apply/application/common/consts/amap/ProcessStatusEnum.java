package com.ets.apply.application.common.consts.amap;

import com.ets.apply.application.common.consts.productOrder.ProductOrderStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ProcessStatusEnum {
    /**
     * PENDING
     * DELIVERING
     * DELIVERY_SUCCESS
     * SUCCESS
     */
    PENDING(ProductOrderStatusEnum.PAID.getCode(), "100", "PENDING"),
    DELIVERING(ProductOrderStatusEnum.SHIPPED.getCode(), "100", "DELIVERING"),
    DELIVERY_SUCCESS(ProductOrderStatusEnum.RECEIVED.getCode(), "210", "DELIVERY_SUCCESS"),
    SUCCESS(ProductOrderStatusEnum.ACTIVATED.getCode(), "221", "SUCCESS"),
    REFUNDED(ProductOrderStatusEnum.CLOSED.getCode(), "500", "REFUNDED");

    private final Integer orderStatus;

    private final String processStatus;
    private final String cpOrderStatus;

    public static ProcessStatusEnum getByProductOrderStatus(Integer orderStatus) {
        for (ProcessStatusEnum node : ProcessStatusEnum.values()) {
            if (node.getOrderStatus().equals(orderStatus)) {
                return node;
            }
        }
        return null;
    }
}
