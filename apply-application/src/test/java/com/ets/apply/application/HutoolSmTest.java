package com.ets.apply.application;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.SM2;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.security.*;

@SpringBootTest(classes = ApplyApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringRunner.class)
@ActiveProfiles(value = "dev")
@Slf4j
public class HutoolSmTest {

    @Test
    public void testContextLoads() {
        //使用随机生成的密钥对加密或解密
//        SM2 sm2 = SmUtil.sm2();
//        // 公钥加密，私钥解密
//        String encryptStr = sm2.encryptBcd(text, KeyType.PublicKey);
//        System.out.println(encryptStr);
//        String decryptStr = StrUtil.utf8Str(sm2.decryptFromBcd(encryptStr, KeyType.PrivateKey));
//
//        System.out.println(decryptStr);



        String data = "certification is Certification(id=5fb5e11958371b3e7362d7fc, portEntry=470605, entryDate=2020-10-10, orderNo=22011191052041380(9622), province=广东省, weight=80.0, consignee=深圳市龙华区永嘉鑫冻品批发行, consigner=深圳市龙岗区新锦华冻品批发行, batchInfoList=[BatchInfo(id=null, batchNo=20200817, countryOfOrigin=303, productName=无, customName=猪小排, specification=10公斤/件, productType=LIS, weight=40.0)], updateTime=Thu Nov 19 11:06:01 CST 2020)";

        //使用自定义密钥对加密或解密
        KeyPair pair = SecureUtil.generateKeyPair("SM2");
        byte[] privateKey = pair.getPrivate().getEncoded();
        byte[] publicKey = pair.getPublic().getEncoded();

        SM2 sm2 = SmUtil.sm2(privateKey, publicKey);
        // 公钥加密，私钥解密
        String encryptStr = sm2.encryptBcd(data, KeyType.PublicKey);
        System.out.println(encryptStr);
        String decryptStr = StrUtil.utf8Str(sm2.decryptFromBcd(encryptStr, KeyType.PrivateKey));
        System.out.println(decryptStr);

    }




}
