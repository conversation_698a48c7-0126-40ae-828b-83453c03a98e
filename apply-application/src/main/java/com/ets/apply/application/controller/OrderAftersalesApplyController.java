package com.ets.apply.application.controller;

import com.ets.apply.application.app.business.orderAftersales.OrderAftersalesBusiness;
import com.ets.apply.application.common.dto.request.orderAftersales.*;
import com.ets.apply.application.common.vo.response.issuer.IssuerAftersalesApplyInitVO;
import com.ets.apply.application.infra.entity.OrderAftersalesApplyEntity;
import com.ets.common.JsonResult;
import com.ets.common.ToolsHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import jakarta.validation.Valid;


@RequestMapping("/orderAftersalesApply")
@RefreshScope
@RestController
@Slf4j
public class OrderAftersalesApplyController extends BaseController {
    @Autowired
    private OrderAftersalesBusiness orderAftersalesBusiness;
    /*
     * 数据结果通知
     */
    @PostMapping("/notifyStatus")
    public JsonResult<Boolean> notifyStatus() {
        return JsonResult.ok();
    }

    /*
     * 商品售后专用接口
     */
    @PostMapping("/check")
    public JsonResult<Boolean> check(@RequestBody @Valid OrderAftersalesCheckDTO dto) {
        return JsonResult.ok(orderAftersalesBusiness.check(dto));
    }

    /*
     * 是否可以做激活操作
     */
    @PostMapping("/checkCanActivate")
    public JsonResult<Boolean> checkCanActivate(@RequestBody @Valid CheckCanActivateDTO dto) {
        return JsonResult.ok(orderAftersalesBusiness.checkCanActivate(dto));
    }

    /*
     * 测试使用，生成task并且执行
     */
    @PostMapping("/task")
    public JsonResult<Boolean> task(@RequestBody @Valid OrderAftersalesTaskDTO dto) {
        return JsonResult.ok(orderAftersalesBusiness.task(dto));
    }

    /*
     * 是否可以做激活操作
     */
    @PostMapping("/getLatestByOrderSn")
    public JsonResult<OrderAftersalesApplyEntity> getLatestByOrderSn(@RequestBody @Valid CheckCanActivateDTO dto) {
        return JsonResult.ok(orderAftersalesBusiness.getLatestByOrderSn(dto));
    }

    /*
     * 更新业务状态为已完成
     */
    @PostMapping("/updateBusinessStatus")
    public JsonResult<Boolean> updateBusinessStatus(@RequestBody @Valid UpdateBussinessStatusDTO dto) {
        return JsonResult.ok(orderAftersalesBusiness.updateBusinessStatus(dto));
    }

    /*
     * 更新业务状态为已完成
     */
    @PostMapping("/businessStatusChange")
    public JsonResult<Boolean> businessStatusChange(@RequestBody @Valid BussinessStatusChangeDTO dto) {
        return JsonResult.ok(orderAftersalesBusiness.businessStatusChange(dto));
    }

    /*
     * 申请售后
     */
    @RequestMapping("/apply")
    public JsonResult<OrderAftersalesApplyEntity> apply(@RequestBody(required = false) @Valid OrderAftersalesApplyDTO dto) {
        if(dto.getUid() < 1){
            ToolsHelper.throwException("uid不能为空");
        }
        return JsonResult.ok(orderAftersalesBusiness.apply(dto, dto.getUid()));
    }

    /*
     *
     * 检查是否需要前置操作
     */
    @RequestMapping("/checkNeedPreOperate")
    public JsonResult<Boolean> applyInit(@RequestBody(required = false) @Valid OrderAftersalesApplyInitDTO dto) {
        return JsonResult.ok(orderAftersalesBusiness.checkNeedPreOperate(dto));
    }

}