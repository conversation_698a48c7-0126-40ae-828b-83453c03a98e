package com.ets.apply.application.app.business.admin;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.apply.application.app.business.BaseBusiness;
import com.ets.apply.application.app.business.ProductPackageBusiness;
import com.ets.apply.application.common.consts.common.AdminStatusEnum;
import com.ets.apply.application.common.consts.productOrder.ChannelTypeEnum;
import com.ets.apply.application.common.dto.adminProductPackageTemplate.AdminProductPackageTemplateAddDTO;
import com.ets.apply.application.common.dto.adminProductPackageTemplate.AdminProductPackageTemplateGetListDTO;
import com.ets.apply.application.common.dto.adminProductPackageTemplate.AdminProductPackageTemplateModifyDTO;
import com.ets.apply.application.common.dto.adminProductPackageTemplate.AdminProductPackageTemplateUpDownDTO;
import com.ets.apply.application.common.dto.request.goods.CheckStallSkuAttrDTO;
import com.ets.apply.application.common.vo.admin.productPackageTemplate.AdminProductPackageTemplateListVO;
import com.ets.apply.application.infra.entity.ProductPackageTemplateEntity;
import com.ets.apply.application.infra.service.ProductPackageTemplateLogService;
import com.ets.apply.application.infra.service.ProductPackageTemplateService;
import com.ets.common.ToolsHelper;
import com.ets.common.util.NumberUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Objects;

@Slf4j
@Component
public class AdminProductPackageTemplateBusiness extends BaseBusiness {
    @Autowired
    private ProductPackageTemplateService productPackageTemplateService;

    @Autowired
    private ProductPackageTemplateLogService productPackageTemplateLogService;
    @Autowired
    private ProductPackageBusiness productPackageBusiness;

    /*
     * 获取全部产品包列表
     */
    public IPage<AdminProductPackageTemplateListVO> getList(AdminProductPackageTemplateGetListDTO dto) {
        // 分页设置
        IPage<ProductPackageTemplateEntity> oPage = new Page<>(dto.getPageNum(), dto.getPageSize(), true);
        LambdaQueryWrapper<ProductPackageTemplateEntity> wrapper = new LambdaQueryWrapper<>();
        // 查询条件设置
        wrapper .eq(NumberUtil.isPositive(dto.getStatus()), ProductPackageTemplateEntity::getStatus, dto.getStatus())
                .like(StringUtils.isNotEmpty(dto.getName()), ProductPackageTemplateEntity::getName, dto.getName())
                .orderByDesc(ProductPackageTemplateEntity::getCreatedAt);


        IPage<ProductPackageTemplateEntity> pageList = productPackageTemplateService.getPageListByWrapper(oPage, wrapper);
        if(pageList.getRecords().isEmpty()){
            return null;
        }
        return pageList.convert(record -> {
            AdminProductPackageTemplateListVO vo = new AdminProductPackageTemplateListVO();
            BeanUtils.copyProperties(record, vo);
            return vo;
        });
    }


    /*
     * 新增模板
     */
    public Boolean add(AdminProductPackageTemplateAddDTO dto, String loginCode) {
        ProductPackageTemplateEntity productPackageTemplate = new ProductPackageTemplateEntity();
        //检查优先级是否唯一
        if(!productPackageTemplateService.checkNameIsValid(dto.getName(),0)){
            ToolsHelper.throwException("模板名称"+dto.getName()+"已存在");
        }
        //检查issuerId和cardId
        productPackageBusiness.checkIssuerIdAndCardId(dto.getIssuerId(),dto.getCardId(),dto.getIsTruck());
        //检查sku是否存在

        productPackageBusiness.checkStallSkuAttr(new CheckStallSkuAttrDTO(
                ChannelTypeEnum.getStallCodeByValue(1),
                dto.getGoodsSku(),
                CheckStallSkuAttrDTO.AttrMap.builder()
                        .issuerId(dto.getIssuerId().toString())
                        .manufacturer(dto.getManufacturer().toString())
                        .deviceColor(dto.getDeviceColor().toString())
                        .deviceType(dto.getDeviceType().toString())
                        .deviceVersion(dto.getDeviceVersion().toString())
                        .build()
        ));

        productPackageTemplate.setName(dto.getName());
        productPackageTemplate.setContent(JSON.toJSONString(dto));
        productPackageTemplate.setStatus(1);
        productPackageTemplate.setOperator(loginCode);
        productPackageTemplateService.create(productPackageTemplate);
        return true;
    }

    /*
     * 修改模板
     */
    public Boolean modify(AdminProductPackageTemplateModifyDTO dto, String loginCode) {
        ProductPackageTemplateEntity productPackageTemplate = productPackageTemplateService.getById(dto.getId());
        if(productPackageTemplate == null){
            ToolsHelper.throwException("模板不存在："+dto.getId());
        }
        //已上下架的模板不能修改
        if(Arrays.asList(AdminStatusEnum.BE_UP.getCode(),  AdminStatusEnum.BE_DOWN.getCode()).contains(productPackageTemplate.getStatus())){
            ToolsHelper.throwException("模板已启用过不可修改");
        }

        //检查优先级是否唯一
        if(!productPackageTemplateService.checkNameIsValid(dto.getName(),dto.getId())){
            ToolsHelper.throwException("模板名称"+dto.getName()+"已存在");
        }
        //检查issuerId和cardId
        productPackageBusiness.checkIssuerIdAndCardId(dto.getIssuerId(),dto.getCardId(),dto.getIsTruck());
        //检查sku是否存在
        productPackageBusiness.checkStallSkuAttr(new CheckStallSkuAttrDTO(
                ChannelTypeEnum.getStallCodeByValue(1),
                dto.getGoodsSku(),
                CheckStallSkuAttrDTO.AttrMap.builder()
                        .issuerId(dto.getIssuerId().toString())
                        .manufacturer(dto.getManufacturer().toString())
                        .deviceColor(dto.getDeviceColor().toString())
                        .deviceType(dto.getDeviceType().toString())
                        .deviceVersion(dto.getDeviceVersion().toString())
                        .build()
        ));


        LambdaUpdateWrapper<ProductPackageTemplateEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ProductPackageTemplateEntity::getId, dto.getId())
                .set(ProductPackageTemplateEntity::getName, dto.getName())
                .set(ProductPackageTemplateEntity::getContent,JSON.toJSONString(dto))
                .set(StringUtils.isNotEmpty(loginCode),ProductPackageTemplateEntity::getOperator, loginCode)
                .set(ProductPackageTemplateEntity::getUpdatedAt, LocalDateTime.now());
        productPackageTemplateService.updateByWrapper(wrapper);

        return true;
    }

    /*
     * 上下架计划
     */
    public Boolean upOrDown(AdminProductPackageTemplateUpDownDTO dto, String loginCode) {
        ProductPackageTemplateEntity productPackageTemplate = productPackageTemplateService.getById(dto.getId());
        if(productPackageTemplate == null){
            ToolsHelper.throwException("模板不存在："+dto.getId());
        }
        if(Objects.equals(productPackageTemplate.getStatus(),dto.getStatus())){
            return true;
        }
        String msg = dto.getReason();
        switch (dto.getStatus()) {
            case 2 -> {
                msg = "变更为启用状态，" + msg;
                //检查优先级是否唯一
                if (!productPackageTemplateService.checkNameIsValid(productPackageTemplate.getName(), productPackageTemplate.getId())) {
                    ToolsHelper.throwException("模板名称"+productPackageTemplate.getName()+"已存在");
                }
            }
            case 3 -> msg = "变更为下架状态，" + msg;
            default -> ToolsHelper.throwException("更新状态错误：" + dto.getStatus());
        }


        LambdaUpdateWrapper<ProductPackageTemplateEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ProductPackageTemplateEntity::getId, dto.getId())
                .set(ProductPackageTemplateEntity::getStatus, dto.getStatus())
                .set(ProductPackageTemplateEntity::getUpdatedAt, LocalDateTime.now());
        productPackageTemplateService.updateByWrapper(wrapper);

        //记录日志
        productPackageTemplateLogService.addLog(
                loginCode,
                productPackageTemplate.getId(),
                "upOrDown",
                msg,
                "",
                ""
        );
        return true;
    }
    /*
     * 获取全部产品包列表
     */
    public Object getParamsById(Integer id) {
        ProductPackageTemplateEntity productPackageTemplate = productPackageTemplateService.getById(id);
        if(productPackageTemplate == null){
            ToolsHelper.throwException("模板不存在："+id);
        }
        JSONObject params = JSON.parseObject(productPackageTemplate.getContent());
        if(params.getInteger("id") == null){
            params.put("id",id);
        }
        return params;
    }
}
