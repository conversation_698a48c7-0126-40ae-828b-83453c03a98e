{"sysTime":"2025-06-23 15:22:43.425","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"68089","thread":"background-preinit","className":"o.h.validator.internal.util.Version:21","methodName":"o.h.validator.internal.util.Version:<clinit>-21","message":"HV000001: Hibernate Validator 8.0.1.Final","thrown":""}
{"sysTime":"2025-06-23 15:22:43.419","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"68089","thread":"main","className":"c.a.nacos.client.logging.NacosLogging:64","methodName":"c.a.nacos.client.logging.NacosLogging:loadConfiguration-64","message":"{\"msg\":\"Load Logback Configuration of Nacos fail, message: org/slf4j/impl/StaticLoggerBinder\"}","thrown":""}
{"sysTime":"2025-06-23 15:22:43.461","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"68089","thread":"main","className":"c.a.nacos.client.logging.NacosLogging:64","methodName":"c.a.nacos.client.logging.NacosLogging:loadConfiguration-64","message":"{\"msg\":\"Load Logback Configuration of Nacos fail, message: org/slf4j/impl/StaticLoggerBinder\"}","thrown":""}
{"sysTime":"2025-06-23 15:22:43.596","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"68089","thread":"main","className":"c.a.nacos.client.logging.NacosLogging:64","methodName":"c.a.nacos.client.logging.NacosLogging:loadConfiguration-64","message":"{\"msg\":\"Load Logback Configuration of Nacos fail, message: org/slf4j/impl/StaticLoggerBinder\"}","thrown":""}
{"sysTime":"2025-06-23 15:22:43.597","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"68089","thread":"main","className":"c.alibaba.nacos.client.utils.ParamUtil:88","methodName":"c.alibaba.nacos.client.utils.ParamUtil:<clinit>-88","message":"[settings] [req-serv] nacos-server port:8848","thrown":""}
{"sysTime":"2025-06-23 15:22:43.597","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"68089","thread":"main","className":"c.alibaba.nacos.client.utils.ParamUtil:99","methodName":"c.alibaba.nacos.client.utils.ParamUtil:<clinit>-99","message":"[settings] [http-client] connect timeout:1000","thrown":""}
{"sysTime":"2025-06-23 15:22:43.597","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"68089","thread":"main","className":"c.alibaba.nacos.client.utils.ParamUtil:106","methodName":"c.alibaba.nacos.client.utils.ParamUtil:<clinit>-106","message":"PER_TASK_CONFIG_SIZE: 3000.0","thrown":""}
{"sysTime":"2025-06-23 15:22:43.615","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"68089","thread":"Thread-1","className":"c.a.n.client.identify.CredentialWatcher:165","methodName":"c.a.n.client.identify.CredentialWatcher:loadCredential-165","message":"null No credential found","thrown":""}
{"sysTime":"2025-06-23 15:22:43.617","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"68089","thread":"main","className":"c.a.n.client.identify.CredentialWatcher:165","methodName":"c.a.n.client.identify.CredentialWatcher:loadCredential-165","message":"null No credential found","thrown":""}
{"sysTime":"2025-06-23 15:22:43.669","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"68089","thread":"main","className":"c.a.nacos.client.logging.NacosLogging:64","methodName":"c.a.nacos.client.logging.NacosLogging:loadConfiguration-64","message":"{\"msg\":\"Load Logback Configuration of Nacos fail, message: org/slf4j/impl/StaticLoggerBinder\"}","thrown":""}
{"sysTime":"2025-06-23 15:22:43.679","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"68089","thread":"main","className":"c.a.n.c.c.impl.LocalConfigInfoProcessor:212","methodName":"c.a.n.c.c.impl.LocalConfigInfoProcessor:<clinit>-212","message":"LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config","thrown":""}
{"sysTime":"2025-06-23 15:22:43.696","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"68089","thread":"main","className":"c.a.nacos.client.config.impl.Limiter:54","methodName":"c.a.nacos.client.config.impl.Limiter:<clinit>-54","message":"limitTime:5.0","thrown":""}
{"sysTime":"2025-06-23 15:22:45.142","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"68089","thread":"main","className":"c.a.n.client.config.http.ServerHttpAgent:115","methodName":"c.a.n.client.config.http.ServerHttpAgent:httpGet-115","message":"{\"msg\":\"[NACOS Exception httpGet] currentServerAddr: http://nacos.public:8848\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=apply-mysql.yaml&tenant=ets-dev&group=apply\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=apply-mysql.yaml&tenant=ets-dev&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-06-23 15:22:45.150","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"68089","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:205","methodName":"c.a.n.client.config.impl.ClientWorker:getServerConfig-205","message":"{\"msg\":\"[fixed-nacos.public_8848-ets-dev] [sub-server] get server config exception, dataId=apply-mysql.yaml, group=apply, tenant=ets-dev\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=apply-mysql.yaml&tenant=ets-dev&group=apply\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=apply-mysql.yaml&tenant=ets-dev&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-06-23 15:22:45.152","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"68089","thread":"main","className":"c.a.n.client.config.NacosConfigService:174","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-174","message":"{\"msg\":\"[fixed-nacos.public_8848-ets-dev] [get-config] get from server error, dataId=apply-mysql.yaml, group=apply, tenant=ets-dev, msg=ErrCode:500, ErrMsg:Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=apply-mysql.yaml&tenant=ets-dev&group=apply\"}","thrown":""}
{"sysTime":"2025-06-23 15:22:45.154","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"68089","thread":"main","className":"c.a.nacos.client.config.utils.JvmUtil:49","methodName":"c.a.nacos.client.config.utils.JvmUtil:<clinit>-49","message":"isMultiInstance:false","thrown":""}
{"sysTime":"2025-06-23 15:22:45.155","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"68089","thread":"main","className":"c.a.n.client.config.NacosConfigService:179","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-179","message":"{\"msg\":\"[fixed-nacos.public_8848-ets-dev] [get-config] get snapshot ok, dataId=apply-mysql.yaml, group=apply, tenant=ets-dev, config=spring:\\n  autoconfigure:\\n    #自动化配置 例外处理\\n    exclude: com.alibaba.druid.spring.boot.autoconfigure.Dr...\"}","thrown":""}
{"sysTime":"2025-06-23 15:22:45.679","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"68089","thread":"Thread-2","className":"c.a.n.common.http.HttpClientBeanHolder:108","methodName":"c.a.n.common.http.HttpClientBeanHolder:shutdown-108","message":"{\"msg\":\"[HttpClientBeanHolder] Start destroying common HttpClient\"}","thrown":""}
