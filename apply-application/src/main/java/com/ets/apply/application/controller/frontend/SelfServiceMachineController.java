package com.ets.apply.application.controller.frontend;

import com.ets.apply.application.app.business.SelfServiceMachineBusiness;
import com.ets.apply.application.common.dto.machine.MachineReadCardDTO;
import com.ets.apply.application.common.dto.machine.MachineWriteCardDTO;
import com.ets.apply.application.common.vo.machine.MachineReadCardVO;
import com.ets.common.JsonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

@RestController
@RequestMapping("/frontend/selfServiceMachine")
public class SelfServiceMachineController {

    @Autowired
    SelfServiceMachineBusiness machineBusiness;

    @RequestMapping("/readCard")
    public JsonResult<MachineReadCardVO> readCard(@RequestBody @Valid MachineReadCardDTO readCardDTO) {
        return JsonResult.ok(machineBusiness.readCard(readCardDTO));
    }

    @RequestMapping("/writeCard")
    public JsonResult<?> writeCard(@RequestBody @Valid MachineWriteCardDTO writeCardDTO) {
        machineBusiness.writeCard(writeCardDTO);
        return JsonResult.ok();
    }
}
