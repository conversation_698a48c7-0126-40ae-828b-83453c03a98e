package com.ets.apply.application.app.factory.creditCard;

import cn.hutool.extra.spring.SpringUtil;
import com.ets.apply.application.app.factory.creditCard.impl.*;
import com.ets.apply.application.common.consts.activityCreditCardUsersInfo.ActivityCreditCardUserInfoWhichBankConstant;
import com.ets.common.ToolsHelper;

public class CreditCardFactory {

    public static CreditCardBase create(Integer whichBank) {
        CreditCardBase creditCardBase = null;
        switch (whichBank) {
            case ActivityCreditCardUserInfoWhichBankConstant.GUANG_FA_INTERFACE:
                creditCardBase = SpringUtil.getBean(CreditCardGuangFa.class);
                break;
            case ActivityCreditCardUserInfoWhichBankConstant.PING_AN_INTERFACE:
                creditCardBase = SpringUtil.getBean(CreditCardPingAn.class);
                break;
            case ActivityCreditCardUserInfoWhichBankConstant.JIAO_TONG:
                creditCardBase = SpringUtil.getBean(CreditCardComm.class);
                break;
            case ActivityCreditCardUserInfoWhichBankConstant.CMBC:
                creditCardBase = SpringUtil.getBean(CreditCardCmbc.class);
                break;
            case ActivityCreditCardUserInfoWhichBankConstant.SPD:
                creditCardBase = SpringUtil.getBean(CreditCardSpd.class);
                break;
            case ActivityCreditCardUserInfoWhichBankConstant.CEB:
                creditCardBase = SpringUtil.getBean(CreditCardCeb.class);
                break;
            case ActivityCreditCardUserInfoWhichBankConstant.CITIC:
                creditCardBase = SpringUtil.getBean(CreditCardCitic.class);
                break;
            default:
                ToolsHelper.throwException("当前信用卡不支持操作");
        }
        return creditCardBase;
    }
}
