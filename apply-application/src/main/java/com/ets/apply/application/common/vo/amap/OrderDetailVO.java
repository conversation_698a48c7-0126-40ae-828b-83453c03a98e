package com.ets.apply.application.common.vo.amap;

import lombok.Data;

import java.util.List;

@Data
public class OrderDetailVO {
    /**
     * subCode	String	是	业务错误码
     * subMsg	String	是	业务失败信息
     * amapOrderId	String	是	高德订单号
     * cpOrderId	String	是	供应商订单号
     * totalSalePrice	Long	是	供应商优惠前总价（单位：分）
     * totalOrderPrice	Long	是	供应商优惠后总价（单位：分）
     * orderStatus	Integer	是	订单状态
     * 5000: 已支付
     * 5300: 待使用
     * 5200：下单失败
     * 5400: 退款中
     * 8000：已使用
     * 8100:已关单(未支付)
     * 8300: 退款失败
     * 8400: 已退款
     * cpOrderStatus	String	否	供应商订单状态
     * certificates	List	否	凭证码列表，有凭证码时必填
     * createTime	Long	否	创建时间（毫秒）
     * currency	String	否	币种
     * buyer	Buyer	否	购买人信息
     * shop	Shop	否	店铺信息
     * items	List	是	子订单列表
     * delivery	Delivery	否	交付信息
     * refundRecords	List < RefundRecord >	否	退款记录
     * process	Object	否	进度信息
     * sourceClient	Integer	否	订单来源
     * parentCpOrderId	String	否	父订单号【改签单为新单的情况下】
     * originCpOrderId	String	否	原始CP订单号
     * estimatedCloseTime	Long	否	预计关单时间（毫秒）
     * marketings	List	否	当前订单的所有优惠
     * reservation	Map<String,Obj>	否	订单扩展信息
     * payment	Payment	否	订单支付信息
     * productData	Map<String,Obj>	否	商品明细数据,与商品模型保持一致【单程、中转、往返】
     * price	Price	否	价格
     */
    private String subCode;
    private String subMsg;
    private String amapOrderId;
    private String cpOrderId;
    private Long totalSalePrice;
    private Long totalOrderPrice;
    private Integer orderStatus;
    private String cpOrderStatus;
    private Long createTime;
    private String currency;
//    private Buyer buyer;
//    private Item[] items;
    private Delivery delivery;
//    private RefundRecord[] refundRecords;
//    private Process process;

    private Product product;

    @Data
    public static class Buyer {
        private String openId;
        private String mobile;
    }

    @Data
    public static class Shop {
        private String amapShopId;
    }

    @Data
    public static class Item {
        private Product product;
    }

    @Data
    public static class Product {
        private String skuName;
        private Integer quantity;
        private Integer salePrice;
        private String skuId;
    }

    @Data
    public static class Delivery {
        private Receiver receiver;
        /**
         * type	Integer	否	交付方式11：线上交付（虚拟） 21：线下到店（取货或退货） 22：线下配送
         * orderId	String	否	配送单号
         * status	String	否	配送单状态
         * statusDesc	String	否	配送单状态描述
         * company	Object	否	公司信息
         */
        private Integer type;
        private String orderId;
        private String status;
        private String statusDesc;
        private Company company;

        private Fulfillment fulfillment;
    }

    @Data
    public static class Company {
//        private String comCode;
        private String name;
    }

    @Data
    public static class Receiver {
        private String name;
        private String mobile;
        private String province;
        private String city;
        private String district;
        private String address;
        private String addressDetail;
        private String adcode;
        private String poiId;
        private String postcode;
        private String longitude;
        private String latitude;

    }

    @Data
    public static class Fulfillment {
        private FulfillmentTarget fulfillmentTarget;
    }

    @Data
    public static class FulfillmentTarget {
        private String targetType;
        private Identifier identifier;
        private Verification verification;
    }

    @Data
    public static class Identifier {
        private String type;
        private String primaryIdentifier;
        private String secondIdentifier;
    }

    @Data
    public static class Verification {
        private Boolean required;
    }

    @Data
    public static class RefundRecord {
        private Long refundAmount;
        private String refundReason;
        private Long refundTime;
    }

    @Data
    public static class Process {
        private List<ProcessDetail> list;
    }

    @Data
    public static class ProcessDetail {
        /**
         * processDesc	String	否	描述
         * processMessage	Integer	否	详细信息
         * processTime	String	否	处理时间
         * processStatus	String	否	状态
         * 0 未开始
         * 1 进行中
         * 2 已完成
         * processType	String	否	类型
         */
//        private String processDesc;
//        private String processMessage;
        private Long processTime;
        private String processStatus;
        private String processType;

        // 构建方法
        public  ProcessDetail(String processStatus, String processType, Long processTime ) {
//            this.processDesc = processDesc;
//            this.processMessage = processMessage;
            this.processTime = processTime;
            this.processStatus = processStatus;
            this.processType = processType;
        }
    }
}
