package com.ets.apply.application.infra.entity.sales;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ets.apply.application.infra.entity.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 加购升级营销计划产品包
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("etc_sales_upgrade_package")
public class SalesUpgradePackageEntity extends BaseEntity<SalesUpgradePackageEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 计划id
     */
    private Integer planId;

    /**
     * 产品包编码
     */
    private String packageSn;
    /**
     * 比例数字
     */
    private Integer rate;
    /**
     * 短信模板id
     */
    private String smsTplId;

    /**
     * 短信发送时间
     */
    private Integer smsSendTime;

    /**
     * 模板消息id
     */
    private String minaTplId;

    /**
     * 模板消息发送时间
     */
    private Integer minaSendTime;

    /**
     * 样式内容json
     */
    private String frontendContent;

    private Integer status;

    private String operator;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
}
