package com.ets.apply.application.feign;

import java.util.List;
import java.util.ArrayList;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.Assert;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.ets.common.BizException;
import com.ets.common.JsonResult;
import com.ets.pay.feign.feign.EntrustFeign;
import com.ets.pay.feign.response.entrust.TaxiEntrustsVo;
import com.ets.pay.feign.request.entrust.QueryEntrustDto;
import com.ets.pay.feign.request.entrust.TaxiEntrustsDto;

@Slf4j
@SpringBootTest
@RunWith(value = SpringRunner.class)
public class EntrustFeignTest {
    @Autowired
    private EntrustFeign entrustFeign;

    @Test
    public void queryTaxiEntrusts() throws BizException {
        // JsonResult<TaxiEntrustsVo> queryTaxiEntrusts(@RequestBody @Validated TaxiEntrustsDto dto) throws BizException;
        List<QueryEntrustDto> params = new ArrayList<>();
        QueryEntrustDto queryDto = new QueryEntrustDto();
        queryDto.setUid(19914998);
        queryDto.setPaymentMode(9);
        queryDto.setPlateNo("粤B0C3Q5");
        queryDto.setCardId(35);
        params.add(queryDto);

        TaxiEntrustsDto dto = new TaxiEntrustsDto();
        dto.setParams(params);

        JsonResult<TaxiEntrustsVo> vo = entrustFeign.queryTaxiEntrusts(dto);
        log.info("{}", vo);

        Assert.assertNotEquals(vo, null);
    }
}
