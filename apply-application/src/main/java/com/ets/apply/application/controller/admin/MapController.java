package com.ets.apply.application.controller.admin;

import com.ets.apply.application.app.business.map.MapBusiness;
import com.ets.apply.application.app.thirdservice.feign.CallPhpApplyFeign;
import com.ets.apply.application.common.dto.request.GetIssuerByPlateNoDTO;
import com.ets.apply.application.common.dto.request.GetProductByProvinceDTO;
import com.ets.apply.application.common.dto.request.GetProductPackageListDTO;
import com.ets.apply.application.common.dto.request.MapCompareDiffDTO;
import com.ets.apply.application.common.vo.map.MapCompareDiffListVO;
import com.ets.apply.application.common.vo.map.MapGetIssuerByPlateNoVO;
import com.ets.apply.application.common.vo.map.MapGetProductByProvinceVO;
import com.ets.apply.application.common.vo.map.MapGetProductPackageListVO;
import com.ets.apply.application.controller.BaseController;
import com.ets.common.JsonResult;
import com.ets.common.RequestHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import jakarta.validation.Valid;

@Slf4j
@Controller
@RequestMapping("/admin/map")
public class MapController extends BaseController {
    @Autowired
    private CallPhpApplyFeign callPhpApplyFeign;
    @Autowired
    private MapBusiness mapBusiness;
    /**
     *
     * @return
     */
    @RequestMapping("/getProductPackageList")
    @ResponseBody
    public JsonResult<MapGetProductPackageListVO> getProductPackageList(@RequestBody(required = false) @Valid GetProductPackageListDTO dto) {
        //log.info(dto.toString());
        String jsonResult =  callPhpApplyFeign.getProductPackageList(dto);
        JsonResult<MapGetProductPackageListVO> result = JsonResult.convertFromJsonStr(jsonResult, MapGetProductPackageListVO.class);
        //MapGetProductPackageListVO vo = result.getData();

        return result;
    }

    /**
     *
     * @return
     */
    @RequestMapping("/prodMap")
    @ResponseBody
    public JsonResult<Boolean> prodMap(@RequestBody(required = false) @Valid MapCompareDiffDTO dto) {
        mapBusiness.updateProdCache(dto,RequestHelper.getHttpServletRequest().getHeader("loginCode"));
        return JsonResult.ok();
    }

    /**
     *
     * @return
     */
    @RequestMapping("/compareDiff")
    @ResponseBody
    public JsonResult<MapCompareDiffListVO> compareDiff(@RequestBody(required = false) @Valid MapCompareDiffDTO dto) {
        String jsonResult =  callPhpApplyFeign.mapCompareDiff(dto);
        //log.info(jsonResult);
        JsonResult<MapCompareDiffListVO> result = JsonResult.convertFromJsonStr(jsonResult, MapCompareDiffListVO.class);
        return result;
    }

    @RequestMapping("/getProductsByProvince")
    @ResponseBody
    public JsonResult<MapGetProductByProvinceVO> getProductsByProvince(@RequestBody(required = false) @Valid GetProductByProvinceDTO dto) {
        //log.info(dto.toString());
        String jsonResult =  callPhpApplyFeign.getProductsByProvince(dto);
        JsonResult<MapGetProductByProvinceVO> result = JsonResult.convertFromJsonStr(jsonResult, MapGetProductByProvinceVO.class);
        return result;
    }

    @RequestMapping("/getIssuerByPlateNo")
    @ResponseBody
    public JsonResult<MapGetIssuerByPlateNoVO> getIssuerByPlateNo(@RequestBody(required = false) @Valid GetIssuerByPlateNoDTO dto) {
        //log.info(dto.toString());
        String jsonResult =  callPhpApplyFeign.getIssuerByPlateNo(dto);
        JsonResult<MapGetIssuerByPlateNoVO> result = JsonResult.convertFromJsonStr(jsonResult, MapGetIssuerByPlateNoVO.class);
        //MapGetProductPackageListVO vo = result.getData();

        return result;
    }
}
