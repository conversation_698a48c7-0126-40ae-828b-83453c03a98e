package com.ets.apply.application.controller.admin;

import com.ets.apply.application.common.config.CosFrontendConfig;
import com.ets.apply.application.common.utils.TencentCosUtil;
import com.ets.common.JsonResult;
import com.ets.common.ToolsHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import java.io.IOException;
import java.util.*;


@RestController
@RequestMapping("/admin/file")
@Slf4j
public class FileUploadController {

	@Resource
	TencentCosUtil tencentUploadUtil;
	@Autowired
	private CosFrontendConfig cosFrontendConfig;

	@PostMapping("/upload-file")
	public JsonResult<?> uploadFile(@Param("files") MultipartFile[] files) {
		if(files == null || files.length < 1){
			ToolsHelper.throwException("文件不能为空！");
		}
		Map<String,List<String>> map = new HashMap<>();
		List<String> list = new ArrayList<>();
		Arrays.stream(files).forEach(file ->{
			String fileName = Calendar.getInstance().getTimeInMillis()+"";
			if(StringUtils.isNotBlank(file.getOriginalFilename())){
				String suffix = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));
				try {
					list.add(tencentUploadUtil.upload(file.getBytes(), cosFrontendConfig.getRootDir()+fileName+suffix));
				} catch (IOException e) {
					e.printStackTrace();
				}
			}

		});
		map.put("url",list);
		return JsonResult.ok(map);
	}


}
