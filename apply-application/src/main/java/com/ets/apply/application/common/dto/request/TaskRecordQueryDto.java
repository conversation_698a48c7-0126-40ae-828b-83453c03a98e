package com.ets.apply.application.common.dto.request;

import lombok.Data;

@Data
public class TaskRecordQueryDto {

    /**
     * 任务流水号
     */
    private String taskSn;

    /**
     * 任务关联sn,对应order_sn之类
     */
    private String referSn;

    /**
     * 任务类型
     */
    private String referType;

    /**
     * 任务状态：0待处理，1处理中，2处理完成3处理失败4暂停处理
     */
    private Integer status;

    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 分页大小
     */
    private Integer pageSize = 10;

}
