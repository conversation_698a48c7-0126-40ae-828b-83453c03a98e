package com.ets.apply.application;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.ets.apply.application.app.disposer.UserIpAddressUploadDisposer;
import com.ets.apply.application.common.bo.UserIpAddressUploadBO;
import com.ets.apply.application.infra.entity.UserIp;
import com.ets.apply.application.infra.service.UserIpService;
import com.ets.starter.queue.QueueDefault;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDateTime;
import java.util.List;

@SpringBootTest(classes = ApplyApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringRunner.class)
@ActiveProfiles(value = "dev")
public class UserIpTest {

    @Autowired
    private QueueDefault queueDefault;

    @Autowired
    private UserIpService userIpService;

    @Test
    public void history() {
        // 查询表数据
        LambdaQueryWrapper<UserIp> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserIp::getStatus, 0)
                .last("limit 1000");
        List<UserIp> userIpList = userIpService.getListByWrapper(queryWrapper);

        if (ObjectUtils.isNotEmpty(userIpList)) {

            // index value
            int index = 0;
            for (UserIp v : userIpList) {
                // 114代表补充上报
                UserIpAddressUploadBO bo = new UserIpAddressUploadBO();
                bo.setActionId(114);
                bo.setIp(v.getIp());
                bo.setUid(v.getUid());
                queueDefault.push(new UserIpAddressUploadDisposer(bo));
                index++;
                if (index % 5 == 0) {
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                }

                // 更新为已处理
                LambdaUpdateWrapper<UserIp> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(UserIp::getId, v.getId())
                        .set(UserIp::getStatus, 1)
                        .set(UserIp::getUpdatedAt, LocalDateTime.now());
                userIpService.updateByWrapper(updateWrapper);
            }
        }
    }
}
