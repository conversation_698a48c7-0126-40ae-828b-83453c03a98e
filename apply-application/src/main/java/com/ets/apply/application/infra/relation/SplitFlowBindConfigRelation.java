package com.ets.apply.application.infra.relation;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.ets.apply.application.infra.entity.splitFlow.SplitFlowConfigEntity;
import com.ets.apply.application.infra.entity.splitFlow.SplitFlowEntity;
import com.ets.common.base.BaseEntityRelation;

import java.util.function.BiConsumer;

public class SplitFlowBindConfigRelation extends BaseEntityRelation<SplitFlowEntity, SplitFlowConfigEntity> {

    @Override
    public BiConsumer<SplitFlowEntity, SplitFlowConfigEntity> getEntityColumn() {
        return SplitFlowEntity::setSplitFlowConfigEntity;
    }

    @Override
    public SFunction<SplitFlowEntity, Object> getMasterColumn() {
        return SplitFlowEntity::getSplitType;
    }

    @Override
    public SFunction<SplitFlowConfigEntity, Object> getAffiliatedColumn() {
        return SplitFlowConfigEntity::getSplitType;
    }
}
