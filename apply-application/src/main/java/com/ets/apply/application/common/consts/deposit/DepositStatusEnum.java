package com.ets.apply.application.common.consts.deposit;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 保证金状态枚举
 */
@Getter
@AllArgsConstructor
public enum DepositStatusEnum {

    NORMAL(1, "正常"),
    FROZEN(2, "冻结"),
    CANCELED(3, "注销"),
    REFUNDED(4, "退款");

    private final Integer value;
    private final String desc;

    /**
     * 根据状态值获取描述
     *
     * @param value 状态值
     * @return 状态描述
     */
    public static String getDescByValue(Integer value) {
        if (value == null) {
            return "";
        }
        
        for (DepositStatusEnum status : DepositStatusEnum.values()) {
            if (status.getValue().equals(value)) {
                return status.getDesc();
            }
        }
        return "";
    }

    /**
     * 根据状态值获取枚举对象
     *
     * @param value 状态值
     * @return 枚举对象，未找到返回null
     */
    public static DepositStatusEnum getByValue(Integer value) {
        if (value == null) {
            return null;
        }
        
        for (DepositStatusEnum status : DepositStatusEnum.values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 判断是否为正常状态
     *
     * @param value 状态值
     * @return true-正常状态，false-非正常状态
     */
    public static boolean isNormal(Integer value) {
        return NORMAL.getValue().equals(value);
    }

    /**
     * 判断是否为冻结状态
     *
     * @param value 状态值
     * @return true-冻结状态，false-非冻结状态
     */
    public static boolean isFrozen(Integer value) {
        return FROZEN.getValue().equals(value);
    }

    /**
     * 判断是否为注销状态
     *
     * @param value 状态值
     * @return true-注销状态，false-非注销状态
     */
    public static boolean isCanceled(Integer value) {
        return CANCELED.getValue().equals(value);
    }

    /**
     * 判断是否为退款状态
     *
     * @param value 状态值
     * @return true-退款状态，false-非退款状态
     */
    public static boolean isRefunded(Integer value) {
        return REFUNDED.getValue().equals(value);
    }
}
