package com.ets.apply.application.common.consts.splitFlowConfig;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum SplitFlowRuleMethodEnum {

    REDIS_INCR("redisIncr", "redis incr"),
    WHITE_LIST("whiteList", "白名单"),
    USER_HAS_CAR_VALID_CARD("userHasCarValidCard", "用户有有效卡");

    private final String value;
    private final String desc;
    public static final Map<String, String> map;
    public static final List<String> list;

    static {
        map = Arrays.stream(SplitFlowRuleMethodEnum.values()).collect(Collectors.toMap(SplitFlowRuleMethodEnum::getValue, SplitFlowRuleMethodEnum::getDesc));
        list = Arrays.stream(SplitFlowRuleMethodEnum.values()).map(SplitFlowRuleMethodEnum::getValue).collect(Collectors.toList());
    }
}
