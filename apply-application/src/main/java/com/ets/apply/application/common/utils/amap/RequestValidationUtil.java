package com.ets.apply.application.common.utils.amap;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.ets.apply.application.common.consts.amap.SubCodeEnum;
import com.ets.apply.application.common.vo.amap.BizResponseVO;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.function.Function;

/**
 * 请求验证工具类
 * 用于抽取验签和获取 biz_content 的通用逻辑
 * 
 * <AUTHOR>
 */
@Slf4j
public class RequestValidationUtil {

    /**
     * 验证请求并解析 biz_content
     * 
     * @param requestMap 请求参数Map
     * @param signChecker 签名验证函数
     * @param failResponseBuilder 失败响应构建函数
     * @param targetClass 目标解析类
     * @param <T> 目标类型
     * @return 验证结果
     */
    public static <T> ValidationResult<T> validateAndParse(
            Map<String, String> requestMap,
            Function<Map<String, String>, Boolean> signChecker,
            Function<String, BizResponseVO> failResponseBuilder,
            Class<T> targetClass) {
        
        return validateAndParse(requestMap, signChecker, failResponseBuilder, targetClass, "biz_content");
    }

    /**
     * 验证请求并解析指定字段内容
     *
     * @param requestMap 请求参数Map
     * @param signChecker 签名验证函数
     * @param failResponseBuilder 失败响应构建函数
     * @param targetClass 目标解析类
     * @param contentKey 内容字段名
     * @param <T> 目标类型
     * @return 验证结果
     */
    public static <T> ValidationResult<T> validateAndParse(
            Map<String, String> requestMap,
            Function<Map<String, String>, Boolean> signChecker,
            Function<String, BizResponseVO> failResponseBuilder,
            Class<T> targetClass,
            String contentKey) {

        try {
            // 1. 验签
            if (!signChecker.apply(requestMap)) {
                BizResponseVO failResponse = failResponseBuilder.apply("签名校验失败");
                return ValidationResult.fail(failResponse, SubCodeEnum.SIGN_ERROR.getCode());
            }

            // 2. 获取内容
            String content = requestMap.get(contentKey);
            if (ObjectUtil.isEmpty(content)) {
                BizResponseVO failResponse = failResponseBuilder.apply(contentKey + "为空");
                return ValidationResult.fail(failResponse, SubCodeEnum.LACK_PARAM.getCode());
            }

            // 3. 解析内容
            T parsedObject;
            try {
                parsedObject = JSON.parseObject(content, targetClass);
            } catch (JSONException e) {
                log.warn("解析{}内容失败: {}", contentKey, e.getMessage());
                BizResponseVO failResponse = failResponseBuilder.apply(contentKey + "格式错误");
                return ValidationResult.fail(failResponse, SubCodeEnum.LACK_PARAM.getCode());
            }

            if (ObjectUtil.isEmpty(parsedObject)) {
                BizResponseVO failResponse = failResponseBuilder.apply(contentKey + "格式错误");
                return ValidationResult.fail(failResponse, SubCodeEnum.LACK_PARAM.getCode());
            }

            return ValidationResult.success(parsedObject);

        } catch (Exception e) {
            log.error("请求验证和解析异常: {}", e.getMessage(), e);
            BizResponseVO failResponse = failResponseBuilder.apply("请求处理失败: " + e.getMessage());
            return ValidationResult.fail(failResponse, SubCodeEnum.FAIL.getCode());
        }
    }

    /**
     * 简化的验签方法
     * 
     * @param requestMap 请求参数Map
     * @param signChecker 签名验证函数
     * @return 验签结果
     */
    public static boolean validateSign(Map<String, String> requestMap, Function<Map<String, String>, Boolean> signChecker) {
        try {
            return signChecker.apply(requestMap);
        } catch (Exception e) {
            log.error("验签异常: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取并解析内容
     * 
     * @param requestMap 请求参数Map
     * @param targetClass 目标解析类
     * @param contentKey 内容字段名
     * @param <T> 目标类型
     * @return 解析结果
     */
    public static <T> T parseContent(Map<String, String> requestMap, Class<T> targetClass, String contentKey) {
        String content = requestMap.get(contentKey);
        if (ObjectUtil.isEmpty(content)) {
            return null;
        }

        try {
            return JSON.parseObject(content, targetClass);
        } catch (JSONException e) {
            log.warn("解析{}内容失败: {}", contentKey, e.getMessage());
            return null;
        }
    }

    /**
     * 获取并解析 biz_content
     *
     * @param requestMap 请求参数Map
     * @param targetClass 目标解析类
     * @param <T> 目标类型
     * @return 解析结果
     */
    public static <T> T parseBizContent(Map<String, String> requestMap, Class<T> targetClass) {
        return parseContent(requestMap, targetClass, "biz_content");
    }



    /**
     * 验证结果封装类
     */
    public static class ValidationResult<T> {
        private final boolean success;
        private final T data;
        private final BizResponseVO failResponse;
        private final String errorCode;

        private ValidationResult(boolean success, T data, BizResponseVO failResponse, String errorCode) {
            this.success = success;
            this.data = data;
            this.failResponse = failResponse;
            this.errorCode = errorCode;
        }

        public static <T> ValidationResult<T> success(T data) {
            return new ValidationResult<>(true, data, null, null);
        }

        public static <T> ValidationResult<T> fail(BizResponseVO failResponse, String errorCode) {
            return new ValidationResult<>(false, null, failResponse, errorCode);
        }

        public boolean isSuccess() {
            return success;
        }

        public T getData() {
            return data;
        }

        public BizResponseVO getFailResponse() {
            return failResponse;
        }

        public String getErrorCode() {
            return errorCode;
        }
    }
}
