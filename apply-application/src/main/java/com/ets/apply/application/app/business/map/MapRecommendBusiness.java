package com.ets.apply.application.app.business.map;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.apply.application.app.thirdservice.feign.CallPhpApplyFeign;
import com.ets.apply.application.common.consts.map.MapRecommendTypeEnum;
import com.ets.apply.application.common.consts.map.MapSetCacheEnum;
import com.ets.apply.application.common.dto.map.*;
import com.ets.apply.application.common.dto.request.MapSetCacheByKeyDTO;
import com.ets.apply.application.common.vo.map.*;
import com.ets.apply.application.infra.entity.map.MapAssignEntity;
import com.ets.apply.application.infra.entity.map.MapPackageSnEntity;
import com.ets.apply.application.infra.entity.map.MapRecommendEntity;
import com.ets.apply.application.infra.service.MapLogService;
import com.ets.apply.application.infra.service.MapRecommendService;
import com.ets.common.ToolsHelper;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;

@Slf4j
@Component
public class MapRecommendBusiness {
    @Autowired
    private MapRecommendService mapRecommendService;
    @Autowired
    private MapConfigBusiness mapConfigBusiness;
    @Autowired
    private CallPhpApplyFeign callPhpApplyFeign;
    @Autowired
    private MapLogService mapLogService;
    /*
     * 获取全部模块列表
     */
    public IPage<MapRecommendListVO> getList(MapRecommendDTO dto) {
        // 分页设置
        IPage<MapRecommendEntity> oPage = new Page<>(dto.getPageNum(), dto.getPageSize(), true);

        // 查询条件设置
        LambdaQueryWrapper<MapRecommendEntity> wrapper = new LambdaQueryWrapper<>();

        wrapper.eq(StringUtils.isNotEmpty(dto.getType()),MapRecommendEntity::getType, dto.getType())
               .eq(MapRecommendEntity::getStatus, 1)
               .orderByDesc(MapRecommendEntity::getCreatedAt);
        IPage<MapRecommendEntity> pageList = mapRecommendService.getPageListByWrapper(oPage, wrapper);
        MapConfigDTO mapConfigDTO = new MapConfigDTO();
        mapConfigDTO.setConfigKey(dto.getType());
        MapConfigVO mapConfigVO = mapConfigBusiness.getInfoByKey(mapConfigDTO);
        return pageList.convert(record -> {
            MapRecommendListVO vo = new MapRecommendListVO();
            BeanUtils.copyProperties(record, vo);
            vo.setStatusStr((vo.getStatus() == 1)?"正常":"无效");
            vo.setKeyDesc(mapConfigVO.getKeyDesc());
            //ArrayList valuesList = new ArrayList();
            //JSONArray recommendValues = JSON.parseArray(record.getRecommendValues());
            JSONArray mapConfigValues = mapConfigVO.getConfigValuesObject();
            for(int j=0; j<mapConfigValues.size();j++){
                if(record.getRecommendValues().equals(mapConfigValues.getJSONObject(j).get("key"))){
                    //valuesList.add(mapConfigValues.getJSONObject(j));
                    vo.setRecommendValuesStr(mapConfigValues.getJSONObject(j).getString("value"));
                    break;
                }
            }

            //vo.setValuesList(valuesList);
            return vo;
        });
    }


    /*
     * 新增产品包
     */
    public Boolean addRecommend(MapRecommendEditDTO dto,String loginCode){
        MapRecommendEntity mapRecommendEntity = new MapRecommendEntity();
        mapRecommendEntity.setRecommendKey(dto.getRecommendKey());
        mapRecommendEntity.setRecommendValues(dto.getRecommendValues());
        mapRecommendEntity.setType(dto.getType());
        mapRecommendService.create(mapRecommendEntity);
        //记录日志
        mapLogService.addLog(
            loginCode,
            "recommend_"+dto.getType(),
            1,
            "addAssign",
            "新增recommend:"+dto.getRecommendKey(),
            "",
            JSONObject.toJSONString(dto)
        );
        //更新Recommend缓存
        MapSetCacheByKeyDTO mapSetCacheByKeyDTO = new MapSetCacheByKeyDTO();
        mapSetCacheByKeyDTO.setType(MapSetCacheEnum.MAP_SET_CACHE_TYPE_PREVIEW.getCode());
        mapSetCacheByKeyDTO.setCacheKey(dto.getType());
        callPhpApplyFeign.mapSetCacheByKey(mapSetCacheByKeyDTO);
        return true;
    }
    /*
     * 更新产品包
     */
    public Boolean updateRecommend(MapRecommendEditDTO dto,String loginCode){
        //对比有无更新
        MapRecommendEntity mapRecommendEntity = mapRecommendService.getById(dto.getId());
        if(mapRecommendEntity == null){
            ToolsHelper.throwException(dto.getId()+"不存在数据");
        }
        //对比数据
        JSONObject compareDifffObject = compareRecommendDiff(mapRecommendEntity,dto);
        if((compareDifffObject.getJSONObject("diff")).size() < 1){
            ToolsHelper.throwException("数据无变更");
        }
        LambdaUpdateWrapper<MapRecommendEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MapRecommendEntity::getId, dto.getId())
            .set(MapRecommendEntity::getRecommendKey, dto.getRecommendKey())
            .set(MapRecommendEntity::getStatus, dto.getStatus())
            .set(MapRecommendEntity::getUpdatedAt, LocalDateTime.now())
            .set(StringUtils.isNotEmpty(dto.getRecommendValues()),MapRecommendEntity::getRecommendValues, dto.getRecommendValues())
           ;
        mapRecommendService.updateByWrapper(wrapper);
        //记录日志
        mapLogService.addLog(
            loginCode,
            "recommend_"+mapRecommendEntity.getType(),
            1,
            "updateRecommend",
            "更新recommend:"+dto.getRecommendKey(),
            (compareDifffObject.getJSONObject("preOperate")).toJSONString(),
            (compareDifffObject.getJSONObject("afterOperate")).toJSONString()
        );
        //更新Recommend缓存
        MapSetCacheByKeyDTO mapSetCacheByKeyDTO = new MapSetCacheByKeyDTO();
        mapSetCacheByKeyDTO.setType(MapSetCacheEnum.MAP_SET_CACHE_TYPE_PREVIEW.getCode());
        mapSetCacheByKeyDTO.setCacheKey(mapRecommendEntity.getType());
        callPhpApplyFeign.mapSetCacheByKey(mapSetCacheByKeyDTO);

        return true;
    }
    /*
     * 更新产品包
     */
    public Boolean delRecommend(MapRecommendEditDTO dto,String loginCode){
        MapRecommendEntity mapRecommendEntity = mapRecommendService.getById(dto.getId());
        if(mapRecommendEntity == null){
            ToolsHelper.throwException(dto.getId()+"不存在数据");
        }
        LambdaUpdateWrapper<MapRecommendEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MapRecommendEntity::getId, dto.getId())
                .set(MapRecommendEntity::getUpdatedAt, LocalDateTime.now())
               .set(MapRecommendEntity::getStatus, dto.getStatus());
        mapRecommendService.updateByWrapper(wrapper);
        //记录日志
        mapLogService.addLog(
            loginCode,
            "recommend_"+mapRecommendEntity.getType(),
            1,
            "delRecommend",
            "删除指定key:"+mapRecommendEntity.getRecommendKey(),
            "",
            ""
        );
        //更新Recommend缓存
        MapSetCacheByKeyDTO mapSetCacheByKeyDTO = new MapSetCacheByKeyDTO();
        mapSetCacheByKeyDTO.setType(MapSetCacheEnum.MAP_SET_CACHE_TYPE_PREVIEW.getCode());
        mapSetCacheByKeyDTO.setCacheKey(mapRecommendEntity.getType());
        callPhpApplyFeign.mapSetCacheByKey(mapSetCacheByKeyDTO);

        return true;
    }
    /*
     * 更新产品包
     */
    public Boolean prod(MapRecommendPordDTO dto){
        //更新Recommend缓存
        MapSetCacheByKeyDTO mapSetCacheByKeyDTO = new MapSetCacheByKeyDTO();
        mapSetCacheByKeyDTO.setType(MapSetCacheEnum.MAP_SET_CACHE_TYPE_PROD.getCode());
        mapSetCacheByKeyDTO.setCacheKey(dto.getType());
        callPhpApplyFeign.mapSetCacheByKey(mapSetCacheByKeyDTO);
        return true;
    }

    public JSONObject compareRecommendDiff(MapRecommendEntity mapRecommendEntity,MapRecommendEditDTO dto){

        JSONObject preOperate = new JSONObject();
        JSONObject afterOperate = new JSONObject();
        JSONObject diff = new JSONObject();
        //recommend_key
        if(!mapRecommendEntity.getRecommendKey().equals(dto.getRecommendKey())){
            preOperate.put("recommend_key",mapRecommendEntity.getRecommendKey());
            afterOperate.put("recommend_key",dto.getRecommendKey());
            diff.put("recommend_key","{\"old\":"+mapRecommendEntity.getRecommendKey()+",\"new\":" + dto.getRecommendKey() + "}");
        }
        //recommend_values
        if(!mapRecommendEntity.getRecommendValues().equals(dto.getRecommendValues())){
            preOperate.put("recommend_values",mapRecommendEntity.getRecommendValues());
            afterOperate.put("recommend_values",dto.getRecommendValues());
            diff.put("recommend_values","{\"old\":"+mapRecommendEntity.getRecommendValues()+",\"new\":" + dto.getRecommendValues() + "}");
        }
        JSONObject returnObject = new JSONObject();
        returnObject.put("preOperate",preOperate);
        returnObject.put("afterOperate",afterOperate);
        returnObject.put("diff",diff);
        return returnObject;
    }
}
