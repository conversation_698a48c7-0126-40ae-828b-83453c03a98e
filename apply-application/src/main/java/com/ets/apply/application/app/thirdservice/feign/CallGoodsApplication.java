package com.ets.apply.application.app.thirdservice.feign;

import com.alibaba.fastjson.JSONObject;
import com.ets.apply.application.app.thirdservice.fallback.CallJavaGoodsFallbackFactory;
import com.ets.apply.application.app.thirdservice.response.goods.CheckStallSkuAttrVO;
import com.ets.apply.application.common.bo.external.ExternalCreateCustomerBO;
import com.ets.apply.application.common.bo.goods.GetSkuByAttrBO;
import com.ets.apply.application.common.bo.orderCenter.OrderCenterApplyBO;
import com.ets.apply.application.common.bo.external.ExternalSubmitSendBackBO;
import com.ets.apply.application.common.dto.goods.OrderCenterUpdateDTO;
import com.ets.apply.application.common.dto.request.goods.CheckStallSkuAttrDTO;
import com.ets.common.JsonResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import jakarta.validation.Valid;

import java.math.BigDecimal;

/**
 * 调用商品中心接口
 */
@FeignClient(
        url = "${microUrls.goods:http://goods-application:20170}",
        name = "CallGoodsApplication",
        fallbackFactory = CallJavaGoodsFallbackFactory.class)
public interface CallGoodsApplication {
    @PostMapping("/orderCenter/updateInfo")
    JsonResult<Object> updateInfo(@RequestBody @Valid OrderCenterUpdateDTO dto);

    @PostMapping(value = "/customerService/getCustomerServiceOrder")
    String getCustomerServiceOrder(@RequestParam(value = "businessSn") String businessSn, @RequestParam(value = "serviceType") Integer serviceType);
    @PostMapping(value = "/orderCenter/updateInfo")
    String orderCenterUpdateInfo(@RequestBody OrderCenterApplyBO dto);

    @PostMapping(value = "/goodsOrder/getRefundNotCancelResult")
    JsonResult<JSONObject>  getRefundNotCancelResult(@RequestParam(value = "goodsOrderSn") String goodsOrderSn, @RequestParam(value = "referSn") String referSn);
    @PostMapping(value = "/sale/recommendEtcByGoodsAttr")
    JsonResult<JSONObject> getSkuByAttr(@RequestBody GetSkuByAttrBO dto);

    @PostMapping(value = "/goodsOrder/refundNotCancel")
    JsonResult<JSONObject>  refundNotCancel(@RequestParam(value = "goodsOrderSn") String goodsOrderSn,
                                            @RequestParam(value = "reason") String reason,
                                            @RequestParam(value = "amount") BigDecimal amount,
                                            @RequestParam(value = "referSn") String referSn,
                                            @RequestParam(value = "isBackCoupon", required = false) Boolean isBackCoupon
    );

    @PostMapping(value = "/sale/checkStallSkuAttr")
    JsonResult<CheckStallSkuAttrVO> checkStallSkuAttr(@RequestBody CheckStallSkuAttrDTO dto);

    @PostMapping("/customerService/createOrder")
    JsonResult<JSONObject> customerServiceCreateOrder(@RequestBody ExternalCreateCustomerBO dto);

    @PostMapping("/customerService/submitSendBack")
    JsonResult<JSONObject> submitSendBack(@RequestBody ExternalSubmitSendBackBO dto);
}
