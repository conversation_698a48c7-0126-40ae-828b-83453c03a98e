package com.ets.apply.application;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ets.apply.application.app.business.ProductOrderBusiness;
import com.ets.apply.application.app.service.thirdPartner.AmapService;
import com.ets.apply.application.common.config.AmapConfig;
import com.ets.apply.application.common.consts.amap.CurrencyEnum;
import com.ets.apply.application.infra.entity.ProductOrderEntity;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;

import java.util.HashMap;
import java.util.Map;

@SpringBootTest(classes = ApplyApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringRunner.class)
@ActiveProfiles(value = "test")
public class AmapTest {

    @Autowired
    AmapConfig amapConfig;

    @Autowired
    ProductOrderBusiness productOrderBusiness;

    @Autowired
    AmapService amapService;
    
    private static final RestTemplate restTemplate = new RestTemplate();



    @Test
    public void orderSyncTest() throws Exception {
        String productOrderSn = "2507141731000063252";
        ProductOrderEntity productOrder = productOrderBusiness.getProductOrderBySn(productOrderSn);
        if(ObjectUtil.isEmpty(productOrder)){
            System.out.println("订单不存在");
        }
        Map<String, String> paramMap = new HashMap<String, String>();

        // 构造业务参数
        JSONObject bizContent = new JSONObject();
        bizContent.put("amapOrderId", productOrder.getThirdOrderSn());
        bizContent.put("cpOrderId", productOrder.getProductOrderSn());
        bizContent.put("bizType",98);
        bizContent.put("cpCode","98001");
        paramMap.put("biz_content", JSON.toJSONString(bizContent));

        // 构造参与加签的公共参数
        paramMap.put("app_id", amapConfig.getAppId());
        paramMap.put("utc_timestamp", String.valueOf(System.currentTimeMillis()));
        paramMap.put("version", "1.0");
        paramMap.put("charset", "UTF-8");
        paramMap.put("method", "amap.standard.order.change");
        paramMap.put("sign", amapService.generateSign(paramMap));
        paramMap.put("sign_type", "RSA2");
        requestAmap(paramMap);

    }

    @Test
    public void orderRefundTest() throws Exception {
        String productOrderSn = "2507161528000063702";
        ProductOrderEntity productOrder = productOrderBusiness.getProductOrderBySn(productOrderSn);
        if(ObjectUtil.isEmpty(productOrder)){
            System.out.println("订单不存在");
        }

        Map<String, String> paramMap = new HashMap<String, String>();

        // 构造业务参数
        JSONObject bizContent = new JSONObject();
        bizContent.put("amapOrderId", productOrder.getThirdOrderSn());
        bizContent.put("cpOrderId", productOrder.getProductOrderSn());
        bizContent.put("refundStatus",6100);
        bizContent.put("refundType",2);
        bizContent.put("finalRefundPrice",productOrder.getPaidAmount().multiply(BigDecimal.valueOf(100)).longValue());
        bizContent.put("finalRefundTime",System.currentTimeMillis());
        bizContent.put("currency", CurrencyEnum.CNY.getCode());
        HashMap<String,String> reason = new HashMap<>();
        reason.put("desc","test");
        bizContent.put("reason",reason);


        paramMap.put("biz_content", JSON.toJSONString(bizContent));

        // 构造参与加签的公共参数
        paramMap.put("app_id", amapConfig.getAppId());
        paramMap.put("utc_timestamp", String.valueOf(System.currentTimeMillis()));
        paramMap.put("version", "1.0");
        paramMap.put("charset", "UTF-8");
        paramMap.put("method", "amap.standard.order.callback.refund");
        paramMap.put("sign", amapService.generateSign(paramMap));
        paramMap.put("sign_type", "RSA2");
        // 调用高德接口
        // 线下联调环境：https://restapi.amap.com/rest/openmp/devgw?key=高德云店秘钥key
        // 线上环境：https://restapi.amap.com/rest/openmp/gw?key=高德云店秘钥key
        // 高德云店秘钥key请登录高德云店「接入准备及配置」页面查看

        requestAmap(paramMap);

    }

    private static MultiValueMap convertToMultiValueMap(Map<String, String> paramMap) {
        MultiValueMap<String, Object> multiValueMap = new LinkedMultiValueMap<>();
        for (Map.Entry<String, String> entry : paramMap.entrySet()) {
            multiValueMap.add(entry.getKey(), entry.getValue());
        }
        return multiValueMap;
    }




    private void requestAmap( Map<String, String> paramMap){
        // 定义Content-type、初始化HttpEntity
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        MultiValueMap<String, Object> formBody = convertToMultiValueMap(paramMap);
        HttpEntity<MultiValueMap<String, Object>> entity = new HttpEntity<>(formBody, headers);

        // 调用高德接口
        // 线下联调环境：https://restapi.amap.com/rest/openmp/devgw?key=高德云店秘钥key
        // 线上环境：https://restapi.amap.com/rest/openmp/gw?key=高德云店秘钥key
        // 高德云店秘钥key请登录高德云店「接入准备及配置」页面查看
        String url = "https://restapi.amap.com/rest/openmp/devgw?key=77ff5350723d29901fa859515f553644";
        ResponseEntity<String> resp = restTemplate.postForEntity(url, entity, String.class);
        if (resp.getBody() != null && !"".equals(resp.getBody())) {
            JSONObject respObj = JSONObject.parseObject(resp.getBody());
            System.out.println("高德返回结果:" + respObj);
        }
    }

    @Test
    public void orderSyncTestFeign() throws Exception {
        String productOrderSn = "2507141552000063192";
        ProductOrderEntity productOrder = productOrderBusiness.getProductOrderBySn(productOrderSn);
        if(ObjectUtil.isEmpty(productOrder)){
            System.out.println("订单不存在");
        }


        Map<String, String> paramMap = new HashMap<String, String>();

        // 构造业务参数
        JSONObject bizContent = new JSONObject();
        bizContent.put("amapOrderId", productOrder.getThirdOrderSn());
        bizContent.put("cpOrderId", productOrder.getProductOrderSn());
        paramMap.put("biz_content", JSON.toJSONString(bizContent));

        // 构造参与加签的公共参数
        paramMap.put("app_id", amapConfig.getAppId());
        paramMap.put("utc_timestamp", String.valueOf(System.currentTimeMillis()));
        paramMap.put("version", "1.0");
        paramMap.put("charset", "UTF-8");
        paramMap.put("method", "amap.standard.order.change");
        paramMap.put("sign", amapService.generateSign(paramMap));
        paramMap.put("sign_type", "RSA2");
//        requestAmap(paramMap);


        String result = amapService.requestAmap(amapConfig.getOrderSyncMethod(), paramMap);
        // 调用高德接口
        // 线下联调环境：https://restapi.amap.com/rest/openmp/devgw?key=高德云店秘钥key
        // 线上环境：https://restapi.amap.com/rest/openmp/gw?key=高德云店秘钥key

        System.out.println("高德返回结果："+result);

    }

}
