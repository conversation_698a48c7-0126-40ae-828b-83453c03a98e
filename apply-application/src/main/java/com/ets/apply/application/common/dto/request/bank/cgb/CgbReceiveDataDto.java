package com.ets.apply.application.common.dto.request.bank.cgb;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;

@Data
public class CgbReceiveDataDto {

    @NotBlank(message = "data不能为空")
    private String data;
    /**
     * cert id
     */
    @NotBlank(message = "certId不能为空")
    private String certId;

    @NotBlank(message = "encryptKey不能为空")
    private String encryptKey;

    @NotBlank(message = "channel不能为空")
    private String channel;
    @NotBlank(message = "signature不能为空")
    private String signature;
}
