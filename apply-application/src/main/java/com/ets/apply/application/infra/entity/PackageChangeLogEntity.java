package com.ets.apply.application.infra.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("package_change_log")
public class PackageChangeLogEntity extends BaseEntity<PackageChangeLogEntity> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 关联产品包sn
     */
    private String packageSn;

    /**
     * 变更操作版本描述
     */
    private String versionDesc;

    /**
     * 变更前参数的数据json
     */
    private String changeBefore;

    /**
     * 变更后参数的数据json
     */
    private String changeAfter;

    /**
     * 备注
     */
    private String remark;

    /**
     * 操作者
     */
    private String operater;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;


}
