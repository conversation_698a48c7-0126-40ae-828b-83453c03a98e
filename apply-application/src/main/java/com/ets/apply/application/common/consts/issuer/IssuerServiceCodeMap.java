package com.ets.apply.application.common.consts.issuer;

import com.ets.apply.application.common.consts.idCard.IdCardEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum IssuerServiceCodeMap {
    ISSUER_SERVICE_CODE_BJ("110101", "北京","京","北京速通科技有限公司"),
    ISSUER_SERVICE_CODE_TJ("120101", "天津","津","北京速通科技有限公司"),
    ISSUER_SERVICE_CODE_HB("130101", "河北","冀","北京速通科技有限公司"),
    ISSUER_SERVICE_CODE_SX("140101", "山西","晋","北京速通科技有限公司"),

    ISSUER_SERVICE_CODE_NM("150101", "内蒙","蒙",""),

    ISSUER_SERVICE_CODE_LN("210101", "辽宁","辽","北京速通科技有限公司"),

    ISSUER_SERVICE_CODE_JL("220101", "吉林","吉","北京速通科技有限公司"),

    ISSUER_SERVICE_CODE_HLJ("230101", "黑龙江","黑","北京速通科技有限公司"),

    ISSUER_SERVICE_CODE_SH("310101", "上海","沪","北京速通科技有限公司"),

    ISSUER_SERVICE_CODE_JS("320101", "江苏","苏","北京速通科技有限公司"),

    ISSUER_SERVICE_CODE_ZJ("330101", "北京","京","北京速通科技有限公司"),

    ISSUER_SERVICE_CODE_AH("340101", "安徽","皖","北京速通科技有限公司"),

    ISSUER_SERVICE_CODE_FJ("350101", "福建","闽","北京速通科技有限公司"),

    ISSUER_SERVICE_CODE_JX("360101", "江西","赣","北京速通科技有限公司"),

    ISSUER_SERVICE_CODE_SD("370101", "山东","鲁","北京速通科技有限公司"),

    ISSUER_SERVICE_CODE_HN("410101", "河南","豫","北京速通科技有限公司"),

    ISSUER_SERVICE_CODE_HUB("420102", "湖北","鄂","北京速通科技有限公司"),

    ISSUER_SERVICE_CODE_HUN("430101", "湖南","湘","北京速通科技有限公司"),

    ISSUER_SERVICE_CODE_GD("440101", "广东","粤","北京速通科技有限公司"),

    ISSUER_SERVICE_CODE_GX("450101", "广西","桂","北京速通科技有限公司"),

    ISSUER_SERVICE_CODE_ZQ("500101", "重庆","渝","北京速通科技有限公司"),

    ISSUER_SERVICE_CODE_SC("510101", "四川","川","北京速通科技有限公司"),

    ISSUER_SERVICE_CODE_GUIZHOU("320101", "贵州","贵","北京速通科技有限公司"),

    ISSUER_SERVICE_CODE_YN("530101", "云南","云","北京速通科技有限公司"),

    ISSUER_SERVICE_CODE_XZ("540101", "西藏","藏","北京速通科技有限公司"),

    ISSUER_SERVICE_CODE_SHANX("610101", "陕西","陕","北京速通科技有限公司"),

    ISSUER_SERVICE_CODE_GS("620101", "甘肃","甘","北京速通科技有限公司"),

    ISSUER_SERVICE_CODE_QH("630101", "青海","青","北京速通科技有限公司"),

    ISSUER_SERVICE_CODE_NX("640101", "宁夏","宁夏","北京速通科技有限公司"),

    ISSUER_SERVICE_CODE_XJ("650101", "新疆","新","北京速通科技有限公司"),

    ISSUER_SERVICE_CODE_HAINAN("320101", "海南","琼","北京速通科技有限公司");



    private final String code;
    private final String province;
    private final String plateNoPre;
    private final String name;

    public static String getNameByPlateNoPre(String plateNoPre) {
        for (IssuerServiceCodeMap node : IssuerServiceCodeMap.values()) {
            if (node.getPlateNoPre().equals(plateNoPre)) {
                return node.getName();
            }
        }
        return "";
    }

}
