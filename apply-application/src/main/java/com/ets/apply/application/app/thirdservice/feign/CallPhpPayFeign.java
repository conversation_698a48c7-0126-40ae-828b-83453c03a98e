package com.ets.apply.application.app.thirdservice.feign;

import com.alibaba.fastjson.JSONObject;
import com.ets.apply.application.app.thirdservice.fallback.CallPhpPayFallbackFactory;
import com.ets.apply.application.app.thirdservice.request.pay.AnyCouponDTO;
import com.ets.apply.application.app.thirdservice.request.pay.QueryRefundResultDTO;
import com.ets.apply.application.common.dto.order.GetInfoByPaymentSnDTO;
import com.ets.apply.application.common.dto.payment.PhpRefundDTO;
import com.ets.apply.application.common.dto.request.entrust.EntrustPayParamsDTO;
import com.ets.apply.application.common.dto.request.entrust.GetBindBankAndVerifyDTO;
import com.ets.apply.application.common.dto.request.productOrder.PaymentRefundDTO;
import com.ets.common.JsonResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 调用PHP队列
 */
@FeignClient(
        url = "${params.microUrls.gd-micro-pay}",
        name = "CallPhpPayFeign",
        fallbackFactory = CallPhpPayFallbackFactory.class
)
public interface CallPhpPayFeign {

    @PostMapping(value = "/payment/manual-refund-async")
    String refund(@RequestBody PaymentRefundDTO paymentRefundDTO);

    @PostMapping(value = "coupon/any")
    String anyCoupon(@RequestBody AnyCouponDTO anyCouponDTO);

    @PostMapping(value = "/payment/manual-refund")
    JsonResult<JSONObject> manualRefund(@RequestBody PhpRefundDTO dto);

    @PostMapping(value = "/entrust/get-bind-bank-and-verify")
    JsonResult<JSONObject> entrustCheck(@RequestBody GetBindBankAndVerifyDTO dto);
    @PostMapping(value = "/payment/entrust-pay")
    JsonResult<JSONObject> entrustPay(@RequestBody EntrustPayParamsDTO dto);

    @PostMapping(value = "/payment/manual-refund-query-by-payment-sn")
    JsonResult<JSONObject> manualRefundQueryByPaymentSn(@RequestBody GetInfoByPaymentSnDTO dto);

    @PostMapping("/payment/query-refund-result")
    JsonResult<JSONObject> queryRefundResult(@RequestBody QueryRefundResultDTO dto);
}
