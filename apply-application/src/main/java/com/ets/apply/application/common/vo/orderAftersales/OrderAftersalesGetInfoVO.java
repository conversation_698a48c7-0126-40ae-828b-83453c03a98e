package com.ets.apply.application.common.vo.orderAftersales;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class OrderAftersalesGetInfoVO {

    private String applySn;
    /**
     * 申请类型
     */
    private Integer type;

    private Long uid;

    /**
     * 订单流水号
     */
    private String orderSn;

    private String reason;

    /**
     * 取消失败的原因
     */
    private String error;

    /**
     * 车牌号（冗余）
     */
    private String plateNo;

    /**
     * 0: 蓝色 ；1: 黄色 ；2: 黑色 ；3: 白色 ；4: 渐变绿色 ；5: 黄绿双拼色 ；6: 蓝白渐变色 ；7: 临时牌照 ；9: 未确定 11: 绿色【不用】 12: 红色
     */
    private Integer plateColor;

    /**
     * 订单申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime applyAt;

    /**
     * 申请状态
     */
    private Integer status;
    /**
     * 业务状态：0未完成1已完成
     */
    private Integer businessStatus;
    /**
     * 对应发卡方编码
     */
    private String issuerCode;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
    /**
     * 是否需要手机验证码
     */
    private Integer needPhone = 0;

}
