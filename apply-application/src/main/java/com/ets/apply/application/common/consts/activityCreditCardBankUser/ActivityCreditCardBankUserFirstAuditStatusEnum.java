package com.ets.apply.application.common.consts.activityCreditCardBankUser;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum ActivityCreditCardBankUserFirstAuditStatusEnum {

    STATUS_DEFAULT(0, "默认"),
    STATUS_PASS_AUDIT(1, "审核通过"),
    STATUS_FAIL_AUDIT(2, "审核不通过")
    ;

    private final int code;
    private final String description;

    public static String getDescByCode(int code) {
        for (ActivityCreditCardBankUserFirstAuditStatusEnum node : ActivityCreditCardBankUserFirstAuditStatusEnum.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }

    public static List<Map<String, String>> getLabelList() {
        List<Map<String, String>> list = new ArrayList<>();

        for (ActivityCreditCardBankUserFirstAuditStatusEnum node : ActivityCreditCardBankUserFirstAuditStatusEnum.values()) {
            Map<String, String> row = new LinkedHashMap<>();
            row.put("label", node.getDescription());
            row.put("value", String.valueOf(node.getCode()));

            list.add(row);
        }

        return list;
    }
}
