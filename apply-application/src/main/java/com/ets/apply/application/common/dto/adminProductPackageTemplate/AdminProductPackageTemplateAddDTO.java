package com.ets.apply.application.common.dto.adminProductPackageTemplate;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import java.math.BigDecimal;

@Data
public class AdminProductPackageTemplateAddDTO {
    @NotNull(message = "name不可为空")
    private String name;
    /*******************商品属性 start************************/
        @NotNull(message = "省份id不可为空")
        private Integer issuerId;
        @NotNull(message = "goodsSku不可为空")
        private String goodsSku;
        @NotNull(message = "设备类型不可为空")
        private Integer deviceType;
        @NotNull(message = "设备厂家不可为空")
        private Integer manufacturer;
        @NotNull(message = "激活视频不可为空")
        private Integer deviceTutorial;
        @NotNull(message = "设备名称不可为空")
        private String deviceName;
        private Integer deviceVersion;
        //颜色
        private Integer deviceColor;
    /*******************商品属性 end************************/

    /*******************申办模块 start************************/
    private Integer isTruck = 0;
    private Integer isBase = 0;
    private Integer scene = 10;
    //营销配置
    private String saleSn;
    @NotNull(message = "flowType不可为空")
    private Integer flowType;
    @NotNull(message = "cardId不可为空")
    private Integer cardId;
    @NotNull(message = "purchaseType不可为空")
    private Integer purchaseType;
    private Integer purchaseParty = 0;
    @NotNull(message = "发货类型不可为空")
    private Integer deliveryType;
    private Integer offlineAutoDelivery = 0;
    @NotNull(message = "签约方式不可为空")
    private Integer paymentMode;
    // 保证金支付模式
    private Integer useDeposit = 0;
    /*****************申办模块 end**************************/

    /*******************其他模块 start************************/
    /**
     * 回收金额
     */
    private BigDecimal recoveryAmount;
    /**
     * 是否支持设备估值 0 不支持 1支持 默认0
     */
    private Integer allowDeviceValuation = 0;

    @NotNull(message = "是否收违约金不可为空")
    private Integer revokeNeedPay;
    //是否只有线下换货的
    private Integer isOnlyOfflineExchange = 0;
    //是否展示重办
    private Integer isShowReapply = 1;
    /*
     * 仓库发货配置
     */
    private Integer addressConfigId;
    /*****************其他模块 end**************************/

}
