package com.ets.apply.application.controller.internal;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.apply.application.common.dto.taxi.*;
import com.ets.apply.application.common.vo.taxi.TaxiImportLogVo;
import com.ets.apply.application.controller.BaseController;
import com.ets.common.ToolsHelper;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;

import lombok.extern.slf4j.Slf4j;

import com.ets.common.JsonResult;
import com.ets.common.BizException;
import com.ets.apply.application.app.business.TaxiBusiness;
import com.ets.apply.application.common.vo.taxi.TaxiImportItemVo;
import com.ets.apply.application.common.vo.taxi.TaxiRepushImportVo;
import com.ets.apply.application.common.vo.taxi.TaxiEditImportVo;
import com.ets.apply.application.infra.entity.TaxiCompanyEntity;
import com.ets.apply.application.infra.entity.TaxiEntrustEntity;
import com.ets.apply.application.infra.entity.TaxiImportEntity;

import jakarta.validation.Valid;

@Slf4j
@RestController
@RequestMapping("/taxi")
public class TaxiController extends BaseController {
    @Autowired
    private TaxiBusiness taxiBusiness;

    @PostMapping("/importData")
    public JsonResult<?> importData(@RequestParam("file") MultipartFile file) throws BizException {
        log.info("{}", file.getSize());

        File fileZip = new File("/tmp/taxi.zip");
        try {
            file.transferTo(fileZip);
        } catch (Exception e) {
            log.info("保存文件失败: {}", e);
        }

        try {
            taxiBusiness.importData(fileZip);
        } catch (IOException e) {
            log.info("导入数据失败: {}", e);
        }

        return JsonResult.ok(null);
    }

    @PostMapping("/importList")
    public JsonResult<?> importList(@RequestBody @Validated ImportListDto dto) throws BizException {
//        IPage<TaxiImportEntity> pageData = taxiBusiness.getImportList(
//                dto.getCompanyName(),
//                dto.getPlateNo(),
//                dto.getImportStatus(),
//                dto.getStartImportTime(),
//                dto.getEndImportTime(),
//                dto.getPageNum(),
//                dto.getPageSize());

        IPage<TaxiImportEntity> pageData = taxiBusiness.getImportList(dto);
        return JsonResult.ok(pageData);
    }

    @PostMapping("/getImportItem")
    public JsonResult<?> getImportItem(@RequestBody @Validated ImportItemDto dto) throws BizException {
        TaxiImportItemVo vo = taxiBusiness.getImportItem(dto.getId());
        return JsonResult.ok(vo);
    }

    @PostMapping("/editImportItem")
    public JsonResult<?> editImportItem(@RequestBody @Validated ImportEditDto dto) throws BizException {
        TaxiEditImportVo vo = taxiBusiness.editImportItem(dto.getId(), dto.getPlateNo(), dto.getName(), dto.getNumber(), dto.getValidDate(), dto.getRegisterNo());
        return JsonResult.ok(vo);
    }

    @PostMapping("/repushImportItem")
    public JsonResult<?> repushImportItem(@RequestBody @Validated ImportItemDto dto) throws BizException {
        TaxiRepushImportVo vo = taxiBusiness.repushImportItem(dto.getId());
        return JsonResult.ok(vo);
    }

    @PostMapping("/entrustList")
    public JsonResult<?> entrustList(@RequestBody @Validated EntrustListDto dto) throws BizException {
        IPage<TaxiEntrustEntity> pageData = taxiBusiness.getEntrustList(dto.getPlateNo(), dto.getLicense(), dto.getPageNum(), dto.getPageSize());
        return JsonResult.ok(pageData);
    }

    @PostMapping("/companyList")
    public JsonResult<?> companyList(@RequestBody @Validated CompanyListDto dto) throws BizException {
        IPage<TaxiCompanyEntity> pageData = taxiBusiness.getCompanyList(dto);
        return JsonResult.ok(pageData);
    }

    @PostMapping("/addCompany")
    public JsonResult<?> addCompany(@RequestBody @Validated CompanyAddDto dto) throws BizException {
        boolean result = taxiBusiness.addCompany(dto.getCompanyName(), dto.getPhone(), dto.getSendArea(), dto.getAddress(), dto.getLicense(), dto.getRegionId());
        return JsonResult.ok(result);
    }

    @RequestMapping("/getSelectOptions")
    @ResponseBody
    public JsonResult<HashMap<String, List<Map<String, String>>>> getSelectOptions() {

        return JsonResult.ok(taxiBusiness.getSelectOptions());
    }
    @RequestMapping("/getLogList")
    @ResponseBody
    public JsonResult<IPage<TaxiImportLogVo>> getLogList(@RequestBody(required = false) @Valid ImportLogListDto request) {

        if (request == null) {
            ToolsHelper.throwException("请传入JSON格式的参数");
        }

        return JsonResult.ok(taxiBusiness.getLogList(request));
    }

}
