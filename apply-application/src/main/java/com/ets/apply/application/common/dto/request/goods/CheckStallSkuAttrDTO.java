package com.ets.apply.application.common.dto.request.goods;

import com.ets.apply.application.common.bo.goods.GetSkuByAttrBO;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class CheckStallSkuAttrDTO {

    @NotBlank
    private String stallCode;

    @NotBlank
    private String skuSn;

    private AttrMap attrMap;

    @Data
    @Builder
    public static class AttrMap {
        //  "attrMap": {"issuer_id":"25","manufacturer":"1","device_color":"0","device_type":"2",
        //  "device_version":"1","wrapper":1}
        @JsonProperty("issuer_id")
        private String issuerId;
        private String manufacturer;
        @JsonProperty("device_color")
        private String deviceColor;
        @JsonProperty("device_type")
        private String deviceType;
        @JsonProperty("device_version")
        private String deviceVersion;
    }


    public CheckStallSkuAttrDTO(String stallCode, String skuSn, AttrMap attrMap) {
        this.stallCode = stallCode;
        this.skuSn = skuSn;
        this.attrMap = attrMap;
    }

}
