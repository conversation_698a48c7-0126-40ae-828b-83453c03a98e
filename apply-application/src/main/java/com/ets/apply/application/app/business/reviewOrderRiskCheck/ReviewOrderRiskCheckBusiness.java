package com.ets.apply.application.app.business.reviewOrderRiskCheck;

import cn.hutool.core.util.ObjectUtil;
import com.ets.apply.application.app.factory.task.TaskFactory;
import com.ets.apply.application.common.consts.reviewOrderRiskCheck.RiskStatus;
import com.ets.apply.application.common.consts.taskRecord.TaskRecordReferTypeEnum;
import com.ets.apply.application.common.dto.task.TaskRecordDTO;
import com.ets.apply.application.infra.entity.ReviewOrderRiskCheckEntity;
import com.ets.apply.application.infra.service.ReviewOrderRiskCheckService;
import com.ets.common.BizException;
import com.ets.common.ToolsHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Component
public class ReviewOrderRiskCheckBusiness {

    @Autowired
    private ReviewOrderRiskCheckService reviewOrderRiskCheckService;

    public void addTask(String riskSn) {
        try {
            //塞队列进行发货操作
            TaskRecordDTO taskRecordDTO = new TaskRecordDTO();
            taskRecordDTO.setReferSn(riskSn);
            taskRecordDTO.setReferType(TaskRecordReferTypeEnum.TASK_RISK_CHECK_UPLOAD.getType());
            // 机器时间不一致，执行时间减一下防止触发不到时间的限制
            taskRecordDTO.setNextExecTime(LocalDateTime.now().minusSeconds(2));
            // 数据暂时不需要存储
            taskRecordDTO.setNotifyContent("");
            TaskFactory.create(TaskRecordReferTypeEnum.TASK_RISK_CHECK_UPLOAD.getType()).addAndPush(taskRecordDTO);
        } catch (BizException e) {
            ToolsHelper.throwException("推送审核资料失败，请稍后重试");
        }
    }

    /**
     * 驳回风控单
     */
    public void rejectRiskCheck(String reviewOrderSn) {
        // 找到最新的一个风控单
        ReviewOrderRiskCheckEntity reviewOrderRiskCheck = reviewOrderRiskCheckService.getLatestByReviewOrderSn(reviewOrderSn);
        if (ObjectUtil.isEmpty(reviewOrderRiskCheck)) {
            return;
        }
        if (ObjectUtil.equal(reviewOrderRiskCheck.getRiskStatus(), RiskStatus.FAIL.getCode())) {
            return;
        }
        // 修改风控单的状态为拒绝
        reviewOrderRiskCheckService.updateRiskStatus(reviewOrderRiskCheck.getRiskCheckSn(), RiskStatus.FAIL.getCode());

    }
}
