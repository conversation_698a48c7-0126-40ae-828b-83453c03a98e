package com.ets.apply.application.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@RefreshScope
@Configuration
@Data
@ConfigurationProperties(prefix = "params.xconnect")
public class XconnectConfig {

    /**
     * 请求路径
     */
    private String hostUrl;
    /**
     * ETC订单保存接口
     */
    private String orderSaveClientId;

    /**
     * 秘钥
     */
    private String orderSaveSecret;


    /**
     * ETC订单保存接口
     */
    private String orderUpdateClientId;

    /**
     * 秘钥
     */
    private String orderUpdateSecret;
}
