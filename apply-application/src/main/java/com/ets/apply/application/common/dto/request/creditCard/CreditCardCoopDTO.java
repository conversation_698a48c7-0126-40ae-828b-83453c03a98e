package com.ets.apply.application.common.dto.request.creditCard;

import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

@Data
public class CreditCardCoopDTO {

    /** 用户ID */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /** 1：广发，2：交通，3：平安 */
    @NotNull(message = "银行不能为空")
    private Integer bank;

    /** 订单号，需要唯一 */
    @NotEmpty(message = "订单不能为空")
    private String orderNo;

}
