package com.ets.apply.application.common.consts.deposit;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 保证金记录状态枚举
 */
@Getter
@AllArgsConstructor
public enum DepositRecordStatusEnum {

    INIT(0, "初始化"),
    SUCCESS(1, "成功"),
    FAIL(2, "失败"),
    ;

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;

    static {
        map = Arrays.stream(DepositRecordStatusEnum.values()).collect(Collectors.toMap(DepositRecordStatusEnum::getValue, DepositRecordStatusEnum::getDesc));
    }
}
