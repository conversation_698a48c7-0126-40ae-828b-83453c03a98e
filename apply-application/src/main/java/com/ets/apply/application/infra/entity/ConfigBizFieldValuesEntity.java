package com.ets.apply.application.infra.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 业务字段配置值
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("config_biz_field_values")
public class ConfigBizFieldValuesEntity extends BaseEntity<ConfigBizFieldValuesEntity> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 字段编码
     */
    private String bizField;

    /**
     * 字段key值
     */
    private String fieldKey;

    /**
     * 字段key值对应名称
     */
    private String name;

    /**
     * 操作者
     */
    private String operator;

    /**
     * 排序
     */
    private Integer sort;
    /**
     * 排序
     */
    private Integer status;
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    private Integer parentId;


}
