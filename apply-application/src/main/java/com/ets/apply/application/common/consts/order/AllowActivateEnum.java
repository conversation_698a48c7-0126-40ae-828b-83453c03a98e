package com.ets.apply.application.common.consts.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AllowActivateEnum {
    NOT_ALLOW_ACTIVATE(0, "不允许激活"),

    ALLOW_ACTIVATE(1, "允许激活");

    private final int code;
    private final String description;

    public static String getDescByCode(int code) {
        for (AllowActivateEnum node : AllowActivateEnum.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }

}
