package com.ets.apply.feign.fallback;

import com.ets.apply.feign.feign.ProductPackageFeign;
import com.ets.apply.feign.request.BatchQueryDTO;
import com.ets.apply.feign.response.ProductPackageBatchQueryResponse;
import com.ets.common.JsonResult;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

import jakarta.validation.Valid;
import java.util.List;

@Component
@Slf4j
public class ProductPackageFallbackFactory implements FallbackFactory<ProductPackageFeign> {

    @Override
    public ProductPackageFeign create(Throwable cause) {

        return new ProductPackageFeign() {

            @Override
            public JsonResult<List<ProductPackageBatchQueryResponse>> batchQuery(@RequestBody @Valid BatchQueryDTO batchQueryDTO) {
                return JsonResult.error("获取产品包信息失败：" + cause.getMessage());
            }
        };
    }
}