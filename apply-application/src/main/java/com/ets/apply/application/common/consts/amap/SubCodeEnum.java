package com.ets.apply.application.common.consts.amap;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum SubCodeEnum {
    /**
     * 200	成功	包括幂等成功
     * 400	通用错误
     * 410	缺少必要参数
     * 420	签名错误
     * 430	权限错误
     * 440	数据不存在
     * 450	限购错误
     * 460	限流
     */
    SUCCESS("200", "成功"),
    FAIL("400", "通用错误"),
    LACK_PARAM("410", "缺少必要参数"),
    SIGN_ERROR("420", "签名错误"),
    PERMISSION_ERROR("430", "权限错误"),
    DATA_NOT_EXIST("440", "数据不存在"),
    LIMIT_ERROR("450", "限购错误"),
    FLOW_LIMIT("460", "限流");

    private final String code;
    private final String description;

    public static String getDescByCode(String code) {
        for (SubCodeEnum node : SubCodeEnum.values()) {
            if (Objects.equals(node.getCode(), code)) {
                return node.getDescription();
            }
        }

        return "";
    }
}
