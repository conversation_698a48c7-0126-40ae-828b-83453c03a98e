package com.ets.apply.application.app.business.creditCardBank;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.apply.application.common.consts.creditCardBankConfig.CreditCardBankConfigBankStatusEnum;
import com.ets.apply.application.common.dto.creditCardBank.CreditCardBankListDTO;
import com.ets.apply.application.common.dto.creditCardBank.CreditCardBankModifyDTO;
import com.ets.apply.application.common.dto.creditCardBank.CreditCardBankSetStatusDTO;
import com.ets.apply.application.common.dto.creditCardBank.CreditCardBankUpdateSortDTO;
import com.ets.apply.application.common.dto.request.creditCardBank.CreditCardBankListInfoDTO;
import com.ets.apply.application.common.vo.creditCardBank.CreditCardBankDetailVO;
import com.ets.apply.application.common.vo.creditCardBank.CreditCardBankInfoListVO;
import com.ets.apply.application.common.vo.creditCardBank.CreditCardBankListVO;
import com.ets.apply.application.infra.entity.CreditCardBankConfigEntity;
import com.ets.apply.application.infra.entity.CreditCardBankEntity;
import com.ets.apply.application.infra.service.ConfigBizFieldValuesService;
import com.ets.apply.application.infra.service.CreditCardBankConfigService;
import com.ets.apply.application.infra.service.CreditCardBankService;
import com.ets.common.RequestHelper;
import com.ets.common.ToolsHelper;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class CreditCardBankBusiness {

    @Autowired
    private CreditCardBankService creditCardBankService;

    @Autowired
    private ConfigBizFieldValuesService configBizFieldValuesService;

    @Autowired
    private CreditCardBankConfigService creditCardBankConfigService;

    public IPage<CreditCardBankListVO> getList(CreditCardBankListDTO listDTO) {
        IPage<CreditCardBankEntity> oPage = new Page<>(listDTO.getPageNum(), listDTO.getPageSize(),
                true);
        LambdaQueryWrapper<CreditCardBankEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StringUtils.isNotBlank(listDTO.getBankName()), CreditCardBankEntity::getBankName,
                        listDTO.getBankName())
                .eq(ObjectUtil.isNotNull(listDTO.getBankType()), CreditCardBankEntity::getBankType,
                        listDTO.getBankType())
                .gt(ObjectUtil.isNotNull(listDTO.getUpdatedAtStart()), CreditCardBankEntity::getUpdatedAt,
                        listDTO.getUpdatedAtStart())
                .le(ObjectUtil.isNotNull(listDTO.getUpdatedAtEnd()), CreditCardBankEntity::getUpdatedAt,
                        listDTO.getUpdatedAtEnd())
                .orderByDesc(CreditCardBankEntity::getSort)
        ;
        IPage<CreditCardBankEntity> pageList = creditCardBankService.getPageListByWrapper(oPage,
                wrapper);
        return pageList.convert(record -> {
            CreditCardBankListVO pageListVO = new CreditCardBankListVO();
            BeanUtils.copyProperties(record, pageListVO);
            // welfareDetail 字段是数组，不能直接复制，需要处理
            // 把welfareDetail 转成json 对象
            Gson gson = new Gson();
            List<Object> list = gson.fromJson(
                    record.getWelfareDetail(),
                    new TypeToken<List<Object>>() {
                    }.getType()
            );
            pageListVO.setWelfareDetail(list);

            if (ObjectUtil.isNotEmpty(record.getTagList())) {
                List<Object> tagList = gson.fromJson(record.getTagList(),
                        new TypeToken<List<Object>>() {
                        }.getType());
                pageListVO.setTagList(tagList);
            }

            if (ObjectUtil.isNotEmpty(record.getExtraInfo())) {
                Object extraInfo = gson.fromJson(record.getExtraInfo(),
                        new TypeToken<Object>() {
                        }.getType());
                pageListVO.setExtraInfo(extraInfo);
            }
            return pageListVO;
        });
    }

    public CreditCardBankDetailVO getDetail(Integer id) {
        CreditCardBankEntity entity = creditCardBankService.getById(id);
        if (ObjectUtil.isNull(entity)) {
            ToolsHelper.throwException("未找到该数据，请确认后重新提交");
        }
        CreditCardBankDetailVO detailVO = new CreditCardBankDetailVO();
        BeanUtils.copyProperties(entity, detailVO);
        // welfareDetail 字段是数组，不能直接复制，需要处理
        // 把welfareDetail 转成json 对象
        Gson gson = new Gson();
        List<Object> list = gson.fromJson(
                entity.getWelfareDetail(),
                new TypeToken<List<Object>>() {
                }.getType()
        );
        detailVO.setWelfareDetail(list);

        if (ObjectUtil.isNotEmpty(entity.getTagList())) {
            List<Object> tagList = gson.fromJson(entity.getTagList(),
                    new TypeToken<List<Object>>() {
                    }.getType());
            detailVO.setTagList(tagList);
        }

        if (ObjectUtil.isNotEmpty(entity.getExtraInfo())) {
            Object extraInfo = gson.fromJson(entity.getExtraInfo(),
                    new TypeToken<Object>() {
                    }.getType());
            detailVO.setExtraInfo(extraInfo);
        }
        return detailVO;
    }

    public void modify(CreditCardBankModifyDTO modifyDTO) {
        CreditCardBankEntity entity = creditCardBankService.getById(modifyDTO.getId());
        if (ObjectUtil.isNull(entity)) {
            ToolsHelper.throwException("未找到该数据，请确认后重新提交");
        }
        BeanUtils.copyProperties(modifyDTO, entity);
        entity.setOperator(RequestHelper.getAdminOperator());
        entity.setWelfareDetail(JSON.toJSON(modifyDTO.getWelfareDetail()).toString());
        entity.setTagList(JSON.toJSON(modifyDTO.getTagList()).toString());
        entity.setExtraInfo(JSON.toJSON(modifyDTO.getExtraInfo()).toString());
        entity.setUpdatedAt(LocalDateTime.now());
        creditCardBankService.updateById(entity);
    }

    public void setStatus(CreditCardBankSetStatusDTO setStatusDTO) {
        CreditCardBankEntity entity = creditCardBankService.getById(setStatusDTO.getId());
        if (ObjectUtil.isNull(entity)) {
            ToolsHelper.throwException("未找到该数据，请确认后重新提交");
        }
        if (entity.getBankStatus().equals(setStatusDTO.getBankStatus())) {
            return;
        }
        entity.setBankStatus(setStatusDTO.getBankStatus());
        entity.setOperator(RequestHelper.getAdminOperator());
        entity.setUpdatedAt(LocalDateTime.now());
        creditCardBankService.updateById(entity);
    }

    public HashMap<String, Object> getSelectOptions() {
        HashMap<String, Object> options = new HashMap<>();
        options.put("bankType", configBizFieldValuesService.getListByBizFieldKey("credit_card_bank_type", false));
        options.put("bankStatus", configBizFieldValuesService.getListByBizFieldKey("credit_card_bank_status", false));
        return options;
    }

    public void updateSort(CreditCardBankUpdateSortDTO dto) {
        CreditCardBankEntity entity = creditCardBankService.getById(dto.getId());
        if (ObjectUtil.isNull(entity)) {
            ToolsHelper.throwException("未找到该数据，请确认后重新提交");
        }
        if (entity.getSort().equals(dto.getSort())) {
            return;
        }
        entity.setSort(dto.getSort());
        entity.setOperator(RequestHelper.getAdminOperator());
        entity.setUpdatedAt(LocalDateTime.now());
        creditCardBankService.updateById(entity);
    }

    public List<CreditCardBankInfoListVO> getCreditCardBankInfoList(CreditCardBankListInfoDTO dto) {

        // 获取可申办银行配置
        List<CreditCardBankConfigEntity> bankConfigList =
                creditCardBankConfigService.getListByBankStatus(CreditCardBankConfigBankStatusEnum.NORMAL.getStatus());
        if (CollectionUtil.isEmpty(bankConfigList)) {
            return new ArrayList<>();
        }


        List<CreditCardBankEntity> list = creditCardBankService.getListByBankType(dto.getBankType());
        if (CollectionUtil.isEmpty(list)) {
            return new ArrayList<>();
        }

        return list.stream()
                // 过滤掉不可申请银行
                .filter(entity -> bankConfigList.stream()
                        .anyMatch(config -> config.getBankId().equals(entity.getBankId())))
                .map(entity -> {
                    CreditCardBankInfoListVO vo = new CreditCardBankInfoListVO();
                    BeanUtils.copyProperties(entity, vo);
                    vo.setTagList(JSON.parseArray(entity.getTagList()));
                    vo.setWelfareDetail(JSON.parseArray(entity.getWelfareDetail()));
                    vo.setExtraInfo(JSON.parseObject(entity.getExtraInfo()));
                    return vo;
                }).collect(Collectors.toList());
    }
}
