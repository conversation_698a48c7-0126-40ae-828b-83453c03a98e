package com.ets.apply.application;
import com.ets.apply.application.app.service.thirdPartner.SzUnicomZopService;
import com.ets.apply.application.common.dto.request.szUnicomZop.*;
import com.ets.apply.application.common.vo.szUnicomZop.SzUnicomZopSelectNumVo;
import com.ets.common.ToolsHelper;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

@SpringBootTest(classes = ApplyApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringRunner.class)
@ActiveProfiles(value = "dev")
@Slf4j
public class UnicomTest {
    private  String  appkey ="B8A0518FB6AA4EA1AA889146E5D236F5";
    private  String  secret ="DT0SHBXqL7H8O544";


    @Autowired
    private SzUnicomZopService szUnicomZopService;

    @Test
    public void sign() {
        try {
            Map<String,String> macMap = new HashMap<>();
            //macMap.put("certName","吴延华");
            //macMap.put("certNum","371523199206055312");
            macMap.put("goodsId","982203315714");
            macMap.put("channel"," 09-2279-bmkj-9999");
            macMap.put("secret",this.secret);
            macMap.put("appKey",this.appkey);
            Set<String> keySet = macMap.keySet();
            String[] keyArray = keySet.toArray(new String[0]);
            Arrays.sort(keyArray);
            StringBuilder sb = new StringBuilder();
            for(int i =0; i<keyArray.length;i++){
                if(String.valueOf(macMap.get(keyArray[i])).length()>0){
                    String values = URLEncoder.encode(macMap.get(keyArray[i]),"UTF-8");
                    if((i+1) == keyArray.length ){
                        sb.append(keyArray[i]).append("=").append(values);
                    }else{
                        sb.append(keyArray[i]).append("=").append(values).append("&");
                    }

                }
            }
          //  System.out.println(sb);
            //System.out.println(sb.toString());
            System.out.println(getSHA256String(sb.toString()).toString());

        } catch (Exception e) {
            log.info("CreditCardSpd 光大银行申请异常：" + e.getMessage());
            ToolsHelper.throwException("当前办理方式不可用，请稍后再试");
        }

    }
    private static String byteArrayToHexString(byte[] b) {
        StringBuilder hs = new StringBuilder();
        String stmp;
        for (int n = 0; b != null && n < b.length; n++) {
            stmp = Integer.toHexString(b[n] & 0XFF);
            if (stmp.length() == 1)
                hs.append('0');
            hs.append(stmp);
        }
        return hs.toString().toUpperCase();
    }

    private static byte[] calculateHmacSHA256(String message, String secretKey) throws NoSuchAlgorithmException, InvalidKeyException, UnsupportedEncodingException {
        Mac mac = Mac.getInstance("HmacSHA256");
        SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey.getBytes("UTF-8"), "HmacSHA256");
        mac.init(secretKeySpec);
        return mac.doFinal(message.getBytes("UTF-8"));
    }
    private static String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }



    /*
     *  排序并且构造签名字符串
     */
    private static String ksortAndBuild(Map<String,String> macMap) throws UnsupportedEncodingException{
        Set<String> keySet = macMap.keySet();
        String[] keyArray = keySet.toArray(new String[0]);
        Arrays.sort(keyArray);
        StringBuilder sb = new StringBuilder();
        for(int i =0; i<keyArray.length;i++){
            if(String.valueOf(macMap.get(keyArray[i])).length()>0){
                String values = URLEncoder.encode(macMap.get(keyArray[i]),"UTF-8");
                if((i+1) == keyArray.length ){
                    sb.append(keyArray[i]).append("=").append(values);
                }else{
                    sb.append(keyArray[i]).append("=").append(values).append("&");
                }

            }
        }
        return sb.toString();
    }



    public static String getSHA256String(String str) throws NoSuchAlgorithmException, UnsupportedEncodingException {
        MessageDigest messageDigest;
        messageDigest = MessageDigest.getInstance("SHA-256");
        messageDigest.update(str.getBytes("UTF-8"));
        String encodeStr = byteArrayToHexString(messageDigest.digest());
        return encodeStr;
    }
    @Test
    public void checkUser() {
        try {
            SzUnicomZopCheckUserDTO dto = new SzUnicomZopCheckUserDTO();
            dto.setCertName("吴延华");
            dto.setCertNum("371523199206055312");
            dto.setProvinceCode("440000");
            dto.setCityCode("440300");
            System.out.println(szUnicomZopService.checkUser(dto).toString());

        } catch (Exception e) {
            log.info("checkUser ：" + e.getMessage());
            ToolsHelper.throwException("checkUser");
        }

    }

    @Test
    public void selectNum() {
        try {
            SzUnicomZopSelectNumDTO dto = new SzUnicomZopSelectNumDTO();
            dto.setProvinceCode("440000");
            dto.setCityCode("440300");
            dto.setSearchCategory("3");
            dto.setSearchType("02");
            dto.setSearchValue("6864");
            System.out.println(szUnicomZopService.selectNum(dto).toString());

        } catch (Exception e) {
            log.info("checkUser ：" + e.getMessage());
            ToolsHelper.throwException("selectNum");
        }

    }
    @Test
    public void safeCode() {
        try {
            SzUnicomZopSafeCodeDTO dto = new SzUnicomZopSafeCodeDTO();
            dto.setCertNo("371523199206055312");
            dto.setContactNum("15918514124");
            System.out.println(szUnicomZopService.safeCode(dto).toString());

        } catch (Exception e) {
            log.info("checkUser ：" + e.getMessage());
            ToolsHelper.throwException("selectNum");
        }

    }

    @Test
    public void checkCode() {
        try {
            SzUnicomZopCheckCodeDTO dto = new SzUnicomZopCheckCodeDTO();
            dto.setCertNo("371523199206055312");
            dto.setContactNum("15918514124");
            dto.setSafeCode("140690");
            System.out.println(szUnicomZopService.checkCode(dto).toString());

        } catch (Exception e) {
            log.info("checkUser ：" + e.getMessage());
            ToolsHelper.throwException("selectNum");
        }

    }
    @Test
    public void checkRisk() {
        try {
            SzUnicomZopCheckRiskDTO dto = new SzUnicomZopCheckRiskDTO();
            dto.setCertName("吴延华");
            dto.setCertNo("371523199206055312");
            dto.setProvinceCode("440000");
            dto.setCityCode("440300");
            dto.setContactNum("15918514124");
            dto.setPostCityCode("440300");
            dto.setPostProvinceCode("440000");
            dto.setPostDistrictCode("440303");
            System.out.println(szUnicomZopService.checkRisk(dto).toString());

        } catch (Exception e) {
            log.info("checkUser ：" + e.getMessage());
            ToolsHelper.throwException("selectNum");
        }

    }
}
