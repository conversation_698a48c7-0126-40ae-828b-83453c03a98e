package com.ets.apply.application.service;

import java.io.File;
import java.nio.file.Path;
import java.nio.file.Paths;

import com.ets.apply.application.common.cos.CosService;
import org.junit.Test;
import org.junit.Assert;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import lombok.extern.slf4j.Slf4j;

import com.ets.apply.application.common.config.CosConfig;

@Slf4j
@SpringBootTest
@RunWith(value = SpringRunner.class)
public class CosServiceTest {
    @Autowired
    private CosService cosService;

    @Autowired
    private CosConfig cosConfig;

    @Test
    public void upload() {
        log.info("=========================== cos config: {}====================", cosConfig);
        File file = new File("/tmp/car_list.xlsx");
        Path cosPath = Paths.get("/dev/taxi/import", "20210728", "car_list.xlsx");
        String fileurl = cosService.upload(file, cosPath.toString());
        Assert.assertEquals(cosConfig.getBaseUrl() + cosPath, fileurl);
    }

    @Test
    public void download() {
        File localFile = new File("/tmp/car_list_download.xlsx");
        Path cosPath = Paths.get("/dev/taxi/import", "20210728", "car_list.xlsx");
        cosService.getObjectToFile(cosConfig.getBucket(), cosPath.toString(), localFile);
        Assert.assertNotEquals(0, localFile.length());
    }
}
