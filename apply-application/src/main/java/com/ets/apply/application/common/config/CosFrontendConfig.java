package com.ets.apply.application.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.List;


@RefreshScope
@Configuration
@ConfigurationProperties(
        prefix = "params.cos.tencent.frontend",
        ignoreInvalidFields = true
)
@Data
public class CosFrontendConfig {

    //腾讯云的SecretId
    private String secretId;
    //腾讯云的SecretKey
    private String secretKey;
    //腾讯云的bucket (存储桶)
    private String bucket;
    //腾讯云的region(bucket所在地区)
    private String region;
    //腾讯云的allowPrefix(允许上传的路径)
    private String allowPrefix;
    //腾讯云的临时密钥时长(单位秒)
    private String durationSeconds;
    //腾讯云的访问基础链接:
    private String baseUrl;
    //腾讯云上传后的读取地址(CDN加速)
    private String readUrl;
    //根目录
    private String rootDir;
    //视频根目录
    private String rootVideoDir;
    //视频格式
    private List<String> videoFormat;

}
