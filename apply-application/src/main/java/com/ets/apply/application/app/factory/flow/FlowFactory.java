package com.ets.apply.application.app.factory.flow;

import cn.hutool.extra.spring.SpringUtil;
import com.ets.apply.application.app.factory.flow.impl.FlowBase;
import com.ets.apply.application.app.factory.flow.impl.NormalFlow;
import com.ets.apply.application.app.factory.flow.impl.TaxiCompanyFlow;
import com.ets.apply.application.common.consts.order.FlowTypeEnum;
import com.ets.common.ToolsHelper;

public class FlowFactory {
    public static FlowBase create(Integer flowType) {
        FlowBase flowBase = null;

        switch (FlowTypeEnum.getNodeByFLowType(flowType)) {
            case FLOW_NORMAL:
                flowBase = SpringUtil.getBean(NormalFlow.class);
                break;
            case FLOW_TAXI_COMPANY:
                flowBase = SpringUtil.getBean(TaxiCompanyFlow.class);
                break;
            default:
                ToolsHelper.throwException("当前信用卡不支持操作");
        }
        return flowBase;
    }

}
