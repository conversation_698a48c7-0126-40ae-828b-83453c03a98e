package com.ets.apply.application.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.apply.application.app.business.ProductManualBusiness;
import com.ets.apply.application.common.dto.productManual.ProductManualAddDTO;
import com.ets.apply.application.common.dto.productManual.ProductManualEditDTO;
import com.ets.apply.application.common.dto.productManual.ProductManualListDTO;
import com.ets.apply.application.common.vo.ProductManualListVO;
import com.ets.common.JsonResult;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/admin/product-manual")
public class AdminProductManualController {

    @Autowired
    private ProductManualBusiness productManualBusiness;

    @PostMapping("/get-list")
    public JsonResult<IPage<ProductManualListVO>> getList(@RequestBody @Valid ProductManualListDTO listDTO) {
        return JsonResult.ok(productManualBusiness.getList(listDTO));
    }

    @PostMapping("/add")
    public JsonResult<?> add(@RequestBody @Valid ProductManualAddDTO addDTO) {
        productManualBusiness.add(addDTO);
        return JsonResult.ok();
    }

    @PostMapping("/edit")
    public JsonResult<?> edit(@RequestBody @Valid ProductManualEditDTO editDTO) {
        productManualBusiness.edit(editDTO);
        return JsonResult.ok();
    }
}
