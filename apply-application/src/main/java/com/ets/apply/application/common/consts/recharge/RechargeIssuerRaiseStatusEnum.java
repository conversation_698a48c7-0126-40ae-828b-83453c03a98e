package com.ets.apply.application.common.consts.recharge;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
@AllArgsConstructor
public enum RechargeIssuerRaiseStatusEnum {
    DEFAULT(0, "未充值"),
    PAID(1, "已充值"),
    FAILED(2, "充值失败"),
    CANCELING(50, "撤销中"),
    CANCELED(60, "撤销完成");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;

    static {
        RechargeIssuerRaiseStatusEnum[] enums = RechargeIssuerRaiseStatusEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
