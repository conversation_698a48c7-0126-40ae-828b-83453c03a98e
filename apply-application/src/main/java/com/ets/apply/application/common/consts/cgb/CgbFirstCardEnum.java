package com.ets.apply.application.common.consts.cgb;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CgbFirstCardEnum {
    //申请一二卡标识：1.一卡2.二卡 3.短表单
    //可用1表示新户（仅表示申请时判定客户的进件状态，新户具体以新户标志为准）
    FIRST_CARD("Y", "一卡"),
    SECOND_CARD("N", "二卡");


    private final String code;
    private final String description;

    public static String getDescByCode(String code) {
        for (CgbFirstCardEnum node : CgbFirstCardEnum.values()) {
            if (node.getCode().equals(code)) {
                return node.getDescription();
            }
        }

        return "";
    }

}
