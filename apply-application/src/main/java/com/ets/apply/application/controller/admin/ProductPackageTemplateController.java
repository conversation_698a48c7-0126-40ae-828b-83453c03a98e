package com.ets.apply.application.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.apply.application.app.business.admin.AdminProductPackageTemplateBusiness;
import com.ets.apply.application.common.dto.adminProductPackageTemplate.*;
import com.ets.apply.application.common.vo.admin.productPackageTemplate.AdminProductPackageTemplateListVO;
import com.ets.apply.application.controller.BaseController;
import com.ets.common.JsonResult;
import com.ets.common.RequestHelper;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;


@Controller
@RequestMapping("/admin/productPackageTemplate")
public class ProductPackageTemplateController extends BaseController {
    @Autowired
    private AdminProductPackageTemplateBusiness productPackageTemplateBusiness;

    /**
     * 营销列表查询
     * @return
     */
    @RequestMapping("/getList")
    @ResponseBody
    public JsonResult<IPage<AdminProductPackageTemplateListVO>> getList(@RequestBody(required = false) @Valid AdminProductPackageTemplateGetListDTO dto) {
        return JsonResult.ok(productPackageTemplateBusiness.getList(dto));
    }


    /**
     *  新增计划
     * @return
     */
    @RequestMapping("/add")
    @ResponseBody
    public JsonResult<Boolean> add(@RequestBody(required = false) @Valid AdminProductPackageTemplateAddDTO dto) {
        productPackageTemplateBusiness.add(dto, RequestHelper.getHttpServletRequest().getHeader("loginCode"));
        return JsonResult.ok(true);
    }

    /**
     *  修改产品包
     * @return
     */
    @RequestMapping("/modify")
    @ResponseBody
    public JsonResult<Boolean> modify(@RequestBody(required = false) @Valid AdminProductPackageTemplateModifyDTO dto) {
        productPackageTemplateBusiness.modify(dto, RequestHelper.getHttpServletRequest().getHeader("loginCode"));
        return JsonResult.ok(true);
    }

    /**
     *  上下架计划
     * @return
     */
    @RequestMapping("/upOrDown")
    @ResponseBody
    public JsonResult<Boolean> upOrDown(@RequestBody(required = false) @Valid AdminProductPackageTemplateUpDownDTO dto) {
        productPackageTemplateBusiness.upOrDown(dto, RequestHelper.getHttpServletRequest().getHeader("loginCode"));
        return JsonResult.ok(true);
    }

    /**
     *  新增计划
     * @return
     */
    @RequestMapping("/getParamsById")
    @ResponseBody
    public JsonResult<Object> getParamsById(@RequestBody(required = false) @Valid AdminProductPackageTemplateGetInfoDTO dto) {
        return JsonResult.ok(productPackageTemplateBusiness.getParamsById(dto.getId()));
    }

}
