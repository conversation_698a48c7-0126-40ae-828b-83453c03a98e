package com.ets.apply.application.common.dto;

import lombok.Data;
import jakarta.validation.constraints.NotNull;

@Data
public class ApplyGuideResultItemDTO {
    /**
     * 地图类型
     */
    @NotNull(message = "地图类型不能为空")
    private Integer mapType;

    /**
     * 问题组合
     */
    @NotNull(message = "问题组合不能为空")
    private String questionMapGroup;

    /**
     * 
     */
    private String questionMapGroupAll;
}