package com.ets.apply.application.infra.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.apply.application.common.consts.deviceValuation.ValuationStatusEnum;
import com.ets.apply.application.infra.entity.CardsEntity;
import com.ets.apply.application.infra.entity.DeviceValuationEntity;
import com.ets.apply.application.infra.mapper.DeviceValuationMapper;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 设备估价 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Service
public class DeviceValuationService extends BaseService<DeviceValuationMapper, DeviceValuationEntity> {

    public DeviceValuationEntity getLastByUid(Long uid) {
        LambdaQueryWrapper<DeviceValuationEntity> wrapper = Wrappers.<DeviceValuationEntity>lambdaQuery()
                .select()
                .eq(DeviceValuationEntity::getUid, uid)
                .eq(DeviceValuationEntity::getValuationStatus, ValuationStatusEnum.NORMAL.getStatus())
                .orderByDesc(DeviceValuationEntity::getUpdatedAt)
                ;
        return this.getOneByWrapper(wrapper);
    }

    /**
     * 获取50 个非无效的设备估价列表
     * @param startTime
     * @param endTime
     * @return
     */
    public List<DeviceValuationEntity> getValidListByCreatedAt(LocalDateTime startTime, LocalDateTime endTime) {
        LambdaQueryWrapper<DeviceValuationEntity> wrapper = Wrappers.<DeviceValuationEntity>lambdaQuery()
                .select()
                .in(DeviceValuationEntity::getValuationStatus, Arrays.asList(ValuationStatusEnum.NORMAL.getStatus(),ValuationStatusEnum.USED.getStatus()))
                .between(DeviceValuationEntity::getCreatedAt, startTime, endTime)
                .orderByDesc(DeviceValuationEntity::getCreatedAt)
                .last("limit 50")
                ;
        return this.getListByWrapper(wrapper);
    }

    public DeviceValuationEntity getOneValidByUidAndPlateNo(Long uid, String plateNo, Integer plateColor) {
        LambdaQueryWrapper<DeviceValuationEntity> wrapper = Wrappers.<DeviceValuationEntity>lambdaQuery()
                .select()
                .eq(DeviceValuationEntity::getUid, uid)
                .eq(DeviceValuationEntity::getPlateNo, plateNo)
                .eq(DeviceValuationEntity::getPlateColor, plateColor)
                .eq(DeviceValuationEntity::getValuationStatus, ValuationStatusEnum.NORMAL.getStatus())
                .orderByDesc(DeviceValuationEntity::getCreatedAt)
                .last("1");
        return this.getOneByWrapper(wrapper);

    }

    /**
     * 绑定关联单号
     * @param uid
     * @param valuationSn
     * @param referSn
     */
    public void bindReferSn(Long uid, String valuationSn, String referSn) {
        LambdaUpdateWrapper<DeviceValuationEntity> updateWrapper = Wrappers.<DeviceValuationEntity>lambdaUpdate()
                .set(DeviceValuationEntity::getReferSn, referSn)
                .eq(DeviceValuationEntity::getUid, uid)
                .eq(DeviceValuationEntity::getValuationSn, valuationSn);
        updateByWrapper(updateWrapper);
    }

    /**
     * 使用
     * @param uid
     * @param valuationSn
     * @param referSn
     */
    public void use(Long uid, String valuationSn, String referSn) {
        LambdaUpdateWrapper<DeviceValuationEntity> updateWrapper = Wrappers.<DeviceValuationEntity>lambdaUpdate()
                .eq(DeviceValuationEntity::getUid, uid)
                .eq(DeviceValuationEntity::getValuationSn, valuationSn)
                .set(DeviceValuationEntity::getReferSn, referSn)
                .set(DeviceValuationEntity::getValuationStatus, ValuationStatusEnum.USED.getStatus())
                .set(DeviceValuationEntity::getBindTime, LocalDateTime.now())
               ;
        updateByWrapper(updateWrapper);
    }
}
