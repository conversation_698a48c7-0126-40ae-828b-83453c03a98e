package com.ets.apply.application.controller.third;

import com.ets.apply.application.app.business.external.ExternalOrderBusiness;
import com.ets.apply.application.common.dto.external.ExternalOrderAfterSaleCreateDTO;
import com.ets.apply.application.common.dto.external.ExternalOrderCancelDTO;
import com.ets.apply.application.common.dto.external.ExternalOrderCreateDTO;
import com.ets.common.JsonResult;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RequestMapping("/third/externalOrder")
@RestController
@Slf4j
public class ThirdExternalOrderController {

    @Autowired
    private ExternalOrderBusiness externalOrderBusiness;

    /*
    @PostMapping("/create")
    public JsonResult<Object> create(@RequestBody @Valid ExternalOrderCreateDTO dto, HttpServletRequest request) {

        return JsonResult.ok(externalOrderBusiness.create(dto, request.getHeader("x-external-name")));
    }

    @PostMapping("/cancel")
    public JsonResult<Object> cancel(@RequestBody @Valid ExternalOrderCancelDTO dto, HttpServletRequest request) {

        externalOrderBusiness.cancel(dto, request.getHeader("x-external-name"));

        return JsonResult.ok();
    }

    @PostMapping("/afterSaleCreate")
    public JsonResult<Object> afterSaleCreate(@RequestBody @Valid ExternalOrderAfterSaleCreateDTO dto, HttpServletRequest request) {

        externalOrderBusiness.afterSaleCreate(dto, request.getHeader("x-external-name"));

        return JsonResult.ok();
    }*/
}
