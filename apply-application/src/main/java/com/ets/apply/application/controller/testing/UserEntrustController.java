package com.ets.apply.application.controller.testing;

import com.ets.apply.application.controller.BaseController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;

/**
 * <p>
 * 免密支付协议内容表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-17
 */
@RequestMapping("/user-entrust")
@RefreshScope
@RestController
@Slf4j
public class UserEntrustController extends BaseController {

    @Resource(name = "defaultRedisTemplate")
    private StringRedisTemplate defaultRedisTemplate;

    @Resource(name = "redisMinorTemplate")
    private StringRedisTemplate redisMinorTemplate;

    @RequestMapping("/echo")
    public String echo(@RequestParam(value = "arg", required = false) String arg) {

        if (arg == null) {
            return "arg 参数不能为空！";
        }

        defaultRedisTemplate.opsForHash().put("hashKey", "subKey", " redis");
        Object cacheValue = defaultRedisTemplate.opsForHash().get("hashKey", "subKey");

        redisMinorTemplate.opsForHash().put("hashKey", "subKey", " minor");
        Object cacheValueMinor = redisMinorTemplate.opsForHash().get("hashKey", "subKey");

        log.error("error test");

        return "provider controller:" + arg + cacheValue + cacheValueMinor;
    }

}

