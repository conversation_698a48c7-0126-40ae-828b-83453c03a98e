package com.ets.apply.application.common.utils.bank.spd;

import java.nio.charset.StandardCharsets;
import java.util.Base64;

public class EncryptTools {

    private EncryptTools() {
    }

    public static String encrypt(String data, String key) {
        byte[] encryptBytes = EncryptUtil.SM4Util.encryptCBC(data.getBytes(), key.getBytes(), key.getBytes());
        return Base64.getEncoder().encodeToString(encryptBytes);
    }

    public static String decrypt(String encryptedData, String key) {
        byte[] decryptBytes = EncryptUtil.SM4Util.decryptCBC(
                Base64.getDecoder().decode(encryptedData), key.getBytes(), key.getBytes()
        );
        return new String(decryptBytes, StandardCharsets.UTF_8);
    }

    public static String sign(String encryptedData, String publicKey) throws Exception {
        return EncryptUtil.SM2Util.signData(encryptedData, publicKey);
    }

    public static boolean validate(String encryptedData, String signature, String privateKey) throws Exception {
        return EncryptUtil.SM2Util.verifySign(encryptedData, signature, privateKey);
    }
}
