package com.ets.apply.application.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.apply.application.app.business.ChannelProductOrderBusiness;
import com.ets.apply.application.common.dto.request.channelProductOrder.ChannelProductFrontendDTO;
import com.ets.apply.application.common.dto.request.channelProductOrder.ChannelProductOrderListDTO;
import com.ets.apply.application.common.dto.request.channelProductOrder.ChannelProductPrepayDTO;
import com.ets.apply.application.common.dto.request.channelProductOrder.ChannelProductRefundDTO;
import com.ets.apply.application.common.vo.channelProductOrder.ChannelProductOrderListVO;
import com.ets.common.JsonResult;
import com.ets.common.ToolsHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

@RequestMapping("/productOrder")
@RestController
@Slf4j
public class ChannelProductOrderController extends BaseController {


    @Autowired
    private ChannelProductOrderBusiness channelProductOrderBusiness;

    @RequestMapping("/getChannelOrderList")
    @ResponseBody
    public JsonResult<IPage<ChannelProductOrderListVO>> getChannelOrderList(@RequestBody(required = false) @Valid ChannelProductOrderListDTO request) {

        if (request == null) {
            ToolsHelper.throwException("请传入JSON格式的参数");
        }
        if (request.getChannelId() == null) {
            ToolsHelper.throwException("绑定渠道必传");
        }

        return JsonResult.ok(channelProductOrderBusiness.getChannelProductOrderList(request));
    }

    @PostMapping("/refundChannelOrder")
    @ResponseBody
    public JsonResult<Object> refund(@RequestBody(required = false) @Valid ChannelProductFrontendDTO request) {
        ChannelProductRefundDTO channelProductRefundDTO = new ChannelProductRefundDTO();
        channelProductRefundDTO.setProductOrderSn(request.getProductOrderSn());
        channelProductRefundDTO.setOperator(request.getOperator());
        channelProductRefundDTO.setReason("渠道操作");
        channelProductOrderBusiness.refund(channelProductRefundDTO);
        return JsonResult.ok();
    }

    @PostMapping("/prepay")
    @ResponseBody
    public JsonResult<Object> prepay(@RequestBody(required = false) @Valid ChannelProductPrepayDTO request) {

        return JsonResult.ok(channelProductOrderBusiness.prepay(request));
    }






}
