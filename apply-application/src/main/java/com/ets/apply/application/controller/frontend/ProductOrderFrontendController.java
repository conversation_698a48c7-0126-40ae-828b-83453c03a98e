package com.ets.apply.application.controller.frontend;

import cn.hutool.core.util.ObjectUtil;
import com.ets.apply.application.app.business.ProductOrderBusiness;
import com.ets.apply.application.common.dto.productOrder.ProductOrderCancelDTO;
import com.ets.apply.application.common.dto.productOrder.ProductOrderChooseBankDTO;
import com.ets.apply.application.common.dto.request.productOrder.PrePayDTO;
import com.ets.apply.application.common.dto.request.productOrder.ThirdOrderPrePayDTO;
import com.ets.apply.application.common.utils.UserUtil;
import com.ets.common.JsonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

@RestController
@RequestMapping("/frontend/product-order")
public class ProductOrderFrontendController {

    @Autowired
    private ProductOrderBusiness productOrderBusiness;
    @RequestMapping("/cancel")

    public JsonResult<?> cancel(@RequestBody @Valid ProductOrderCancelDTO cancelDTO) {

        productOrderBusiness.cancelBeforeShipped(cancelDTO, UserUtil.getUid());
        return JsonResult.ok();
    }

    /**
     * 选择银行
     */
    @RequestMapping("/choose-bank")
    public JsonResult<?> chooseBank(@RequestBody @Valid ProductOrderChooseBankDTO chooseBankDTO){
        productOrderBusiness.chooseBank(UserUtil.getUid(),chooseBankDTO);
        return JsonResult.ok();
    }

    @PostMapping("/prepay")
    @ResponseBody
    public JsonResult<Object> prepay(@RequestBody(required = false) @Valid PrePayDTO prePayDTO) {

        ThirdOrderPrePayDTO request = new ThirdOrderPrePayDTO();
        request.setCompanyId(prePayDTO.getCompanyId());
        // 支付域约定，无openid 时，使用uid代替
        request.setOpenId(ObjectUtil.isEmpty(prePayDTO.getOpenId()) ? UserUtil.getUid().toString() :
                prePayDTO.getOpenId());
        request.setSpOrderId(prePayDTO.getProductOrderSn());
        return JsonResult.ok(productOrderBusiness.prepay(request));
    }

}
