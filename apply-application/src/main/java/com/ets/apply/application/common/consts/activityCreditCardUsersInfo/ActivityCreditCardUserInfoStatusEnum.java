package com.ets.apply.application.common.consts.activityCreditCardUsersInfo;

import com.ets.apply.application.common.consts.creditCard.ApplyStatusVoEnum;
import com.ets.common.BizException;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ActivityCreditCardUserInfoStatusEnum {


    STATUS_CANCEL(0, "无需审核"),
    STATUS_NOT_AUDIT(1, "未审核完成"),
    STATUS_FAIL_AUDIT(2, "审核不通过"),
    STATUS_PASS_AUDIT(3, "审核通过，暂未发放"),
    STATUS_GRANT(4, "已发放，未领取"),
    STATUS_GOT(5, "已领取"),
    STATUS_REFUND(6, "已退还（只有下返还设备费情况下）"),
    STATUS_BACK_RED(7, "红包未领取，已退回"),
    STATUS_GET_COUPON(8, "已发券"),
    STATUS_QUALIFICATION_FINISHED(9, "资格已完结（订单已激活）"),
    STATUS_ACTIVATE_BU_NOT_NEW_USER(10, "信用卡激活（非新户）"),
    STATUS_CREDIT_CARD_ACTIVATE(11, "信用卡激活(新户)"),
    STATUS_WAIT(50, "待定状态");

    private final Integer code;
    private final String description;

    public static String getDescByCode(int code) {
        for (ActivityCreditCardUserInfoStatusEnum node : ActivityCreditCardUserInfoStatusEnum.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }

    /**
     * 通过状态获取对应常量
     *
     * @param status
     * @return
     */
    public static ActivityCreditCardUserInfoStatusEnum of(Integer status) {
        for (ActivityCreditCardUserInfoStatusEnum anEnum : ActivityCreditCardUserInfoStatusEnum.values()) {
            if (status.equals(anEnum.getCode())) {
                return anEnum;
            }
        }
        throw new BizException("尚未匹配到信用卡状态:" + status);
    }

    public ApplyStatusVoEnum toApplyStatusVoEnum() {
        switch (this) {
            case STATUS_WAIT:
                return ApplyStatusVoEnum.STATUS_UNKNOWN;
            case STATUS_CANCEL:
                return ApplyStatusVoEnum.NO_APPLY;
            case STATUS_FAIL_AUDIT:
                return ApplyStatusVoEnum.STATUS_FAIL_AUDIT;
            case STATUS_CREDIT_CARD_ACTIVATE:
                return ApplyStatusVoEnum.STATUS_CREDIT_CARD_ACTIVATE;
            case STATUS_ACTIVATE_BU_NOT_NEW_USER:
                return ApplyStatusVoEnum.STATUS_ACTIVATE_BU_NOT_NEW_USER;
            case STATUS_PASS_AUDIT:
                return ApplyStatusVoEnum.STATUS_PASS_AUDIT;
            case STATUS_NOT_AUDIT:
                return ApplyStatusVoEnum.STATUS_NOT_AUDIT;
            case STATUS_QUALIFICATION_FINISHED:
            case STATUS_GRANT:
            case STATUS_GOT:
            case STATUS_REFUND:
            case STATUS_BACK_RED:
            case STATUS_GET_COUPON:
                return ApplyStatusVoEnum.QUALIFICATION_CONSUMED;
            default:
                throw new BizException("未匹配到对应状态:" + this.getCode());
        }
    }

}
