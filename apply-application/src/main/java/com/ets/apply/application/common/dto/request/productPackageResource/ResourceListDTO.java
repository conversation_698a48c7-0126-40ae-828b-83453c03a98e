package com.ets.apply.application.common.dto.request.productPackageResource;

import lombok.Data;

import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.Min;


@Data
public class ResourceListDTO {

    @Min(1)
    private Integer pageNum = 1;

    @Min(1)
    private Integer pageSize = 20;
    private String resourceSn;

    private String resourceName;

    /**
     * 资源分类[0-无类别 1-发行 2-增购]
     */
    @Digits(integer = 3, fraction = 0, message = "请选择正确的分类：选项不能超过1000")
    private Integer resourceType;

    /**
     * 状态
     */
    private Integer resourceStatus;

}
