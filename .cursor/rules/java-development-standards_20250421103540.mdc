---
description: 
globs: 
alwaysApply: false
---
# Java 开发规范指南

## API 设计规范

### 接口设计原则
1. 不使用 Swagger API 文档注解
2. 请求方式选择：
   - POST 方式：适用于带有请求参数的接口，使用 `@RequestBody` 传参
   - GET 方式：仅适用于少量单一 ID 参数的数据获取接口，使用 `@RequestParams` 传参

### 命名规范
1. 接口路由：使用连字符（-）方式命名，禁止使用驼峰
   - 正确示例：`/api/user-profile`
   - 错误示例：`/api/userProfile`

2. DTO 命名：
   - 以 DTO 结尾
   - 存放位置：`common/dto` 目录下
   - 可按业务类型分类存放

3. VO 命名：
   - 以 VO 结尾
   - 存放位置：`common/vo` 目录下
   - 可按业务类型分类存放

### 接口返回值规范
1. 统一使用 `JsonResult<?>` 作为返回值包装类
2. 返回值结构：
```java
@Data
public class JsonResult<T> {
    private Integer code;    // 状态码：0-成功，-1-失败
    private String msg;      // 提示信息
    private T data;         // 数据
    
    // 成功静态方法
    public static <T> JsonResult<T> success(T data) {
        JsonResult<T> result = new JsonResult<>();
        result.setCode(0);
        result.setMsg("success");
        result.setData(data);
        return result;
    }
    
    // 成功静态方法 - 无数据
    public static <T> JsonResult<T> success() {
        return success(null);
    }
    
    // 失败静态方法
    public static <T> JsonResult<T> error(String msg) {
        JsonResult<T> result = new JsonResult<>();
        result.setCode(-1);
        result.setMsg(msg);
        return result;
    }
}
```

### 异常处理规范
1. 业务异常抛出规范：
   - 使用 `ToolsHelper.throwException()` 方法抛出业务异常
   - 异常信息应清晰明确，便于前端展示
   - 示例：
```java
// 抛出业务异常
if (condition) {
    ToolsHelper.throwException("用户名已存在");
}
```

2. 全局异常处理：
```java
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    @ExceptionHandler(BusinessException.class)
    public JsonResult<?> handleBusinessException(BusinessException e) {
        return JsonResult.error(e.getMessage());
    }
    
    @ExceptionHandler(Exception.class)
    public JsonResult<?> handleException(Exception e) {
        log.error("系统异常", e);
        return JsonResult.error("系统异常，请稍后重试");
    }
}
```

### 实体类规范
1. 注解使用：
   - 使用 `@Data` 注解自动生成 getter/setter/toString 等方法
   - 必要时可配合使用其他 Lombok 注解
   - 示例：
```java
@Data
public class UserDO {
    private Long id;
    private String username;
    private String password;
    private String email;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
}

@Data
public class UserDTO {
    @NotBlank(message = "用户名不能为空")
    private String username;
    
    @NotBlank(message = "密码不能为空")
    private String password;
    
    @Email(message = "邮箱格式不正确")
    private String email;
}

@Data
public class UserVO {
    private Long id;
    private String username;
    private String email;
    private String roleName;
}
```

## 开发规范

### 分层架构规范
1. Controller 层职责：
   - 仅负责参数接收和结果返回
   - 参数校验（可使用 @Valid 注解）
   - 不允许编写业务逻辑
   - 调用 Business 层处理业务

2. Business 层职责：
   - 存放具体的业务逻辑处理代码
   - 可以调用多个 Service 完成业务处理
   - 处理业务异常
   - 数据组装和转换
   - 命名以 Business 结尾

3. Service 层职责：
   - 负责数据库操作
   - 提供基础的数据访问服务
   - 不处理具体业务逻辑
   - 命名以 Service 结尾

### 依赖注入规范
1. 使用 `@Autowired` 注解进行依赖注入
2. 推荐在属性上直接使用 `@Autowired`
3. 建议将注入的属性声明为 final

### 数据访问规范
1. Service 层数据库操作规范：
   - 优先使用 MyBatis-Plus 提供的方法
   - 继承 ServiceImpl<Mapper, Entity> 获取基础 CRUD 能力
   - 复杂查询使用 LambdaQueryWrapper 构建
   - 特别复杂的 SQL 才使用自定义 Mapper 方法

2. Mapper 层规范：
   - 继承 BaseMapper<Entity>
   - 仅在必要时编写自定义方法
   - 复杂查询优先使用注解方式
   - 特别复杂的 SQL 才使用 XML

### 分层示例
```java
// Controller 层示例
@RestController
@RequestMapping("/api/user")
@Slf4j
public class UserController {
    @Autowired
    private UserBusiness userBusiness;

    @PostMapping("/register")
    public JsonResult<UserVO> register(@RequestBody @Valid UserRegisterDTO dto) {
        return JsonResult.success(userBusiness.registerUser(dto));
    }
    
    @GetMapping("/list")
    public JsonResult<List<UserVO>> list(@RequestParam(required = false) String keyword) {
        return JsonResult.success(userBusiness.listUsers(keyword));
    }
}

// Business 层示例
@Component
@Slf4j
public class UserBusiness {
    @Autowired
    private UserService userService;
    
    @Autowired
    private RoleService roleService;

    public UserVO registerUser(UserRegisterDTO dto) {
        // 业务逻辑处理
        if (userService.existsByUsername(dto.getUsername())) {
            ToolsHelper.throwException("用户名已存在");
        }
        
        UserDO user = new UserDO();
        BeanUtils.copyProperties(dto, user);
        userService.save(user);
        
        roleService.assignDefaultRole(user.getId());
        
        return convertToVO(user);
    }
}

// Service 层示例
@Service
@Slf4j
public class UserService extends ServiceImpl<UserMapper, UserDO> {
    
    public boolean existsByUsername(String username) {
        return count(new LambdaQueryWrapper<UserDO>()
                .eq(UserDO::getUsername, username)) > 0;
    }
    
    public List<UserDO> listByKeyword(String keyword) {
        LambdaQueryWrapper<UserDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StringUtils.isNotBlank(keyword), UserDO::getUsername, keyword)
               .or()
               .like(StringUtils.isNotBlank(keyword), UserDO::getNickname, keyword);
        return list(wrapper);
    }
}

// 实体类示例
@Data
@TableName("t_user")
public class UserDO {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String username;
    private String password;
    private String email;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
}
```

### 技术要求
1. 基于 Java 17 开发
2. Controller 层日志使用规范：
   - 尽量避免在 Controller 层添加日志
   - 如需使用日志，必须使用 SLF4J 的注解方式

### 代码示例

#### 接口示例
```java
@RestController
@RequestMapping("/api/user")
public class UserController {
    // GET 示例 - 单一参数
    @GetMapping("/profile/{id}")
    public ResponseEntity<UserProfileVO> getProfile(@PathVariable Long id) {
        // 实现逻辑
    }

    // POST 示例 - 复杂参数
    @PostMapping("/update-profile")
    public ResponseEntity<Void> updateProfile(@RequestBody UserProfileUpdateDTO dto) {
        // 实现逻辑
    }
}
```

#### DTO 示例
```java
public class UserProfileUpdateDTO {
    private Long userId;
    private String username;
    private String email;
    // getter 和 setter
}
```

#### VO 示例
```java
public class UserProfileVO {
    private Long userId;
    private String username;
    private String email;
    // getter 和 setter
}
```
