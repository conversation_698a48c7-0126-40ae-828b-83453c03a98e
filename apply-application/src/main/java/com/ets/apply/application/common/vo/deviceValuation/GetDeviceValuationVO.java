package com.ets.apply.application.common.vo.deviceValuation;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class GetDeviceValuationVO {
    private Long uid;
    private String plateNo;
    private Integer plateColor;
    private BigDecimal price;
    private String valuationSn;
    private Integer valuationStatus;
    private Integer deviceType;
    private Integer useYear;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime issuedTime;
    private Long issuedDays = 0L;


}
