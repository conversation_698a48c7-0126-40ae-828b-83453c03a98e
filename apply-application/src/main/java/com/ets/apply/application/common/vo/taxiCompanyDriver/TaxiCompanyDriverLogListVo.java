package com.ets.apply.application.common.vo.taxiCompanyDriver;

import com.ets.apply.application.common.consts.taxidriver.TaxiDriverLogTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class TaxiCompanyDriverLogListVo {

    private Integer id;

    /**
     * 日志类型
     */
    private Integer logType;

    /**
     * 出租车司机id
     */
    private Integer driverId;

    /**
     * 日志内容
     */
    private String content;

    /**
     * 操作人
     */
    private String operator;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;


    /**
     * 日志类型
     * @return
     */
    public String getLogTypeStr(){
       return TaxiDriverLogTypeEnum.getDescByCode(logType);
    }

}
