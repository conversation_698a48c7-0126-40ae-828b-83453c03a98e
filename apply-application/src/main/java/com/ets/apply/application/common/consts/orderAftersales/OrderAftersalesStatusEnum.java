package com.ets.apply.application.common.consts.orderAftersales;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum OrderAftersalesStatusEnum {
    STATUS_INIT(0, "初始化"),

    STATUS_APPLY(10, "已申请"),

    STATUS_PROCESSING(20, "处理中"),
    STATUS_SUCCESS(50, "已完成"),

    STATUS_FAIL(99, "已取消");

    private final Integer status;
    private final String desc;


    public static OrderAftersalesStatusEnum getByStatus(int status) {
        for (OrderAftersalesStatusEnum node : OrderAftersalesStatusEnum.values()) {
            if (node.getStatus() == status) {
                return node;
            }
        }
        return null;
    }

    public static String getDescByStatus(int status) {
        for (OrderAftersalesStatusEnum node : OrderAftersalesStatusEnum.values()) {
            if (node.getStatus().equals(status)) {
                return node.getDesc();
            }
        }
        return "";
    }
}
