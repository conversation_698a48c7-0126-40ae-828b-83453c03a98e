package com.ets.apply.application.service;

import com.ets.apply.application.infra.service.TaxiCompanyService;
import org.junit.Test;
import org.junit.runner.RunWith;
import java.util.List;
import org.junit.Assert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import com.ets.apply.application.infra.entity.TaxiCompanyEntity;

@Slf4j
@SpringBootTest
@RunWith(value = SpringRunner.class)
public class TaxiCompanyServiceTest {
    @Autowired
    private TaxiCompanyService taxiCompanyService;

    @Test
    public void getAllCompanyName() {
        List<TaxiCompanyEntity> list = taxiCompanyService.getAllCompanyName();
        log.info("list: {}", list);
        Assert.assertNotEquals(null, list);
    }
}
