package com.ets.apply.application.common.consts.bank.ceb;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
@AllArgsConstructor
public enum CebSettlementEnum {

    YES("1", "已结算"),
    NO("0", "未结算");

    private final String value;
    private final String desc;
    public static final Map<String, String> map;

    static {
        CebSettlementEnum[] enums = CebSettlementEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
