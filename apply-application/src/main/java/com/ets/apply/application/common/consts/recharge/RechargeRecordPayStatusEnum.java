package com.ets.apply.application.common.consts.recharge;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
@AllArgsConstructor
public enum RechargeRecordPayStatusEnum {
    DEFAULT(0, "未支付"),
    SUCCESS(1, "支付成功"),
    REFUNDED(2, "已退款"),
    REFUNDING(3, "退款中");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;

    static {
        RechargeRecordPayStatusEnum[] enums = RechargeRecordPayStatusEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
