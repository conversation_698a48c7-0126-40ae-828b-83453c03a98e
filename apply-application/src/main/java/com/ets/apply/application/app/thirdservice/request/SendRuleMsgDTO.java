package com.ets.apply.application.app.thirdservice.request;

import lombok.Data;

@Data
public class SendRuleMsgDTO {
    private Long uid;
    private String noticeRuleId;
    private String noticeId;
    private Object params;
    private String startTime;
    private String endTime;

     public SendRuleMsgDTO(Long userId, String noticeRuleId, String noticeId, Object params, String startTime, String endTime) {
            this.uid = userId;
            this.noticeId = noticeId;
            this.noticeRuleId = noticeRuleId;
            this.params = params;
            this.startTime = startTime;
            this.endTime = endTime;
    }
}
