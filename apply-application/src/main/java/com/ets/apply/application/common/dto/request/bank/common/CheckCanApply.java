package com.ets.apply.application.common.dto.request.bank.common;

import lombok.Data;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.util.List;

@Data
public class CheckCanApply {

    @Min(value = 1)
    private Long uid;

    @Size(max = 10,min =1)
    private List<Integer> whichBankList;

    @Min(value = 0,message = "请选择是否自动补充城市信息")
    @Max(value = 1,message = "请选择是否自动补充城市信息")
    private Integer autoFillCity;

    private String city;
}
