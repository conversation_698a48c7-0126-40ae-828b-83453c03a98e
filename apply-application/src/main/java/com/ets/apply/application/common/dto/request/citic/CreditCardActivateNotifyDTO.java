package com.ets.apply.application.common.dto.request.citic;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class CreditCardActivateNotifyDTO {

    /**
     * uid
     */
    private Long uid;

    /**
     * 银行
     */
    @JsonProperty(value = "which_bank")
    private Integer whichBank;

    @JsonProperty(value = "apply_number")
    private String applyNumber;

    @JsonProperty(value = "activate_time")
    private String activateTime;

    @JsonProperty(value = "is_new_user")
    private Integer isNewUser;

    private Integer classify;

    private Integer status;

}
