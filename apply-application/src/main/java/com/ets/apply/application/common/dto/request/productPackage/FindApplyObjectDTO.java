package com.ets.apply.application.common.dto.request.productPackage;

import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.util.List;

@Data
public class FindApplyObjectDTO {
    @NotNull(message = "issuerId不可为空")
    private Integer issuerId;
    @NotNull(message = "申办流程不可为空")
    private Integer flowType;

    private Integer purchaseParty;

    private List<Integer> creditBank;

    public FindApplyObjectDTO(Integer issuerId,Integer flowType,Integer purchaseParty, List<Integer> creditBank){
        this.issuerId = issuerId;
        this.flowType = flowType;
        this.purchaseParty = purchaseParty;
        this.creditBank = creditBank;
    }
}
