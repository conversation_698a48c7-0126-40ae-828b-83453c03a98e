package com.ets.apply.application.common.consts.recharge;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
@AllArgsConstructor
public enum RechargeWriteStatusEnum {
    UNKNOWN(-1, "未知"),
    DEFAULT(0, "未圈存"),
    SUCCESS(1, "圈存成功"),
    FAIL(2, "圈存失败");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;

    static {
        RechargeWriteStatusEnum[] enums = RechargeWriteStatusEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
