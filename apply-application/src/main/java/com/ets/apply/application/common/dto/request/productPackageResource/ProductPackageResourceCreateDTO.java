package com.ets.apply.application.common.dto.request.productPackageResource;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

@Data
public class ProductPackageResourceCreateDTO {
    /**
     * 资源名称
     */
    @NotBlank(message = "请输入资源名称")
    @Length(min = 1, max = 20, message = "请输入20个以内字符")
    private String resourceName;

    /**
     * 备注
     */
    @Length(min = 0, max = 100, message = "备注请输入长度20以内的字符")
    private String remark;

    /**
     * 资源配置信息
     */
    private Object content;


    /**
     * 资源分类[0-无类别 1-发行 2-增购]
     */
    @Digits(integer = 3, fraction = 0, message = "请选择正确的分类：选项不能超过1000")
    @NotNull(message = "资源分类不能为空")
    private Integer resourceType;
}
