package com.ets.apply.application.infra.service;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.apply.application.common.bo.creditCard.CreditCardLogBO;
import com.ets.apply.application.common.dto.request.creditCard.CreditCardLogDTO;
import com.ets.apply.application.infra.entity.ActivityCreditCardLog;
import com.ets.apply.application.infra.mapper.ActivityCreditCardLogMapper;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <p>
 * 信用卡申办记录操作日志表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-30
 */
@Service
@DS("db-etc")
public class ActivityCreditCardLogService extends BaseService<ActivityCreditCardLogMapper, ActivityCreditCardLog> {

    public void addLog(CreditCardLogBO logBO) {
        ActivityCreditCardLog log = BeanUtil.copyProperties(logBO, ActivityCreditCardLog.class);
        log.setCreatedAt(LocalDateTime.now());
        log.setUpdatedAt(LocalDateTime.now());
        this.baseMapper.insert(log);
    }

    public IPage<ActivityCreditCardLog> getLogByOrderSn(CreditCardLogDTO logDTO) {
        Wrapper<ActivityCreditCardLog> wrapper = Wrappers.<ActivityCreditCardLog>lambdaQuery()
                .eq(ActivityCreditCardLog::getOrderSn, logDTO.getOrderSn())
                .orderByDesc(ActivityCreditCardLog::getCreatedAt);
        return this.baseMapper.selectPage(new Page<>(logDTO.getPageNum(), logDTO.getPageSize()), wrapper);
    }
}
