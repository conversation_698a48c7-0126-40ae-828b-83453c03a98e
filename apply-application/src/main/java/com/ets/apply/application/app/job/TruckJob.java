package com.ets.apply.application.app.job;


import com.ets.apply.application.app.business.OrderBusiness;
import com.ets.apply.application.app.business.TruckBusiness;
import com.ets.apply.application.app.factory.benefit.BenefitFactory;
import com.ets.apply.application.app.thirdservice.feign.UserApplicationFeign;
import com.ets.apply.application.app.thirdservice.request.user.GetDepositRecordListDTO;
import com.ets.apply.application.app.thirdservice.request.user.UpdateDepositRecordStatusDTO;
import com.ets.apply.application.app.thirdservice.request.user.UpdateUserDepositDTO;
import com.ets.apply.application.app.thirdservice.response.user.UserDepositVO;
import com.ets.apply.application.app.thirdservice.response.user.UsersDepositRecordVO;
import com.ets.apply.application.common.consts.deposit.DepositRecordStatusEnum;
import com.ets.apply.application.common.consts.deposit.DepositStatusEnum;
import com.ets.apply.application.common.consts.orderBenefitRecord.OrderBenefitRecordStatusEnum;
import com.ets.apply.application.common.consts.segmentBenefit.BenefitTypeEnum;
import com.ets.apply.application.common.dto.request.order.OrderFindDTO;
import com.ets.apply.application.common.vo.order.GetRefundInfoByOrderSnVo;
import com.ets.apply.application.infra.entity.OrderBenefitRecord;
import com.ets.apply.application.infra.entity.OrderOrderEntity;
import com.ets.apply.application.infra.service.OrderBenefitRecordService;
import com.ets.apply.application.infra.service.OrderLogService;
import com.ets.apply.application.infra.service.OrderOrderService;
import com.ets.common.JsonResult;
import com.ets.common.ToolsHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class TruckJob {

    @Autowired
    private OrderBenefitRecordService orderBenefitRecordService;

    @Autowired
    private OrderOrderService orderOrderService;

    @Autowired
    private OrderLogService orderLogService;

    @Autowired
    private TruckBusiness truckBusiness;

    @Autowired
    private UserApplicationFeign userApplicationFeign;

    @Autowired
    private OrderBusiness orderBusiness;

    private void processOrderBenefitRecord(OrderBenefitRecord record, String successLogMsg) {
        try {
            // 查询订单
            OrderOrderEntity order = orderOrderService.getByOrderSn(record.getOrderSn());
            if (ObjectUtils.isEmpty(order)) {
                ToolsHelper.throwException("订单不存在");
            }

            if (truckBusiness.checkTruckOrderMatch(record.getOrderSn())) {
                // 发放权益
                BenefitFactory.create(record.getBenefitType()).send(record.getId());

                // 更新状态为发放成功
                record.setStatus(OrderBenefitRecordStatusEnum.SUCCESS.getValue());
                record.setErrorMsg("");
                record.setUpdatedAt(LocalDateTime.now());
                orderBenefitRecordService.updateById(record);

                // 订单记录日志
                orderLogService.addLog(order, successLogMsg);
            }
        } catch (Exception e) {
            // 更新状态为发放失败
            record.setStatus(OrderBenefitRecordStatusEnum.FAIL.getValue());
            record.setErrorMsg(e.getMessage());
            record.setUpdatedAt(LocalDateTime.now());
            orderBenefitRecordService.updateById(record);

            log.error("货车权益处理失败，记录ID：{}，错误信息：{}", record.getId(), e.getMessage(), e);
        }
    }

    @XxlJob("truckPayOrderRefundHandler")
    public ReturnT<String> truckPayOrderRefundHandler(String params) {
        log.info("开始执行货车付费订单退款任务");
        int limit = Integer.parseInt(params);

        try {
            // 查询待处理的记录
            List<OrderBenefitRecord> records = orderBenefitRecordService.getNeedSendRecordList(
                    BenefitTypeEnum.TRUCK_REFUND.getType(),
                    limit
            );

            if (ObjectUtils.isEmpty(records)) {
                log.info("没有待处理的货车付费订单退款任务记录");
                return ReturnT.SUCCESS;
            }

            for (OrderBenefitRecord record : records) {
                processOrderBenefitRecord(record, "货车用户满足活动条件退款");
            }
        } catch (Exception e) {
            log.error("货车付费订单任务执行异常", e);
            throw e;
        }

        return ReturnT.SUCCESS;
    }


    @XxlJob("truckTermCouponSendHandler")
    public ReturnT<String> truckTermCouponSend(String params) {
        log.info("开始执行货车券包发放任务");
        int limit = Integer.parseInt(params);

        try {
            // 查询待处理的记录
            List<OrderBenefitRecord> records = orderBenefitRecordService.getNeedSendRecordList(
                    BenefitTypeEnum.TRUCK_TERM_COUPON.getType(),
                    limit
            );

            if (ObjectUtils.isEmpty(records)) {
                log.info("没有待处理的货车券包记录");
                return ReturnT.SUCCESS;
            }

            for (OrderBenefitRecord record : records) {
                processOrderBenefitRecord(record, "货车用户账期达标，发放权益券包至车牌");
            }
        } catch (Exception e) {
            log.error("货车券包发放任务执行异常", e);
            throw e;
        }
        return ReturnT.SUCCESS;
    }

    // 常量定义
    private static final int DEFAULT_PAGE_SIZE = 200;

    /**
     * 定时查询货车保证金记录，检查退款状态并更新
     *
     * @param params 参数，直接传入数字，如：200（表示每页200条记录）
     * @return 执行结果
     */
    @XxlJob("checkDepositRefundStatusHandler")
    public ReturnT<String> checkDepositRefundStatusHandler(String params) {
        XxlJobLogger.log("开始执行货车保证金退款状态检查任务，参数：{}", params);

        try {
            int pageSize = parsePageSize(params);
            int totalProcessed = 0;
            int successCount = 0;
            int pageNum = 1;

            // 分页处理，避免一次性加载所有数据到内存
            while (true) {
                List<UsersDepositRecordVO> recordList = fetchDepositRecords(pageNum, pageSize);
                if (ObjectUtils.isEmpty(recordList)) {
                    break;
                }

                // 处理当前页的记录
                int currentPageSuccess = processDepositRecords(recordList);
                successCount += currentPageSuccess;
                totalProcessed += recordList.size();

                XxlJobLogger.log("处理第{}页，共{}条记录，成功{}条", pageNum, recordList.size(), currentPageSuccess);

                // 如果返回的记录数小于请求的数量，说明没有更多记录了
                if (recordList.size() < pageSize) {
                    break;
                }

                pageNum++;
            }

            XxlJobLogger.log("货车保证金退款状态检查任务完成，总处理{}条记录，成功处理{}条记录", totalProcessed, successCount);
            return ReturnT.SUCCESS;

        } catch (Exception e) {
            log.error("货车保证金退款状态检查任务异常：{}", e.getMessage(), e);
            XxlJobLogger.log("货车保证金退款状态检查任务执行失败：{}", e.getMessage());
            return ReturnT.FAIL;
        }
    }

    /**
     * 解析页面大小参数
     */
    private int parsePageSize(String params) {
        int pageSize = DEFAULT_PAGE_SIZE;

        if (StringUtils.isNotEmpty(params)) {
            try {
                int inputPageSize = Integer.parseInt(params.trim());
                if (inputPageSize > 0 && inputPageSize <= 1000) { // 限制最大页面大小
                    pageSize = inputPageSize;
                } else {
                    log.warn("页面大小超出范围(1-1000)，使用默认值：{}，输入值：{}", DEFAULT_PAGE_SIZE, inputPageSize);
                }
            } catch (NumberFormatException e) {
                log.warn("解析页面大小失败，使用默认值：{}，参数：{}", DEFAULT_PAGE_SIZE, params);
            }
        }

        return pageSize;
    }

    /**
     * 获取保证金记录
     */
    private List<UsersDepositRecordVO> fetchDepositRecords(int pageNum, int pageSize) {
        GetDepositRecordListDTO dto = new GetDepositRecordListDTO();
        dto.setPageNum(pageNum);
        dto.setPageSize(pageSize);
        dto.setStatus(DepositRecordStatusEnum.INIT.getCode());

        JsonResult<List<UsersDepositRecordVO>> result = userApplicationFeign.getDepositRecordList(dto);
        if (!result.isSuccess()) {
            log.warn("查询保证金记录失败，页码：{}，页面大小：{}", pageNum, pageSize);
            return new ArrayList<>();
        }

        return ObjectUtils.isEmpty(result.getData()) ? new ArrayList<>() : result.getData();
    }

    /**
     * 处理保证金记录列表
     */
    private int processDepositRecords(List<UsersDepositRecordVO> records) {
        int successCount = 0;

        for (UsersDepositRecordVO record : records) {
            try {
                if (processDepositRecord(record)) {
                    successCount++;
                }
            } catch (Exception e) {
                log.error("处理货车保证金记录异常，ID：{}，异常：{}", record.getId(), e.getMessage(), e);
            }
        }

        return successCount;
    }

    /**
     * 处理单个保证金记录
     */
    private boolean processDepositRecord(UsersDepositRecordVO record) {
        // 查询保证金账户信息获取订单号
        if (StringUtils.isEmpty(record.getUserDepositSn())) {
            log.warn("保证金记录缺少账户编号，无法获取订单号，ID：{}", record.getId());
            return false;
        }

        // 查询退款状态
        if (!isRefundSuccessful(record)) {
            return false;
        }

        // 更新保证金记录状态
        if (!updateDepositRecordStatus(record)) {
            return false;
        }

        // 更新保证金账户状态
        return updateDepositAccountStatus(record);
    }

    /**
     * 检查退款是否成功
     * 使用OrderBusiness的getRefundInfoByOrderSn方法来判断退款状态
     */
    private boolean isRefundSuccessful(UsersDepositRecordVO record) {
        try {
            // 先获取保证金账户信息以获取订单号
            JsonResult<UserDepositVO> depositResult = userApplicationFeign.getUserDepositByUserDepositSn(record.getUserDepositSn());
            if (!depositResult.isSuccess() || depositResult.getData() == null) {
                log.error("查询货车保证金账户失败，账户编号：{}，错误信息：{}", record.getUserDepositSn(), depositResult.getMsg());
                return false;
            }

            UserDepositVO userDeposit = depositResult.getData();
            if (StringUtils.isEmpty(userDeposit.getOrderSn())) {
                log.warn("保证金账户缺少订单号，账户编号：{}", record.getUserDepositSn());
                return false;
            }

            // 使用OrderBusiness查询退款信息
            OrderFindDTO orderFindDTO = new OrderFindDTO();
            orderFindDTO.setOrderSn(userDeposit.getOrderSn());

            GetRefundInfoByOrderSnVo refundInfo = orderBusiness.getRefundInfoByOrderSn(orderFindDTO);
            if (refundInfo == null) {
                log.warn("未查询到退款信息，订单号：{}", userDeposit.getOrderSn());
                return false;
            }

            // 检查退款状态：1表示退款成功
            boolean isRefundSuccess = refundInfo.getStatus() != null && refundInfo.getStatus() == 1;

            if (isRefundSuccess) {
                log.info("订单退款成功，订单号：{}，退款单号：{}", userDeposit.getOrderSn(), refundInfo.getRefundSn());
            } else {
                log.debug("订单退款未成功，订单号：{}，退款状态：{}", userDeposit.getOrderSn(), refundInfo.getStatus());
            }

            return isRefundSuccess;
        } catch (Exception e) {
            log.error("查询退款状态异常，保证金记录ID：{}，异常：{}", record.getId(), e.getMessage());
            return false;
        }
    }

    /**
     * 更新保证金记录状态
     */
    private boolean updateDepositRecordStatus(UsersDepositRecordVO record) {
        try {
            UpdateDepositRecordStatusDTO updateDto = new UpdateDepositRecordStatusDTO();
            updateDto.setId(record.getId());
            updateDto.setStatus(DepositRecordStatusEnum.SUCCESS.getCode());

            JsonResult<Void> updateResult = userApplicationFeign.updateDepositRecordStatus(updateDto);
            if (!updateResult.isSuccess()) {
                log.error("更新货车保证金记录状态失败，ID：{}，错误信息：{}", record.getId(), updateResult.getMsg());
                return false;
            }
            return true;
        } catch (Exception e) {
            log.error("更新货车保证金记录状态异常，ID：{}，异常：{}", record.getId(), e.getMessage());
            return false;
        }
    }

    /**
     * 更新保证金账户状态
     */
    private boolean updateDepositAccountStatus(UsersDepositRecordVO record) {
        if (StringUtils.isEmpty(record.getUserDepositSn())) {
            log.warn("保证金记录缺少账户编号，ID：{}", record.getId());
            return true; // 记录状态已更新，认为处理成功
        }

        try {
            // 查询保证金账户信息
            JsonResult<UserDepositVO> depositResult = userApplicationFeign.getUserDepositByUserDepositSn(record.getUserDepositSn());
            if (!depositResult.isSuccess() || depositResult.getData() == null) {
                log.error("查询货车保证金账户失败，账户编号：{}，错误信息：{}", record.getUserDepositSn(), depositResult.getMsg());
                return false;
            }

            UserDepositVO userDeposit = depositResult.getData();

            // 更新账户状态为退款状态
            UpdateUserDepositDTO updateUserDepositDTO = new UpdateUserDepositDTO();
            updateUserDepositDTO.setUserDepositSn(userDeposit.getUserDepositSn());
            updateUserDepositDTO.setStatus(DepositAccountStatusEnum.REFUNDED.getCode());
            updateUserDepositDTO.setExpendAmount(userDeposit.getDepositAmount());

            JsonResult<Void> updateDepositResult = userApplicationFeign.updateUserDeposit(updateUserDepositDTO);
            if (!updateDepositResult.isSuccess()) {
                log.error("更新货车保证金账户失败，账户ID：{}，错误信息：{}", userDeposit.getId(), updateDepositResult.getMsg());
                return false;
            }

            log.info("成功更新货车保证金账户状态，账户ID：{}，账户编号：{}", userDeposit.getId(), userDeposit.getUserDepositSn());
            return true;
        } catch (Exception e) {
            log.error("更新货车保证金账户异常，账户编号：{}，异常：{}", record.getUserDepositSn(), e.getMessage());
            return false;
        }
    }
}
