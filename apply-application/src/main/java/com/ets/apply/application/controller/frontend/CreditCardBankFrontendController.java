package com.ets.apply.application.controller.frontend;

import com.ets.apply.application.app.business.creditCardBank.CreditCardBankBusiness;
import com.ets.apply.application.common.dto.request.creditCard.CreditCardApplyStatusDTO;
import com.ets.apply.application.common.dto.request.creditCardBank.CreditCardBankListInfoDTO;
import com.ets.apply.application.common.utils.UserUtil;
import com.ets.apply.application.common.vo.creditCard.CreditCardApplyStatusVO;
import com.ets.apply.application.common.vo.creditCardBank.CreditCardBankInfoListVO;
import com.ets.common.JsonResult;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping("/frontend/credit-card-bank")
@RefreshScope
@RestController
@Slf4j
public class CreditCardBankFrontendController {

    @Autowired
    private CreditCardBankBusiness creditCardBankBusiness;

    @PostMapping("/bank-info-list")
    @ResponseBody
    public JsonResult<List<CreditCardBankInfoListVO>> bankInfoList(@RequestBody(required = false) @Valid CreditCardBankListInfoDTO dto) {
        return JsonResult.ok(creditCardBankBusiness.getCreditCardBankInfoList(dto));
    }


}
