package com.ets.apply.application.common.vo.adminUnicom;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class UnicomPageListVO {

    /**
     * 序号
     */
    private Integer id;

    /**
     * 申请单号
     */
    private String unicomApplySn;

    /**
     * 用户uid
     */
    private Long uid;

    /**
     * 办理手机号码（加密）
     */
    private String phone;

    /**
     * 号卡订单状态
     */
    private String unicomOrderState;

    /**
     * 用户号卡状态，同步接口状态
     */
    private Integer phoneStatus;

    /**
     * 是否三无
     */
    private Integer isThreeNone;

    /**
     * 是否黑名单
     */
    private Integer isBlackList;

    /**
     * 累计充值金额
     */
    private BigDecimal accumulatedAmount;

    /**
     * 首充金额，单位分
     */
    private Integer firstRecharge;

    /**
     * 关联类型：1申办订单
     */
    private Integer referType;

    /**
     * 关联流水号，1申办订单号order_sn
     */
    private String referSn;

    /**
     * 联通商品编号
     */
    private String unicomGoodsId;

    /**
     * 联通订单号
     */
    private String unicomOrderSn;

    /**
     * 申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime applyTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 号卡订单状态
     */
    private String unicomOrderStateStr;

    /**
     * 用户号卡状态，同步接口状态
     */
    private String phoneStatusStr;

    /**
     * 是否三无
     */
    private String isThreeNoneStr;

}
