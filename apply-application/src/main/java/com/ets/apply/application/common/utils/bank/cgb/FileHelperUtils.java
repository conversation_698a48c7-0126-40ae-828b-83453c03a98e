package com.ets.apply.application.common.utils.bank.cgb;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;

/**
 * <AUTHOR>
 */
public final class FileHelperUtils {

    private static final int BUFF_SIZE = 0x10000;

    private FileHelperUtils() {
    }

    public static void write(String filePath, byte[] data) throws IOException {
        if (filePath == null) {
            throw new IllegalArgumentException("Illegal Argument: filePath");
        }
        if (data == null) {
            throw new IllegalArgumentException("Illegal Argument: data");
        }
        try (FileOutputStream fos = new FileOutputStream(filePath)) {
            fos.write(data, 0, data.length);
            fos.flush();
        } catch (IOException e) {
            throw e;
        }
    }

    public static byte[] read(String filePath) throws IOException {
        if (filePath == null) {
            throw new IllegalArgumentException("Illegal Argument: filePath");
        }
        try (FileInputStream crls = new FileInputStream(filePath)) {
            byte[] out = new byte[crls.available()];
            byte[] buffer = new byte[BUFF_SIZE];
            int rLength;
            for (int offset = 0; (rLength = crls.read(buffer, 0, buffer.length)) != -1; offset += rLength) {
                System.arraycopy(buffer, 0, out, offset, rLength);
            }
            return out;
        } catch (IOException e) {
            throw e;
        }
    }
}
