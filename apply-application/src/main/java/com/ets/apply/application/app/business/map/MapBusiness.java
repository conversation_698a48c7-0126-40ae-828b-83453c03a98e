package com.ets.apply.application.app.business.map;

import com.ets.apply.application.app.thirdservice.feign.CallPhpApplyFeign;
import com.ets.apply.application.common.bo.map.MapSetCacheByAssignIdBO;
import com.ets.apply.application.common.consts.map.MapSetCacheEnum;
import com.ets.apply.application.common.dto.request.MapCompareDiffDTO;
import com.ets.apply.application.common.dto.request.MapSetCacheByKeyDTO;
import com.ets.apply.application.common.dto.request.MapSetCacheByModuleDTO;
import com.ets.apply.application.infra.entity.map.MapAssignEntity;
import com.ets.apply.application.infra.entity.map.MapPackageSnEntity;
import com.ets.apply.application.infra.entity.mapModule.MapModuleEntity;
import com.ets.apply.application.infra.service.MapAssignService;
import com.ets.apply.application.infra.service.MapLogService;
import com.ets.apply.application.infra.service.MapModuleService;
import com.ets.apply.application.infra.service.MapPackageSnService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;


@Slf4j
@Component
public class MapBusiness {
    @Autowired
    private CallPhpApplyFeign callPhpApplyFeign;
    @Autowired
    private MapModuleService mapModuleService;
    @Autowired
    private MapAssignService mapAssignService;
    @Autowired
    private MapPackageSnService mapPackageSnService;
    @Autowired
    private MapLogService mapLogService;
    /*
     * 更新产品包
     */
    public Boolean updateProdCache(MapCompareDiffDTO dto, String loginCode){
        //直接更新全部
        if(dto.getSeparateId() == 0){
            //根据不同的module更新缓存
            MapSetCacheByModuleDTO mapSetCacheByModuleDTO = new MapSetCacheByModuleDTO();
            mapSetCacheByModuleDTO.setType(MapSetCacheEnum.MAP_SET_CACHE_TYPE_PROD.getCode());
            MapSetCacheByKeyDTO mapSetCacheByKeyDTO = new MapSetCacheByKeyDTO();
            mapSetCacheByKeyDTO.setType(MapSetCacheEnum.MAP_SET_CACHE_TYPE_PROD.getCode());

            for (MapSetCacheEnum node : MapSetCacheEnum.values()) {
                switch (MapSetCacheEnum.getByCode(node.getCode())){
                    //normal
                    case MAP_SET_CACHE_MODULE_NORMAL:
                        mapSetCacheByModuleDTO.setParentModule(MapSetCacheEnum.MAP_SET_CACHE_MODULE_NORMAL.getCode());
                        mapSetCacheByModuleDTO.setModule(MapSetCacheEnum.MAP_SET_CACHE_MODULE_NORMAL.getCode());
                        callPhpApplyFeign.mapSetCacheByModule(mapSetCacheByModuleDTO);
                        break;
                    case MAP_SET_CACHE_MODULE_UPGRADE:
                        mapSetCacheByModuleDTO.setParentModule(MapSetCacheEnum.MAP_SET_CACHE_MODULE_UPGRADE.getCode());
                        mapSetCacheByModuleDTO.setModule(MapSetCacheEnum.MAP_SET_CACHE_MODULE_UPGRADE.getCode());
                        callPhpApplyFeign.mapSetCacheByModule(mapSetCacheByModuleDTO);
                        break;
                    case MAP_SET_CACHE_MODULE_NORMAL_TRUCK:
                        mapSetCacheByModuleDTO.setParentModule(MapSetCacheEnum.MAP_SET_CACHE_MODULE_NORMAL_TRUCK.getCode());
                        mapSetCacheByModuleDTO.setModule(MapSetCacheEnum.MAP_SET_CACHE_MODULE_NORMAL_TRUCK.getCode());
                        callPhpApplyFeign.mapSetCacheByModule(mapSetCacheByModuleDTO);
                        break;
                    //assign_pl
                    case MAP_SET_CACHE_MODULE_ASSIGN_PL:
                    case MAP_SET_CACHE_MODULE_ASSIGN_COUPONID:
                    case MAP_SET_CACHE_MODULE_ASSIGN_PROMOTION:
                    case MAP_SET_CACHE_MODULE_ASSIGN_COOPERATION:
                        mapSetCacheByModuleDTO.setParentModule(MapSetCacheEnum.MAP_SET_CACHE_MODULE_ASSIGN.getCode());
                        mapSetCacheByModuleDTO.setModule(node.getCode());
                        callPhpApplyFeign.mapSetCacheByModule(mapSetCacheByModuleDTO);
                        break;

                    case MAP_SET_CACHE_KEY_RULE_COMBINE:
                    case MAP_SET_CACHE_KEY_RULE_ITEM:
                        mapSetCacheByKeyDTO.setCacheKey(node.getCode());
                        callPhpApplyFeign.mapSetCacheByKey(mapSetCacheByKeyDTO);
                        break;
                    case MAP_SET_CACHE_MODULE_BACKUP:
                        mapSetCacheByModuleDTO.setParentModule(MapSetCacheEnum.MAP_SET_CACHE_MODULE_BACKUP.getCode());
                        mapSetCacheByModuleDTO.setModule(MapSetCacheEnum.MAP_SET_CACHE_MODULE_BACKUP.getCode());
                        callPhpApplyFeign.mapSetCacheByModule(mapSetCacheByModuleDTO);
                        break;
                }

            }
            //记录日志
            mapLogService.addLog(
                    loginCode,
                    "updateProdCache",
                    1,
                    "updateProdCache",
                    "发布映射",
                    "",
                    ""
            );
            return true;
        }
        MapSetCacheByModuleDTO mapSetCacheByModuleDTO = new MapSetCacheByModuleDTO();

        switch (MapSetCacheEnum.getByCode(dto.getSeparateModule())){
            //normal
            case MAP_SET_CACHE_MODULE_NORMAL:
                mapSetCacheByModuleDTO.setType(MapSetCacheEnum.MAP_SET_CACHE_TYPE_PROD.getCode());
                mapSetCacheByModuleDTO.setParentModule(MapSetCacheEnum.MAP_SET_CACHE_MODULE_NORMAL.getCode());
                mapSetCacheByModuleDTO.setModule(MapSetCacheEnum.MAP_SET_CACHE_MODULE_NORMAL.getCode());
                callPhpApplyFeign.mapSetCacheByModule(mapSetCacheByModuleDTO);
                break;
            case MAP_SET_CACHE_MODULE_UPGRADE:
                mapSetCacheByModuleDTO.setType(MapSetCacheEnum.MAP_SET_CACHE_TYPE_PROD.getCode());
                mapSetCacheByModuleDTO.setParentModule(MapSetCacheEnum.MAP_SET_CACHE_MODULE_UPGRADE.getCode());
                mapSetCacheByModuleDTO.setModule(MapSetCacheEnum.MAP_SET_CACHE_MODULE_UPGRADE.getCode());
                callPhpApplyFeign.mapSetCacheByModule(mapSetCacheByModuleDTO);
                break;
            case MAP_SET_CACHE_MODULE_NORMAL_TRUCK:
                mapSetCacheByModuleDTO.setType(MapSetCacheEnum.MAP_SET_CACHE_TYPE_PROD.getCode());
                mapSetCacheByModuleDTO.setParentModule(MapSetCacheEnum.MAP_SET_CACHE_MODULE_NORMAL_TRUCK.getCode());
                mapSetCacheByModuleDTO.setModule(MapSetCacheEnum.MAP_SET_CACHE_MODULE_NORMAL_TRUCK.getCode());
                callPhpApplyFeign.mapSetCacheByModule(mapSetCacheByModuleDTO);
                break;
            //assign_pl
            case MAP_SET_CACHE_MODULE_ASSIGN_PL:
            case MAP_SET_CACHE_MODULE_ASSIGN_COUPONID:
            case MAP_SET_CACHE_MODULE_ASSIGN_PROMOTION:
            case MAP_SET_CACHE_MODULE_ASSIGN_COOPERATION:
                MapSetCacheByAssignIdBO mapSetCacheByAssignIdBO = new MapSetCacheByAssignIdBO();
                mapSetCacheByAssignIdBO.setType(MapSetCacheEnum.MAP_SET_CACHE_TYPE_PROD.getCode());
                mapSetCacheByAssignIdBO.setMapAssignId(dto.getSeparateId());
                callPhpApplyFeign.mapSetCacheByAssignId(mapSetCacheByAssignIdBO);
                break;

            case MAP_SET_CACHE_KEY_RULE_COMBINE:
            case MAP_SET_CACHE_KEY_RULE_ITEM:
                MapSetCacheByKeyDTO mapSetCacheByKeyDTO = new MapSetCacheByKeyDTO();
                mapSetCacheByKeyDTO.setType(MapSetCacheEnum.MAP_SET_CACHE_TYPE_PROD.getCode());
                mapSetCacheByKeyDTO.setCacheKey(dto.getSeparateModule());
                callPhpApplyFeign.mapSetCacheByKey(mapSetCacheByKeyDTO);
                break;
            case MAP_SET_CACHE_MODULE_BACKUP:
                mapSetCacheByModuleDTO.setType(MapSetCacheEnum.MAP_SET_CACHE_TYPE_PROD.getCode());
                mapSetCacheByModuleDTO.setParentModule(MapSetCacheEnum.MAP_SET_CACHE_MODULE_BACKUP.getCode());
                mapSetCacheByModuleDTO.setModule(MapSetCacheEnum.MAP_SET_CACHE_MODULE_BACKUP.getCode());
                callPhpApplyFeign.mapSetCacheByModule(mapSetCacheByModuleDTO);
                break;
        }

        //记录日志
        mapLogService.addLog(
                loginCode,
                "updateProdCache",
                1,
                "updateProdCache",
                "发布映射",
                "",
                ""
        );

        return true;
    }

    public Boolean updatePreviewCache(Integer objectId){
        MapPackageSnEntity mapPackageSnEntity = mapPackageSnService.getById(objectId);
        switch (mapPackageSnEntity.getObjectType()){
            case "module":
                MapModuleEntity mapModuleEntity = mapModuleService.getById(mapPackageSnEntity.getObjectId());
                if(Arrays.asList(
                        MapSetCacheEnum.MAP_SET_CACHE_MODULE_NORMAL.getCode(),
                        MapSetCacheEnum.MAP_SET_CACHE_MODULE_NORMAL_TRUCK.getCode(),
                        MapSetCacheEnum.MAP_SET_CACHE_MODULE_UPGRADE.getCode(),
                        MapSetCacheEnum.MAP_SET_CACHE_MODULE_BACKUP.getCode()
                ).contains(mapModuleEntity.getModule())){
                    MapSetCacheByModuleDTO mapSetCacheByModuleDTO = new MapSetCacheByModuleDTO();
                    mapSetCacheByModuleDTO.setParentModule(mapModuleEntity.getModule());
                    mapSetCacheByModuleDTO.setModule(mapModuleEntity.getModule());
                    mapSetCacheByModuleDTO.setType(MapSetCacheEnum.MAP_SET_CACHE_TYPE_PREVIEW.getCode());
                    callPhpApplyFeign.mapSetCacheByModule(mapSetCacheByModuleDTO);
                }
                break;
            case "assign":
            case "assignAlternative":
                MapAssignEntity mapAssignEntity = mapAssignService.getById(mapPackageSnEntity.getObjectId());
                MapSetCacheByModuleDTO mapSetCacheByModuleDTO = new MapSetCacheByModuleDTO();
                mapSetCacheByModuleDTO.setParentModule(MapSetCacheEnum.MAP_SET_CACHE_MODULE_ASSIGN.getCode());
                mapSetCacheByModuleDTO.setModule(mapAssignEntity.getModule());
                mapSetCacheByModuleDTO.setType(MapSetCacheEnum.MAP_SET_CACHE_TYPE_PREVIEW.getCode());
                callPhpApplyFeign.mapSetCacheByModule(mapSetCacheByModuleDTO);
                break;
        }
        return true;
    }


}
