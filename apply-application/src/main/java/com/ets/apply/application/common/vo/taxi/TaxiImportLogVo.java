package com.ets.apply.application.common.vo.taxi;

import com.ets.apply.application.common.consts.productOrder.ProductOrderStatusEnum;
import com.ets.apply.application.common.consts.taxiImport.ImportStatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>
 * 出租车导入日志
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-01
 */
@Data
public class TaxiImportLogVo {


    /**
     * 自增长id
     */

    private Integer id;

    /**
     * 导入的数据id
     */
    private Integer importId;

    /**
     * 操作内容
     */
    private String content;

    /**
     * 申办订单号
     */
    private String applyOrderSn;

    /**
     * 导入状态
     */
    private Integer importStatus;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    public String getImportStatusName() {
        return ImportStatusEnum.getDescByCode(importStatus);
    }

}
