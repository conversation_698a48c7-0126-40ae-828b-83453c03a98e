package com.ets.apply.application.app.service.bigdata;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.apply.application.app.thirdservice.feign.BigDataApiFeign;
import com.ets.apply.application.app.thirdservice.feign.BigDataUserProfileApiFeign;
import com.ets.apply.application.app.thirdservice.request.bigData.AtomcmpsTagListDTO;
import com.ets.apply.application.app.thirdservice.request.bigData.TagRetTagDTO;
import com.ets.apply.application.app.thirdservice.request.bigData.BigDataOrderSettlementDTO;
import com.ets.apply.application.app.thirdservice.request.bigData.BigDataWhetherOurCompanyDTO;
import com.ets.apply.application.app.thirdservice.response.bigData.BigDataOrderSettlementVO;
import com.ets.apply.application.app.thirdservice.response.bigData.BigDataWhetherOurCompanyVO;
import com.ets.apply.application.common.dto.adminBigData.TagGetListDTO;
import com.ets.apply.application.common.vo.BigDataRechargeNoticeVO;
import com.ets.apply.application.common.vo.admin.bigData.TagListVO;
import com.ets.common.JsonResult;
import com.ets.common.ToolsHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class BigDataService {

    @Autowired
    private BigDataApiFeign bigDataApiFeign;
    @Autowired
    private BigDataUserProfileApiFeign bigDataUserProfileApiFeign;
    public List<BigDataRechargeNoticeVO> rechargeNotice(Integer startIndex, Integer count) {
        Map<String, Object> map = new HashMap<>();
        map.put("start_index", startIndex);
        map.put("count", count);
        String jsonString = JSON.toJSONString(map);
        String result = bigDataApiFeign.rechargeNotice(jsonString);
        JSONObject jsonObject = JSON.parseObject(result);
        Integer code = jsonObject.getInteger("code");
        if (!code.equals(200)) {
            String msg = jsonObject.getString("msg");
            ToolsHelper.throwException(msg);
        }

        return jsonObject.getObject("data", new TypeReference<List<BigDataRechargeNoticeVO>>(){});
    }

    public String getUserCity(Long uid) {
        try {
            TagRetTagDTO tagRetTagDTO = new TagRetTagDTO();
            tagRetTagDTO.setUid(uid);
            Map<String, List<String>> tags = new HashMap<>();
            List<String> cityListDto = new ArrayList<>();
            cityListDto.add("city");
            tags.put("1", cityListDto);
            tagRetTagDTO.setTags(tags);
            String jsonString = bigDataApiFeign.checkUidWithTagsRetTagValue(tagRetTagDTO);
            JsonResult<Object> result = JsonResult.convertFromJsonStr(jsonString, Object.class);
            if (200 != result.getCode()) {
                ToolsHelper.throwException("获取用户城市失败,大数据接口请求失败" + result.getMsg(), result.getCode());
            }
            JSONObject data = (JSONObject) result.getData();
            if (data == null) {
                return "";
            }

            JSONArray tagKeyList = data.getJSONArray("tag_keys");
            if (tagKeyList == null) {
                return "";
            }
            for (Object tagKey : tagKeyList) {
                JSONObject tagKeyObject = (JSONObject) tagKey;
                JSONArray cityTagList = tagKeyObject.getJSONArray("1");
                if (ObjectUtil.isEmpty(cityTagList)) {
                    return "";
                }
                for (Object cityTag : cityTagList) {
                    JSONObject cityTagObject = (JSONObject) cityTag;
                    if (ObjectUtil.isNotEmpty(cityTagObject.get("tag_value"))) {
                        return cityTagObject.getString("tag_value");
                    }
                }

            }
            return "";
        } catch (Exception e) {
            log.error("获取用户城市失败{},原因{}", uid, e.getMessage());
            return "";
        }

    }

    public BigDataWhetherOurCompanyVO whetherOurCompany(Long uid) {
        BigDataWhetherOurCompanyDTO dto = new BigDataWhetherOurCompanyDTO();
        dto.setUid(uid);
        JsonResult<BigDataWhetherOurCompanyVO> result = bigDataApiFeign.whetherOurCompany(dto);
        if (result.getCode() != 200) {
            ToolsHelper.throwException(result.getMsg());
        }
        return result.getData();
    }

    public BigDataOrderSettlementVO orderSettlement(String orderSn) {
        BigDataOrderSettlementDTO dto = new BigDataOrderSettlementDTO();
        dto.setOrderSn(orderSn);
        JsonResult<BigDataOrderSettlementVO> result = bigDataApiFeign.orderSettlement(dto);
        if (result.getCode()!= 200) {
            ToolsHelper.throwException(result.getMsg());
        }
        return result.getData();
    }

    /*
     *  获取人群包标签列表
     */
    public Page<TagListVO> atomcmpsTagList(TagGetListDTO dto) {
        AtomcmpsTagListDTO atomcmpsTagListDTO = new AtomcmpsTagListDTO();
        atomcmpsTagListDTO.setL1((dto.getPageNum() - 1) * dto.getPageSize());
        atomcmpsTagListDTO.setL2(dto.getPageSize());
        atomcmpsTagListDTO.setTagName(dto.getTagName());
        String response = bigDataUserProfileApiFeign.atomcmpsTagList(atomcmpsTagListDTO);
        JSONObject jsonObject = JSON.parseObject(response);
        Integer code = jsonObject.getInteger("result");
        if (!code.equals(200)) {
            String msg = jsonObject.getString("msg");
            ToolsHelper.throwException(msg);
        }
        JSONArray dataList = jsonObject.getJSONArray("data");
        List<TagListVO> crowdPackages = dataList.toJavaList(TagListVO.class);
        // 总条数
        Integer total = jsonObject.getInteger("count");
        return new Page<TagListVO>()
                .setCurrent(dto.getPageNum())
                .setSize(dto.getPageSize())
                .setTotal(total)
                .setRecords(crowdPackages);
    }
}
