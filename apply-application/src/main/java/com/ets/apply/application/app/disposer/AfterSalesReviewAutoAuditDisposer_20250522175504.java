package com.ets.apply.application.app.disposer;

import com.ets.apply.application.common.bo.AfterSalesReviewAutoAuditBO;
import com.ets.apply.application.infra.entity.aftersalesreview.AftersalesReviews;
import com.ets.apply.application.infra.entity.aftersalesreview.AftersalesReviewsVehicles;
import com.ets.apply.application.infra.service.AftersalesReviewsService;
import com.ets.apply.application.infra.service.AftersalesReviewsVehiclesService;
import com.ets.common.ToolsHelper;
import com.ets.common.queue.BaseDisposer;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@NoArgsConstructor
@Component(value = "afterSalesReviewAutoAuditJobBean")
public class AfterSalesReviewAutoAuditDisposer extends BaseDisposer {

    @Autowired
    private AftersalesReviewsService aftersalesReviewsService;

    @Autowired
    private AftersalesReviewsVehiclesService aftersalesReviewsVehiclesService;

    public AfterSalesReviewAutoAuditDisposer(Object params) {
        super(params);
    }

    @Override
    public String getJobBeanName() {
        return "afterSalesReviewAutoAuditJobBean";
    }

    @Override
    public void execute(Object content) {
        AfterSalesReviewAutoAuditBO bo = getParamsObject(content, AfterSalesReviewAutoAuditBO.class);

        // 获取审核单数据
        AftersalesReviews aftersalesReviews = aftersalesReviewsService.getByReviewSn(bo.getReviewSn());
        if (aftersalesReviews == null) {
            ToolsHelper.throwException("售后审核单记录不存在");
        }

        // 获取行驶证数据
        List<AftersalesReviewsVehicles> vehiclesList = aftersalesReviewsVehiclesService.getByReviewSn(aftersalesReviews.getReviewSn());
        if (ObjectUtils.isEmpty(vehiclesList)) {
            ToolsHelper.throwException("行驶证数据不存在");
        }

        boolean hasType1 = vehiclesList.stream().anyMatch(vehicle -> AftersalesReviewsDataTypeEnum.TYPE_1.getCode().equals(vehicle.getDataType()));
        boolean hasType2 = vehiclesList.stream().anyMatch(vehicle -> DataType.TYPE_2.getCode().equals(vehicle.getDataType()));
        
        if (!hasType1 || !hasType2) {
            ToolsHelper.throwException("行驶证数据不完整，需要同时存在类型1和类型2的数据");
        }

        // 判断是否可以自动审核

    }
}
