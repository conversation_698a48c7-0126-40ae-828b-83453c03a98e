package com.ets.apply.application.app.thirdservice.fallback;

import com.ets.apply.application.app.thirdservice.feign.BlacklistFeign;
import com.ets.apply.application.common.dto.blacklist.BlacklistDTO;
import com.ets.common.JsonResult;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

@Component

public class BlacklistFallbackFactory implements FallbackFactory<BlacklistFeign> {

    @Override
    public BlacklistFeign create(Throwable cause) {

        return new BlacklistFeign() {

            @Override
            public String add(@RequestBody BlacklistDTO dto) {
                return JsonResult.error("请求etc-blacklist-main 服务失败: " + cause.getMessage()).toString();

            }

            @Override
            public String remove(@RequestBody BlacklistDTO dto) {
                return JsonResult.error("请求etc-blacklist-main 服务失败: " + cause.getMessage()).toString();

            }

            @Override
            public String cancel(@RequestBody BlacklistDTO dto) {
                return JsonResult.error("请求etc-blacklist-main 服务失败: " + cause.getMessage()).toString();

            }

            @Override
            public String one(@RequestBody BlacklistDTO dto) {
                return JsonResult.error("请求etc-blacklist-main 服务失败: " + cause.getMessage()).toString();

            }
        };
    }
}
