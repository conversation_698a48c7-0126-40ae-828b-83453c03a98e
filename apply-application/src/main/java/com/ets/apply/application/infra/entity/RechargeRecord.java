package com.ets.apply.application.infra.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 储值记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("etc_recharge_record")
public class RechargeRecord extends BaseEntity<RechargeRecord> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 储值订单号
     */
    private String orderSn;

    /**
     * 用户id
     */
    private Long uid;

    /**
     * 对应user_cards.id
     */
    private Integer userCardId;

    /**
     * 发卡方id
     */
    private Integer issuerId;

    /**
     * 卡种etc_cards.id
     */
    private Integer cardId;

    /**
     * 发行渠道：1线上申请发行，2线下发行
     */
    private Integer issuerChannel;

    /**
     * 申请订单号
     */
    private String applyOrderSn;

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 车牌颜色
     */
    private Integer plateColor;

    /**
     * 储值卡号
     */
    private String cardSn;

    /**
     * 终端机编号
     */
    private String terminalNo;

    /**
     * 用户姓名
     */
    private String customName;

    /**
     * 储值金额
     */
    private BigDecimal raiseValue;

    /**
     * 支付状态 0未支付 1已支付 2已退款 3退款中 
     */
    private Integer payStatus;

    /**
     * 退款类型:0:其它, 1:线下退款, 2:预退款, 3:线上退款
     */
    private Integer refundType;

    /**
     * 第三方充值状态 0:未充值 1:已充值 2:充值失败 50:撤销中 60:撤销完成 
     */
    private Integer issuerRaiseStatus;

    /**
     * 是否圈存 0未圈存 1圈存成功 2圈存失败
     */
    private Integer isWriteCard;

    /**
     * 支付订单流水号（支付成功）
     */
    private String paymentSn;

    /**
     * ETC充值交易时间
     */
    private LocalDateTime tradeTime;

    /**
     * 退款来源：1/线上；2/线下
     */
    private Integer refundSource;

    /**
     * 0:再次圈存需要判断金额1:不需
     */
    private Integer isSkip;

    /**
     * 客户端类型: 0:其它, 1:小程序, 2:公众号
     */
    private Integer clientType;

    /**
     * 订单来源[1-充值易 2-自助机]
     */
    private Integer orderSource;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;


}
