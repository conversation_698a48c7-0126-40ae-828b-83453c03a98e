package com.ets.apply.application.common.vo.applyOrder;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class OrderStatisticExcelVO {

    /**
     * 包sn
     */
    @ExcelProperty(value = "产品包编码")
    private String packageSn;

    /**
     * 包名
     */
    @ExcelProperty(value = "产品包名称")
    private String packageName;

    /**
     * 总计
     */
    @ExcelProperty(value = "订单总数")
    private Integer total;
}
