package com.ets.apply.application.common.vo.productPackage;

import com.alibaba.fastjson.JSONArray;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 产品包信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-19
 */
@Data
public class ProductPackageInfoVo {


    /**
     * 商品套餐流水号
     */
    private String packageSn;


    /**
     * 商品套餐名称
     */
    private String packageName;


    private Integer issuerId;

    private Integer cardId;


    private String title;


    private BigDecimal packageFee;


    private String subTitle;


    private String protocol;


    private Integer term;


    private Integer manufacturer;


    private Integer deviceType;


    private Integer deviceVersion;


    private String productImg;


    private boolean hasCoupon;


    private String issuerServiceName;


    private JSONArray productTabs;


    private AfterSales afterSales;

    private String packageInfo;

    private String oriFee;

    private Integer bizType;

    private String skuSn;

    private Integer deviceColor;

    private String bizTypeName;

    private String deviceName;


    @Data
    public static class AfterSales {
        private Integer showReapply = 1;
        private Integer onlyOfflineExchange = 0;
        private Integer revokeNeedPay = 1;
        private Integer warrantyTime = 3;
    }
}
