package com.ets.apply.application.common.consts.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SceneEnum {

    Car(1, "普通客车"),

    Taxi(2, "出租车"),

    STD(3, "储值卡转化"),

    CUSTOMER_TRANSLATE(4, "老客户转化"),

    TRUCK(5, "货车"),

    EXCHARGE_CARD(6, "储值卡申办"),

    PINGAN_GOOD_CAR_OWNER(7, "平安好车主申办"),

    PRE_PUSH_ORDER(8, "h5 2.0 流程处理"),

    BAIDU_MINA_PREPUSH(9, "百度小程序快发版本"),

    CAR_OWNER_SERVICE(10, "车主卡办理场景");

    private final int code;
    private final String description;

}
