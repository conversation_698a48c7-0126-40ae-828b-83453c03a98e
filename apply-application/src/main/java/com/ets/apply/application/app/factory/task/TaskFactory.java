package com.ets.apply.application.app.factory.task;

import com.alibaba.fastjson.JSON;
import com.ets.apply.application.app.factory.task.impl.TaskBase;
import com.ets.apply.application.common.consts.taskRecord.TaskRecordReferTypeEnum;
import com.ets.apply.application.common.dto.task.TaskRecordDTO;
import com.ets.common.SpringBeanHelper;
import com.ets.common.ToolsHelper;

import java.time.LocalDateTime;


public class TaskFactory {

    public static TaskBase create(String referType) {

        TaskRecordReferTypeEnum typeEnum = TaskRecordReferTypeEnum.getReferTypeEnumByType(referType);

        return create(typeEnum);
    }

    public static TaskBase create(TaskRecordReferTypeEnum typeEnum) {

        if (typeEnum == null) {
            ToolsHelper.throwException("task 任务未定义");
        }

        return SpringBeanHelper.getBean(typeEnum.getJob());
    }

    public static void createByRefer(Object paramsBO, TaskRecordReferTypeEnum referType, String referSn) {

        TaskRecordDTO taskRecordDTO = new TaskRecordDTO();
        taskRecordDTO.setReferSn(referSn);
        taskRecordDTO.setReferType(referType.getType());
        taskRecordDTO.setNextExecTime(LocalDateTime.now().minusMinutes(10));
        taskRecordDTO.setNotifyContent(JSON.toJSONString(paramsBO));
        create(referType).addAndPush(taskRecordDTO);
    }
}
