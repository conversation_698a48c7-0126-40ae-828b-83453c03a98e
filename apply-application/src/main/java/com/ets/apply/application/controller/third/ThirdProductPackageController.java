package com.ets.apply.application.controller.third;

import com.ets.apply.application.app.business.ProductPackageBusiness;
import com.ets.apply.application.controller.BaseController;
import com.ets.apply.feign.feign.ProductPackageFeign;
import com.ets.apply.feign.request.BatchQueryDTO;
import com.ets.apply.feign.response.ProductPackageBatchQueryResponse;
import com.ets.common.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.util.List;

@Validated
@RequestMapping("/third/product-package")
@RefreshScope
@RestController
@Slf4j
public class ThirdProductPackageController extends BaseController implements ProductPackageFeign {

    @Autowired
    private ProductPackageBusiness productPackageBusiness;

    @Override
    @RequestMapping("/batch-query")
    public JsonResult<List<ProductPackageBatchQueryResponse>> batchQuery(@RequestBody @Valid BatchQueryDTO batchQueryDTO) {
        return JsonResult.ok(productPackageBusiness.batchQuery(batchQueryDTO));

    }
}
