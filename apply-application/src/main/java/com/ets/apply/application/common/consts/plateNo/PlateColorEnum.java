package com.ets.apply.application.common.consts.plateNo;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor

public enum PlateColorEnum {
//    /**
//     * 蓝色
//     */
//    const BLUE = 0;
//    /**
//     * 黄色
//     */
//    const YELLOW = 1;
//    /**
//     *黑色
//     */
//    const BLACK = 2;
//    /**
//     *白色
//     */
//    const WHITE = 3;
//    /**
//     * 渐变绿色
//     */
//    const GRADIENT_GREEN = 4;
//    /**
//     * 黄绿双拼色
//     */
//    const YELLOW_HYBRID_GREEN = 5;
//    /**
//     * 蓝白渐变色
//     */
//    const BLUE_GRADIENT_WHITE = 6;
//    /**
//     *临时牌照
//     */
//    const TEMP = 7;
//    /**
//     *未确定
//     */
//    const UNCERTAIN = 9;
//    /**
//     *绿色
//     */
//    const GREEN = 11;
//    /**
//     *红色
//     */
//    const RED = 12;

    BLUE(0, "蓝色"),
    YELLOW(1, "黄色"),
    BLACK(2, "黑色"),
    WHITE(3, "白色"),
    GRADIENT_GREEN(4, "渐变绿色"),
    YELLOW_HYBRID_GREEN(5, "黄绿双拼色"),
    BLUE_GRADIENT_WHITE(6, "蓝白渐变色"),
    TEMP(7, "临时牌照"),
    UNCERTAIN(9, "未确定"),
    GREEN(11, "绿色"),
    RED(12, "红色"),
    ;

    private final Integer code;
    private final String desc;
}
