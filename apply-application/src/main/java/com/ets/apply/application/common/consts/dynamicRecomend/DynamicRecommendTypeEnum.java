package com.ets.apply.application.common.consts.dynamicRecomend;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum DynamicRecommendTypeEnum {
    DEFAULT(0, "默认"),
    RECOMMEND_BY_UNPAID_ORDER(1, "未支付订单推荐类型"),
    RECOMMEND_BY_ORDER_CANCEL(2, "取消订单推荐"),
    RECOMMEND_BY_USER_ACCESS_PAGE(3, "用户浏览商详页面推荐"),
    RECOMMEND_BY_CREDIT_CARD(4, "信用卡未进件推荐"),
    RECOMMEND_BY_PAID_ORDER(5, "已支付订单推荐类型")
    ;

    private final Integer type;
    private final String description;

    public static DynamicRecommendTypeEnum getEnumByType(Integer type) {
        for (DynamicRecommendTypeEnum dynamicRecommendTypeEnum : DynamicRecommendTypeEnum.values()) {
            if (dynamicRecommendTypeEnum.getType().equals(type)) {
                return dynamicRecommendTypeEnum;
            }
        }
        return null;
    }
}
