package com.ets.apply.application.app.service.thirdPartner;

import cn.hutool.core.util.ObjectUtil;
import com.ets.apply.application.app.thirdservice.feign.VaspFeign;
import com.ets.apply.application.common.bo.productOrder.ProductOrderRefundBO;
import com.ets.apply.application.common.bo.productOrder.ProductOrderSaleAfterBO;
import com.ets.apply.application.common.bo.productOrder.ProductOrderShipBO;
import com.ets.apply.application.common.bo.vasp.VaspOrderResultBO;
import com.ets.apply.application.common.config.VaspConfig;
import com.ets.apply.application.common.consts.vasp.VaspIsRefundEnum;
import com.ets.apply.application.common.consts.vasp.VaspOrderStatusEnum;
import com.ets.apply.application.common.utils.Md5Utils;
import com.ets.common.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Locale;
import java.util.Objects;

@Slf4j
@Component(value = "VaspService")
public class VaspService {

    @Autowired
    private VaspConfig vaspConfig;

    @Autowired
    private VaspFeign vaspFeign;

    /**
     * 生成签名
     *
     * @param thirdOrderSn 参数
     * @param timestamp    时间戳 YYYYMMDDHH24MISS
     * @return 签名
     */
    public String generateSign(String thirdOrderSn, String timestamp) {

        String originStr = thirdOrderSn + timestamp + vaspConfig.getSecret() + vaspConfig.getAgentId();
        String sign = Md5Utils.encodeByMD5(originStr).toLowerCase(Locale.ROOT);
        log.info("vasp 生成签名前的参数 = {}，签名sign = {}", originStr, sign);
        return sign;
    }

    /**
     * 处理返回数据，成功错误码转换为通用系统成功码
     *
     * @param responseStr
     * @return
     */
    public JsonResult getResponseData(String responseStr) {
        JsonResult<String> result = JsonResult.convertFromJsonStr(responseStr, String.class);
        if (Objects.equals(200, result.getCode())) {
            result.setCode(JsonResult.SUCCESS_CODE);
        }
        return result;
    }


    public String refund(ProductOrderRefundBO productOrderRefundBO) {
        // 组装退款参数
        VaspOrderResultBO resultBO = new VaspOrderResultBO();
        resultBO.setOrderId(productOrderRefundBO.getThirdOrderSn());
        resultBO.setCancelReason(productOrderRefundBO.getRefundReason());
        resultBO.setIsRefund(VaspIsRefundEnum.IS_REFUND_YES.getCode());
        // 已发货走退货退款通知
        if (productOrderRefundBO.getHasShip()) {
            resultBO.setOrderStatus(VaspOrderStatusEnum.ORDER_STATUS_RETURN.getCode());
        } else {
            // 直接通知取消订单
            resultBO.setOrderStatus(VaspOrderStatusEnum.ORDER_STATUS_CANCEL.getCode());
        }
        resultBO.setRefundAmt(productOrderRefundBO.getPaidAmount().doubleValue());
        // 系统参数
        resultBO.setTimestamp(DateTimeFormatter.ofPattern("yyyyMMddHHmmss").format(LocalDateTime.now()));
        resultBO.setSign(this.generateSign(resultBO.getOrderId(), resultBO.getTimestamp()));
        resultBO.setAgent_id(vaspConfig.getAgentId());

        String result = vaspFeign.orderResult(resultBO);
        JsonResult resultFormat = this.getResponseData(result);
        resultFormat.checkError();
        return "";
    }


    /**
     * 通知vasp发货
     *
     * @param shipBO
     */
    public void ship(ProductOrderShipBO shipBO) {
        // 组装发货参数
        VaspOrderResultBO resultBO = new VaspOrderResultBO();
        resultBO.setOrderId(shipBO.getThirdOrderSn());
        resultBO.setOrderStatus(VaspOrderStatusEnum.ORDER_STATUS_SHIP.getCode());
        resultBO.setOrderAddr(shipBO.getSendAddress());
        resultBO.setDeliveryNo(shipBO.getLogisticNumber());
        resultBO.setLogisticCompany(shipBO.getLogisticCompany());
        // 系统参数
        resultBO.setTimestamp(DateTimeFormatter.ofPattern("yyyyMMddHHmmss").format(LocalDateTime.now()));
        resultBO.setSign(this.generateSign(resultBO.getOrderId(), resultBO.getTimestamp()));
        resultBO.setAgent_id(vaspConfig.getAgentId());

        String result = vaspFeign.orderResult(resultBO);
        JsonResult resultFormat = this.getResponseData(result);
        resultFormat.checkError();
    }

    /**
     * 当前版本不启用该流程
     *
     * @param salesReturnBO
     */
    public void saleAfter(ProductOrderSaleAfterBO salesReturnBO) {

        // 组装退款参数
        VaspOrderResultBO resultBO = new VaspOrderResultBO();
        resultBO.setOrderId(salesReturnBO.getThirdOrderSn());
        resultBO.setOrderStatus(VaspOrderStatusEnum.ORDER_STATUS_RETURN.getCode());
        if (ObjectUtil.isNotNull(salesReturnBO.getIsRefund()) && salesReturnBO.getIsRefund()) {
            resultBO.setIsRefund(VaspIsRefundEnum.IS_REFUND_YES.getCode());
            resultBO.setRefundAmt(salesReturnBO.getPaidAmount().doubleValue());
        } else {
            resultBO.setIsRefund(VaspIsRefundEnum.IS_REFUND_NO.getCode());
            resultBO.setRefundAmt((double) 0);
        }
        // 系统参数
        resultBO.setTimestamp(DateTimeFormatter.ofPattern("yyyyMMddHHmmss").format(LocalDateTime.now()));
        resultBO.setSign(this.generateSign(resultBO.getOrderId(), resultBO.getTimestamp()));
        resultBO.setAgent_id(vaspConfig.getAgentId());

        String result = vaspFeign.orderResult(resultBO);
        JsonResult resultFormat = this.getResponseData(result);
        resultFormat.checkError();
    }

}
