package com.ets.apply.application.controller.third;

import com.ets.apply.application.app.business.amap.AmapBusiness;
import com.ets.apply.application.common.consts.amap.SubCodeEnum;
import com.ets.apply.application.common.vo.amap.BizResponseVO;
import com.ets.common.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;
@RequestMapping("/third/standard")
@RefreshScope
@RestController
@Slf4j
public class AmapController {

    @Autowired
    private AmapBusiness amapBusiness;

    /**
     *  写一个get 接口，路由无下级定义
     *  用于高德网关验证时对方进行请求
     */
    @GetMapping("")
    public JsonResult<?> get(@RequestParam Map<String, String> requestMap) throws Exception {
        return JsonResult.ok(requestMap);
    }

    @RequestMapping("/order/create")
    public BizResponseVO createOrder(@RequestParam Map<String, String> requestMap) throws Exception {
        return amapBusiness.createOrder(requestMap);
    }

    /**
     * 车牌是否可以办理检查
     */
    @RequestMapping(value = "/order/availableCheck",consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    public BizResponseVO availableCheck(@RequestParam Map<String, String> requestMap) throws Exception {
        try {
            return amapBusiness.availableCheck(requestMap);
        } catch (Exception e) {
            // 限流异常的捕捉
            log.error("availableCheck异常", e);
            return amapBusiness.failResponse(SubCodeEnum.FAIL.getCode(), "请求失败：" + e.getMessage());
        }
    }

    /**
     * 验签方法参考签名验签帮助文档 <a href="https://x-one.amap.com/docs/public/sign_verify">...</a>
     * 查询订单详情
     */
    @RequestMapping("/order/queryDetail")
    public BizResponseVO queryDetail(@RequestParam Map<String, String> requestMap) throws Exception {
        return amapBusiness.queryDetail(requestMap);
    }
}
