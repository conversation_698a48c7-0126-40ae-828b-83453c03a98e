package com.ets.apply.application.infra.entity;

import java.time.LocalDateTime;
import lombok.Data;
import lombok.experimental.Accessors;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

@Data
@Accessors(chain = true)
@TableName("etc_taxi_company_drivers")
public class TaxiEntrustEntity {
    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户id
     */
    private Integer uid;

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 许可证号
     */
    private String license;

    /**
     * 是否是首签司机
     */
    private Integer isFirstSign;

    /**
     * 卡id
     */
    private Integer cardId;

    /**
     * 是否已删除
     */
    private Integer isDeleted;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 签约状态
     */
    @TableField(exist = false)
    private Integer entrustStatus;

    /**
     * 签约ID
     */
    @TableField(exist = false)
    private Integer entrustId;

    /**
     * 签约时间
     */
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime operateTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
}
