package com.ets.apply.application.app.factory.productOrder.impl;

import com.ets.apply.application.app.business.ProductOrderBusiness;
import com.ets.apply.application.common.bo.productOrder.ProductOrderRefundBO;
import com.ets.apply.application.common.bo.productOrder.ProductOrderSaleAfterBO;
import com.ets.apply.application.common.bo.productOrder.ProductOrderShipBO;
import com.ets.apply.application.common.dto.request.productOrder.PaymentRefundDTO;
import com.ets.apply.application.infra.entity.ProductOrderEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;

public abstract class ProductOrderBase implements IProductOrder{
    @Autowired
    private ProductOrderBusiness productOrderBusiness;
    public String refund(ProductOrderRefundBO productOrderRefundBO){
        //默认模式，调用支付域进行退款
        if (!productOrderRefundBO.getPaymentSn().isEmpty()) {
            PaymentRefundDTO refundDTO = new PaymentRefundDTO();
            refundDTO.setPaymentSn(productOrderRefundBO.getPaymentSn());
            refundDTO.setReason(productOrderRefundBO.getRefundReason());
            return productOrderBusiness.paymentRefund(refundDTO);
        }
        return "";
    }

    public  void ship(ProductOrderShipBO productOrderShipBO){
    }

    public void saleAfter(ProductOrderSaleAfterBO productOrderSaleAfterBO){

    }

}
