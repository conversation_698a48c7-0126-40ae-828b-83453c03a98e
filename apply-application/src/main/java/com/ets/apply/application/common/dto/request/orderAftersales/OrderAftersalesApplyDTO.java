package com.ets.apply.application.common.dto.request.orderAftersales;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.PositiveOrZero;

@Data
public class OrderAftersalesApplyDTO {
    @NotNull(message = "申办订单号必填")
    private String orderSn;

    private Long uid;

    @PositiveOrZero(message = "售后类型不能为空")
    private Integer type;

    //短信验证码
    private String code;


    @Length(min = 0, max = 300, message = "申请原因请输入长度300以内的字符")
    private String reason = "";
}
