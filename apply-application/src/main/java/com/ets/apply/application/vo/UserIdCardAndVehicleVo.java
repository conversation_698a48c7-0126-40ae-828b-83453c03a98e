package com.ets.apply.application.vo;

import lombok.Data;

@Data
public class UserIdCardAndVehicleVo {

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 审核单号
     */
    private String reviewOrderSn;

    /**
     * 车牌号码
     */
    private String plateNo;

    /**
     * 姓名
     */
    private String name;

    /**
     * 身份证号码
     */
    private String idCardNo;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 发动机编号
     */
    private String engineNo;

    /**
     * 车辆品牌号
     */
    private String modelName;

    /**
     * 注册日期
     */
    private String firstRegisterDate;

    /**
     * 车主姓名
     */
    private String carOwnerName;

    /**
     * 车主姓名证件类型
     */
    private String ownerCredentialType;

    /**
     * 车主身份证号码
     */
    private String ownerCredentialNo;

}
