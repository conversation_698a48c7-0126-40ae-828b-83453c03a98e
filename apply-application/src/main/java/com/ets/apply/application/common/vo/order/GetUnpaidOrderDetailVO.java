package com.ets.apply.application.common.vo.order;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class GetUnpaidOrderDetailVO {
    private String orderSn;
    private Integer status;
    private AddressInfo addressInfo;

    /**
     * 总计需支付（元）
     */
    private BigDecimal needPay;

    private Integer purchaseParty;
    private Integer purchaseType;

    /**
     * 设备费（元）
     */
    private BigDecimal productFee;


    /**
     * 是否可继续申请
     */
    private Integer canContinueApply;

    /**
     * 产品包信息
     */
    private ProductPackage productPackage;


    @Data
    public static class AddressInfo {
        private String sendName;
        private String sendPhone;
        private String sendAddress;
        private String sendArea;
    }

    @Data
    public static class ProductPackage {
        /**
         * 产品包号
         */
        private String packageSn;
        /**
         * 产品包名称
         */
        private String packageName;

        /**
         * 产品包配置信息
         */
        private Object packageInfo;

    }

}
