package com.ets.apply.application.common.dto.external.chuanqi;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class ChuanQiCreateDTO {

    @NotBlank(message = "外部订单号不能为空")
    private String orderNo;

    @NotNull(message = "商品信息不能为空")
    @Valid
    private GoodsInfo goods;

    @NotNull(message = "收货信息不能为空")
    @Valid
    private ReceiveInfo receiveInfo;

    @Data
    public static class GoodsInfo {
        @NotBlank(message = "产品编号不能为空")
        private String threeGoodNo;

        @NotNull(message = "商品数量不能为空")
        @Min(value = 1, message = "商品数量不能小于1")
        @Max(value = 1, message = "商品数量不能大于1")
        private Integer num;

        private String treeNo;

        private String goodsName;
    }

    @Data
    public static class ReceiveInfo {
        @NotBlank(message = "收件人姓名不能为空")
        private String receiver;

        @NotBlank(message = "收件人手机号不能为空")
        private String receiverMobile;

        @NotBlank(message = "收件地址不能为空")
        private String address;
    }
}
