package com.ets.apply.application.common.vo.applyOrder;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class OrderAverageStatisticExcelVO {

    /**
     * 包sn
     */
    @ExcelProperty(value = "产品包编码")
    private String packageSn;

    /**
     * 包名
     */
    @ExcelProperty(value = "产品包名称")
    private String packageName;

    /**
     * 平均总数
     */
    @ExcelProperty(value = "订单总数")
    private Integer total;

    @ExcelProperty(value = "平均订单总数/天")
    private BigDecimal average;
    /**
     * 总计
     */
    @ExcelProperty(value = "区间订单总数")
    private Integer countTotal;

    /**
     * 涨跌幅
     */
    @ExcelProperty(value = "涨跌幅（%）")
    private BigDecimal variation = BigDecimal.ZERO;
}
