package com.ets.apply.application.common.consts.orderCancelApply;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CancelStatusEnum {

    STATUS_APPLY(2, "待取消"),
    STATUS_WAIT(4, "取消中"),
    STATUS_PASS(6, "取消成功"),
    STATUS_REJECTED(8, "取消失败"),;

    private final int code;
    private final String description;

    public static String getDescByCode(int code) {
        for (CancelStatusEnum node : CancelStatusEnum.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }
}
