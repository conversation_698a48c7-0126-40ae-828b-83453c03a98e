<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ets.apply.application.infra.mapper.TaxiCompanyDriverMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ets.apply.application.infra.entity.TaxiCompanyDriverEntity">
        <id column="id" property="id" />
        <result column="uid" property="uid" />
        <result column="virtual_uid" property="virtualUid" />
        <result column="plate_no" property="plateNo" />
        <result column="license" property="license" />
        <result column="apply_order_sn" property="applyOrderSn" />
        <result column="is_first_sign" property="isFirstSign" />
        <result column="is_activated" property="isActivated" />
        <result column="card_id" property="cardId" />
        <result column="company_id" property="companyId" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, uid, virtual_uid, plate_no, license, apply_order_sn, is_first_sign, is_activated, card_id, company_id, created_at, updated_at
    </sql>

</mapper>
