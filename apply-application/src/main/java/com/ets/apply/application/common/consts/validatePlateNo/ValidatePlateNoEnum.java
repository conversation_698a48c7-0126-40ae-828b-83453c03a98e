package com.ets.apply.application.common.consts.validatePlateNo;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ValidatePlateNoEnum {

    VALIDATE_STATUS_DEFAILT(0,"未知"),

    VALIDATE_STATUS_SUCCESS(1, "成功"),

    VALIDATE_STATUS_FAIL(2, "失败");

    private final int code;
    private final String description;

    public static String getDescByCode(int code) {
        for (ValidatePlateNoEnum node : ValidatePlateNoEnum.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }

}
