package com.ets.apply.application.common.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.common.collect.ImmutableMap;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Map;
import java.util.Optional;

@Data
public class UserCardVo {

    private Integer id;

    private Long uid;

    /**
     * 卡种（对应 etc_cards.id）
     */
    private Integer cardId;

    /**
     * 车牌号（冗余）
     */
    private String plateNo;

    /**
     * ETC通行卡号
     */
    private String cardNo;

    /**
     * OBU设备电子标签号
     */
    private String obuDeviceSn;

    /**
     * 第三方系统的账号
     */
    private String issuerAccount;

    /**
     * 申请订单流水号
     */
    private String applyOrderSn;

    /**
     * 最新
     */
    private String lastApplyOrderSn;

    private String flowTypeStr;

    /**
     * 其他申请中的订单流程类型
     */
    private String otherApplyFlowType;

    /**
     * 身份证信息 etc_users_idcards.id
     */
    private Integer idcardInfoId;

    /**
     * 行驶证信息 etc_users_vehicles.id
     */
    private Integer vehicleInfoId;

    /**
     * 已付保证金
     */
    private BigDecimal earnestMoney;

    /**
     * 卡片余额（元）
     */
    private BigDecimal surplusMoney;

    /**
     * 欠费总额（元）
     */
    private BigDecimal debtAmount;

    /**
     * 通行消费（元）
     */
    private BigDecimal tollAmount;

    /**
     * 通行次数
     */
    private Integer tollTimes;

    /**
     * 是否允许重新激活
     */
    private Integer allowReactivate;

    /**
     * 激活时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime activatedAt;

    /**
     * 卡片余额更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime surplusUpdatedAt;

    /**
     * 发起注销时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime voidAt;

    /**
     * 微行ETC注销时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime etcVoidAt;

    /**
     * 发卡方注销时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime issuerVoidAt;

    /**
     * 首次通行时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime firstTollAt;

    /**
     * 首次激活时间，二次激活不更新
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime firstActivatedAt;

    private Integer isDel;

    /**
     * 卡片状态 0:正常 2:挂失 3:补办中 4:作废 8:注销中 9:已注销
     */
    private Integer status;

    /**
     * 通行扣费支付渠道 1、我方自己发起的扣费 2、米大师发起的扣费 3、招行代扣
     */
    private Integer tollPayChannel;

    /**
     * 是否开启售后权限
     */
    private Integer serviceStatus;

    /**
     * 是否允许用户注销 0:不允许 1:允许
     */
    private Integer voidStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 设备商类型（埃特斯1、金溢2、聚力3、万集4、成谷5）
     */
    private Integer manufacturer;

    private Integer deviceType;

    /**
     * 营业执照信息 etc_users_biz_license.id
     */
    private Integer bizInfoId;

    /**
     * 车牌颜色：0、蓝色 1、黄色 2、黑色 3、白色 4、渐变绿色 5、黄绿双拼色 6、蓝白渐变色 7、临时牌照 9、未确定 11、绿色 12、红色
     */
    private Integer plateColor;

    /**
     * 债务乐观锁
     */
    private Integer debtVer;

    /**
     * 通行债务
     */
    private BigDecimal tollDebtAmount;

    /**
     * 结算周期：1日结2周结3月结
     */
    private Integer settleCycle;

    private String settleCycleStr;

    /**
     * 是否已修改签约[0-未改签 1-已改签]
     */
    private Integer hasChangeSign;

    /**
     * 记账卡 / 储值卡
     */
    private String cardTypeStr;

    /**
     * 状态显名称
     */
    private String statusStr;

    /**
     * 是否货车
     */
    private Boolean isTruck;

    /**
     * 需从行驶证表查询
     * 货车业务类型：0默认为空 1货车运政车
     */
    private Integer truckType;

    /**
     * 是否当前卡
     */
    private Boolean isCurrentCard = false;

    /**
     * 卡名称
     */
    private String cardIssuerName;

    private Integer issuerId;

    /**
     * 省份名称
     */
    private String province = "";

    /**
     * 质保期
     */
    private String warrantyTime;

    /**
     * 设备商类型（埃特斯1、金溢2、聚力3、万集4、成谷5）
     */
    private String manufacturerStr;

    /**
     * 设备类型 普通、可充电设备
     */
    private String deviceTypeStr;

    /**
     * OBU停用状态[0-启用 1-停用]
     */
    private Integer obuStopStatus;

    /**
     * 设备灵敏度 01-高灵敏度 02-中灵敏度 03-低灵敏度
     */
    private String sensLevel;

    /**
     * 设备停启用调整时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime obuStopChangedTime;

    /**
     * 设备灵敏度调整时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime sensLevelChangedTime;

    /**
     * 车主姓名
     */
    private String ownerName;

    private ServiceQuota serviceQuota;

    private Integer isFront;

    @Data
    public static class ServiceQuota {
        private Integer reactivate;
    }

    private UserInfo userInfo;

    @Data
    public static class UserInfo {

        /**
         * 下单人昵称
         */
        private String nickname;

        /**
         * 邮寄联系人
         */
        private String sendName;

        /**
         * 邮寄联系手机
         */
        private String sendPhone;

        /**
         * 邮寄地址
         */
        private String sendAddress;

        /**
         * 1个人车 2公司车
         */
        private String vehicleBelongStr;

        /**
         * 身份证类型
         */
        private String idcardTypeStr;

        /**
         * 身份证号
         */
        private String number;

        /**
         * 姓名
         */
        private String name;

        /**
         * 证据有效期
         */
        private String validDate;

        /**
         * 实名认证手机号
         */
        private String realPhone;

        /**
         * 实名认证身份证号
         */
        private String realNumber;

    }

    public String getObuStopStatusStr() {
        if (Arrays.asList(2, 10).contains(this.manufacturer) && Integer.valueOf(2).equals(this.deviceType)) {
            if (Integer.valueOf(1).equals(this.obuStopStatus)) {
                return "停用";
            } else {
                return "启用";
            }
        } else {
            return null;
        }
    }

    public String getSensLevelStr() {
       return ImmutableMap.of(
                "01", "高",
                "02", "初始化",
                "03", "低"
        ).getOrDefault(this.sensLevel, "初始化");
    }
}
