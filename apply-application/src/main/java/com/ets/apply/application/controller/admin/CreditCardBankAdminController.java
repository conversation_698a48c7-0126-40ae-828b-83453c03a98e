package com.ets.apply.application.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.apply.application.app.business.creditCardBank.CreditCardBankBusiness;
import com.ets.apply.application.common.dto.creditCardBank.CreditCardBankListDTO;
import com.ets.apply.application.common.dto.creditCardBank.CreditCardBankModifyDTO;
import com.ets.apply.application.common.dto.creditCardBank.CreditCardBankSetStatusDTO;
import com.ets.apply.application.common.dto.creditCardBank.CreditCardBankUpdateSortDTO;
import com.ets.apply.application.common.dto.productPackagePopup.PopupSetStatusDTO;
import com.ets.apply.application.common.vo.creditCardBank.CreditCardBankListVO;
import com.ets.common.JsonResult;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;

/**
 * <AUTHOR>
 */
@Controller
@RestController
@RequestMapping("/admin/credit-card-bank")
public class CreditCardBankAdminController {

    @Autowired
    private CreditCardBankBusiness creditCardBankBusiness;

    @RequestMapping("/get-list")
    @ResponseBody
    JsonResult<IPage<CreditCardBankListVO>> getList(@RequestBody @Valid CreditCardBankListDTO listDTO) {

        return JsonResult.ok(creditCardBankBusiness.getList(listDTO));
    }

    @RequestMapping("/get-detail")
    @ResponseBody
    JsonResult<?> getDetail(@RequestParam Integer id) {
        return JsonResult.ok(creditCardBankBusiness.getDetail(id));
    }


    @RequestMapping("/modify")
    @ResponseBody
    JsonResult<?> modify(@RequestBody @Valid CreditCardBankModifyDTO modifyDTO) {
        creditCardBankBusiness.modify(modifyDTO);

        return JsonResult.ok();
    }

    @RequestMapping("/set-status")
    JsonResult<?> setStatus(@RequestBody @Valid CreditCardBankSetStatusDTO setStatusDTO){
        creditCardBankBusiness.setStatus(setStatusDTO);
        return JsonResult.ok();
    }


    @RequestMapping("/get-select-options")
    @ResponseBody
    public JsonResult<HashMap<String, Object>> getSelectOptions() {

        return JsonResult.ok(creditCardBankBusiness.getSelectOptions());
    }

    @RequestMapping("/update-sort")
    JsonResult<?> updateSort(@RequestBody @Valid CreditCardBankUpdateSortDTO updateSortDTO){
        creditCardBankBusiness.updateSort(updateSortDTO);
        return JsonResult.ok();
    }


}
