package com.ets.apply.application.app.disposer;

import com.ets.apply.application.app.business.ProductOrderImportBusiness;
import com.ets.apply.application.common.dto.productOrder.ProductOrderImportDTO;
import com.ets.common.queue.BaseDisposer;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@NoArgsConstructor
@Component(value = "ProductOrderImportBatchJobBean")
@Slf4j
public class ProductOrderImportBatchDisposer extends BaseDisposer {

    @Autowired
    private ProductOrderImportBusiness productOrderImportBusiness;

    public ProductOrderImportBatchDisposer(Object params) {
        super(params);
    }

    @Override
    public String getJobBeanName() {
        return "ProductOrderImportBatchJobBean";
    }

    @Override
    public void execute(Object content) {

        ProductOrderImportDTO dto = super.getParamsObject(content, ProductOrderImportDTO.class);

        productOrderImportBusiness.executeBatch(dto.getBatchNo(), dto.getLoginCode());
    }
}
