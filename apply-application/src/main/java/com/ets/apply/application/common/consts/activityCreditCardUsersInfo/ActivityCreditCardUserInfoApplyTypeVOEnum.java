package com.ets.apply.application.common.consts.activityCreditCardUsersInfo;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum ActivityCreditCardUserInfoApplyTypeVOEnum {
    TYPE_APPLY("1", "新办"),
    TYPE_REAPPLY("2", "重办"),
    TYPE_UPGRADE("3", "设备升级");


    private final String code;
    private final String description;

    public static String getDescByCode(String code) {
        for (ActivityCreditCardUserInfoApplyTypeVOEnum node : ActivityCreditCardUserInfoApplyTypeVOEnum.values()) {
            if (node.getCode().equals(code)) {
                return node.getDescription();
            }
        }
        return "";
    }

    public static List<Map<String, String>> getLabelList() {
        List<Map<String, String>> list = new ArrayList<>();

        for (ActivityCreditCardUserInfoApplyTypeVOEnum node : ActivityCreditCardUserInfoApplyTypeVOEnum.values()) {
            Map<String, String> row = new LinkedHashMap<>();
            row.put("label", node.getDescription());
            row.put("value", String.valueOf(node.getCode()));

            list.add(row);
        }

        return list;
    }
}
