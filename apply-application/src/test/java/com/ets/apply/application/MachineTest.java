package com.ets.apply.application;

import com.ets.apply.application.app.business.SelfServiceMachineBusiness;
import com.ets.apply.application.app.thirdservice.feign.CallCenterMarketingFeign;
import com.ets.apply.application.common.dto.machine.MachineReadCardDTO;
import com.ets.apply.application.common.dto.request.machine.MachineCommandDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@SpringBootTest(classes = ApplyApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringRunner.class)
@ActiveProfiles(value = "dev")
public class MachineTest {

    @Autowired
    CallCenterMarketingFeign centerMarketingFeign;

    @Autowired
    SelfServiceMachineBusiness machineBusiness;

    @Test
    public void command() {
        MachineCommandDTO commandDTO = new MachineCommandDTO();
        commandDTO.setCmd("F1");
        commandDTO.setDeviceNo("J107580");
        String resultStr = centerMarketingFeign.command(commandDTO);
        System.out.println(resultStr);
    }

    @Test
    public void readCard() {
        MachineReadCardDTO dto = new MachineReadCardDTO();
        dto.setDeviceNo("J107580");
        dto.setIssuerId(1);
        machineBusiness.readCard(dto);
    }
}
