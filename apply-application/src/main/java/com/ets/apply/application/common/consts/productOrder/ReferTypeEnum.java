package com.ets.apply.application.common.consts.productOrder;

import com.ets.apply.application.common.consts.taskRecord.TaskRecordReferTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ReferTypeEnum {

    DEFAULT(0, "默认无渠道"),

    CREDIT_CARD_APPLY_TYPE(1, "关联信用卡申办"),
    THIRD_PARTNER_TYPE(2, "第三方合作");

    private final Integer code;

    private final String description;


    /**
     * 对应的task 任务类型
     * @param referType
     * @return
     */
    public TaskRecordReferTypeEnum taskTypeMapEnum(ReferTypeEnum referType) {
        TaskRecordReferTypeEnum recordReferTypeEnum = null;
        switch (referType) {
            case THIRD_PARTNER_TYPE:
                return TaskRecordReferTypeEnum.TASK_THIRD_PARTNER_SHIP_NOTIFY;
            case DEFAULT:
            case CREDIT_CARD_APPLY_TYPE:
                return null;
        }
        return recordReferTypeEnum;
    }

    /**
     * 通过referType 获取枚举
     * @param code
     * @return
     */
    public static ReferTypeEnum getReferTypeEnumByType(Integer code) {
        for (ReferTypeEnum referTypeEnum : ReferTypeEnum.values()) {
            if (referTypeEnum.getCode().equals(code)) {
                return referTypeEnum;
            }
        }
        return null;
    }
}
