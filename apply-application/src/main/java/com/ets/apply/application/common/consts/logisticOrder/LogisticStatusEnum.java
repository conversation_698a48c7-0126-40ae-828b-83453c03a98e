package com.ets.apply.application.common.consts.logisticOrder;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum LogisticStatusEnum {

    DEFAULT(1, "未发货"),

    WAITING_SHIP(2, "待揽收"),

    ON_THE_WAY(3, "在途"),

    DELIVERY(4, "送货中"),

    SIGNED(5, "已签收"),

    REJECTED(6, "拒收"),

    ABNORMAL(7, "异常件");

    private final int code;
    private final String description;

    public static String getDescByCode(int code) {
        for (LogisticStatusEnum node : LogisticStatusEnum.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }
}
