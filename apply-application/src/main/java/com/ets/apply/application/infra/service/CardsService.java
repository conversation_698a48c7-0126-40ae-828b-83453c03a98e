package com.ets.apply.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.apply.application.infra.entity.CardsEntity;
import com.ets.apply.application.infra.mapper.CardsMapper;
import lombok.extern.slf4j.Slf4j;
import com.ets.common.ToolsHelper;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 卡片种类 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-07
 */
@Service
@Slf4j
@DS("db-etc")
public class CardsService extends BaseService<CardsMapper, CardsEntity> {

    private final CardsMapper cardsMapper;

    public CardsService(CardsMapper cardsMapper) {
        this.cardsMapper = cardsMapper;
    }

    public CardsEntity getByCardId(Integer id) {
        return super.baseMapper.selectById(id);
    }

    public List<Integer> getAllTruckIds() {
        Wrapper<CardsEntity> wrapper = Wrappers.<CardsEntity>lambdaQuery()
                .select(CardsEntity::getId)
                .eq(CardsEntity::getIsTruck, 1);
        List<CardsEntity> cardList = cardsMapper.selectList(wrapper);
        List<Integer> truckIds = new ArrayList<>();
        if (ObjectUtils.isEmpty(cardList)) {
            return truckIds;
        }

        cardList.forEach(card -> {
            truckIds.add(card.getId());
        });

        return truckIds;
    }

    /**
     * 获取所有卡车id
     * 缓存300s
     */
    @Cacheable(value = "getAllTruckIdsWithCache#7200", key = "'truckIds'", unless = "#result.isEmpty()")
    public List<Integer> getAllTruckIdsWithCache() {

            LambdaQueryWrapper<CardsEntity> wrapper = Wrappers.<CardsEntity>lambdaQuery()
                    .select(CardsEntity::getId)
                    .eq(CardsEntity::getIsTruck, 1);
            // 使用Stream API简化列表转换
            return cardsMapper.selectList(wrapper)
                    .stream()
                    .map(CardsEntity::getId)
                    .collect(Collectors.toList());

    }

    public List<CardsEntity> getByCardIds(List<Integer> cardIds) {
        LambdaQueryWrapper<CardsEntity> wrapper = Wrappers.<CardsEntity>lambdaQuery()
                .in(CardsEntity::getId, cardIds);
        return super.baseMapper.selectList(wrapper);
    }
    public List<CardsEntity> getListByIds(List<Integer> ids) {
        return super.baseMapper.selectBatchIds(ids);
    }

    /*
     * 根据card_id获取 发卡方编码
     */
    public String getIssuerCodeById(Integer id) {
        CardsEntity  cardsEntity = super.baseMapper.selectById(id);
        if(cardsEntity == null){
            ToolsHelper.throwException("card_id"+id+"不存在");
        }
        return cardsEntity.getClassName();
    }
}
