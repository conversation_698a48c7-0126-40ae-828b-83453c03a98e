package com.ets.apply.application.common.consts.validatePlateNo;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum ValidatePlateNoTermEnum {

    NORMAL("normal", "普通申请");

    private final String term;
    private final String description;

    public static String getDescByCode(String term) {
        for (ValidatePlateNoTermEnum node : ValidatePlateNoTermEnum.values()) {
            if (Objects.equals(node.term, term)) {
                return node.getDescription();
            }
        }

        return "";
    }

}
