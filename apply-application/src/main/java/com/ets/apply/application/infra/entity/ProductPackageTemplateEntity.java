package com.ets.apply.application.infra.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * <p>
 * 产品包模板
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("product_package_template")
public class ProductPackageTemplateEntity extends BaseEntity<ProductPackageTemplateEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 模板名称
     */
    private String name;

    /**
     * 模板内容json格式
     */
    private String content;

    /**
     * 状态：1-待上架 2-上架，3-下架
     */
    private Integer status;

    /**
     * 操作者
     */
    private String operator;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
