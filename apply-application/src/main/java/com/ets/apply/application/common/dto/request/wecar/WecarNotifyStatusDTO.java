package com.ets.apply.application.common.dto.request.wecar;

import lombok.Data;

import jakarta.validation.constraints.NotNull;

@Data
public class WecarNotifyStatusDTO {
    @NotNull(message = "腾讯出⾏⽤户标识不能为空")
    private String userCode;
    /*
     *服务商订单号
     */
    @NotNull(message = "服务商订单号不能为空")
    private String outOrderId;
    /*
     *状态 ⽀付成功:SUCCESS ⽀付失败关闭:CLOSED
     */
    @NotNull(message = "订单状态不能为空")
    private Integer status;

    /*
     * 物流状态 1: 待发货 2: 已发货 3: 已送达
     */
    private Integer logisticsStatus;

    /*
     *服务商订单号
     */
    @NotNull(message = "状态变更时间不能为空")
    private Long actionTime;
    /*
     *⽀付平台交易号（⽀付通知时必传）
     */
    @NotNull(message = "⻋牌号不能为空")
    private String vehicleNo;
    /*
     *⽀付⾦额，分（⽀付通知时必传）
     */
    @NotNull(message = "服务商订单状态不能为空")
    private String spOrderStatus;

}
