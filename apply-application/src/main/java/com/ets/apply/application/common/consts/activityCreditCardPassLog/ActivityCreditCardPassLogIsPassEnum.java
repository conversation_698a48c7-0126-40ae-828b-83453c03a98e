package com.ets.apply.application.common.consts.activityCreditCardPassLog;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ActivityCreditCardPassLogIsPassEnum {

    PASS_YES(1, "审核通过"),
    PASS_NOT(2, "审核未通过");

    private final int code;
    private final String description;

    public static String getDescByCode(int code) {
        for (ActivityCreditCardPassLogIsPassEnum node : ActivityCreditCardPassLogIsPassEnum.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }

}
