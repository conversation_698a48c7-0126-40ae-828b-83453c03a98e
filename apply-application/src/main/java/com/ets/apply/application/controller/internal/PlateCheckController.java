package com.ets.apply.application.controller.internal;

import com.alibaba.fastjson.JSONObject;
import com.ets.apply.application.app.business.ValidatePlateNoBusiness;
import com.ets.apply.application.common.dto.request.PlateCheckQueryDto;
import com.ets.apply.application.common.vo.PlateCheckQueryVO;
import com.ets.apply.application.controller.BaseController;
import com.ets.common.BizException;
import com.ets.common.JsonResult;
import com.ets.common.RequestHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <p>
 * 批量车牌+颜色查询校验
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-17
 */
@RequestMapping("/plateCheck")
@RefreshScope
@RestController
@Slf4j
public class PlateCheckController extends BaseController {
    @Autowired
    private ValidatePlateNoBusiness validatePlaeNoBusiness;
    @Autowired
    private HttpServletRequest request;

    /**
     * 根据查询的车牌号+颜色，返回对应的结果列表
     * @return
     * @throws BizException
     */
    @RequestMapping("/getList")
    public JsonResult<List<PlateCheckQueryVO>> getList() throws BizException {
        Object body = RequestHelper.getPostData(request);
        PlateCheckQueryDto plateCheckQueryDto = JSONObject.toJavaObject(JSONObject.parseObject(JSONObject.parseObject(body.toString()).get("requestData").toString()), PlateCheckQueryDto.class);
        plateCheckQueryDto.setTerm(JSONObject.parseObject(body.toString()).get("companyId").toString());
        plateCheckQueryDto.setPageSize(1000);
        return JsonResult.ok(validatePlaeNoBusiness.plateNosCheck(plateCheckQueryDto));
    }
}
