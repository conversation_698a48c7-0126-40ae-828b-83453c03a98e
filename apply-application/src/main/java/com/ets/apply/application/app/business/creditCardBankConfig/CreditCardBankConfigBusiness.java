package com.ets.apply.application.app.business.creditCardBankConfig;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.ets.apply.application.common.dto.creditCardBank.CreditCardBankSetStatusDTO;
import com.ets.apply.application.common.dto.creditCardBankConfig.CreditCardBankConfigSetStatusDTO;
import com.ets.apply.application.common.vo.creditCardBankConfig.CreditCardBankConfigListVO;
import com.ets.apply.application.infra.entity.CreditCardBankConfigEntity;
import com.ets.apply.application.infra.entity.CreditCardBankEntity;
import com.ets.apply.application.infra.service.CreditCardBankConfigService;
import com.ets.common.RequestHelper;
import com.ets.common.ToolsHelper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Component
public class CreditCardBankConfigBusiness {

    @Autowired
    private CreditCardBankConfigService creditCardBankConfigService;
    public List<CreditCardBankConfigListVO> getList(){

        List<CreditCardBankConfigEntity> list = creditCardBankConfigService.getAll();
        if(CollectionUtil.isEmpty(list)){
            return new ArrayList<>();
        }
        List<CreditCardBankConfigListVO> voList = new ArrayList<>();
        for(CreditCardBankConfigEntity entity:list){
            CreditCardBankConfigListVO vo = new CreditCardBankConfigListVO();
            BeanUtils.copyProperties(entity,vo);
            voList.add(vo);
        }
        return voList;

    }

    public void setStatus(CreditCardBankConfigSetStatusDTO setStatusDTO) {
        CreditCardBankConfigEntity entity = creditCardBankConfigService.getById(setStatusDTO.getId());
        if (ObjectUtil.isNull(entity)) {
            ToolsHelper.throwException("未找到该数据，请确认后重新提交");
        }
        if (entity.getBankStatus().equals(setStatusDTO.getBankStatus())) {
            return;
        }
        entity.setBankStatus(setStatusDTO.getBankStatus());
        entity.setOperator(RequestHelper.getAdminOperator());
        entity.setUpdatedAt(LocalDateTime.now());
        creditCardBankConfigService.updateById(entity);
    }

}
