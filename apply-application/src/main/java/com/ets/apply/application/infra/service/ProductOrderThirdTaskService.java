package com.ets.apply.application.infra.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.apply.application.common.consts.productOrderThirdTask.ProductOrderThirdTaskResultStatusEnum;
import com.ets.apply.application.infra.entity.ProductOrderEntity;
import com.ets.apply.application.infra.entity.ProductOrderThirdTaskEntity;
import com.ets.apply.application.infra.mapper.ProductOrderThirdTaskMapper;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 商城单第三方任务 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-17
 */
@Service
public class ProductOrderThirdTaskService extends BaseService<ProductOrderThirdTaskMapper,
        ProductOrderThirdTaskEntity> {

    /**
     * 获取未完成的第三方任务
     */
    public List<ProductOrderThirdTaskEntity> getUnfinishedTasks(Integer thirdType,LocalDateTime startTime, LocalDateTime endTime) {
        Wrapper<ProductOrderThirdTaskEntity> wrapper = Wrappers.<ProductOrderThirdTaskEntity>lambdaQuery()
                .eq(ProductOrderThirdTaskEntity::getThirdType, thirdType)
                .gt(ProductOrderThirdTaskEntity::getCreatedAt, startTime)
                .lt(ProductOrderThirdTaskEntity::getCreatedAt, endTime)
                .in(ProductOrderThirdTaskEntity::getResultStatus,
                        Arrays.asList(ProductOrderThirdTaskResultStatusEnum.DEFAULT.getValue(),
                                ProductOrderThirdTaskResultStatusEnum.PROGRESS.getValue()))
                .orderByDesc(ProductOrderThirdTaskEntity::getCreatedAt)
                .last("limit 1000");

        return this.baseMapper.selectList(wrapper);
    }

    public ProductOrderThirdTaskEntity getByProductOrderSn(String productOrderSn) {
        return this.baseMapper.selectOne(Wrappers.<ProductOrderThirdTaskEntity>lambdaQuery()
                .eq(ProductOrderThirdTaskEntity::getProductOrderSn, productOrderSn));
    }
}
