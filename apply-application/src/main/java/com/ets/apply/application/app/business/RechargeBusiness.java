package com.ets.apply.application.app.business;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ets.apply.application.app.service.php.CallIssuerService;
import com.ets.apply.application.common.bo.recharge.*;
import com.ets.apply.application.common.consts.recharge.RechargeIssuerRaiseStatusEnum;
import com.ets.apply.application.common.consts.recharge.RechargeLogTypeEnum;
import com.ets.apply.application.common.consts.recharge.RechargeRecordPayStatusEnum;
import com.ets.apply.application.common.consts.recharge.RechargeWriteStatusEnum;
import com.ets.apply.application.common.consts.writeCard.*;
import com.ets.apply.application.common.vo.response.issuer.*;
import com.ets.apply.application.infra.entity.CardConsumeRecords;
import com.ets.apply.application.infra.entity.RechargeRecord;
import com.ets.apply.application.infra.entity.RechargeWriteCard;
import com.ets.apply.application.infra.service.*;
import com.ets.common.ToolsHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Slf4j
@Component
public class RechargeBusiness {
    @Autowired
    RechargeRecordService rechargeRecordService;

    @Autowired
    RechargeLogService rechargeLogService;

    @Autowired
    RechargeWriteCardService writeCardService;

    @Autowired
    RechargeWriteCardLogService writeCardLogService;

    @Autowired
    CardConsumeRecordsService cardConsumeRecordsService;

    @Autowired
    CallIssuerService issuerService;

    public RechargeRecord getRecordByOrderSn(String orderSn) {
        return rechargeRecordService.getOneByOrderSn(orderSn);
    }

    public RechargeWriteCard getWriteCardByOrderSn(String orderSn) {
        return writeCardService.getOneByOrderSn(orderSn);
    }

    /**
     * 上传交易记录
     * @param record 充值记录
     * @param recordList 交易记录
     */
    public void cardConsumeRecord(RechargeRecord record, List<ConsumeRecordBO> recordList) {
        // 删除旧记录
        Map<String, Object> deleteMap = new HashMap<>();
        deleteMap.put("order_sn", record.getOrderSn());
        cardConsumeRecordsService.removeByMap(deleteMap);

        // 插入新纪录
        LocalDateTime now = LocalDateTime.now();
        List<CardConsumeRecords> records = new ArrayList<>();
        recordList.forEach(r -> {
            CardConsumeRecords consumeRecords = new CardConsumeRecords();
            BeanUtil.copyProperties(r, consumeRecords);
            consumeRecords.setUid(record.getUid());
            consumeRecords.setOrderSn(record.getOrderSn());
            consumeRecords.setCardSn(record.getCardSn());
            consumeRecords.setCreatedAt(now);
            consumeRecords.setUpdatedAt(now);
            records.add(consumeRecords);
        });
        cardConsumeRecordsService.saveBatch(records);
    }

    /**
     * 获取密钥
     * @param record 充值记录
     * @param writeCard 写卡记录
     * @param macBO 机器mac1
     * @return 卡方mac2
     */
    public WriteCardSecretKeyBO getSecretKey(RechargeRecord record, RechargeWriteCard writeCard, WriteCardMacBO macBO) {
        WriteCardSecretKeyBO secretKeyBO = new WriteCardSecretKeyBO();

        try {
            BigDecimal ebBalance = (new BigDecimal(macBO.getBalance()))
                    .divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
            // 初始化一条圈存写卡记录
            if (ObjectUtils.isEmpty(writeCard)) {
                writeCard = new RechargeWriteCard();
                writeCard.setOrderSn(record.getOrderSn());
                writeCard.setUid(record.getUid());
                writeCard.setCardId(record.getIssuerId());
                writeCard.setCardSn(record.getCardSn());
                writeCard.setRaiseValue(record.getRaiseValue());
                writeCard.setEbbalance(ebBalance);
                writeCard.setTerminalNo(macBO.getTerminalNo());
                writeCard.setSerialNo(macBO.getSerialNo());
                writeCard.setWriteCardTimes(0);
                writeCard.setResult(WriteCardResultEnum.DEFAULT.getValue());
                writeCard.setCreatedAt(LocalDateTime.now());
                writeCard.setUpdatedAt(LocalDateTime.now());
                writeCardService.save(writeCard);
                log.info("【圈存】【获取密钥】初始化写卡记录：{}", writeCard);
            }

            // 请求圈存密钥
            LocalDateTime now = LocalDateTime.now();
            WriteCardApplyBO applyBO = new WriteCardApplyBO();
            applyBO.setRechargeRecord(record)
                    .setBalance(macBO.getBalance())
                    .setRandomValue(macBO.getRand())
                    .setSerialNo(macBO.getSerialNo())
                    .setTerminalNo(macBO.getTerminalNo())
                    .setTradeTime(now.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
            IssuerWriteCardApplyVO applyVO = issuerService.writeCardApply(applyBO);
            log.info("【圈存】【获取密钥】writeCardApply：{}", applyVO);

            // 更新圈存记录
            BigDecimal writeCardValue = (new BigDecimal(applyVO.getWriteCardAmount()))
                    .divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
            LambdaUpdateWrapper<RechargeWriteCard> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(RechargeWriteCard::getId, writeCard.getId())
                    .set(RechargeWriteCard::getSerialNo, macBO.getSerialNo())
                    .set(RechargeWriteCard::getTradeTime, now)
                    .set(RechargeWriteCard::getSecretKey, applyBO.getTradeTime() + applyVO.getWriteCardSecretKey())
                    .set(RechargeWriteCard::getIssuerTradeNo, applyVO.getWriteCardAcceptNo())
                    .set(RechargeWriteCard::getResult, WriteCardResultEnum.BEGIN_WRITE.getValue())
                    .set(RechargeWriteCard::getWriteCardTimes, writeCard.getWriteCardTimes() + 1)
                    .set(RechargeWriteCard::getWriteCardValue, writeCardValue)
                    .set(RechargeWriteCard::getUpdatedAt, LocalDateTime.now());
            writeCardService.updateByWrapper(updateWrapper);

            // 记录充值日志
            RechargeLogBO rechargeLogBO = new RechargeLogBO();
            rechargeLogBO.setOrderSn(record.getOrderSn());
            rechargeLogBO.setCardSn(record.getCardSn());
            rechargeLogBO.setUid(record.getUid());
            rechargeLogBO.setPostData(JSON.toJSONString(macBO));
            rechargeLogBO.setReturnMsg("密钥请求成功");
            rechargeLogBO.setType(RechargeLogTypeEnum.GET_SECRET_KEY.getValue());
            rechargeLogBO.setStatus(1);
            rechargeLogService.addLog(rechargeLogBO);

            // 记录写卡日志
            WriteCardLogBO writeCardLogBO = new WriteCardLogBO();
            writeCardLogBO.setOrderSn(record.getOrderSn());
            writeCardLogBO.setUid(record.getUid());
            writeCardLogBO.setOperation("自助机读卡，请求密钥成功，卡内余额" + ebBalance + "元。");
            writeCardLogService.addLog(writeCardLogBO);

            secretKeyBO.setTradeTime(applyBO.getTradeTime());
            secretKeyBO.setSecretKey(applyVO.getWriteCardSecretKey());
        } catch (Throwable e) {
            log.error("【圈存】【获取密钥】异常：{}", e.getLocalizedMessage());
            // 记录充值日志
            RechargeLogBO rechargeLogBO = new RechargeLogBO();
            rechargeLogBO.setOrderSn(record.getOrderSn());
            rechargeLogBO.setCardSn(record.getCardSn());
            rechargeLogBO.setUid(record.getUid());
            rechargeLogBO.setPostData(JSON.toJSONString(macBO));
            rechargeLogBO.setReturnMsg(e.getLocalizedMessage());
            rechargeLogBO.setType(RechargeLogTypeEnum.GET_SECRET_KEY.getValue());
            rechargeLogService.addLog(rechargeLogBO);

            // 记录写卡日志
            WriteCardLogBO writeCardLogBO = new WriteCardLogBO();
            writeCardLogBO.setOrderSn(record.getOrderSn());
            writeCardLogBO.setUid(record.getUid());
            writeCardLogBO.setOperation("请求圈存密钥失败，错误信息：" + e.getLocalizedMessage());
            writeCardLogService.addLog(writeCardLogBO);

            ToolsHelper.throwException(e.getLocalizedMessage());
        }
        return secretKeyBO;
    }

    /**
     * 处理写卡异常情况
     * @param record 充值记录
     * @param writeCard 写卡记录
     * @param macBO 机器mac1
     * @param consumeList 交易记录
     * @return boolean 处理结果
     */
    public Boolean handleWriteCard(RechargeRecord record, RechargeWriteCard writeCard, WriteCardMacBO macBO, List<ConsumeRecordBO> consumeList) {
        // 查卡状态
        IssuerCheckCardStateVO cardStateVO = issuerService.checkCardState(record.getCardSn());
        // 车牌颜色不一致 以卡方接口返回为准
        if (!record.getPlateColor().equals(cardStateVO.getPlateColor())) {
            LambdaUpdateWrapper<RechargeRecord> updateRecord = new LambdaUpdateWrapper<>();
            updateRecord.eq(RechargeRecord::getId, record.getId())
                    .set(RechargeRecord::getPlateColor, cardStateVO.getPlateColor())
                    .set(RechargeRecord::getUpdatedAt, LocalDateTime.now());
            rechargeRecordService.updateByWrapper(updateRecord);
        }

        // 未知结果 查询卡方
        if (writeCard.getResult().equals(WriteCardResultEnum.BEGIN_WRITE.getValue())) {
            Integer checkResult = checkWriteStatus(record, writeCard, macBO);

            // 圈存成功
            if (checkResult.equals(IssuerOrderStatusEnum.SUCCESS.getValue())) {
                return true;
            }
        }

        // 圈存初始化
        Integer initResult = handleWriteCardInit(record, macBO, consumeList);

        // 初始化异常处理
        if (initResult.equals(WriteCardInitResultEnum.ERROR.getValue())) {
            Integer exceptionResult = handleWriteCardException(record, writeCard, macBO, consumeList);

            // 异常处理成功
            if (exceptionResult.equals(WriteCardExceptionResultEnum.CONFIRM.getValue())) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查卡方订单状态
     * @param record 充值记录
     * @param writeCard 写卡记录
     * @param macBO 机器mac1
     * @return 卡方状态
     */
    public Integer checkWriteStatus(RechargeRecord record, RechargeWriteCard writeCard, WriteCardMacBO macBO) {
        IssuerRechargeOrderCheckVO checkVO = issuerService.rechargeOrderCheck(record);
        Integer orderStatus = checkVO.getOrderStatus();

        // 圈存成功
        if (orderStatus.equals(IssuerOrderStatusEnum.SUCCESS.getValue())) {
            BigDecimal balance = new BigDecimal(macBO.getBalance()).divide(new BigDecimal(100), RoundingMode.HALF_UP);
            // 记录写卡日志
            WriteCardLogBO writeCardLogBO = new WriteCardLogBO();
            writeCardLogBO.setOrderSn(record.getOrderSn());
            writeCardLogBO.setUid(record.getUid());
            writeCardLogBO.setOperation("圈存写卡成功，发卡方充值查询返回【已圈存】，金额" + record.getRaiseValue() + "元，充值后卡内余额" + balance + "元。");
            writeCardLogService.addLog(writeCardLogBO);
        }

        // 冲正、退款
        if (Arrays.asList(IssuerOrderStatusEnum.REVERSE.getValue(), IssuerOrderStatusEnum.REFUND.getValue())
                .contains(orderStatus)) {
            // 修改充值记录
            LambdaUpdateWrapper<RechargeRecord> updateRecord = new LambdaUpdateWrapper<>();
            updateRecord.eq(RechargeRecord::getId, record.getId())
                    .set(RechargeRecord::getPayStatus, RechargeRecordPayStatusEnum.REFUNDED.getValue())
                    .set(RechargeRecord::getIssuerRaiseStatus, RechargeIssuerRaiseStatusEnum.CANCELED.getValue())
                    .set(RechargeRecord::getRefundType, 1)
                    .set(RechargeRecord::getUpdatedAt, LocalDateTime.now());
            rechargeRecordService.updateByWrapper(updateRecord);

            // 修改写卡记录
            writeCardFail(writeCard, macBO);

            // 记录写卡日志
            WriteCardLogBO writeCardLogBO = new WriteCardLogBO();
            writeCardLogBO.setOrderSn(record.getOrderSn());
            writeCardLogBO.setUid(record.getUid());
            writeCardLogBO.setOperation("该笔充值订单通行宝已做了冲正退款处理，状态更新为已退款。我方不需再发起退款。");
            writeCardLogService.addLog(writeCardLogBO);

            ToolsHelper.throwException("充值订单已退款，请重新发起充值圈存");
        }

        return orderStatus;
    }

    /**
     * 圈存初始化
     * @param record 充值记录
     * @param macBO 机器mac1
     * @param consumeList 交易记录
     * @return 初始化结果
     */
    public Integer handleWriteCardInit(RechargeRecord record, WriteCardMacBO macBO, List<ConsumeRecordBO> consumeList) {
        // 初始化
        String lastData = "0";
        if (ObjectUtils.isNotEmpty(consumeList) && consumeList.size() > 0) {
            ConsumeRecordBO lastRecord = consumeList.get(0);
            LocalDateTime time = LocalDateTime.parse(lastRecord.getInTransTime(), DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            lastData = time.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        WriteCardInitBO initBO = new WriteCardInitBO();
        initBO.setRechargeRecord(record);
        initBO.setBalance(macBO.getBalance());
        initBO.setSerialNo(macBO.getSerialNo());
        initBO.setLastData(lastData);
        IssuerWriteCardInitVO initVO = issuerService.writeCardInit(initBO);

        // 处理结果
        // 正常 可圈存金额大于0
        if (initVO.getCanDepositAmount() > 0) {
            return WriteCardInitResultEnum.NORMAL.getValue();
        }

        // 正常 圈存异常金额小于等于0
        if (initVO.getAbnormalMoney() <= 0) {
            return WriteCardInitResultEnum.NORMAL.getValue();
        }

        // 异常
        return WriteCardInitResultEnum.ERROR.getValue();
    }

    /**
     * 圈存异常处理
     * @param record 充值记录
     * @param writeCard 写卡记录
     * @param macBO 机器mac1
     * @param consumeList 交易记录
     * @return 异常处理结果
     */
    public Integer handleWriteCardException(RechargeRecord record, RechargeWriteCard writeCard, WriteCardMacBO macBO, List<ConsumeRecordBO> consumeList) {
        IssuerWriteCardExceptionVO exceptionVO = issuerService.writeCardExceptionHandling(record, consumeList);
        Integer handlingResult = exceptionVO.getHandlingResult();

        if (handlingResult.equals(WriteCardExceptionResultEnum.CONFIRM.getValue())) {
            BigDecimal balance = new BigDecimal(macBO.getBalance()).divide(new BigDecimal(100), RoundingMode.HALF_UP);
            // 记录写卡日志
            WriteCardLogBO writeCardLogBO = new WriteCardLogBO();
            writeCardLogBO.setOrderSn(record.getOrderSn());
            writeCardLogBO.setUid(record.getUid());
            writeCardLogBO.setOperation("圈存写卡成功，发卡方返回【确认圈存】，金额" + record.getRaiseValue() + "元，充值后卡内余额" + balance + "元。");
            writeCardLogService.addLog(writeCardLogBO);
        } else if (handlingResult.equals(WriteCardExceptionResultEnum.CANCEL.getValue())) {
            // 更新写卡失败
            writeCardFail(writeCard, macBO);

            // 记录写卡日志
            WriteCardLogBO writeCardLogBO = new WriteCardLogBO();
            writeCardLogBO.setOrderSn(record.getOrderSn());
            writeCardLogBO.setUid(record.getUid());
            writeCardLogBO.setOperation("圈存写卡失败，发卡方返回【取消圈存】，可通知用户重新操作圈存。");
            writeCardLogService.addLog(writeCardLogBO);

        } else if (handlingResult.equals(WriteCardExceptionResultEnum.MANUAL_HANDLE.getValue())) {
            // 记录写卡日志
            WriteCardLogBO writeCardLogBO = new WriteCardLogBO();
            writeCardLogBO.setOrderSn(record.getOrderSn());
            writeCardLogBO.setUid(record.getUid());
            writeCardLogBO.setOperation("圈存写卡状态不明确，发卡方返回【需人工处理】，可通知用户联系江苏通行宝（客服电话：02596777）或请到通行宝线下网点处理。");
            writeCardLogService.addLog(writeCardLogBO);

            ToolsHelper.throwException("存在充值异常订单，请到通行宝线下网点处理或联系江苏通行宝客服。客服电话：02596777");
        }

        return handlingResult;
    }

    /**
     * 写卡成功
     * @param successBO 参数
     */
    public void writeCardSuccess(WriteCardSuccessBO successBO) {
        // 更新充值记录
        RechargeRecord record = successBO.getRechargeRecord();
        LambdaUpdateWrapper<RechargeRecord> updateRecord = new LambdaUpdateWrapper<>();
        updateRecord.eq(RechargeRecord::getId, record.getId())
                .set(RechargeRecord::getIsWriteCard, RechargeWriteStatusEnum.SUCCESS.getValue())
                .set(RechargeRecord::getUpdatedAt, LocalDateTime.now());
        rechargeRecordService.updateByWrapper(updateRecord);

        // 更新写卡记录
        RechargeWriteCard writeCard = successBO.getWriteCard();
        BigDecimal balance = (new BigDecimal(successBO.getBalance())).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);

        LambdaUpdateWrapper<RechargeWriteCard> updateWriteCard = new LambdaUpdateWrapper<>();
        updateWriteCard.eq(RechargeWriteCard::getId, writeCard.getId())
                .set(RechargeWriteCard::getEabalance, balance)
                .set(RechargeWriteCard::getTac, successBO.getTac())
                .set(RechargeWriteCard::getSerialNoAfter, successBO.getSerialNo())
                .set(RechargeWriteCard::getResult, WriteCardResultEnum.SUCCESS.getValue())
                .set(RechargeWriteCard::getUpdatedAt, LocalDateTime.now());
        writeCardService.updateByWrapper(updateWriteCard);
    }

    /**
     * 写卡失败
     * @param writeCard 写卡记录
     * @param macBO 机器mac1
     */
    public void writeCardFail(RechargeWriteCard writeCard, WriteCardMacBO macBO) {
        // 更新写卡失败
        LambdaUpdateWrapper<RechargeWriteCard> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(RechargeWriteCard::getId, writeCard.getId())
                .set(RechargeWriteCard::getTerminalNo, macBO.getTerminalNo())
                .set(RechargeWriteCard::getSerialNo, macBO.getSerialNo())
                .set(RechargeWriteCard::getResult, WriteCardResultEnum.FAIL.getValue())
                .set(RechargeWriteCard::getUpdatedAt, LocalDateTime.now());
        writeCardService.updateByWrapper(updateWrapper);
    }

    /**
     * 圈存成功通知
     * @param record 充值记录
     * @param writeCard 写卡记录
     * @param needNotify 是否需要通知
     */
    public void writeCardNotify(RechargeRecord record, RechargeWriteCard writeCard, boolean needNotify) {
        try {
            if (needNotify) {
                // 通知卡方
                WriteCardNotifyBO notifyBO = new WriteCardNotifyBO();
                notifyBO.setRechargeRecord(record);
                notifyBO.setWriteCard(writeCard);
                IssuerWriteCardNotifyVO notifyVO = issuerService.writeCardNotify(notifyBO);

                // 记录充值日志
                RechargeLogBO rechargeLogBO = new RechargeLogBO();
                rechargeLogBO.setOrderSn(record.getOrderSn());
                rechargeLogBO.setCardSn(record.getCardSn());
                rechargeLogBO.setUid(record.getUid());
                rechargeLogBO.setPostData(JSON.toJSONString(notifyBO));
                rechargeLogBO.setReturnMsg(JSON.toJSONString(notifyVO));
                rechargeLogBO.setType(RechargeLogTypeEnum.WRITE_COMPLETE.getValue());
                rechargeLogBO.setStatus(1);
                rechargeLogService.addLog(rechargeLogBO);
            }

            // 更新写卡记录
            LambdaUpdateWrapper<RechargeWriteCard> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(RechargeWriteCard::getId, writeCard.getId())
                    .set(RechargeWriteCard::getNotifyResult, WriteCardNotifyResultEnum.SUCCESS.getValue())
                    .set(RechargeWriteCard::getUpdatedAt, LocalDateTime.now());
            writeCardService.updateByWrapper(updateWrapper);

            // 写卡日志
            WriteCardLogBO writeCardLogBO = new WriteCardLogBO();
            writeCardLogBO.setUid(writeCard.getUid());
            writeCardLogBO.setOperation(writeCard.getOrderSn());
            writeCardLogBO.setOperation("圈存写卡成功，金额" + writeCard.getRaiseValue() + "元，充值后卡内余额" + writeCard.getEabalance() + "元。");
            writeCardLogService.addLog(writeCardLogBO);

        } catch (Throwable e) {
            LambdaUpdateWrapper<RechargeWriteCard> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(RechargeWriteCard::getId, writeCard.getId())
                    .set(RechargeWriteCard::getNotifyResult, WriteCardNotifyResultEnum.FAIL.getValue())
                    .set(RechargeWriteCard::getUpdatedAt, LocalDateTime.now());
            writeCardService.updateByWrapper(updateWrapper);

            // 记录充值日志
            RechargeLogBO rechargeLogBO = new RechargeLogBO();
            rechargeLogBO.setOrderSn(record.getOrderSn());
            rechargeLogBO.setCardSn(record.getCardSn());
            rechargeLogBO.setUid(record.getUid());
            rechargeLogBO.setPostData(JSON.toJSONString(record));
            rechargeLogBO.setReturnMsg(e.getLocalizedMessage());
            rechargeLogBO.setType(RechargeLogTypeEnum.WRITE_COMPLETE.getValue());
            rechargeLogService.addLog(rechargeLogBO);
        }
    }
}
