package com.ets.apply.application.controller.internal;

import com.ets.apply.application.app.business.creditCard.CgbBusiness;
import com.ets.apply.application.common.dto.bank.*;
import com.ets.apply.application.common.vo.bank.CgbOpenQueryVO;
import com.ets.apply.application.common.vo.bank.CgbSm2VO;
import com.ets.apply.application.common.vo.bank.CgbVerifyCodeVO;
import com.ets.apply.application.controller.BaseController;
import com.ets.common.JsonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;


/**
 * 广发开放平台国密加密接口
 * <AUTHOR>
 * @date 2022-04-28
 */
@RequestMapping("/gfo")
@RefreshScope
@RestController
public class CgbController extends BaseController {

	@Autowired
	private CgbBusiness cgbBusiness;

	/**
	 * sm2签免获取
	 * @param dto：
	 * @return:
	 */
	@PostMapping("/sm2Sign")
	public JsonResult<?> sm2Sign(@RequestBody CgbSmDto dto) {
		return JsonResult.ok (cgbBusiness.sign (dto));
	}

	/**
	 * sm2加密
	 * @param dto：
	 * @return: 对象
	 */
	@PostMapping("/sm2Encrypt")
	public JsonResult<?> sm2Encrypt(@RequestBody CgbSmDto dto) {
		return JsonResult.ok (cgbBusiness.sm2Encrypt (dto));
	}

	/**
	 * 国密SM4解密
	 * @param dto:
	 * @return
	 */
	@PostMapping("/sm4Decrypt")
	public JsonResult<?> sm4Decrypt(@RequestBody CgbSm4Dto dto) {
		return JsonResult.ok (cgbBusiness.sm4Decrypt (dto));
	}


	@PostMapping("/verify")
	public JsonResult<String> verify(@RequestBody CgbVerifyDTO dto) {
		return JsonResult.ok(cgbBusiness.verify(dto));
	}

	/**
	 * 国密2加密字符串
	 * @param dto
	 * @return
	 */
	@PostMapping("/sm2EncryptStr")
	public JsonResult<CgbSm2VO> sm2EncryptStr(@RequestBody @Valid CgbSm2DTO dto) {
		return JsonResult.ok(cgbBusiness.sm2EncryptStr(dto));
	}

	@PostMapping("/openQuery")
	public JsonResult<CgbOpenQueryVO> openQuery(@RequestBody @Valid CgbOpenQueryDTO openQueryDTO) {
	    return JsonResult.ok(cgbBusiness.openQuery(openQueryDTO));
	}

	@PostMapping("/verifyCode")
	public JsonResult<CgbVerifyCodeVO> verifyCode(@RequestBody @Valid CgbVerifyCodeDTO verifyCodeDTO) {
	    return JsonResult.ok(cgbBusiness.verifyCode(verifyCodeDTO));
	}
}
