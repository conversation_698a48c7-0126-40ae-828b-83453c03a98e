package com.ets.apply.application.common.vo.creditCardBank;

import lombok.Data;

import java.util.List;

@Data
public class CreditCardBankInfoListVO {
    private Integer id;

    /**
     * 银行id
     */
    private Integer bankId;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 标签
     */
    private List<Object> tagList;

    /**
     * 主权益
     */
    private String welfare;

    /**
     * 权益说明
     */
    private List<Object> welfareDetail;

    /**
     * 附加信息
     */
    private  Object extraInfo;


    /**
     * 银行类型[1-申办 2-活动]
     */
    private Integer bankType;

    private Integer bankStatus;
}
