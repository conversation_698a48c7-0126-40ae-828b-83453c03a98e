package com.ets.apply.application.controller.frontend;

import com.ets.apply.application.app.business.ApplyGuideBusiness;
import com.ets.apply.application.common.vo.applyGuide.ApplyGuideMapVO;
import com.ets.apply.application.common.vo.applyGuide.ApplyGuideResultVO;
import com.ets.apply.application.common.vo.applyGuide.ApplyGuideQuestionGroupVO;
import com.ets.apply.application.common.dto.QuestionGroupRequestDTO;
import com.ets.common.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/frontend/apply-guide")
@Validated
public class ApplyGuideController {

    @Autowired
    private ApplyGuideBusiness applyGuideBusiness;

    @PostMapping("/get-question-map")
    public JsonResult<List<ApplyGuideMapVO>> getQuestionMap(@RequestParam(required = false) Integer mapType) {
        return JsonResult.ok(applyGuideBusiness.getQuestionMap(mapType));
    }

    @PostMapping("/get-result-map")
    public JsonResult<List<ApplyGuideResultVO>> getResultMap(@RequestParam(required = false) Integer mapType) {
        return JsonResult.ok(applyGuideBusiness.getResultMap(mapType));
    }

    @PostMapping("/get-question-group-list")
    public JsonResult<List<ApplyGuideQuestionGroupVO>> getQuestionGroupList(@Valid @RequestBody QuestionGroupRequestDTO request) {
        List<ApplyGuideQuestionGroupVO> result = applyGuideBusiness.getQuestionGroupList(request.getMapType(), request.getQuestionTypeList());
        return JsonResult.ok(result);
    }
}

