package com.ets.apply.application;

import com.alibaba.fastjson.JSON;
import com.ets.apply.application.app.business.ApplyConfigBusiness;
import com.ets.apply.application.app.business.EventBusiness;
import com.ets.apply.application.app.business.ProductOrderBusiness;
import com.ets.apply.application.app.business.wecar.WecarBusiness;
import com.ets.apply.application.app.thirdservice.feign.CallWecarFeign;
import com.ets.apply.application.common.config.WecarConfig;
import com.ets.apply.application.common.dto.event.EventTriggerDTO;
import com.ets.apply.application.common.dto.request.applyConfig.ApplyConfigSetDto;
import com.ets.apply.application.common.dto.request.productOrder.ProductOrderShipDTO;
import com.ets.apply.application.common.dto.request.wecar.*;
import com.ets.common.ToolsHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.hibernate.validator.HibernateValidator;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


@SpringBootTest(classes = ApplyApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringRunner.class)
@ActiveProfiles(value = "dev")
@Slf4j
@RefreshScope
public class WecarTest {
    @Autowired
    private WecarConfig wecarConfig;
    @Autowired
    private static Validator validator;
     static {
         validator = Validation.byProvider(HibernateValidator.class).configure().failFast(true)
             .buildValidatorFactory().getValidator();
     }
    @Autowired
    private ApplyConfigBusiness applyConfigBusiness;
    @Autowired
    private WecarBusiness wecarBusiness;
    @Autowired
    private CallWecarFeign callWecarFeign;
    @Autowired
    ProductOrderBusiness productOrderBusiness;
    @Autowired
    private EventBusiness eventBusiness;
    @Test
    public void config() {
        try {
            String configParams = "[{\"aliasName\":\"一代 ETC\",\"icon\":\"xxxx.png\",\"originalPrice\":199,\"presentPrice\":99,\"cardProviderId\":\"gx\",\"cardProviderName\":\"江苏速通\",\"id\":\"CardPackage00698\",\"obuType\":\"DOUBLE\"},{\"aliasName\":\"二代-mini ETC\",\"icon\":\"\",\"originalPrice\":299,\"presentPrice\":180,\"cardProviderId\":\"gx\",\"cardProviderName\":\"江苏速通\",\"id\":\"CardPackage00698\",\"obuType\":\"SINGLE\"}]";
            ApplyConfigSetDto applyConfigSetDto = new ApplyConfigSetDto();
            applyConfigSetDto.setParams(configParams);
            applyConfigSetDto.setDomainKey(wecarBusiness.wecarDomainKey);
            applyConfigBusiness.setConfig(applyConfigSetDto);
        } catch (Exception e) {
            ToolsHelper.throwException(e.getMessage());
        }

        System.out.println(999);
    }

    @Test
    public void getList() {
        System.out.println(wecarBusiness.getProductList().toString());
    }

    @Test
    public void addOrder() {
        try {
            WecarAddOrderDTO dto = new WecarAddOrderDTO();
            dto.setVehicleNo("粤A7NN10");
            dto.setReceiverAddress("广东省广州市番禺区南怡大街5号");
            dto.setReceiver("何生");
            dto.setPlateColor(0);
            dto.setMobile("***********");
            dto.setApplyProductId("CarPackage00500");
            dto.setUserCode("12345654");
            System.out.println(wecarBusiness.addOrder(dto).toString());
        } catch (Exception e) {
            System.out.println("999:"+e.getStackTrace());
        }

    }
    @Test
    public void paid() {
        try {
           //  Long t1= 9900L;
          //   BigDecimal t2 =  new BigDecimal("99.00");
           // System.out.println(t2.longValue());
            Date date = new  Date();
            WecarNotifyPaidDTO dto = new WecarNotifyPaidDTO();
            dto.setCompanyId("SYSTEM");
            dto.setSpOrderId("2310271730000001041");
            dto.setPayTime(date.getTime());
            dto.setTotalFee(9900L);
            dto.setStatus("SUCCESS");
            dto.setTradeNo("tradeNo12346");
            System.out.println(wecarBusiness.notifyPaid(dto).toString());
        } catch (Exception e) {
            System.out.println("999:"+e.getMessage());
        }

    }

    @Test
    public void refund() {
        try {
            //  Long t1= 9900L;
            //   BigDecimal t2 =  new BigDecimal("99.00");
            // System.out.println(t2.longValue());
            Date date = new  Date();
            WecarNotifyRefundDTO dto = new WecarNotifyRefundDTO();
            dto.setCompanyId("SYSTEM");
            dto.setSpOrderId("2310271730000001041");
            dto.setRefundTime(date.getTime());
            dto.setRefundFee(9900L);
            dto.setStatus("SUCCESS");
            dto.setOutRefundNo("tradeNo12346");
            System.out.println(wecarBusiness.notifyRefund(dto).toString());
        } catch (Exception e) {
            System.out.println("999:"+e.getMessage());
        }

    }

    @Test
    public void getOrderList() {
        try {
            WecarGetOrderListDTO dto = new WecarGetOrderListDTO();
            dto.setCompanyId("SYSTEM");
            dto.setUserCode("12345654");
            System.out.println(wecarBusiness.getOrderList(dto).toString());
        } catch (Exception e) {
            System.out.println("999:"+e.getMessage());
        }
        System.out.println("end");
    }

    @Test
    public void cancelOrder() {
        try {
            WecarCancelOrderDTO dto = new WecarCancelOrderDTO();
            dto.setCompanyId("SYSTEM");
            dto.setUserCode("12345654");
            dto.setSpOrderId("2310271730000001041");
            System.out.println(wecarBusiness.cancelOrder(dto).toString());
        } catch (Exception e) {
            System.out.println("999:"+e.getMessage());
        }
        System.out.println("end");
    }

    @Test
    public void modifyOrder() {
        try {
            WecarModifyOrderDTO dto = new WecarModifyOrderDTO();
            dto.setCompanyId("SYSTEM");
            dto.setMobile("12345654");
            dto.setReceiverAddress("广东省广州市番禺区南怡大街5号");
            dto.setReceiver("12345654");
            dto.setSpOrderId("2310271730000001041");
            System.out.println(wecarBusiness.modifyOrder(dto).toString());
        } catch (Exception e) {
            System.out.println("999:"+e.getMessage());
        }
        System.out.println("end");
    }

    @Test
    public void notifyStatus() {
        try {
            int ts = (int)(System.currentTimeMillis()/1000);
            String apiSercret= "3b9344934a974cbfb1a4";
            Map<String,Object> macMap = new HashMap<>();
            macMap.put("api_key","fedb992b8ab145e6b187");
            macMap.put("seq_id","fd5d72b6-7e64-4319-bd8e-7b33756b2cb0");
            macMap.put("timestamp",ts);
            macMap.put("nonce","2ae81c4c-2");
            macMap.put("seqId","fd5d72b6-7e64-4319-bd8e-7b33756b2cb0");
            macMap.put("userCode","d07fc4f573be4b06854139e252a3e953");
            macMap.put("outOrderId","2310271730000001041");
            macMap.put("status",34);
            macMap.put("logisticsStatus",2);
            macMap.put("actionTime",ts);
            macMap.put("vehicleNo","闽D8A67J");
            macMap.put("spOrderStatus","CREATED");
            String signStr = "/etcOrder/statusSync?"+ksortAndBuild(macMap)+apiSercret;
            System.out.println(signStr);
            System.out.println(md5(signStr).toUpperCase());
            macMap.put("sig",md5(signStr).toUpperCase());
            System.out.println(callWecarFeign.statusSync(URI.create(wecarConfig.getNotifyUrl()),macMap));
        } catch (Exception e) {
            System.out.println("999:"+e.getMessage());
        }
        System.out.println("end");
    }
    /*
     *  排序并且构造签名字符串
     */
    private static String ksortAndBuild(Map<String,Object> macMap) throws UnsupportedEncodingException {
        Set<String> keySet = macMap.keySet();
        String[] keyArray = keySet.toArray(new String[0]);
        Arrays.sort(keyArray);
        StringBuilder sb = new StringBuilder();
        for(int i =0; i<keyArray.length;i++){
            if(String.valueOf(macMap.get(keyArray[i])).length()>0){
                //String values = URLEncoder.encode(macMap.get(keyArray[i]).toString(),"UTF-8");
                String values = macMap.get(keyArray[i]).toString();
                if((i+1) == keyArray.length ){
                    sb.append(keyArray[i]).append("=").append(values);
                }else{
                    sb.append(keyArray[i]).append("=").append(values).append("&");
                }

            }
        }
        return sb.toString();
    }

    public static String md5(String source) {
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            byte[] md5byte = md5.digest(source.getBytes("UTF-8"));
            StringBuffer strHexString = new StringBuffer();
            for (int i = 0; i < md5byte.length; i++) {
                String hex = Integer.toHexString(0xff & md5byte[i]);
                if (hex.length() == 1) {
                    strHexString.append('0');
                }
                strHexString.append(hex);
            }
            return strHexString.toString();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

    }

    @Test
    public void gwin() {
        try {
            Map<String,Object> requestMap = new HashMap<>();
            requestMap.put("companyId","wecar");
            requestMap.put("nonce","1234565412311");
            requestMap.put("timestamp","20231108184640");
//            requestMap.put("vehicleNo","粤A7NN11");
//            requestMap.put("receiverAddress","广东省广州市番禺区南怡大街5号");
//            requestMap.put("receiver","何生");
//            requestMap.put("applyProductId","CarPackage00500");
//            requestMap.put("mobile","***********");

            requestMap.put("spOrderId","2311081843000001081");
            requestMap.put("userCode","12345654");


            requestMap.put("token",getSign(requestMap,"mUJZNdaImttQDopQ"));
            System.out.println(requestMap.toString());
        } catch (Exception e) {
            System.out.println("999:"+e.getMessage());
        }

    }


    /**
     * 根据Map key进行升序排序
     *
     * @param map 待处理map
     * @return 处理结果
     */
    private static Map<String, Object> sortMapByKeyToAsc(Map<String, Object> map) {
        if (map == null || map.isEmpty()) {
            return Collections.emptyMap();
        }

        return map.entrySet().stream()
            .sorted(Map.Entry.comparingByKey())
            .filter(mapItem -> !org.springframework.util.StringUtils.isEmpty(mapItem.getValue()))
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,
                (oldValue, newValue) -> oldValue, LinkedHashMap::new));
    }

    /**
     * 字符串转成小写
     *
     * @param plaintext 字符串
     * @return String
     */
    public static String encode(String plaintext) {
        return DigestUtils.md5Hex(plaintext.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * 生成签名
     *
     * @param paramMap body参数转成map
     * @param secret   签名密钥
     * @return String
     */
    public static String getSign(Map<String, Object> paramMap, String secret) {
        Map<String, Object> signMap = sortMapByKeyToAsc(paramMap);
        StringBuilder sbA = new StringBuilder();
        for (Map.Entry<String, Object> map : signMap.entrySet()) {
            sbA.append(map.getKey()).append("=").append(map.getValue()).append("&");
        }
        sbA.append("secret=").append(secret);
        log.info("签名字符串：{}", sbA);
        return encode(sbA.toString()).toLowerCase();
    }
    /*
     * 发货通知
     */
    @Test
    public void shipNotify() {
        try {
            ProductOrderShipDTO dto = new ProductOrderShipDTO();
            dto.setOrderSn("2312181738000001211");
            dto.setExpressCorp("京东快递");
            dto.setExpressNumber("123456789");
            dto.setExpressTime(LocalDateTime.now());
            productOrderBusiness.ship(dto);
            System.out.println("finish");
        } catch (Exception e) {
            System.out.println("999:"+e.getMessage());
        }
        System.out.println("end");
    }

    @Test
    public void task() {
        try {
            System.out.println(wecarConfig.toString());

            //TaskFactory.create("activated_notify_wecar").execute("2312211540000002121");
            System.out.println("finish");
        } catch (Exception e) {
            System.out.println("999:"+e.getMessage());
        }
        System.out.println("end");
    }

    @Test
    public void actived() {
        try {
            EventTriggerDTO dto = new EventTriggerDTO();
            dto.setEventBeanName("orderActivatedEventBean");
            String str = "{\"orderSn\":\"231208095900262011A1\"}";
            dto.setParams(JSON.parseObject(str));
            eventBusiness.trigger(dto.getEventBeanName(), dto.getParams());
            System.out.println("finish");
        } catch (Exception e) {
            System.out.println("999:"+e.getMessage());
        }
        System.out.println("end");
    }
}
