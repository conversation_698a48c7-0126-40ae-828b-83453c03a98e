package com.ets.apply.application.app.business.creditCard;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ets.apply.application.app.service.common.notice.TemplateMsgService;
import com.ets.apply.application.common.bo.creditCard.CreditCardLogBO;
import com.ets.apply.application.common.consts.activityCreditCardOperateLog.CreditCardOperateLogTypeEnum;
import com.ets.apply.application.common.consts.activityCreditCardUsersInfo.ActivityCreditCardUserInfoReferTypeEnum;
import com.ets.apply.application.common.consts.activityCreditCardUsersInfo.ActivityCreditCardUserInfoStatusEnum;
import com.ets.apply.application.common.consts.notice.MessageTypeEnum;
import com.ets.apply.application.common.consts.notice.MinaAppCodeEnum;
import com.ets.apply.application.common.consts.notice.MinaTemplateEnum;
import com.ets.apply.application.infra.entity.ActivityCreditCardUsersInfoEntity;
import com.ets.apply.application.infra.service.ActivityCreditCardLogService;
import com.ets.apply.application.infra.service.ActivityCreditCardUsersInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class CreditCardUsersInfoBusiness {

    @Autowired
    private ActivityCreditCardUsersInfoService activityCreditCardUsersInfoService;

    @Autowired
    private TemplateMsgService templateMsgService;

    @Autowired
    private ActivityCreditCardLogService creditCardLogService;



    /**
     * 申请提交
     */
    public void applySubmit(ActivityCreditCardUsersInfoEntity activityCreditCardUsersInfoEntity) {
        // 重复通知状态未变化不进行修改
        if (!activityCreditCardUsersInfoEntity.getStatus().equals(ActivityCreditCardUserInfoStatusEnum.STATUS_WAIT.getCode())) {
            log.info(activityCreditCardUsersInfoEntity.getOrderSn() + "信用卡状态非待定状态不进行修改为：进件");
            return;
        }
        LambdaUpdateWrapper<ActivityCreditCardUsersInfoEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ActivityCreditCardUsersInfoEntity::getId, activityCreditCardUsersInfoEntity.getId())
                .set(ActivityCreditCardUsersInfoEntity::getStatus, ActivityCreditCardUserInfoStatusEnum.STATUS_NOT_AUDIT.getCode());
        activityCreditCardUsersInfoService.updateByWrapper(wrapper);
    }

    public void auditRefuse(ActivityCreditCardUsersInfoEntity usersInfoEntity,
                            String failReason) {
        // 未审核通过的单才能进行驳回操作
        if (!Arrays.asList(
                ActivityCreditCardUserInfoStatusEnum.STATUS_WAIT.getCode(),
                ActivityCreditCardUserInfoStatusEnum.STATUS_CANCEL.getCode(),
                ActivityCreditCardUserInfoStatusEnum.STATUS_NOT_AUDIT.getCode(),
                ActivityCreditCardUserInfoStatusEnum.STATUS_PASS_AUDIT.getCode()
        ).contains(usersInfoEntity.getStatus())) {
            log.info(usersInfoEntity.getOrderSn() + " 订单非未审核通过，不进行驳回操作");
            return;
        }
        LambdaUpdateWrapper<ActivityCreditCardUsersInfoEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ActivityCreditCardUsersInfoEntity::getId, usersInfoEntity.getId())
                .set(ActivityCreditCardUsersInfoEntity::getStatus, ActivityCreditCardUserInfoStatusEnum.STATUS_FAIL_AUDIT.getCode())
                .set(ActivityCreditCardUsersInfoEntity::getFailVerifyReason, failReason);
        activityCreditCardUsersInfoService.updateByWrapper(wrapper);

        // 记录日志审核不通过
        String content = "我方审核拒绝，拒绝原因：" + failReason;
        CreditCardLogBO logBO = new CreditCardLogBO();
        logBO.setOrderSn(usersInfoEntity.getOrderSn());
        logBO.setOperateType(CreditCardOperateLogTypeEnum.TYPE_AUDIT.getValue());
        logBO.setOperateContent(content);
        creditCardLogService.addLog(logBO);

        // 发送小程序通知审核不通过
        this.auditFailNotice(
                usersInfoEntity.getUid(),
                usersInfoEntity.getPlateNo(),
                usersInfoEntity.getReferType()
        );

    }

    /**
     * 面签拒绝时驳回掉
     * @param usersInfoEntity
     * @param failReason
     */
    public void refuse(ActivityCreditCardUsersInfoEntity usersInfoEntity,
                       String failReason) {
        // 未审核通过的单才能进行驳回操作
        if (!Arrays.asList(
                ActivityCreditCardUserInfoStatusEnum.STATUS_WAIT.getCode(),
                ActivityCreditCardUserInfoStatusEnum.STATUS_CANCEL.getCode(),
                ActivityCreditCardUserInfoStatusEnum.STATUS_NOT_AUDIT.getCode(),
                ActivityCreditCardUserInfoStatusEnum.STATUS_PASS_AUDIT.getCode()
        ).contains(usersInfoEntity.getStatus())) {
            log.info(usersInfoEntity.getOrderSn() + " 订单非未审核通过，不进行驳回操作");
            return;
        }
        LambdaUpdateWrapper<ActivityCreditCardUsersInfoEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ActivityCreditCardUsersInfoEntity::getId, usersInfoEntity.getId())
                .set(ActivityCreditCardUsersInfoEntity::getStatus, ActivityCreditCardUserInfoStatusEnum.STATUS_FAIL_AUDIT.getCode())
                .set(ActivityCreditCardUsersInfoEntity::getFailVerifyReason, failReason);
        activityCreditCardUsersInfoService.updateByWrapper(wrapper);


        // 发送小程序通知审核不通过
        this.auditFailNotice(
                usersInfoEntity.getUid(),
                usersInfoEntity.getPlateNo(),
                usersInfoEntity.getReferType()
        );

    }




    public void auditFailNotice(Long uid, String plateNo, Integer referType) {
        // 申办类型才需要主动发消息
        if (referType != ActivityCreditCardUserInfoReferTypeEnum.TYPE_NEW_APPLY.getCode()) {
            return;
        }
        Map<String, Object> keywords = new HashMap<>();
        keywords.put("car_number1", plateNo);
        keywords.put("thing2", "信用卡状态更新");
        keywords.put("thing4", "点此查看最新进度");
        templateMsgService.sendMinaNotice(uid, "pages/newIndex/page/newGoodsDetail", MinaTemplateEnum.SERVICE_STATUS_NOTICE_LONG_TERM.getTemplate(),
                MinaAppCodeEnum.ETC_ASSISTANT, MessageTypeEnum.PERMANENT, keywords);
    }


    public void auditPass(ActivityCreditCardUsersInfoEntity activityCreditCardUsersInfoEntity, String innerVersion) {
        // 重复通知状态未变化不进行修改
        // 未审核、无需审核、审核失败、等待审核状态允许通知审核通过
        if (!Arrays.asList(
                ActivityCreditCardUserInfoStatusEnum.STATUS_CANCEL.getCode(),
                ActivityCreditCardUserInfoStatusEnum.STATUS_NOT_AUDIT.getCode(),
                ActivityCreditCardUserInfoStatusEnum.STATUS_FAIL_AUDIT.getCode(),
                ActivityCreditCardUserInfoStatusEnum.STATUS_WAIT.getCode()
        ).contains(activityCreditCardUsersInfoEntity.getStatus())) {
            return;
        }
        // 允许修改的状态才做变更
        LambdaUpdateWrapper<ActivityCreditCardUsersInfoEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ActivityCreditCardUsersInfoEntity::getId, activityCreditCardUsersInfoEntity.getId())
                .set(ActivityCreditCardUsersInfoEntity::getStatus, ActivityCreditCardUserInfoStatusEnum.STATUS_PASS_AUDIT.getCode())
                .set(ActivityCreditCardUsersInfoEntity::getInnerVersion, innerVersion) // 新版本接口内部版本补偿记录
                .set(ActivityCreditCardUsersInfoEntity::getFailVerifyReason, "");
        activityCreditCardUsersInfoService.updateByWrapper(wrapper);

    }


    /**
     * 新户激活标记
     */
    public void newUserActivate(ActivityCreditCardUsersInfoEntity usersInfoEntity) {
        // 重复通知状态未变化不进行修改
        // 允许银行在激活状态未到最高返佣状态前重复通知
        if (Arrays.asList(
                ActivityCreditCardUserInfoStatusEnum.STATUS_QUALIFICATION_FINISHED.getCode(),
                ActivityCreditCardUserInfoStatusEnum.STATUS_CREDIT_CARD_ACTIVATE.getCode()
        ).contains(usersInfoEntity.getStatus())) {
            return;
        }

        // 新用户激活标记状态修改到新户激活状态
        LambdaUpdateWrapper<ActivityCreditCardUsersInfoEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ActivityCreditCardUsersInfoEntity::getId, usersInfoEntity.getId())
                .set(ActivityCreditCardUsersInfoEntity::getStatus, ActivityCreditCardUserInfoStatusEnum.STATUS_CREDIT_CARD_ACTIVATE.getCode())
                .set(ActivityCreditCardUsersInfoEntity::getFailVerifyReason, "")
        ;
        activityCreditCardUsersInfoService.updateByWrapper(wrapper);

    }


}
