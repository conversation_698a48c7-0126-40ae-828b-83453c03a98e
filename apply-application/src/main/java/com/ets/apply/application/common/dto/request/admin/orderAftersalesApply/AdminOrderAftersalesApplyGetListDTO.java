package com.ets.apply.application.common.dto.request.admin.orderAftersalesApply;

import jakarta.validation.constraints.Min;
import lombok.Data;

@Data
public class AdminOrderAftersalesApplyGetListDTO {
    private String applySn = "";
    private String orderSn = "";
    private String plateNo = "";
    private String plateColor = "";
    private Long uid;
    //申请类型1设备补办2设备更换3退货退款4仅取消5仅退款
    private Integer type;
    //申办业务状态
    private Integer status;
    //商品订单业务状态
    private Integer businessStatus;

    private String createTimeBegin;

    private String createTimeEnd;

    @Min(1)
    private Integer pageNum = 1;

    @Min(1)
    private Integer pageSize = 20;

}
