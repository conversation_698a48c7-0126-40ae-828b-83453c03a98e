package com.ets.apply.application.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@RefreshScope
@Configuration
@Data
@ConfigurationProperties(prefix = "params.amap")
public class AmapConfig {

    /**
     * etc 助手私钥
     */
    private String etcRsaPrivateKey;

    /**
     * etc 助手公钥
     */
    private String etcRsaPublicKey;

    /**
     * 高德公钥
     */
    private String amapPublicKey;

    private String appId;
    private String shopId;

    private String url;


    private String key;

    private String refundMethod;

    private String orderSyncMethod;


}
