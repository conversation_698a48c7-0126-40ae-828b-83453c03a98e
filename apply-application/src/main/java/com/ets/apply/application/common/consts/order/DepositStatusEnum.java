package com.ets.apply.application.common.consts.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
@AllArgsConstructor
public enum DepositStatusEnum {
    NONE(0, "没有保证金"),
    USED(1, "使用保证金"),
    REFUND(2, "保证金已退款");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;

    static {
        DepositStatusEnum[] enums = DepositStatusEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
