package com.ets.apply.application.app.thirdservice.response.user;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class UserDepositVO {

    /**
     * 用于后续数据同步，业务中禁止使用
     */
    private Integer id;

    /**
     * 主键
     */
    private String userDepositSn;

    /**
     * 用户id
     */
    private Long uid;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 订单类型, 1:申办订单, 2:改签订单
     */
    private Integer orderType;

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 车牌颜色 0: 蓝色 ；1: 黄色 ；2: 黑色 ；3: 白色 ；4: 渐变绿色 ；5: 黄绿双拼色 ；6: 蓝白渐变色 ；7: 临时牌照 ；9: 未确定 11: 绿色【不用】 12: 红色
     */
    private Integer plateColor;

    /**
     * 保证金金额
     */
    private BigDecimal depositAmount;

    /**
     * 支出金额
     */
    private BigDecimal expendAmount;

    /**
     * 账户状态[1-正常 2-冻结 3-注销 4-退款]
     */
    private Integer status;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updatedAt;
}
