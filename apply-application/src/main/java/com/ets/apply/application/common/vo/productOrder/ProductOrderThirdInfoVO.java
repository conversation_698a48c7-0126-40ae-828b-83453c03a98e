package com.ets.apply.application.common.vo.productOrder;
import lombok.Data;

@Data
public class ProductOrderThirdInfoVO {

    /*
     * ⻋牌号
     */
    private String vehicleNo;
    private Integer status;
    /*
     * 发⾏商id
     */
    private String cardProviderId;
    /*
     * 发⾏商名称
     */
    private String cardProviderName;
    private Long createTime;
    private String spOrderId;
    private String applyProductId;
    private String applyProductName;
    private String transCompany;
    private String transOrderNo;
    /*
     * 物流状态 1: 待发货
     *        2: 已发货
     *        3: 已送达
     */
    private Integer logisticsStatus;

    private String receiver;
    private String mobile;
    private String receiverAddress;

    private Long payTime;
    private Long actualAmount;
    private String productImg;
}
