package com.ets.apply.application.common.consts.productPackage;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
@AllArgsConstructor
public enum DeviceTypeEnum {

    NORMAL(0, "普通", "一代-经典ETC"),
    CHARGEABLE(1, "可充电", "一代-可充电设备"),
    MONOLITHIC(2, "单片式", "二代-mini ETC");

    private final Integer value;
    private final String desc;
    private final String name;
    public static final Map<Integer, String> map;
    public static final Map<Integer, String> nameMap;

    static {
        DeviceTypeEnum[] enums = DeviceTypeEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
        nameMap = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getName()),
                Map::putAll);
    }


    public static String getDescByCode(int code) {
        for (DeviceTypeEnum node : DeviceTypeEnum.values()) {
            if (node.getValue().equals(code)) {
                return node.getDesc();
            }
        }
        return "";
    }
}
