package com.ets.apply.application.common.consts.map;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MapStatusEnum {
    STAUTS_NORMAL(1, "正常"),
    STATUS_FAIL(2, "无效");
    private final int code;
    private final String description;

    public static String getDescByCode(int code) {
        for (MapStatusEnum node : MapStatusEnum.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }
}
