package com.ets.apply.application.common.consts.amap;

import com.ets.apply.application.common.consts.productOrder.ProductOrderStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;


@Getter
@AllArgsConstructor
public enum OrderStatusEnum {
    // 订单状态
    //5000: 已支付
    //5300: 待使用
    //5200：下单失败
    //5400: 退款中
    //8000：已使用
    //8100:已关单(未支付)
    //8300: 退款失败
    //8400: 已退款
    // 按pdf 文档修改，

    PAID(ProductOrderStatusEnum.PAID.getCode(), 5000, "已支付"),
    WAIT_USE(ProductOrderStatusEnum.SHIPPED.getCode(), 5000, "待使用"),
    RECEIVED(ProductOrderStatusEnum.RECEIVED.getCode(), 5000, "待使用"),
    // 售后中也是支付后的状态
    AFTER_SALE(ProductOrderStatusEnum.AFTER_SALE.getCode(), 5000, "待使用"),
    REFUNDING(ProductOrderStatusEnum.REFUNDING.getCode(), 5400, "退款中"),
    ACTIVATED(ProductOrderStatusEnum.ACTIVATED.getCode(), 8000, "已使用"),
    CLOSED(ProductOrderStatusEnum.CLOSED.getCode(), 8400, "已退款");


    private final Integer productOrderStatus;
    private final int code;
    private final String description;

    public static String getDescByCode(int code) {
        for (OrderStatusEnum node : OrderStatusEnum.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }

    public static OrderStatusEnum getByProductOrderStatus(Integer productOrderStatus) {
        for (OrderStatusEnum node : OrderStatusEnum.values()) {
            if (node.getProductOrderStatus().equals(productOrderStatus)) {
                return node;
            }
        }

        return null;
    }


}
