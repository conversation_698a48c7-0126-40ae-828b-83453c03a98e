package com.ets.apply.application.common.config.creditBank;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;

/**
 * <AUTHOR>
 */
@RefreshScope
@Configuration
@Data
@ConfigurationProperties(prefix = "credit-bank.citic")
public class CiticCreditBankConfig {

    private String appId;
    private String applyUrl;
    private String partnerId;
    private String statusQueryUrl;
    /**
     * 内部版本
     */
    private String innerVersion;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 老用户失败原因
     */
    private String oldUserFailReason;

    /**
     * 渠道申请地址
     */
    private HashMap<Integer,String> subReferTypeApplyUrlMap;




}
