package com.ets.apply.application.common.consts.activityCreditCardPassLog;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ActivityCreditCardPassLogForcePassEnum {

    FORCE_PASS_YES(1, "强制通过"),
    FORCE_PASS_NOT(2, "非强制通过");

    private final int code;
    private final String description;

    public static String getDescByCode(int code) {
        for (ActivityCreditCardPassLogForcePassEnum node : ActivityCreditCardPassLogForcePassEnum.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }

}
