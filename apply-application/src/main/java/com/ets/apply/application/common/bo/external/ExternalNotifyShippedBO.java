package com.ets.apply.application.common.bo.external;

import com.ets.apply.application.common.bo.productOrder.ProductOrderThirdParamBO;
import com.ets.apply.application.common.consts.productOrder.ReferValueEnum;
import com.ets.apply.application.infra.entity.ProductOrderEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

@Data
public class ExternalNotifyShippedBO {

    // 商品订单状态
    private Integer goodsOrderStatus = 6;

    /**
     * 外部用户标识(oneid)
     */
    private String userCode;

    /**
     * 高灯订单号
     */
    private String spOrderId;

    /**
     * 快递单号
     */
    private String expressNumber;

    /**
     * 快递公司（非必须）
     */
    private String expressCompany;

    /**
     * 发货时间
     */
    private String expressTime;

    /* 内部传递变量***/
    @JsonIgnore
    private ProductOrderEntity productOrder;

    @JsonIgnore
    private ReferValueEnum referValueEnum;
}
