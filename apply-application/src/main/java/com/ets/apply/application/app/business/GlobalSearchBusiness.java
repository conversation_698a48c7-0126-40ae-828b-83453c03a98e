package com.ets.apply.application.app.business;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ets.apply.application.common.dto.GlobalSearchResultDTO;
import com.ets.apply.application.common.vo.GlobalSearchResultVO;
import com.ets.apply.application.infra.entity.SearchBusinessConfig;
import com.ets.apply.application.infra.entity.SearchResult;
import com.ets.apply.application.infra.service.SearchBusinessConfigService;
import com.ets.apply.application.infra.service.SearchResultService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class GlobalSearchBusiness {

    @Autowired
    private SearchBusinessConfigService searchBusinessConfigService;

    @Autowired
    private SearchResultService searchResultService;

    public GlobalSearchResultVO getResult(GlobalSearchResultDTO searchResultDTO) {

        // 去空格
        searchResultDTO.setKeyword(searchResultDTO.getKeyword().replaceAll("\\s+", ""));

        // 英文字母转大写
        searchResultDTO.setKeyword(searchResultDTO.getKeyword().toUpperCase());

        LambdaQueryWrapper<SearchResult> resultWrapper = new LambdaQueryWrapper<>();
        resultWrapper.eq(SearchResult::getKeyword, searchResultDTO.getKeyword())
                .eq(SearchResult::getResultStatus, 1);
        SearchResult result = searchResultService.getOneByWrapper(resultWrapper);
        // 没有匹配结果 查询默认值
        if (ObjectUtils.isEmpty(result)) {
            result = searchResultService.getOneByColumn("default", SearchResult::getKeyword);
        }

        // 没有数据 返回
        GlobalSearchResultVO resultVO = new GlobalSearchResultVO();
        if (ObjectUtils.isEmpty(result)) {
            return resultVO;
        }

        // 组装id 查询业务配置
        List<Integer> businessIdList = new ArrayList<>();
        List<Integer> resultIdList = new ArrayList<>();
        List<Integer> guessIdList = new ArrayList<>();
        if (StringUtils.isNotEmpty(result.getResultBusinessId())) {
            String[] resultIdArr = result.getResultBusinessId().split(",");
            resultIdList = Arrays.stream(resultIdArr).map(Integer::parseInt).collect(Collectors.toList());
            businessIdList.addAll(resultIdList);
        }

        if (StringUtils.isNotEmpty(result.getGuessBusinessId())) {
            String[] guessIdArr = result.getGuessBusinessId().split(",");
            guessIdList = Arrays.stream(guessIdArr).map(Integer::parseInt).collect(Collectors.toList());
            businessIdList.addAll(guessIdList);
        }

        // 没有配置 返回
        if (ObjectUtils.isEmpty(businessIdList)) {
            return resultVO;
        }
        LambdaQueryWrapper<SearchBusinessConfig> configWrapper = new LambdaQueryWrapper<>();
        configWrapper.in(SearchBusinessConfig::getId, businessIdList)
                .eq(SearchBusinessConfig::getConfigStatus, 1);
        List<SearchBusinessConfig> businessConfigList = searchBusinessConfigService.getListByWrapper(configWrapper);
        Map<Integer, SearchBusinessConfig> resultMap = businessConfigList.stream().collect(Collectors.toMap(SearchBusinessConfig::getId, v -> v));

        // 获取结果
        if (ObjectUtils.isNotEmpty(resultIdList)) {
            List<SearchBusinessConfig> tmpList = new ArrayList<>();
            resultIdList.forEach(id -> {
                if (ObjectUtils.isNotEmpty(resultMap.getOrDefault(id, null))) {
                    tmpList.add(resultMap.get(id));
                }
            });
            List<GlobalSearchResultVO.BusinessConfig> resultList = new ArrayList<>();
            tmpList.forEach(item -> {
                GlobalSearchResultVO.BusinessConfig businessConfig = BeanUtil.copyProperties(item, GlobalSearchResultVO.BusinessConfig.class);
                businessConfig.setPagePath(item.getResultPagePath());
                resultList.add(businessConfig);
            });
            resultVO.setResultList(resultList);
        }
        if (ObjectUtils.isNotEmpty(guessIdList)) {
            List<SearchBusinessConfig> tmpList = new ArrayList<>();
            guessIdList.forEach(id -> {
                if (ObjectUtils.isNotEmpty(resultMap.getOrDefault(id, null))) {
                    tmpList.add(resultMap.get(id));
                }
            });
            List<GlobalSearchResultVO.BusinessConfig> guessList = new ArrayList<>();
            tmpList.forEach(item -> {
                GlobalSearchResultVO.BusinessConfig businessConfig = BeanUtil.copyProperties(item, GlobalSearchResultVO.BusinessConfig.class);
                businessConfig.setPagePath(item.getGuessPagePath());
                guessList.add(businessConfig);
            });
            resultVO.setGuessList(guessList);
        }

        return resultVO;
    }
}
