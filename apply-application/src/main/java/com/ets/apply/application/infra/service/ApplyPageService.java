package com.ets.apply.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ets.apply.application.common.consts.applyPage.ApplyPageTypeEnum;
import com.ets.apply.application.infra.entity.ApplyPageEntity;
import com.ets.apply.application.infra.mapper.ApplyPageMapper;
import com.ets.common.RequestHelper;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-02
 */
@Service
@DS("db-apply")
public class ApplyPageService extends BaseService<ApplyPageMapper, ApplyPageEntity>  {

    /**
     * 只能有一个兜底模板，后续需要加上页面值区分筛选，一个页面一个
     * @return
     */
    @Cacheable(value = "applyPageTemplate", key = "#category", unless = "#result == null")
    public ApplyPageEntity getTemplatePage(Integer category){
        LambdaQueryWrapper<ApplyPageEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ApplyPageEntity::getPageType, ApplyPageTypeEnum.TEMPLATE.getType())
                .eq(ApplyPageEntity::getPageCategory, category);
        return getOneByWrapper(wrapper);

    }

    @CacheEvict(value = "applyPageTemplate",key = "#category")
    public void setTemplatePage(String pageSn, Integer category, Integer pageType, String operator) {
        LambdaUpdateWrapper<ApplyPageEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ApplyPageEntity::getPageSn, pageSn)
                .eq(ApplyPageEntity::getPageCategory, category)
                .set(ApplyPageEntity::getPageType, pageType)
                .set(ApplyPageEntity::getOperator, operator)
        ;
        updateByWrapper(wrapper);
    }
}
