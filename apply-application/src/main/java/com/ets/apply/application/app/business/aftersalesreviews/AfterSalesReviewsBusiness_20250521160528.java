package com.ets.apply.application.app.business.aftersalesreviews;

import cn.hutool.core.bean.BeanUtil;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.apply.application.common.dto.aftersalesreviews.AdminAfReviewsReviewDTO;
import com.ets.apply.application.common.dto.aftersalesreviews.AdminAfterSalesReviewsGetListDTO;
import com.ets.apply.application.common.dto.aftersalesreviews.CancelAfterSalesReviewDTO;
import com.ets.apply.application.common.dto.aftersalesreviews.CreateAfterSalesReviewDTO;
import com.ets.apply.application.common.consts.aftersalesreviews.AftersalesReviewsDataTypeEnum;
import com.ets.apply.application.common.consts.aftersalesreviews.AftersalesReviewsStatusEnum;
import com.ets.apply.application.common.vo.aftersalesreivews.AdminAfterSalesReviewsDetailVO;
import com.ets.apply.application.common.vo.aftersalesreivews.AdminAfterSalesReviewsListVO;
import com.ets.apply.application.common.vo.aftersalesreivews.AfterSalesReviewsVO;
import com.ets.apply.application.infra.entity.aftersalesreview.AftersalesReviews;
import com.ets.apply.application.infra.entity.aftersalesreview.AftersalesReviewsLog;
import com.ets.apply.application.infra.entity.aftersalesreview.AftersalesReviewsVehicles;
import com.ets.apply.application.infra.service.AftersalesReviewsLogService;
import com.ets.apply.application.infra.service.AftersalesReviewsService;
import com.ets.apply.application.infra.service.AftersalesReviewsVehiclesService;
import com.ets.common.ToolsHelper;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;

/**
 * 售后审核单业务逻辑
 */
@Slf4j
@Component
public class AfterSalesReviewsBusiness {

    @Autowired
    private AftersalesReviewsService aftersalesReviewsService;

    @Autowired
    private AftersalesReviewsVehiclesService aftersalesReviewsVehiclesService;

    @Autowired
    private AftersalesReviewsLogService aftersalesReviewsLogService;

    @Resource(name = "redisPermanentTemplate")
    private StringRedisTemplate redisPermanentTemplate;

    /**
     * 创建售后审核单
     *
     * @param dto 创建售后审核单请求参数
     * @return 售后审核单
     */
    @Transactional(rollbackFor = Exception.class)
    public AfterSalesReviewsVO createAfterSalesReview(CreateAfterSalesReviewDTO dto) {
        // 检查业务单是否存在待审核记录
        AftersalesReviews reviews = aftersalesReviewsService.getByOrderSn(dto.getOrderSn());
        if (reviews != null && reviews.getReviewStatus().equals(AftersalesReviewsStatusEnum.PENDING.getValue())) {
            ToolsHelper.throwException("该业务单已存在待审核记录，请勿重复提交");
        }

        // 创建审核单
        AftersalesReviews aftersalesReviews = BeanUtil.copyProperties(dto, AftersalesReviews.class);

        // 生成审核单流水号
        String reviewSn = ToolsHelper.genNum(redisPermanentTemplate, "AftersalesReviews", "prod", 8);

        // 设置属性
        aftersalesReviews.setReviewSn(reviewSn);
        aftersalesReviews.setApplyTime(LocalDateTime.now());

        // 设置状态
        aftersalesReviews.setReviewStatus(AftersalesReviewsStatusEnum.PENDING.getValue()); // 待审核

        // 保存审核单
        aftersalesReviewsService.create(aftersalesReviews);

        // 保存审核行驶证信息
        if (dto.getReviewVehicleInfo() != null) {
            saveVehicleInfo(reviewSn, dto.getReviewVehicleInfo(), AftersalesReviewsDataTypeEnum.REVIEW_DATA.getValue());
        }

        // 保存订单行驶证信息
        if (dto.getOrderVehicleInfo() != null) {
            saveVehicleInfo(reviewSn, dto.getOrderVehicleInfo(), AftersalesReviewsDataTypeEnum.ORDER_DATA.getValue());
        }

        // 记录操作日志
        AftersalesReviewsLog reviewsLog = new AftersalesReviewsLog();
        reviewsLog.setReviewSn(reviewSn);
        reviewsLog.setOperateContent("创建售后审核");
        reviewsLog.setOperator("system");
        reviewsLog.setType("create");
        aftersalesReviewsLogService.create(reviewsLog);

        log.info("创建售后审核单成功，审核单号：{}", reviewSn);

        return BeanUtil.copyProperties(aftersalesReviews, AfterSalesReviewsVO.class);
    }

    /**
     * 保存行驶证信息
     *
     * @param reviewSn 审核单号
     * @param vehicleInfo 行驶证信息
     * @param dataType 数据类型 [1-审核资料 2-申办资料]
     */
    private void saveVehicleInfo(String reviewSn, CreateAfterSalesReviewDTO.VehicleInfoDTO vehicleInfo, Integer dataType) {
        AftersalesReviewsVehicles vehicle = BeanUtil.copyProperties(vehicleInfo, AftersalesReviewsVehicles.class);
        vehicle.setReviewSn(reviewSn);
        vehicle.setDataType(dataType);

        aftersalesReviewsVehiclesService.create(vehicle);
    }

    /**
     * 取消售后审核单
     *
     * @param dto 取消售后审核单请求参数
     */
    public void cancelAfterSalesReview(CancelAfterSalesReviewDTO dto) {
        // 查询审核单
        AftersalesReviews aftersalesReviews = aftersalesReviewsService.getByReviewSn(dto.getReviewSn());
        if (aftersalesReviews == null) {
            ToolsHelper.throwException("审核单不存在");
        }

        // 检查审核单状态
        if (aftersalesReviews.getReviewStatus().equals(AftersalesReviewsStatusEnum.CANCELED.getValue())) {
            return;
        }

        if (Arrays.asList(
                AftersalesReviewsStatusEnum.APPROVED.getValue(),
                AftersalesReviewsStatusEnum.REJECTED.getValue()).contains(aftersalesReviews.getReviewStatus())
        ) {
            ToolsHelper.throwException("审核单已有审核结果，无法取消");
        }

        // 更新审核单状态
        aftersalesReviews.setReviewStatus(AftersalesReviewsStatusEnum.CANCELED.getValue()); // 已取消
        aftersalesReviews.setReviewRemark(dto.getCancelReason());

        // 保存审核单
        aftersalesReviewsService.updateById(aftersalesReviews);

        // 记录操作日志
        AftersalesReviewsLog reviewsLog = new AftersalesReviewsLog();
        reviewsLog.setReviewSn(dto.getReviewSn());
        reviewsLog.setOperateContent("取消售后审核，取消原因：" + dto.getCancelReason());
        reviewsLog.setOperator("system");
        reviewsLog.setType("cancel");
        aftersalesReviewsLogService.create(reviewsLog);

        log.info("取消售后审核单成功，审核单号：{}", dto.getReviewSn());

    }

    public IPage<AdminAfterSalesReviewsListVO> getList(AdminAfterSalesReviewsGetListDTO dto) {
        // 构建查询条件
        LambdaQueryWrapper<AftersalesReviews> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(dto.getReviewSn()), AftersalesReviews::getReviewSn, dto.getReviewSn())
                .eq(dto.getReviewStatus() != null, AftersalesReviews::getReviewStatus, dto.getReviewStatus())
                .ge(dto.getApplyTimeStart() != null, AftersalesReviews::getApplyTime, dto.getApplyTimeStart())
                .le(dto.getApplyTimeEnd() != null, AftersalesReviews::getCreateTime, dto.get())
                .orderByDesc(AftersalesReviews::getCreateTime);

        // 分页查询
        Page<AftersalesReviews> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        IPage<AftersalesReviews> reviewsPage = aftersalesReviewsService.page(page, queryWrapper);

        // 转换为VO对象
        return reviewsPage.convert(reviews -> {
            AdminAfterSalesReviewsListVO vo = new AdminAfterSalesReviewsListVO();
            BeanUtil.copyProperties(reviews, vo);
            return vo;
        });
        return null;
    }

    public AdminAfterSalesReviewsDetailVO getDetail(String reviewSn) {
        return null;
    }

    public void review(AdminAfReviewsReviewDTO dto) {

    }
}
