package com.ets.apply.application.common.consts.productPackage;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
@AllArgsConstructor
public enum ManufacturerEnum {

    UNKNOW(0, "未知"),
    AITESI(1, "埃特斯"),
    JINYI(2, "金溢"),
    JULI(3, "聚力"),
    WANGJI(4, "万集"),
    CHENGGU(5, "成谷"),
    YUNXINGYU(6, "云星宇"),
    SOULIN(10, "搜林"),
    MINGCHUANG(27, "铭创");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;

    static {
        ManufacturerEnum[] enums = ManufacturerEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }

    public static String getDescByCode(int code) {
        for (ManufacturerEnum node : ManufacturerEnum.values()) {
            if (node.getValue().equals(code)) {
                return node.getDesc();
            }
        }
        return "";
    }
}
