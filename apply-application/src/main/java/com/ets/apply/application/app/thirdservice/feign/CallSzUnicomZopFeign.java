package com.ets.apply.application.app.thirdservice.feign;

import com.ets.apply.application.app.thirdservice.fallback.CallSzUnicomZopFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestPart;

import java.net.URI;
import java.util.Map;

/**
 * 深圳对外ZOP接口2023
 */
@FeignClient(url = "EMPTY", name = "CallSzUnicomZopFeign",   fallbackFactory = CallSzUnicomZopFallbackFactory.class)
public interface CallSzUnicomZopFeign {

    @PostMapping(value = "/check-user",consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    String checkUser(URI uri, @RequestParam Map<String, String> map);

    @PostMapping(value = "/select-num",consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    String selectNum(URI uri, @RequestParam Map<String, String> map);

    @PostMapping(value = "/safe-code",consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    String safeCode(URI uri, @RequestParam Map<String, String> map);

    @PostMapping(value = "/check-code",consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    String checkCode(URI uri, @RequestParam Map<String, String> map);

    @PostMapping(value = "/check/risk",consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    String checkRisk(URI uri, @RequestParam Map<String, String> map);

    @PostMapping(value = "/create-order/intention",consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    String createOrderIntention(URI uri, @RequestParam Map<String, String> map);

    @PostMapping(value = "/create-order/intention-formal",consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    String createOrderIntentionFormal(URI uri, @RequestParam Map<String, String> map);


    @PostMapping(value = "/order/get-card",consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    String orderGetCard(URI uri, @RequestParam Map<String, String> map);
}
