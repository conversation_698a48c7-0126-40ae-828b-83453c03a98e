package com.ets.apply.application.common.consts.activityCreditCardUsersInfo;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum ActivityCreditCardUserInfoSubReferTypeEnum {

    DEFAULT(0, ""),
    TYPE_APPLY(1, "新办"),
    TYPE_REAPPLY(2, "重办"),
    TYPE_UPGRADE(3, "设备升级"),
    TYPE_ACTIVITY_FREE_APPLY(4, "0元换新-信用卡办理"),
    TYPE_ACTIVITY_PROMOTION_DITUI(5, "地推信用卡免费办理"),
    TYPE_ACTIVITY_MEMBER_RIGHTS(6, "赠送会员权益"),
    TYPE_APPLY_ACTIVITY(7, "申办-活动"),

    ;


    private final int code;
    private final String description;

    public static String getDescByCode(int code) {
        for (ActivityCreditCardUserInfoSubReferTypeEnum node : ActivityCreditCardUserInfoSubReferTypeEnum.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }
        return "";
    }

    public static List<Map<String, String>> getLabelList() {
        List<Map<String, String>> list = new ArrayList<>();

        for (ActivityCreditCardUserInfoSubReferTypeEnum node : ActivityCreditCardUserInfoSubReferTypeEnum.values()) {
            Map<String, String> row = new LinkedHashMap<>();
            row.put("label", node.getDescription());
            row.put("value", String.valueOf(node.getCode()));

            list.add(row);
        }

        return list;
    }
}
