package com.ets.apply.application.common.consts.dynamicRecomend;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DynamicRecommendVersion {

    VERSION_0(0, "初始版本"),
    VERSION_1(1, "版本1-未支付单都进行推荐");

    private final Integer version;
    private final String description;

    public static DynamicRecommendVersion getEnumByVersion(Integer version) {
        for (DynamicRecommendVersion dynamicRecommendVersion : DynamicRecommendVersion.values()) {
            if (dynamicRecommendVersion.getVersion().equals(version)) {
                return dynamicRecommendVersion;
            }
        }
        return null;
    }
}
