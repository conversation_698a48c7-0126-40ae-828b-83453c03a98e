package com.ets.apply.application.common.dto.goods;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.HashMap;

@Data
public class OrderCenterUpdateDTO {

    /**
     * 业务单号
     */
    @NotBlank(message = "业务单号不能为空")
    private String productSn;
    /**
     * 业务类型
     */
    @NotNull(message = "业务类型不能为空")
    private Integer productType;
    /**
     * 实际支付金额
     */
    private BigDecimal amount;
    /**
     * 通用订单状态
     */
    private Integer status;
    /**
     * 其他属性值
     */
    private HashMap<String, Object> extra;

    /**
     * 是否显示在列表页
     */
    private Boolean isShow;


    /* **************兼容订单未创建就直接更新需传的参数**********************/
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 用户id
     */
    private Long uid;
    /**
     * 订单创建时间
     */
    private String createTime;

    private String plateNo;

    private Integer plateColor;
}
