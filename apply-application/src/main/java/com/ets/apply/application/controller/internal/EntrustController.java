package com.ets.apply.application.controller.internal;

import com.ets.apply.application.app.business.entrust.EntrustBusiness;
import com.ets.apply.application.common.dto.request.entrust.CheckByOrderSnAndBankIdDTO;
import com.ets.apply.application.common.vo.entrust.EntrustCheckVO;
import com.ets.apply.application.controller.BaseController;
import com.ets.common.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

@RequestMapping("/entrust")
@RefreshScope
@RestController
@Slf4j
public class EntrustController extends BaseController {

    @Autowired
    private EntrustBusiness entrustBusiness;
    /*
     * 通过订单号和银行ID，查询签约状态
     */
    @PostMapping("/checkSignByOrderSnAndBankId")
    public JsonResult<EntrustCheckVO> checkSignByOrderSnAndBankId(@RequestBody @Valid CheckByOrderSnAndBankIdDTO dto) {
        return JsonResult.ok(entrustBusiness.checkIsSignByOrderSnAndBankId(dto));
    }


}
