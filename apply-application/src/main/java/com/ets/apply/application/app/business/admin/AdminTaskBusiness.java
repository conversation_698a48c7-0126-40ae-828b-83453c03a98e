package com.ets.apply.application.app.business.admin;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.apply.application.app.business.BaseBusiness;
import com.ets.apply.application.common.consts.taskRecord.TaskRecordStatusEnum;
import com.ets.apply.application.common.dto.request.admin.task.AdminTaskGetListDTO;
import com.ets.apply.application.common.dto.request.admin.task.AdminTaskUpdateDTO;
import com.ets.apply.application.common.vo.admin.task.AdminTaskVO;
import com.ets.apply.application.infra.entity.TaskRecordEntity;
import com.ets.apply.application.infra.service.ConfigBizFieldValuesService;
import com.ets.apply.application.infra.service.TaskRecordService;
import com.ets.common.ToolsHelper;
import com.ets.common.util.NumberUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;

@Slf4j
@Component
public class AdminTaskBusiness extends BaseBusiness {
    @Autowired
    private TaskRecordService taskRecordService;

    @Autowired
    private ConfigBizFieldValuesService configBizFieldValuesService;

    /*
     * 获取全部产品包列表
     */
    public IPage<AdminTaskVO> getList(AdminTaskGetListDTO dto) {
        // 分页设置
        IPage<TaskRecordEntity> oPage = new Page<>(dto.getPageNum(), dto.getPageSize(), true);
        LocalDateTime beginTime = null;
        LocalDateTime endTime = null;
        if(StringUtils.isNotEmpty(dto.getCreateTimeBegin())){
            beginTime = ToolsHelper.getLocalDateTime(dto.getCreateTimeBegin() + " 00:00:00");
        }
        if(StringUtils.isNotEmpty(dto.getCreateTimeEnd())){
            endTime = ToolsHelper.getLocalDateTime(dto.getCreateTimeEnd() + " 23:59:59");
        }
        LambdaQueryWrapper<TaskRecordEntity> wrapper = new LambdaQueryWrapper<>();
        //如果有taskSn或者referSn，则忽略其他查询参数
        if(
           StringUtils.isEmpty(dto.getTaskSn()) &&
           StringUtils.isEmpty(dto.getReferSn())
        ){
            // 时间范围限制在一个月内
            if (
               StringUtils.isEmpty(dto.getCreateTimeBegin()) ||
               StringUtils.isEmpty(dto.getCreateTimeEnd())
            ) {
                ToolsHelper.throwException("请选择开始和结束时间");
            }
            if(ChronoUnit.MONTHS.between(beginTime, endTime) > 1){
                ToolsHelper.throwException("开始和结束时间不可超过一个月");
            }
            if(StringUtils.isEmpty(dto.getReferType())){
                //默认过滤不查询指定的referType
                wrapper.notIn(TaskRecordEntity::getReferType, configBizFieldValuesService.getTaskReferTypeNotSearchList());
            }

        }
        wrapper.eq(StringUtils.isNotEmpty(dto.getTaskSn()), TaskRecordEntity::getTaskSn, dto.getTaskSn())
               .eq(StringUtils.isNotEmpty(dto.getReferSn()), TaskRecordEntity::getReferSn, dto.getReferSn())
               .eq(StringUtils.isNotEmpty(dto.getReferType()), TaskRecordEntity::getReferType, dto.getReferType())
               .eq(NumberUtil.isPositive(dto.getStatus()), TaskRecordEntity::getStatus, dto.getStatus())
               .ge(StringUtils.isNotEmpty(dto.getCreateTimeBegin()), TaskRecordEntity::getCreatedAt, beginTime)
               .le(StringUtils.isNotEmpty(dto.getCreateTimeEnd()), TaskRecordEntity::getCreatedAt, endTime)
               .orderByDesc(TaskRecordEntity::getCreatedAt);

        IPage<TaskRecordEntity> pageList = taskRecordService.getPageListByWrapper(oPage, wrapper);
        if(pageList.getRecords().isEmpty()){
            return null;
        }
        HashMap<String, String> taskReferType =configBizFieldValuesService.getListByBizFieldKeyMap("task_refer_type");
        return pageList.convert(record -> {
            AdminTaskVO vo = new AdminTaskVO();
            BeanUtils.copyProperties(record, vo);
            vo.setReferTypeStr(taskReferType.get(record.getReferType()));
            return vo;
        });
    }
    /*
     * 更新task的状态
     */
    public Boolean updateTask(AdminTaskUpdateDTO dto) {
        TaskRecordEntity taskRecord = taskRecordService.getOneByTaskSn(dto.getTaskSn());
        if(taskRecord == null){
            ToolsHelper.throwException("任务不存在");
        }
        switch (dto.getType()){
            //暂停操作
            case 1:
                taskRecord.setStatus(TaskRecordStatusEnum.TASK_STATUS_STOP.getCode());
                break;
            //置为失败
            case 2:
                taskRecord.setStatus(TaskRecordStatusEnum.TASK_STATUS_FAIL.getCode());
                break;
            //置为完成
            case 3:
                taskRecord.setStatus(TaskRecordStatusEnum.TASK_STATUS_FINISH.getCode());
                break;
            //手动清零
            case 4:
                taskRecord.setStatus(TaskRecordStatusEnum.TASK_STATUS_FAIL.getCode());
                taskRecord.setExecTimes(0);
                break;
            default:
                ToolsHelper.throwException("操作类型错误");
        }
        if(StringUtils.isNotEmpty(dto.getExecError())){
            taskRecord.setExecError(dto.getExecError());
        }
        taskRecordService.updateById(taskRecord);
        return true;
    }
}
