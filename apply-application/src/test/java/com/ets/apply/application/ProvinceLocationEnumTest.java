package com.ets.apply.application;

import com.ets.apply.application.common.consts.location.ProvinceLocationEnum;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@SpringBootTest(classes = ApplyApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringRunner.class)
@ActiveProfiles(value = "test")
public class ProvinceLocationEnumTest {

    @Test
    public void testGetById() {
        ProvinceLocationEnum province = ProvinceLocationEnum.getById("110000");
        Assert.assertEquals(ProvinceLocationEnum.BEIJING, province);
        
        province = ProvinceLocationEnum.getById("440000");
        Assert.assertEquals(ProvinceLocationEnum.GUANGDONG, province);
        
        province = ProvinceLocationEnum.getById("999999"); // 不存在的ID
        Assert.assertNull(province);
    }
    
    @Test
    public void testGetByValue() {
        ProvinceLocationEnum province = ProvinceLocationEnum.getByValue("北京市");
        Assert.assertEquals(ProvinceLocationEnum.BEIJING, province);
        
        province = ProvinceLocationEnum.getByValue("广东省");
        Assert.assertEquals(ProvinceLocationEnum.GUANGDONG, province);
        
        province = ProvinceLocationEnum.getByValue("不存在的省份"); // 不存在的名称
        Assert.assertNull(province);
    }
    
    @Test
    public void testIsMunicipality() {
        Assert.assertTrue(ProvinceLocationEnum.BEIJING.isMunicipality());
        Assert.assertTrue(ProvinceLocationEnum.TIANJIN.isMunicipality());
        Assert.assertTrue(ProvinceLocationEnum.SHANGHAI.isMunicipality());
        Assert.assertTrue(ProvinceLocationEnum.CHONGQING.isMunicipality());
        
        Assert.assertFalse(ProvinceLocationEnum.GUANGDONG.isMunicipality());
        Assert.assertFalse(ProvinceLocationEnum.INNER_MONGOLIA.isMunicipality());
    }
    
    @Test
    public void testIsAutonomousRegion() {
        Assert.assertTrue(ProvinceLocationEnum.INNER_MONGOLIA.isAutonomousRegion());
        Assert.assertTrue(ProvinceLocationEnum.GUANGXI.isAutonomousRegion());
        Assert.assertTrue(ProvinceLocationEnum.TIBET.isAutonomousRegion());
        Assert.assertTrue(ProvinceLocationEnum.NINGXIA.isAutonomousRegion());
        Assert.assertTrue(ProvinceLocationEnum.XINJIANG.isAutonomousRegion());
        
        Assert.assertFalse(ProvinceLocationEnum.BEIJING.isAutonomousRegion());
        Assert.assertFalse(ProvinceLocationEnum.GUANGDONG.isAutonomousRegion());
    }
    
    @Test
    public void testIsSpecialAdministrativeRegion() {
        Assert.assertTrue(ProvinceLocationEnum.HONG_KONG.isSpecialAdministrativeRegion());
        Assert.assertTrue(ProvinceLocationEnum.MACAO.isSpecialAdministrativeRegion());
        
        Assert.assertFalse(ProvinceLocationEnum.BEIJING.isSpecialAdministrativeRegion());
        Assert.assertFalse(ProvinceLocationEnum.GUANGDONG.isSpecialAdministrativeRegion());
    }
    
    @Test
    public void testEnumCount() {
        // 验证枚举总数：4个直辖市 + 23个省 + 5个自治区 + 2个特别行政区 = 34个
        Assert.assertEquals(34, ProvinceLocationEnum.values().length);
    }
}
