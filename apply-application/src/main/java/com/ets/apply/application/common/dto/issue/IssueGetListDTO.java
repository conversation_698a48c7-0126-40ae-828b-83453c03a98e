package com.ets.apply.application.common.dto.issue;

import lombok.Data;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;

@Data
public class IssueGetListDTO {

    /**
     * 用户uid
     */
    private Long uid;

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 签约服务单号
     */
    private String serviceSn;

    /**
     * 签约协议号
     */
    private String contractId;

    /**
     * 商品订单号
     */
    private String goodsOrderSn;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 激活状态
     */
    private Integer activatedStatus;

    /**
     * 申请时间
     */
    private String createdAtStart;
    private String createdAtEnd;

    /**
     * 页码
     */
    @Min(value = 1, message = "当前页码必须大于0")
    private Integer page = 1;

    /**
     * 分页大小
     */
    @Max(value = 100, message = "每页最多100条")
    private Integer pageSize = 20;
}
