package com.ets.apply.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.apply.application.infra.entity.aftersalesreview.AftersalesReviews;
import com.ets.apply.application.infra.mapper.AftersalesReviewsMapper;

import java.util.List;

import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Service
@DS("db-issuer-admin-proxy")
public class AftersalesReviewsService extends BaseService<AftersalesReviewsMapper, AftersalesReviews> {

    public IPage<AftersalesReviews> getList(AdminRe)

    public AftersalesReviews getByOrderSn(String orderSn) {
        Wrapper<AftersalesReviews> wrapper = Wrappers.<AftersalesReviews>lambdaQuery()
                .eq(AftersalesReviews::getOrderSn, orderSn)
                .orderByDesc(AftersalesReviews::getCreatedAt)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }

    public AftersalesReviews getByReviewSn(String reviewSn) {
        Wrapper<AftersalesReviews> wrapper = Wrappers.<AftersalesReviews>lambdaQuery()
                .eq(AftersalesReviews::getReviewSn, reviewSn)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }
}
