package com.ets.apply.application.common.dto.request.bank.common;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class CreditCardApplyOrderDTO {


    /**
     * uid
     */
    private Long uid;

    /**
     * 银行
     */
    @JsonProperty(value = "which_bank")
    private Integer whichBank;

    private Integer classify;

    /**
     * 关联类型
     */
    @JsonProperty(value = "refer_type")
    private Integer referType;


    /**
     * 关联单号
     */
    @JsonProperty(value = "refer_sn")
    private String  referSn;

    /**
     * 第三方回调url
     */
    @JsonProperty(value = "callback_url")
    private String  callbackUrl;


    /**
     * 银行回跳前端页面地址
     */
    @JsonProperty(value = "redirect_url")
    private String redirectUrl;

    @JsonProperty(value = "sub_refer_type")
    private Integer subReferType;

}
