package com.ets.apply.application.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@RefreshScope
@Configuration
@Data
@ConfigurationProperties(prefix = "params")
public class PrivilegePackageSnsConfig {
    // 普通权益
    private List<String> privilegePackageSns;

    // 救援权益
    private List<String> rescuePrivilegePackageSns;
}
