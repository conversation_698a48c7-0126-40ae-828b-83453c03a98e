package com.ets.apply.application.common.consts.writeCard;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
@AllArgsConstructor
public enum WriteCardNotifyResultEnum {
    DEFAULT(0, "未通知"),
    SUCCESS(1, "通知成功"),
    FAIL(2, "通知失败");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;

    static {
        WriteCardNotifyResultEnum[] enums = WriteCardNotifyResultEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
