package com.ets.apply.application.controller.third;

import com.ets.apply.application.app.business.creditCard.CebBusiness;
import com.ets.apply.application.app.business.creditCard.SpdBusiness;
import com.ets.apply.application.common.dto.request.bank.ceb.CebReceiveDataDto;
import com.ets.apply.application.common.dto.request.spd.SpdReceiveDataDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.text.ParseException;

@RequestMapping("/third/ceb")
@RefreshScope
@RestController
@Slf4j
public class CebController {


    @Autowired
    SpdBusiness spdBusiness;

    @Autowired
     CebBusiness cebBusiness;

    @PostMapping("/receiveOrder")
    public String receiveOrder(@RequestBody @Valid String data) throws Exception {
        return cebBusiness.receiveData(data);
    }
}
