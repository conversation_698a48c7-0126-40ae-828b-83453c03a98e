package com.ets.apply.application.infra.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 更换手机号记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("etc_change_phone")
public class ChangePhone extends BaseEntity<ChangePhone> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 操作单号
     */
    private String changeSn;

    /**
     * 用户ID
     */
    private Long uid;

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 原手机号
     */
    private String oldPhoneNo;

    /**
     * 新手机号
     */
    private String newPhoneNo;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 操作人uid
     */
    private Integer opUid;

    /**
     * 操作时间
     */
    private LocalDateTime opTime;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;


}
