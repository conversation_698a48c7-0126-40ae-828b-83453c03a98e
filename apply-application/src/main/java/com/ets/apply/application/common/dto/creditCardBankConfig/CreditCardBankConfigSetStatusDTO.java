package com.ets.apply.application.common.dto.creditCardBankConfig;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class CreditCardBankConfigSetStatusDTO {
    @NotNull(message = "id不能为空")
    private Integer id;

    @NotNull(message = "状态不能为空")
    @Min(value = 0, message = "状态值不能小于0")
    @Max(value = 1, message = "状态值不能大于1")
    private Integer bankStatus;
}
