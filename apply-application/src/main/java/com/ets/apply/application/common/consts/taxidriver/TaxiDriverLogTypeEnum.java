package com.ets.apply.application.common.consts.taxidriver;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TaxiDriverLogTypeEnum {

    DEFAULT(0, ""),
    DELETE(1, "删除");

    private final Integer code;

    private final String description;

    public static String getDescByCode(Integer code) {
        for (TaxiDriverLogTypeEnum node : TaxiDriverLogTypeEnum.values()) {
            if (node.getCode().equals(code)) {
                return node.getDescription();
            }
        }

        return "";
    }

}