package com.ets.apply.application.common.dto.request.productOrder;

import com.ets.common.annotation.PhoneAnnotation;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import jakarta.validation.constraints.*;
import java.math.BigDecimal;

@Data
public class ProductOrderCreateDTO {

    /**
     * 第三方订单号
     */
    @NotBlank(message = "第三方订单号不能为空")
    private String thirdOrderSn;

    /**
     * 产品包sn
     */
    @NotBlank(message = "产品包不能为空")
    private String packageSn;

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 收货人
     */
    @Pattern(regexp="(^[\u4e00-\u9fa5]+$)|([\u4e00-\u9fa5]+\\[(.*)\\]$)",message="收货人名称只能输入中文或者中文[任意内容]格式")
    @Length(min = 1,max = 20,message = "名称长度为1到20个字符")
    @NotNull(message = "收货人姓名不能为空")
    private String sendName;

    /**
     * 收货手机
     */
    @PhoneAnnotation(message = "收货手机号校验错误")
    @NotBlank(message = "收货手机号不能为空")
    private String sendPhone;

    @PhoneAnnotation(message = "申办手机号校验错误")
    @NotBlank(message = "申办手机号不能为空")
    private String etcPhone;

    /**
     * 收货详细地址
     */
    @NotBlank(message = "收货地址不能为空")
    @Pattern(regexp="^[\\u4e00-\\u9fa5_a-zA-Z0-9]+$",message="收货地址不能包含特殊字符，请检查")
    @Length(min = 1,max = 200,message = "地址长度不符合要求")
    private String sendAddress;

    /**
     * 实际销售价格
     */
    @Max(value = 99999999, message = "实际销售价格不能超过99999999")
    @Min(value = 0, message = "实际销售价格不能小于0")
    @NotNull(message = "实际销售价不能为空")
    @Digits(integer = 8, fraction = 2, message = "实际销售价格小数点需在两位以内")
    private BigDecimal paidAmount;

}
