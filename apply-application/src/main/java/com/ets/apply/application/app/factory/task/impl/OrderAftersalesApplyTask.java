package com.ets.apply.application.app.factory.task.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ets.apply.application.app.business.OrderCenterSyncBusiness;
import com.ets.apply.application.app.business.ProductOrderBusiness;
import com.ets.apply.application.app.business.RealNameBusiness;
import com.ets.apply.application.app.business.orderAftersales.OrderAftersalesBusiness;
import com.ets.apply.application.app.thirdservice.feign.CallEtcNotificationFeign;
import com.ets.apply.application.app.thirdservice.feign.CallPhpApplyFeign;
import com.ets.apply.application.app.thirdservice.feign.CallPhpUserFeign;
import com.ets.apply.application.app.thirdservice.request.SendRuleMsgDTO;
import com.ets.apply.application.common.bo.msg.AuditMsgBO;
import com.ets.apply.application.common.config.SendRuleMsgConfig;
import com.ets.apply.application.common.consts.order.AftersaleStatus;
import com.ets.apply.application.common.consts.orderAftersales.*;
import com.ets.apply.application.common.consts.reviewOrder.ThirdReviewStatus;
import com.ets.apply.application.common.consts.taskRecord.TaskRecordErrorCodeConstant;
import com.ets.apply.application.common.dto.request.orderAftersales.GetCustomerServiceOrderDTO;
import com.ets.apply.application.common.dto.request.orderAftersales.OrderAftersalesCancelUserCardDTO;
import com.ets.apply.application.common.dto.request.orderAftersales.OrderAftersalesChangeDTO;
import com.ets.apply.application.common.vo.orderAftersales.GetCustomerServiceOrderVO;
import com.ets.apply.application.common.vo.response.issuer.IssuerAftersalesApplyVO;
import com.ets.apply.application.infra.entity.*;
import com.ets.apply.application.infra.service.*;
import com.ets.common.BizException;
import com.ets.common.ToolsHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Objects;

@Component
public class OrderAftersalesApplyTask extends TaskBase {
    @Autowired
    private OrderAftersalesApplyService orderAftersalesApplyService;
    @Autowired
    private OrderAftersalesBusiness orderAftersalesBusiness;
    @Autowired
    private OrderOrderService orderOrderService;

    @Autowired
    private ReviewOrderService reviewOrderService;

    @Autowired
    private CallPhpApplyFeign callPhpApplyFeign;

    @Autowired
    private ProductOrderBusiness productOrderBusiness;

    @Autowired
    private OrderCenterSyncBusiness orderCenterSyncBusiness;

    @Autowired
    private CallPhpUserFeign callPhpUserFeign;
    @Autowired
    private RealNameBusiness realNameBusiness;

    @Autowired
    private CallEtcNotificationFeign callEtcNotificationFeign;

    @Autowired
    private TaskRecordService taskRecordService;
    @Autowired
    private SendRuleMsgConfig sendRuleMsgConfig;


    @Override
    public void childExec(TaskRecordEntity taskRecord) {
        //判断售后单的状态
        OrderAftersalesApplyEntity orderAftersalesApply = orderAftersalesApplyService.getOneByColumn(taskRecord.getReferSn(),OrderAftersalesApplyEntity::getApplySn);
        try {
            if(orderAftersalesApply == null || Objects.equals(orderAftersalesApply.getStatus(), OrderAftersalesStatusEnum.STATUS_FAIL.getStatus())){
                ToolsHelper.throwException("售后单无需要处理", TaskRecordErrorCodeConstant.ERROR_CODE_NEED_FINISH);
            }
            OrderOrderEntity orderOrder = orderOrderService.getByOrderSn(orderAftersalesApply.getOrderSn());

            //商品售后处理
            dealBussinessStatusIsFinished(orderAftersalesApply,orderOrder);
            //处理发卡方售后
            dealStatusIsFinished(orderAftersalesApply,orderOrder,taskRecord);

            //根据不同的类型进行处理
            switch (Objects.requireNonNull(OrderAftersalesTypeEnum.getByType(orderAftersalesApply.getType()))) {
                //解绑【退货退款，仅取消】
                case TYPE_UNBIND_REFUND, TYPE_UNBIND_CANCEL ->
                        afterIssuerOrderCancelFinished(orderAftersalesApply, orderOrder);
                case TYPE_UNBIND_ONLY -> {
                    //仅取消类型，通知etc-apply,看看是否需要触发售后完成事件
                    OrderAftersalesChangeDTO dto = new OrderAftersalesChangeDTO();
                    dto.setBusinessOrderSn(orderOrder.getOrderSn());
                    dto.setGoodsOrderSn(orderOrder.getGoodsOrderSn());
                    dto.setCustomerStatus(4);
                    dto.setServiceType(99);
                    dto.setUid(orderOrder.getUid());
                    callPhpApplyFeign.goodsOrderAfterSaleChange(dto);
                    afterIssuerOrderCancelFinished(orderAftersalesApply, orderOrder);
                }
                //换设备
                //补办设备
                case TYPE_EXCHANGE_GOODS, TYPE_REISSUE_GOODS -> {
                    //重置发行状态
                    if (orderOrder.getAftersaleStatus().equals(AftersaleStatus.STATUS_APPLY.getValue())) {
                        orderOrderService.aftersaleStatusUpdate(orderOrder, AftersaleStatus.STATUS_NORMAL.getValue(), "售后重置完成");
                    }
                }
                default -> ToolsHelper.throwException("申请类型不支持");
            }
            orderCenterSyncBusiness.addTask(orderAftersalesApply.getOrderSn(),"OrderAftersalesApplyTask");
        } catch (BizException e) {
            orderCenterSyncBusiness.addTask(orderAftersalesApply.getOrderSn(),"OrderAftersalesApplyTask");
            //原路输出
            ToolsHelper.throwException(e.getMessage(), e.getErrorCode());
        } catch (Exception e1) {
            ToolsHelper.throwException("系统错误" + e1.getMessage());
        }
    }
    /*
     * 轮询处理
     */
    public void afterCallIssuerOrderCancel(OrderAftersalesApplyEntity orderAftersalesApply,IssuerAftersalesApplyVO issuerAftersalesApplyVO) {
        IssuerAftersalesStatusEnum issuerAftersalesStatusEnum = IssuerAftersalesStatusEnum.getByStatus(issuerAftersalesApplyVO.getBussinessStatus());
        if(issuerAftersalesStatusEnum == null){
            ToolsHelper.throwException("处理状态异常");
        }
        switch (issuerAftersalesStatusEnum) {
            //拒绝
            case STATUS_REJECT -> {
                //取消售后单申请，返回msg
                String error = "申请发卡方取消失败，" + issuerAftersalesApplyVO.getMsg();
                orderAftersalesApplyService.modifyStatus(
                        orderAftersalesApply.getApplySn(),
                        OrderAftersalesStatusEnum.STATUS_PROCESSING.getStatus(),
                        error
                );
                ToolsHelper.throwException(error);
            }
            //成功
            case STATUS_FINISH ->
                orderAftersalesApplyService.modifyStatus(
                        orderAftersalesApply.getApplySn(),
                        OrderAftersalesStatusEnum.STATUS_SUCCESS.getStatus(),
                        ""
                );

            default -> {
                if(!Objects.equals(orderAftersalesApply.getStatus(), OrderAftersalesStatusEnum.STATUS_PROCESSING.getStatus())){
                    orderAftersalesApplyService.modifyStatus(
                            orderAftersalesApply.getApplySn(),
                            OrderAftersalesStatusEnum.STATUS_PROCESSING.getStatus(),
                            ""
                    );
                }

                ToolsHelper.throwException("申请发卡方取消进行中");
            }

        }
    }

    public void afterCallIssuerOrderReset(OrderAftersalesApplyEntity orderAftersalesApply,OrderOrderEntity orderOrder,IssuerAftersalesApplyVO issuerAftersalesApplyVO) {
        IssuerAftersalesStatusEnum issuerAftersalesStatusEnum = IssuerAftersalesStatusEnum.getByStatus(issuerAftersalesApplyVO.getBussinessStatus());
        if(issuerAftersalesStatusEnum == null){
            ToolsHelper.throwException("处理状态异常");
        }

        switch (issuerAftersalesStatusEnum) {
            //拒绝
            case STATUS_REJECT ->{
                //取消售后单申请，返回msg
                String error = "申请发卡方重置失败，" + issuerAftersalesApplyVO.getMsg();
                orderAftersalesApplyService.modifyStatus(
                        orderAftersalesApply.getApplySn(),
                        OrderAftersalesStatusEnum.STATUS_PROCESSING.getStatus(),
                        error
                );
                ToolsHelper.throwException(error);
            }
            //成功
            case STATUS_FINISH -> {
                //售后申请单
                if (!Objects.equals(orderAftersalesApply.getStatus(), OrderAftersalesStatusEnum.STATUS_SUCCESS.getStatus())) {
                    orderAftersalesApplyService.modifyStatus(
                            orderAftersalesApply.getApplySn(),
                            OrderAftersalesStatusEnum.STATUS_SUCCESS.getStatus(),
                            ""
                    );
                }
            }
            default -> ToolsHelper.throwException("申请发卡方重置进行中");

        }
    }
    /*
     * 处理商品售后
     */
    public void dealBussinessStatusIsFinished(OrderAftersalesApplyEntity orderAftersalesApply,OrderOrderEntity orderOrder) {
        if(Objects.equals(orderAftersalesApply.getBusinessStatus(), OrderBusinessStatusEnum.STATUS_FINISHED.getStatus())) {
            return ;
        }
        //仅取消类型，跟商品售后无关
        if (Objects.equals(orderAftersalesApply.getType(), OrderAftersalesTypeEnum.TYPE_UNBIND_ONLY.getType())) {
            orderAftersalesApplyService.modifyBusinessStatus(
                    orderAftersalesApply.getApplySn(),
                    OrderBusinessStatusEnum.STATUS_FINISHED.getStatus(),
                    ""
            );
            return ;
        }
        //非仅取消类型 请求售后接口查状态
        GetCustomerServiceOrderDTO dto = new GetCustomerServiceOrderDTO();
        dto.setBusinessSn(orderAftersalesApply.getOrderSn());
        dto.setServiceType(Objects.requireNonNull(OrderAftersalesTypeEnum.getByType(orderAftersalesApply.getType())).getServiceType());
        GetCustomerServiceOrderVO getCustomerServiceOrderVO = orderAftersalesBusiness.getCustomerServiceOrder(dto);

        switch(Objects.requireNonNull(CustomerServiceStatus.getByCode(getCustomerServiceOrderVO.getStatus()))){
            case CREATED:
            case FAILED:
                ToolsHelper.throwException("商品售后未完成");
            case FINISHED:
                orderAftersalesApplyService.modifyBusinessStatus(
                        orderAftersalesApply.getApplySn(),
                        OrderBusinessStatusEnum.STATUS_FINISHED.getStatus(),
                        ""
                );
                return ;
            //商品售后取消
            case CANCELED:{
                orderAftersalesApplyService.modifyStatus(
                        orderAftersalesApply.getApplySn(),
                        OrderAftersalesStatusEnum.STATUS_FAIL.getStatus(),
                        ""
                );
                //恢复申办订单的售后状态为正常
                orderOrderService.aftersaleStatusUpdate(orderOrder,AftersaleStatus.STATUS_NORMAL.getValue(),"商品售后单已取消");
                //更新电商订单状态
                productOrderBusiness.aftersalesMarkByOrderSn(orderOrder,2,orderAftersalesApply.getApplySn());
                ToolsHelper.throwException("商品售后单已取消", TaskRecordErrorCodeConstant.ERROR_CODE_NEED_FINISH);
            }


            default:
                ToolsHelper.throwException("售后异常");
        }
    }



    /*
     * 检查发卡方售后是否已完成
     */
    public void dealStatusIsFinished(OrderAftersalesApplyEntity orderAftersalesApply,OrderOrderEntity orderOrder,TaskRecordEntity taskRecord) {
        //已完成
        if(Objects.equals(orderAftersalesApply.getStatus(), OrderAftersalesStatusEnum.STATUS_SUCCESS.getStatus())) {
            return ;
        }
        //发卡方售后是否处理中，是否需要验证码
        if(
                Objects.equals(orderAftersalesApply.getStatus(), OrderAftersalesStatusEnum.STATUS_APPLY.getStatus()) &&
                        Arrays.asList(
                                OrderAftersalesTypeEnum.TYPE_UNBIND_REFUND.getType(),
                                OrderAftersalesTypeEnum.TYPE_UNBIND_ONLY.getType(),
                                OrderAftersalesTypeEnum.TYPE_UNBIND_CANCEL.getType()
                        ).contains(orderAftersalesApply.getType())&&
                        StringUtils.isNotEmpty(orderOrder.getReviewOrderSn()) &&
                        orderAftersalesBusiness.checkCardIdIsWl(orderOrder.getCardId())
        ){
            JSONObject notifyContent = JSONObject.parseObject(taskRecord.getNotifyContent());
            //判断是否需要推送模板消息
            Integer isSendCode = notifyContent.getInteger( "isSendCode");
            if(isSendCode == null){
                AuditMsgBO auditMsgBO = new AuditMsgBO();
                auditMsgBO.setAfterSalesType(OrderAftersalesTypeEnum.getDescByType(orderAftersalesApply.getType()));
                auditMsgBO.setAfterSalesStatus("待实名验证");
                auditMsgBO.setOrderSn(orderOrder.getOrderSn());
                auditMsgBO.setGuidText("验证实名解绑车辆，避免影响后续ETC办理");
                callEtcNotificationFeign.noticePush( new SendRuleMsgDTO(orderOrder.getUid(), sendRuleMsgConfig.getAduitTemplate(), "", auditMsgBO, "21:00:00", "09:00:00"));
                //更新task
                notifyContent.put( "isSendCode", 1);
                taskRecord.setNotifyContent(notifyContent.toJSONString());
                taskRecordService.saveOrUpdate(taskRecord);
            }

            ToolsHelper.throwException("需要验证码");
        }
        try {
            IssuerAftersalesApplyVO issuerAftersalesApplyVO;
            //根据不同的类型进行处理
            switch (Objects.requireNonNull(OrderAftersalesTypeEnum.getByType(orderAftersalesApply.getType()))) {
                //解绑【退货退款，仅取消】
                case TYPE_UNBIND_REFUND, TYPE_UNBIND_ONLY, TYPE_UNBIND_CANCEL -> {
                    //取消发卡方订单
                    issuerAftersalesApplyVO = orderAftersalesBusiness.callIssuerOrderCancel(orderAftersalesApply, orderOrder, "");
                    //处理申请结果
                    afterCallIssuerOrderCancel(orderAftersalesApply,issuerAftersalesApplyVO);
                }
                //换设备
                //补办设备
                case TYPE_EXCHANGE_GOODS, TYPE_REISSUE_GOODS -> {
                    //重置发行状态
                    issuerAftersalesApplyVO = orderAftersalesBusiness.callIssuerOrderReset(orderAftersalesApply, orderOrder);
                    afterCallIssuerOrderReset(orderAftersalesApply, orderOrder, issuerAftersalesApplyVO);
                }
                default -> ToolsHelper.throwException("申请类型不支持");
            }
        }catch (BizException e) {
            //发送模板消息
            JSONObject notifyContent = JSON.parseObject(taskRecord.getNotifyContent());
            //判断是否需要推送模板消息
            Integer isSendIssuerFail = notifyContent.getInteger( "isSendIssuerFail");
            if(isSendIssuerFail == null){
                AuditMsgBO auditMsgBO = new AuditMsgBO();
                auditMsgBO.setAfterSalesType(OrderAftersalesTypeEnum.getDescByType(orderAftersalesApply.getType()));
                auditMsgBO.setAfterSalesStatus("处理异常");
                auditMsgBO.setGuidText(e.getMessage().length() > 20 ? e.getMessage().substring(0,20):e.getMessage());
                auditMsgBO.setOrderSn(orderOrder.getOrderSn());
                callEtcNotificationFeign.noticePush( new SendRuleMsgDTO(orderOrder.getUid(), sendRuleMsgConfig.getAduitTemplate(), "", auditMsgBO, "21:00:00", "09:00:00"));
                //更新task
                notifyContent.put( "isSendIssuerFail", 1);
                taskRecord.setNotifyContent(notifyContent.toJSONString());
                taskRecordService.saveOrUpdate(taskRecord);
            }
            //原路输出
            ToolsHelper.throwException(e.getMessage(), e.getErrorCode());
        }


    }

    /*
     *  仅取消，退货退款，仅退款 完成后公共部分
     */
    public void afterIssuerOrderCancelFinished(OrderAftersalesApplyEntity orderAftersalesApply,OrderOrderEntity orderOrder) {
        ReviewOrderEntity reviewOrder = reviewOrderService.getNeedDealReviewOrderSn(orderOrder);
        if (reviewOrder != null) {
            //取消审核平台的审核单
            orderAftersalesBusiness.cancelReviewOrder(orderOrder.getOrderSn(), orderAftersalesApply.getReason());
            //取消用户卡签数据
            if (reviewOrder.getThirdReviewStatus() == ThirdReviewStatus.pass.getCode()) {
                orderAftersalesBusiness.cancelUserCard(orderOrder.getOrderSn(), orderOrder.getUid());
            }
        }
        orderOrderService.aftersaleStatusUpdate(orderOrder,AftersaleStatus.STATUS_APPLY_FINISH.getValue(), orderAftersalesApply.getReason());
        //更新电商订单状态
        productOrderBusiness.aftersalesMarkByOrderSn(orderOrder, 1, orderAftersalesApply.getApplySn());
        //清理当前订单 apply_order_sn
        callPhpUserFeign.updateUserApplyOrderSn(new OrderAftersalesCancelUserCardDTO("",orderOrder.getUid()));
        //实名清理
        realNameBusiness.addRealNameClearTask(orderOrder.getUid(),"OrderAftersalesApplyTask");
    }

}
