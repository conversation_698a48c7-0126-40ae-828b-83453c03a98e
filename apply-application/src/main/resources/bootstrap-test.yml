spring:
  application:
    name: apply-application
  cloud:
    nacos:
      config:
        server-addr: nacos.public:8848
        namespace: test
        group: apply
        file-extension: yaml
        extension-configs:
          - data-id: apply-mysql.yaml
            group: apply
          - data-id: common-starter-config.yaml
            group: apply
          - data-id: apply-cos-tencent.properties
            group: apply
          - data-id: big-data-config.yaml
            group: apply
            refresh: true
          - data-id: template-msg-ids.yaml
            group: apply
            refresh: true
          - data-id: apply-credit-card.yaml
            group: apply
            refresh: true

          - data-id: apply-application.yaml
            group: apply
            refresh: true
