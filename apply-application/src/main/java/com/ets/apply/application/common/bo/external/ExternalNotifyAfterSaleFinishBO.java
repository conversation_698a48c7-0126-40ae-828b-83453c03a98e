package com.ets.apply.application.common.bo.external;

import lombok.Data;

@Data
public class ExternalNotifyAfterSaleFinishBO {

    // 售后服务单状态
    private Integer customerStatus = 4;

    /**
     * 外部用户标识(oneid)
     */
    private String userCode;

    /**
     * 高灯订单号
     */
    private String spOrderId;

    /**
     * 售后服务类型
     */
    private Integer serviceType = 1;

    /**
     * 完成时间
     */
    private String finishTime;
}
