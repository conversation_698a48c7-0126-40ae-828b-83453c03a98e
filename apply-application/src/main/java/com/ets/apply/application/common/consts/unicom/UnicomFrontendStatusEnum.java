package com.ets.apply.application.common.consts.unicom;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum UnicomFrontendStatusEnum {

    WAIT_HOME(1, "待业务人员上门"),

    ACTIVATED(2, "激活且已充值"),

    CANCELED(9, "已失效");


    private final int code;
    private final String description;

    public static String getDescByCode(int code) {
        for (UnicomFrontendStatusEnum node : UnicomFrontendStatusEnum.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }
}
