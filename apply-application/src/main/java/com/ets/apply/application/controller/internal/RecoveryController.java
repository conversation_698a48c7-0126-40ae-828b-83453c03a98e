package com.ets.apply.application.controller.internal;

import com.ets.apply.application.app.business.RecoveryBusiness;
import com.ets.apply.application.common.dto.recovery.RecoveryDTO;
import com.ets.apply.application.common.vo.recovery.RecoveryCheckAllowApplyVO;
import com.ets.apply.application.common.vo.recovery.RecoveryRewardCheckVO;
import com.ets.apply.application.common.vo.recovery.RecoveryRewardVO;
import com.ets.apply.application.controller.BaseController;
import com.ets.common.JsonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;


@RequestMapping("/recovery")
@RefreshScope
@RestController
public class RecoveryController extends BaseController {

    @Autowired
    private RecoveryBusiness recoveryBusiness;

    @RequestMapping("/checkAllowApply")
    public JsonResult<RecoveryCheckAllowApplyVO> checkAllowApply(@RequestBody @Valid RecoveryDTO dto) {

        return JsonResult.ok(recoveryBusiness.checkAllowApply(dto.getOrderSn()));
    }

    @RequestMapping("/rewardCheck")
    public JsonResult<RecoveryRewardCheckVO> rewardCheck(@RequestBody @Valid RecoveryDTO dto) {

        return JsonResult.ok(recoveryBusiness.rewardCheck(dto.getOrderSn()));
    }

    @RequestMapping("/reward")
    public JsonResult<RecoveryRewardVO> reward(@RequestBody @Valid RecoveryDTO dto) {

        return JsonResult.ok(recoveryBusiness.reward(dto.getOrderSn(), dto.getReturnOrderSn()));
    }

}
