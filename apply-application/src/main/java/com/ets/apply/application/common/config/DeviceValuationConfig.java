package com.ets.apply.application.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;

/**
 * <AUTHOR>
 */
@RefreshScope
@Configuration
@Data
@ConfigurationProperties(prefix = "device-valuation")
public class DeviceValuationConfig {

    private HashMap<String,HashMap<String, String>>goodsMap;

    /**
     * 获取列表的限制天数
     */
    private Integer limitListDays;
}
