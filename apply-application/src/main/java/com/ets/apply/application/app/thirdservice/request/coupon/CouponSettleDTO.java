package com.ets.apply.application.app.thirdservice.request.coupon;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class CouponSettleDTO {
    /**
     * 类目，不能为空
     */
    private String categoryNo;

    /**
     * 用户id，不能为空
     */
    private Long uid;

    /**
     * 订单金额，不能为空
     */
    private BigDecimal orderAmount;

    /**
     * 满减金额，有可能不使用订单金额作为满减金额，比如洗车业务可能有vip价，
     * 订单金额为vip价，但是判断是否满足满减使用的是非vip价，如果不传，就使用订单金额
     */
    private BigDecimal fullValue;

    /**
     * 优惠券编号，可以为空
     */
    private String couponNo;

    /**
     * 使用平台：A0101车点点、A0102田螺、A0103智能洗车等
     */
    private String usePlatform;

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 车牌颜色
     */
    private Integer plateColor;

    /**
     * 券批次列表
     */
    private List<String> couponBatchNos;
}
