package com.ets.apply.application.app.thirdservice.fallback;

import com.ets.apply.application.app.thirdservice.feign.CgbFeign;
import com.ets.common.JsonResult;
import feign.Response;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

@Component
public class CgbFallbackFactory implements FallbackFactory<CgbFeign> {
    @Override
    public CgbFeign create(Throwable throwable) {
        return (uri, params, headers) -> Response.builder()
                .body(JsonResult.error("请求广发接口异常：" + throwable.getLocalizedMessage()).toString().getBytes())
                .build();
    }
}
