package com.ets.apply.application.app.factory.dynamicRecommend;

import cn.hutool.extra.spring.SpringUtil;
import com.ets.apply.application.app.factory.dynamicRecommend.impl.BigDataRecommendStrategy;
import com.ets.apply.application.app.factory.dynamicRecommend.impl.CreditCardOrderRecommendStrategy;
import com.ets.apply.application.app.factory.dynamicRecommend.impl.PaidOrderRecommendStrategy;
import com.ets.apply.application.app.factory.dynamicRecommend.impl.UnpaidOrderRecommendStrategy;
import com.ets.apply.application.common.consts.dynamicRecomend.DynamicRecommendTypeEnum;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class DynamicRecommendStrategyFactory {
    private static final Map<DynamicRecommendTypeEnum, RecommendStrategy> STRATEGY_MAP = new HashMap<>();

    static {
        STRATEGY_MAP.put(DynamicRecommendTypeEnum.RECOMMEND_BY_UNPAID_ORDER,
                SpringUtil.getBean(UnpaidOrderRecommendStrategy.class));
        STRATEGY_MAP.put(DynamicRecommendTypeEnum.RECOMMEND_BY_ORDER_CANCEL,
                SpringUtil.getBean(BigDataRecommendStrategy.class));
        STRATEGY_MAP.put(DynamicRecommendTypeEnum.RECOMMEND_BY_USER_ACCESS_PAGE,
                SpringUtil.getBean(BigDataRecommendStrategy.class));
        STRATEGY_MAP.put(DynamicRecommendTypeEnum.RECOMMEND_BY_CREDIT_CARD,
                SpringUtil.getBean(CreditCardOrderRecommendStrategy.class));
        STRATEGY_MAP.put(DynamicRecommendTypeEnum.RECOMMEND_BY_PAID_ORDER,
                SpringUtil.getBean(PaidOrderRecommendStrategy.class));
    }

    public static RecommendStrategy getRecommendStrategy(DynamicRecommendTypeEnum recommendTypeEnum) {
        return STRATEGY_MAP.get(recommendTypeEnum);
    }
}
