package com.ets.apply.application.common.vo.map;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class MapThirdPartnerListVO {

    /**
     * 模块
     */
    private Integer id;
    /**
     * 模块
     */
    private String module;


    /**
     * 模块名称
     */
    private String companyId;

    /**
     * 模块名称
     */
    private String name;

    /*
     * 状态 1-有效 2-无效
     */
    private Integer status;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

}
