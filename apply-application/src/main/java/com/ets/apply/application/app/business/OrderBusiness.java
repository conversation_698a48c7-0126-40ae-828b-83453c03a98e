package com.ets.apply.application.app.business;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.csp.sentinel.util.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.ets.apply.application.app.business.creditCard.CreditCardBusiness;
import com.ets.apply.application.app.business.reviewOrder.ReviewOrderBusiness;
import com.ets.apply.application.app.business.reviewOrderRiskCheck.ReviewOrderRiskCheckBusiness;
import com.ets.apply.application.app.factory.orderRiskCheck.RiskCheckFactory;
import com.ets.apply.application.app.service.bigdata.BigDataService;
import com.ets.apply.application.app.thirdservice.feign.AftersaleFeign;
import com.ets.apply.application.app.thirdservice.feign.CallGoodsApplication;
import com.ets.apply.application.app.thirdservice.feign.CallPhpPayFeign;
import com.ets.apply.application.app.thirdservice.feign.DeliveryFeign;
import com.ets.apply.application.app.thirdservice.request.LogisticsFindByOrderSnDTO;
import com.ets.apply.application.app.thirdservice.request.aftersale.CheckAuthorizeActivateDTO;
import com.ets.apply.application.app.thirdservice.response.LogisticsVO;
import com.ets.apply.application.app.thirdservice.response.aftersale.AuthorizeActivateVO;
import com.ets.apply.application.common.config.ActivityConfig;
import com.ets.apply.application.common.config.AllowApplyProductPackageRule;
import com.ets.apply.application.common.config.AuthorizeActivateConfig;
import com.ets.apply.application.common.consts.reviewOrderRiskCheck.RiskStatus;
import com.ets.apply.application.common.consts.activityCreditCardUsersInfo.ActivityCreditCardUserInfoWhichBankConstant;
import com.ets.apply.application.common.consts.apply.ExceptionEnum;
import com.ets.apply.application.common.consts.card.CarTypeEnum;
import com.ets.apply.application.common.consts.card.CardIssuerClassEnum;
import com.ets.apply.application.common.consts.card.CardIssuerIdEnum;
import com.ets.apply.application.common.consts.card.CardTypeEnum;
import com.ets.apply.application.common.consts.common.GenerateSnEnum;
import com.ets.apply.application.common.consts.common.YesOrNoEnum;
import com.ets.apply.application.common.consts.creditCard.CityConstant;
import com.ets.apply.application.common.consts.creditCardBankConfig.CreditCardBankConfigBankStatusEnum;
import com.ets.apply.application.common.consts.entrust.EntrustModeEnum;
import com.ets.apply.application.common.consts.order.*;
import com.ets.apply.application.common.consts.payment.PaymentModeEnum;
import com.ets.apply.application.common.consts.productPackage.DeviceTypeEnum;
import com.ets.apply.application.common.consts.productPackage.ProductPackageStatusEnum;
import com.ets.apply.application.common.consts.reviewOrder.ReviewOrderGuide;
import com.ets.apply.application.common.consts.reviewOrder.ThirdReviewStatus;
import com.ets.apply.application.common.consts.reviewOrderRiskCheck.RiskType;
import com.ets.apply.application.common.dto.order.GetInfoByPaymentSnDTO;
import com.ets.apply.application.common.dto.order.GetUnpaidOrderDetailDTO;
import com.ets.apply.application.common.dto.order.HideInfoDTO;
import com.ets.apply.application.common.dto.order.OrderBeforeActivateImgDTO;
import com.ets.apply.application.common.dto.request.applyOrder.GetBanksByCityDTO;
import com.ets.apply.application.common.dto.request.applyOrder.GetBanksByPackageSnDTO;
import com.ets.apply.application.common.dto.request.order.*;
import com.ets.apply.application.common.dto.request.order.AuthorizeActivateOrderDetailDTO;
import com.ets.apply.application.common.dto.request.order.AuthorizeActivateOrderListDTO;
import com.ets.apply.application.common.dto.request.order.EffectOrderListDTO;
import com.ets.apply.application.common.dto.request.order.OrderFindDTO;
import com.ets.apply.application.common.utils.UserUtil;
import com.ets.apply.application.common.vo.UserSourceInfoVo;
import com.ets.apply.application.common.vo.apply.BanksByPackageSnVo;
import com.ets.apply.application.common.vo.order.*;
import com.ets.apply.application.common.vo.response.order.EffectOrderResponse;
import com.ets.apply.application.infra.entity.*;
import com.ets.apply.application.infra.relation.order.OrderOrderBindCardsRelation;
import com.ets.apply.application.infra.relation.order.OrderOrderBindProductPackageRelation;
import com.ets.apply.application.infra.relation.order.OrderOrderBindReviewOrderRelation;
import com.ets.apply.application.infra.service.*;
import com.ets.apply.application.vo.UserIdCardAndVehicleVo;
import com.ets.apply.feign.request.FindOneOrderReq;
import com.ets.apply.feign.request.OrderListDTO;
import com.ets.apply.feign.response.OrderAddressResponse;
import com.ets.apply.feign.response.OrderPackageInfoResponse;
import com.ets.apply.feign.response.OrderResponse;
import com.ets.common.BeanHelper;
import com.ets.common.BizException;
import com.ets.common.JsonResult;
import com.ets.common.ToolsHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Component
public class OrderBusiness extends BaseBusiness {

    @Autowired
    private OrderOrderService orderService;
    @Autowired
    private ReviewOrderService reviewOrderService;
    @Autowired
    private CardsService cardsService;

    @Autowired
    private ReviewOrderIdCardService idCardService;

    @Autowired
    private ReviewOrderVehicleService vehicleService;

    @Autowired
    private LogisticOrderService logisticOrderService;
    @Autowired
    private ReviewOrderIdCardService reviewOrderIdCardService;
    @Autowired
    private ReviewOrderBizLicenceService reviewOrderBizLicenceService;
    @Autowired
    private ProductPackageService productPackageService;

    @Autowired
    private ActivityConfig activityConfig;

    @Autowired
    private IssueServiceService issueServiceService;

    @Autowired
    private AllowApplyProductPackageRule allowApplyProductPackageRule;

    @Autowired
    private AuthorizeActivateConfig authorizeActivateConfig;

    @Autowired
    private AftersaleFeign aftersaleFeign;
    @Autowired
    private CallPhpPayFeign callPhpPayFeign;

    @Autowired
    private CallGoodsApplication callGoodsApplication;

    @Autowired
    private DeliveryFeign deliveryFeign;
    @Autowired
    private ProductOrderService productOrderService;
    @Autowired
    private ProductManualService productManualService;

    @Autowired
    private ProductPackageBusiness productPackageBusiness;

    @Autowired
    private CreditCardBankConfigService creditCardBankConfigService;

    @Autowired
    private CreditCardBusiness creditCardBusiness;

    @Autowired
    private ReviewOrderRiskCheckService reviewOrderRiskCheckService;

    @Autowired
    private ReviewOrderRiskCheckBusiness reviewOrderRiskCheckBusiness;

    @Autowired
    private ReviewOrderBusiness reviewOrderBusiness;
    public Object getUserHaveMaterialsValidCarOrders(Long uid) {
        List<Integer> cardIds = cardsService.getAllTruckIds();
        List<OrderOrderEntity> orders = orderService.getUserValidCarOrders(uid, cardIds);
        //检查订单里面，用户是否已经上传用户资料及车档信息
        if (ObjectUtils.isEmpty(orders)) {
            return null;
        }

        Map<String, UserIdCardAndVehicleVo> userInfoMap = new HashMap<>();
        orders.forEach(order -> {
            UserIdCardAndVehicleVo vo = new UserIdCardAndVehicleVo();
            ReviewOrderIdCardEntity idCard = idCardService.getByReviewOrderSn(order.getReviewOrderSn());
            if (ObjectUtils.isEmpty(idCard)) {
                return;
            }

            if (StringUtils.isEmpty(idCard.getName()) || StringUtils.isEmpty(idCard.getNumber())) {
                return;
            }

            vo.setOrderSn(order.getOrderSn());
            vo.setReviewOrderSn(order.getReviewOrderSn());
            vo.setName(idCard.getName());
            vo.setIdCardNo(idCard.getNumber());
            vo.setPlateNo(order.getPlateNo());
            vo.setOwnerCredentialType("0" + idCard.getIdcardType());
            ReviewOrderVehicleEntity vehicleInfo = vehicleService.getByReviewOrderSn(order.getReviewOrderSn());
            if (ObjectUtils.isNotEmpty(vehicleInfo)) {
                vo.setVin(vehicleInfo.getVin());
                vo.setCarOwnerName(vehicleInfo.getOwner());
                vo.setEngineNo(vehicleInfo.getEngineNo());
                vo.setFirstRegisterDate(vehicleInfo.getRegisterDate());
                vo.setModelName(vehicleInfo.getModel());
            }

            userInfoMap.put(order.getPlateNo(), vo);
        });

        return userInfoMap;
    }

    /**
     * 通过车牌号码及车牌颜色, 获取最新订单记录
     * @param req:参数
     * @return: 返回对象
     */
    public OrderResponse findOneByPlate(FindOneOrderReq req) {
        OrderResponse response = new OrderResponse();
        OrderOrderEntity entity = orderService.findOne(req.getPlateNo(), req.getPlateColor());
        if (ObjectUtils.isEmpty(entity)) {
            return null;
        }
        Boolean isPre = entity.isPrePush(entity.getFlowType());
        BeanUtils.copyProperties(entity, response);
        response.setIsPre(isPre);
        return response;
    }

    /**
     * 通过订单号获取订单信息
     * @param orderSn:订单号
     * @return:订单对象
     */
    public OrderResponse finOneByOrderSn(String orderSn) {
        OrderResponse response = new OrderResponse();
        OrderOrderEntity entity = orderService.findByOrderSn(orderSn);
        if (ObjectUtils.isEmpty(entity)) {
            return null;
        }
        Boolean isPre = entity.isPrePush(entity.getFlowType());
        BeanUtils.copyProperties(entity, response);
        response.setIsPre(isPre);
        return response;
    }

    /**
     * 根据订单号获取订单列表数据
     * @param orderSns
     * @return
     */
    public List<OrderResponse> getOrderListBySn(List<String> orderSns) {

        if (orderSns == null || orderSns.size() == 0) {
            return null;
        }

        List<OrderOrderEntity> orderEntities = orderService.getListBySns(orderSns, OrderOrderEntity::getOrderSn);

        List<OrderResponse> result = new ArrayList<>();

        for (OrderOrderEntity orderEntity: orderEntities) {
            result.add(BeanHelper.copy(OrderResponse.class, orderEntity));
        }

        return result;
    }

    /**
     * 根据订单号获取订单信息
     * @param orderSn 订单号
     * @return OrderOrderEntity
     * @throws BizException 业务异常
     */
    public OrderOrderEntity getByOrderSn(String orderSn) throws BizException {
        if (StringUtils.isEmpty(orderSn)) {
            ToolsHelper.throwException("订单号不能为空");
        }

        OrderOrderEntity orderEntity = orderService.getByOrderSn(orderSn);
        if (orderEntity == null) {
            ToolsHelper.throwException("订单不存在");
        }

        return orderEntity;
    }

    /**
     * 通过订单详情 获取用户信息
     * @param OrderSn
     * @return
     * @throws BizException
     */
    public UserSourceInfoVo getUserInfoByOrderSn(String OrderSn) {

        OrderOrderEntity orderEntity = getByOrderSn(OrderSn);
        ReviewOrderIdCardEntity idCard = null;
        UserSourceInfoVo userInfoVo = new UserSourceInfoVo();
        if (!StringUtils.isEmpty(orderEntity.getReviewOrderSn())) {
            idCard = reviewOrderIdCardService.getByReviewOrderSn(orderEntity.getReviewOrderSn());
            if(idCard != null){
                BeanUtils.copyProperties(idCard, userInfoVo);
            }
        }
        if(orderEntity.getOrderSn() != null){
            BeanUtils.copyProperties(orderEntity, userInfoVo);
        }

        return userInfoVo;
    }

    public List<OrderResponse> getOrderListByPlate(OrderListDTO orderListDTO) {
        List<OrderOrderEntity> orderList = orderService.getOrdersByPlateNo(orderListDTO.getPlateNo());
        List<OrderResponse> returnList = new ArrayList<>();
        orderList.forEach(order -> {
            OrderResponse orderResponse = BeanUtil.copyProperties(order, OrderResponse.class);
            returnList.add(orderResponse);
        });
        return returnList;
    }

    /*
     * 通过产品包获取到产品包
     */
    public BanksByPackageSnVo getBanksByPacakgeSn(GetBanksByPackageSnDTO dto,Long uid) {
        BanksByPackageSnVo banksByPackageSnVoList = new BanksByPackageSnVo();
        ProductPackageEntity productPackageEntity = productPackageService.getBySn(dto.getPackageSn());
        if( productPackageEntity == null ){
            return banksByPackageSnVoList;
        }
        //查询产品包的银行列表和版本号
        JSONObject jsonObject = JSONObject.parseObject(productPackageEntity.getPackageInfo());
        if(jsonObject.getString("credit_bank") == null){
            return banksByPackageSnVoList;
        }
        // 城市不传就从大数据获取
        if (ObjectUtil.isEmpty(dto.getCity())) {
            dto.setCity(creditCardBusiness.getUserBankCity(uid));
        }
        JSONObject creditBank = JSONObject.parseObject(jsonObject.getString("credit_bank"));
        banksByPackageSnVoList.setVersion(creditBank.getString("version"));
        List<BanksByPackageSnVo.Banks> banksList = new ArrayList<>();
        for (Object bank : creditBank.getJSONArray("banks")) {

            // 银行办理限制
            List<Integer> normalBankIds = creditCardBankConfigService.getBankIdListByBankStatus(
                    CreditCardBankConfigBankStatusEnum.NORMAL.getStatus()
            );

            Integer bankId = (Integer) bank;

            if (!normalBankIds.contains(bankId)) {
                continue;
            }
            BanksByPackageSnVo.Banks banks = new BanksByPackageSnVo.Banks();
            banks.setBank((Integer) bank);

            List<String> listCity = new ArrayList<>();
            switch ((Integer) bank) {
                case ActivityCreditCardUserInfoWhichBankConstant.GUANG_FA_INTERFACE:
                    listCity = CityConstant.CITY_GDB_LIST;
                    break;
                case ActivityCreditCardUserInfoWhichBankConstant.PING_AN_INTERFACE:
                    listCity = CityConstant.CITY_PAB_LIST;
                    break;
                case ActivityCreditCardUserInfoWhichBankConstant.JIAO_TONG:
                    listCity = CityConstant.CITY_COMM_LIST;
                    break;
                case ActivityCreditCardUserInfoWhichBankConstant.CMBC:
                    listCity = CityConstant.CITY_CMBC_LIST;
                    break;
                case ActivityCreditCardUserInfoWhichBankConstant.SPD:
                    listCity = CityConstant.CITY_SPD_LIST;
                    break;
                case ActivityCreditCardUserInfoWhichBankConstant.CEB:
                    listCity = CityConstant.CITY_CEB_LIST;
                    break;
                case ActivityCreditCardUserInfoWhichBankConstant.CITIC:
                    listCity = CityConstant.CITY_CITIC_LIST;
                    break;
            }
            if(listCity.contains(dto.getCity())){
                banks.setCanApply(true);
            }
            banksList.add(banks);
        }
        banksByPackageSnVoList.setBanks(banksList);
        return banksByPackageSnVoList;

    }

    public OrderAddressResponse getOrderAddressByOrderSn(String orderSn) {
        OrderAddressResponse response = new OrderAddressResponse();
        OrderOrderEntity entity = orderService.getAddressInfo(orderSn);
        if(entity == null){
            return null;
        }
        BeanUtils.copyProperties(entity, response);
        return response;
    }

    public OrderPackageInfoResponse getOrderPackageInfoByOrderSn(String orderSn) {
        OrderOrderEntity order = getByOrderSn(orderSn);
        OrderPackageInfoResponse orderPackageInfo = BeanUtil.copyProperties(order, OrderPackageInfoResponse.class);
        if (!StringUtils.isEmpty(order.getPackageSn())) {
            ProductPackageEntity productPackage = productPackageService.getBySn(order.getPackageSn());
            if (productPackage != null) {
                JSONObject jsonObject = JSONObject.parseObject(productPackage.getPackageInfo());
                if (jsonObject != null) {
                    OrderPackageInfoResponse.PackageInfo packageInfo = new OrderPackageInfoResponse.PackageInfo();
                    packageInfo.setPackageFee(productPackage.getPackageFee());
                    packageInfo.setPackageName(productPackage.getPackageName());
                    Integer manufacturer = jsonObject.getInteger("manufacturer");
                    if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(manufacturer)) {
                        packageInfo.setManufacturer(manufacturer);
                    }
                    Integer deviceType = jsonObject.getInteger("device_type");
                    if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(deviceType)) {
                        packageInfo.setDeviceType(deviceType);
                    }
                    if (jsonObject.containsKey("java")) {
                        JSONObject java = jsonObject.getJSONObject("java");
                        if (java !=null) {
                            Integer term = java.getInteger("term");
                            Integer giftRights = java.getInteger("gift_rights");

                            OrderPackageInfoResponse.PackageInfo.Marketing marketing = new OrderPackageInfoResponse.PackageInfo.Marketing();
                            marketing.setTerm(term);
                            marketing.setGiftRights(giftRights);
                            packageInfo.setMarketing(marketing);
                        }
                    }
                    orderPackageInfo.setPackageInfo(packageInfo);
                }
            }
        }

        return orderPackageInfo;
    }

    public OrderGiftRightVO getUserEffectOrderOrActivatedOrderAndCheckGiftRight(Long uid, Integer giftRights){
        OrderGiftRightVO orderGiftRightVO  =new OrderGiftRightVO();
        orderGiftRightVO.setHasEffectOrder(false);
        orderGiftRightVO.setGiftRights(false);

        try{
            List<OrderOrderEntity> orders = orderService.getUserEffectOrderOrActivatedOrder(uid);
            //检查订单里面
            if (ObjectUtils.isEmpty(orders)) {
                return orderGiftRightVO;
            }
            orderGiftRightVO.setHasEffectOrder(true);

            orders.forEach(order ->{
                if (StringUtils.isEmpty(order.getPackageSn())){
                    return ;
                }
                String packageInfo = productPackageService.getPackageInfoBySnWithCache(order.getPackageSn());
                // TODO 优化循环
                if (!StringUtils.isEmpty(packageInfo)) {
                    JSONObject jsonObject = JSONObject.parseObject(packageInfo);
                    if (jsonObject != null && jsonObject.containsKey("java")) {
                        JSONObject java = jsonObject.getJSONObject("java");
                        if (java !=null) {
                            Integer orderGiftRights = java.getInteger("gift_rights");
                            if(giftRights.equals(orderGiftRights)){
                                orderGiftRightVO.setGiftRights(true);
                            }

                        }
                    }
                }

            });
        }catch (Exception e){
            log.info("getUserEffectOrderOrActivatedOrderAndCheckGiftRight 处理异常:"+e.getMessage());
        }

        return  orderGiftRightVO;
    }

    public Boolean checkUserHasSatisfyOrder() {
        Long uid = UserUtil.getUid();
        // 可做成参数反射获取
        ActivityConfig.OrderCondition condition = activityConfig.getMengNiu();
        if (ObjectUtils.isEmpty(condition)) {
            ToolsHelper.throwException("活动配置异常");
        }

        if (LocalDateTime.now().isAfter(LocalDateTime.parse(condition.getExpiredTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))) {
            ToolsHelper.throwException("活动已过期");
        }

        // 支付时间范围内 已支付 未取消的订单 没有售后或售后中或售后取消
        LambdaQueryWrapper<OrderOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderOrderEntity::getUid, uid)
                .in(OrderOrderEntity::getStatus, condition.getOrderStatus())
                .in(OrderOrderEntity::getAftersaleStatus,
                        Arrays.asList(AftersaleStatus.STATUS_NORMAL.getValue(),
                                AftersaleStatus.STATUS_APPLY.getValue(),
                                AftersaleStatus.STATUS_APPLY_CANCEL.getValue()
                        ))
                .ge(OrderOrderEntity::getPaidAt, condition.getPayStartTime())
                .le(OrderOrderEntity::getPaidAt, condition.getPayEndTime());
        List<OrderOrderEntity> orderList = orderService.getListByWrapper(wrapper);
        return !ObjectUtils.isEmpty(orderList);
    }

    public OrderBeforeActivateImgVO getOrderBeforeActivateImg(OrderBeforeActivateImgDTO orderBeforeActivateImgDTO) {
        OrderOrderEntity order = orderService.getByOrderSn(orderBeforeActivateImgDTO.getOrderSn());
        if (ObjectUtils.isEmpty(order)) {
            ToolsHelper.throwException("订单不存在");
        }

        // 兼容旧接口uid不存在
        if (orderBeforeActivateImgDTO.getUid() != null && !orderBeforeActivateImgDTO.getUid().equals(order.getUid())) {
            ToolsHelper.throwException("订单不属于当前用户");
        }

        if (StringUtils.isEmpty(order.getReviewOrderSn())) {
            ToolsHelper.throwException("订单审核号为空");
        }

        ReviewOrderVehicleEntity reviewOrderVehicle = vehicleService.getByReviewOrderSn(order.getReviewOrderSn());
        if (ObjectUtils.isEmpty(reviewOrderVehicle)) {
            ToolsHelper.throwException("行驶证审核单信息不存在");
        }

        OrderBeforeActivateImgVO imgVO = new OrderBeforeActivateImgVO();
        imgVO.setFrontCarImgUrl(reviewOrderVehicle.getFrontCarImgUrl());
        imgVO.setGearActivateImgUrl(reviewOrderVehicle.getGearActivateImgUrl());
        imgVO.setCarImgUrl(reviewOrderVehicle.getCarImgUrl());

        return imgVO;
    }

    public OrderInfoVo getOrderInfoByOrderSn(OrderOrderEntity order) {
       OrderInfoVo orderInfoVo = new OrderInfoVo();
       BeanUtils.copyProperties(order, orderInfoVo);
       CardsEntity cardsEntity = cardsService.getByCardId(order.getCardId());
       //vehicle_belong 重定义单位车个人车
       orderInfoVo.setVehicleBelong(reDefineVehicleBelong(order,cardsEntity));
       OrderInfoCardVo orderInfoCardVo = new OrderInfoCardVo();
       BeanUtils.copyProperties(cardsEntity, orderInfoCardVo);
       orderInfoVo.setCard(orderInfoCardVo);
       //产品包
        ProductPackageEntity productPackage = null;
       if(StringUtil.isNotEmpty(order.getPackageSn())){
          productPackage = productPackageService.getBySn(order.getPackageSn());
           JSONObject packageInfo = productPackageBusiness.getPackageInfoPopup(JSONObject.parseObject(productPackage.getPackageInfo()));
          orderInfoVo.setPackageInfo(packageInfo);
       }

       //payment_mode
       orderInfoVo.setPaymentMode(getPaymenMode(order,cardsEntity));
       //settleMent_Mode
       orderInfoVo.setSettlementMode(getSettleMentMode(order.getPurchaseParty()));
       //获取车辆的业务类型
       orderInfoVo.setCarBusinessType(getCarBusinessType(order.getReviewOrderSn()));
       //是否出租车
       orderInfoVo.setIsTaxi(getIsTaxi(order.getScene()));
       //获取设备类型
       orderInfoVo.setDeviceType(getDeviceType(productPackage));
       //区分卡方订单来源
       orderInfoVo.setMarketSource(getMarketSource(productPackage));
       //etc发行服务
       orderInfoVo.setIssueService(getIssueService(order));
       //审核单状态
       orderInfoVo.setThirdReviewStatus(getThirdReviewStatus(order.getReviewOrderSn()));
       //获取发货时间
       orderInfoVo.setShippedAt(getShippedAt(order));
        //获取支付单号
        orderInfoVo.setPaymentSn(order.getPaymentSn());
        //获取支付时间
       orderInfoVo.setPaidAt(order.getPaidAt());

       orderInfoVo.setCombineStatusStr(getCombineStatusStr(order));
       //商品订单号
       orderInfoVo.setGoodsOrderSn(order.getGoodsOrderSn());

        // 获取身份信息
        if (ObjectUtils.isNotEmpty(order.getReviewOrderSn())) {
            ReviewOrderIdCardEntity idCard = idCardService.getByReviewOrderSn(order.getReviewOrderSn());
            if (ObjectUtils.isNotEmpty(idCard)) {
                orderInfoVo.setIdCard(new OrderInfoVo.IdCard(idCard.getName(), idCard.getNumber(), idCard.getIdcardType()));
            }
        }

       return orderInfoVo;
    }
    /*
     * 重定义单位车个人车
     */
    public Integer reDefineVehicleBelong(OrderOrderEntity order,CardsEntity card){
        //个人车不需要处理
        if(order.getVehicleBelong().equals(VehicleBelongEnum.Personal.getCode())){
            return VehicleBelongEnum.Personal.getCode();
        }
        if(
            // 江苏出租车不处理
            card.getClassName().equals(CardIssuerClassEnum.JSUTONG_TAXI.getCode()) ||
            // 非江苏、天津速通客车、北京速通客车,网路智联不处理
            !Arrays.asList(
                CardIssuerIdEnum.JSUTONG.getId(),
                CardIssuerIdEnum.TSUTONG.getId(),
                CardIssuerIdEnum.BSUTONG.getId(),
                CardIssuerIdEnum.WANGLUZL.getId()
            ).contains(card.getIssuerId())
        ){
            return order.getVehicleBelong();
        }
        return VehicleBelongEnum.Personal.getCode();
    }

    /*
     * payment_mode
     */
    public Integer getPaymenMode(OrderOrderEntity order,CardsEntity card){
        Integer returnPaymentMode = 0;
        if(PurchaseTypeEnum.getEnumByCode(order.getPurchaseType()) != null){
            switch (PurchaseTypeEnum.getEnumByCode(order.getPurchaseType())){
                case BindBank:
                    returnPaymentMode = PaymentModeEnum.PAYMENT_MODE_VEHICLE_BIND_BANK.getCode();
                    break;
                case WeBank:
                    returnPaymentMode = PaymentModeEnum.PAYMENT_MODE_WEBANK.getCode();
                    break;
                case CreditCard:
                    if(Arrays.asList(
                        PurchasePartyEnum.ningxiaNewGx.getCode(),
                        PurchasePartyEnum.bocJsNewaccount.getCode(),
                        PurchasePartyEnum.bocBjNewaccount.getCode()
                    ).contains(order.getPurchaseParty())){
                        returnPaymentMode = PaymentModeEnum.PAYMENT_MODE_VEHICLE_BIND_BANK.getCode();
                    }
                    break;
                default:
                    break;
            }
        }

        if(returnPaymentMode == 0){
          returnPaymentMode = EntrustModeEnum.getPaymentModeByCode(card.getEntrustMode());
        }
        return returnPaymentMode;
    }
    /*
     * payment_mode
     */
    public Integer getSettleMentMode(Integer purchaseParty){
        Integer returnSettleMentMode = SettleMentModeEnum.SETTLEMENT_MODE_DAY.getCode();
        if(Arrays.asList(
            PurchasePartyEnum.truckJsutongWeek.getCode(),
            PurchasePartyEnum.PURCHASE_PARTY_TRUCK_MENGTONG_WEEK.getCode(),
            PurchasePartyEnum.PURCHASE_PARTY_TRUCK_JINGTONG_WEEK.getCode(),
            PurchasePartyEnum.PURCHASE_PARTY_TRUCK_JSUTONGWL_WEEK.getCode()
        ).contains(purchaseParty)){
            returnSettleMentMode = SettleMentModeEnum.SETTLEMENT_MODE_WEEK.getCode();
        }
        return returnSettleMentMode;
    }

    /*
     * 获取车辆的业务类型
     * 0为没有类型
     * 1为货车运政车类型
     */
    public Integer getCarBusinessType(String reviewOrderSn){
        Integer returnCarBusinessType = 0;
        if(StringUtil.isEmpty(reviewOrderSn)){
            return returnCarBusinessType;
        }
        //business_type =1 并且road_transport_img_url 不为空，则表示为运政车模式
        ReviewOrderVehicleEntity vehicle = vehicleService.getByReviewOrderSn(reviewOrderSn);
        if(
            vehicle != null &&
            vehicle.getBusinessType() == 1 &&
            StringUtil.isNotEmpty(vehicle.getRoadTransportImgUrl())
        ){
            returnCarBusinessType = 1;
        }
        return returnCarBusinessType;
    }

    /*
     *  获取订单是否出租车
     */
    public Integer getIsTaxi(Integer scene){
        return (scene == SceneEnum.Taxi.getCode())?1:0 ;
    }
    /*
     *  获取订单是否出租车
     */
    public Integer getDeviceType(ProductPackageEntity productPackage){
        if (productPackage != null) {
            // 产品包获取设备类型
            JSONObject jsonObject = JSONObject.parseObject(productPackage.getPackageInfo());
            if (jsonObject != null) {
                Integer deviceType = jsonObject.getInteger("device_type");
                if (deviceType != null) {
                   return deviceType;
                }

            }
        }
        return 0 ;
    }

    /*
     *  区分卡方订单来源,直接定义使用market_source
     */
    public Integer getMarketSource(ProductPackageEntity productPackage){
        if (productPackage != null) {
            // 产品包获取设备类型
            JSONObject jsonObject = JSONObject.parseObject(productPackage.getPackageInfo());
            if (jsonObject != null) {
                Integer marketSource = jsonObject.getInteger("market_source");
                if (marketSource != null) {
                    return marketSource;
                }
            }
        }

        return 0 ;
    }
    /*
     *  获取审核单第三方的状态
     */
    public Integer getThirdReviewStatus(String reviewOrderSn){
        Integer thirdReviewStatus = 1;
        if(!StringUtil.isEmpty(reviewOrderSn)){
            ReviewOrderEntity reviewOrderEntity = reviewOrderService.getByReviewOrderSn(reviewOrderSn);
            if(reviewOrderEntity != null){
                thirdReviewStatus = reviewOrderEntity.getThirdReviewStatus();
            }
        }
        return thirdReviewStatus ;
    }
    /*
     *  获取订单发行关联信息
     */
    public OrderInfoIssueServiceVo getIssueService(OrderOrderEntity order){
        OrderInfoIssueServiceVo orderInfoIssueServiceVo = new OrderInfoIssueServiceVo();
        Integer issuerServiceType = FlowTypeEnum.getIssueServiceType(order.getFlowType());
        if(issuerServiceType == 0){
            return null;
        }
        IssueServiceEntity issueServiceEntity = issueServiceService.getByPlateNo(issuerServiceType,order.getUid(),order.getPlateNo());
        if(issueServiceEntity == null){
            return null;
        }
        BeanUtils.copyProperties(issueServiceEntity, orderInfoIssueServiceVo);
        return orderInfoIssueServiceVo;
    }
    /*
       车辆质量转化处理
     */
    public Long dealWithWeight(String weight) {
        String s = weight.replaceAll("\\D+","");
        if(s.length() > 0){
            return Long.valueOf(s);
        }else{
            return 0L;
        }
    }
    /*
     * 通过订单号获取审核单车辆信息
     */
    public GetVehicleInfoByOrderSnVo getVhicleInfoByOrderSn(OrderOrderEntity order) {
        if(StringUtil.isEmpty(order.getReviewOrderSn())){
            return null;
        }
        ReviewOrderVehicleEntity reviewOrderVehicleEntity = vehicleService.getByReviewOrderSn(order.getReviewOrderSn());
        if(reviewOrderVehicleEntity == null){
            return null;
        }
        GetVehicleInfoByOrderSnVo getVhicleInfoByOrderSnVo = new GetVehicleInfoByOrderSnVo();

        BeanUtils.copyProperties(reviewOrderVehicleEntity, getVhicleInfoByOrderSnVo);

        getVhicleInfoByOrderSnVo.setWeight(dealWithWeight(reviewOrderVehicleEntity.getWeight()));
        getVhicleInfoByOrderSnVo.setTotalWeight(dealWithWeight(reviewOrderVehicleEntity.getTotalWeight()));
        getVhicleInfoByOrderSnVo.setCarryWeight(dealWithWeight(reviewOrderVehicleEntity.getCarryWeight()));
        getVhicleInfoByOrderSnVo.setTractionWeight(dealWithWeight(reviewOrderVehicleEntity.getTractionWeight()));
        getVhicleInfoByOrderSnVo.setCarType(getCarType(reviewOrderVehicleEntity));

        //是否有reviewOrderTransportLicence
        GetTransportLicenceByOrderSnVo getTransportLicenceByOrderSnVo = new GetTransportLicenceByOrderSnVo();
        getTransportLicenceByOrderSnVo.setImgUrl(reviewOrderVehicleEntity.getRoadTransportImgUrl());
        getVhicleInfoByOrderSnVo.setReviewOrderTransportLicence(getTransportLicenceByOrderSnVo);

        return getVhicleInfoByOrderSnVo;
    }
    /*
     * 通过订单号获取审核单车主证件信息
     */
    public GetIdCardInfoByOrderSnVo getIdCardInfoByOrderSn(OrderOrderEntity order) {
        if(StringUtil.isEmpty(order.getReviewOrderSn())){
            return null;
        }
        ReviewOrderIdCardEntity reviewOrderIdCardEntity = idCardService.getByReviewOrderSn(order.getReviewOrderSn());
        if(reviewOrderIdCardEntity == null){
            return null;
        }
        GetIdCardInfoByOrderSnVo getIdCardInfoByOrderSnVo = new GetIdCardInfoByOrderSnVo();
        BeanUtils.copyProperties(reviewOrderIdCardEntity, getIdCardInfoByOrderSnVo);
        return getIdCardInfoByOrderSnVo;
    }
    /*
     * 车型的获取
     */
    public Integer getCarType(ReviewOrderVehicleEntity reviewOrderVehicleEntity) {
        Integer carType = 0;
        if(reviewOrderVehicleEntity.getVehicleType() > 0){
            return reviewOrderVehicleEntity.getVehicleType();
        }
        Pattern p = Pattern.compile("[^0-9]");
        Matcher m = p.matcher(reviewOrderVehicleEntity.getPassengers());
        Integer passengers = Integer.valueOf(m.replaceAll("").trim());


        Long totalWeight = dealWithWeight(reviewOrderVehicleEntity.getTotalWeight());

        Matcher m1 = Pattern.compile("\\d+").matcher(reviewOrderVehicleEntity.getOverallSize());
        List<String> matches = new ArrayList<>();
        while(m1.find()){
            matches.add(m1.group());
        }

        Integer carSize = Integer.valueOf(matches.get(0));
        if(reviewOrderVehicleEntity.getType().contains("作业")){
            switch (reviewOrderVehicleEntity.getAxles()){
                case 2:
                    carType = 21;
                    if(carSize >= 6000 || totalWeight >=4500 ){
                        carType = 22;
                    }
                    break;
                case 3:
                    carType = 23;
                    break;
                case 4:
                    carType = 24;
                    break;
                case 5:
                    carType = 25;
                    break;
                case 6:
                    carType = 26;
                    break;
                default:
                    break;
            }
        } else if (
            reviewOrderVehicleEntity.getType().contains("货车") ||
            reviewOrderVehicleEntity.getType().contains("运输车") ||
            reviewOrderVehicleEntity.getType().contains("半挂")
        ) {
            switch (reviewOrderVehicleEntity.getAxles()){
                case 2:
                    carType = 11;
                    if(carSize >= 6000 || totalWeight >=4500 ){
                        carType = 12;
                    }
                    break;
                case 3:
                    carType = 13;
                    break;
                case 4:
                    carType = 14;
                    break;
                case 5:
                    carType = 15;
                    break;
                case 6:
                    carType = 16;
                    break;
                default:
                    break;
            }
        }else if (
            reviewOrderVehicleEntity.getType().contains("轿车") ||
            reviewOrderVehicleEntity.getType().contains("客车") ||
            reviewOrderVehicleEntity.getType().contains("微型") ||
            reviewOrderVehicleEntity.getType().contains("小型")
        ) {
            if(passengers <= 9){
                carType = 1;
            } else if (passengers <= 19) {
                carType = 2;
            } else if (passengers <= 39) {
                carType = 3;
            }else if (passengers > 39) {
                carType = 4;
            }
        }
        return carType;
    }
    /*
     * 通过订单号获取审核单车主证件信息
     */
    public GetBizlicenceInfoByOrderSnVo getBizlicenceInfoByOrderSn(OrderOrderEntity order) {
        if(StringUtil.isEmpty(order.getReviewOrderSn())){
            return null;
        }
        ReviewOrderBizLicenceEntity reviewOrderBizLicenceEntity = reviewOrderBizLicenceService.getByReviewOrderSn(order.getReviewOrderSn());
        if(reviewOrderBizLicenceEntity == null){
            return null;
        }
        GetBizlicenceInfoByOrderSnVo reviewOrderBizLicenceVo = new GetBizlicenceInfoByOrderSnVo();
        BeanUtils.copyProperties(reviewOrderBizLicenceEntity, reviewOrderBizLicenceVo);
        return reviewOrderBizLicenceVo;
    }


    /*
     * 组合返回发卡方信息
     */
    public GetIssueInfoByOrderSnVo getIssueInfoByOrderSn(OrderOrderEntity order) {
        GetIssueInfoByOrderSnVo getIssueInfoByOrderSnVo = new GetIssueInfoByOrderSnVo();
        getIssueInfoByOrderSnVo.setOrderInfo(getOrderInfoByOrderSn(order));
        getIssueInfoByOrderSnVo.setVehicleInfo(getVhicleInfoByOrderSn(order));
        getIssueInfoByOrderSnVo.setIdCardInfo(getIdCardInfoByOrderSn(order));
        getIssueInfoByOrderSnVo.setBizlicenceInfo(getBizlicenceInfoByOrderSn(order));
        return getIssueInfoByOrderSnVo;
    }


    /*
     *  获取审核单第三方的状态
     */
    public LocalDateTime getShippedAt(OrderOrderEntity order) {
        if (Arrays.asList(
                StatusEnum.WAIT_FOR_RECEIVE.getCode(),
                StatusEnum.FINISH.getCode()
        ).contains(order.getStatus())) {
            LogisticOrderEntity logisticOrderEntity = logisticOrderService.getByLogisticOrderSn(order.getLogisticOrderSn());
            if (logisticOrderEntity == null) {
                return null;
            }
            return logisticOrderEntity.getShippedAt();
        }

        return null;
    }

    // 是否激活判断
    public Boolean isActivated(OrderOrderEntity orderEntity) {

        return orderEntity.getActivatedStatus().equals(ActivatedStatusEnum.ACTIVATED.getCode());
    }

    // 是否已退款判断
    public Boolean orderHasRefund(OrderOrderEntity orderEntity) {

        return StringUtils.isNotEmpty(orderEntity.getRefundSn())
                || orderEntity.getAftersaleStatus().equals(AftersaleStatus.STATUS_APPLY_FINISH.getValue());
    }

    // 已支付
    public Boolean hasPaid(OrderOrderEntity orderEntity) {

        return Arrays.asList(StatusEnum.WAIT_FOR_DELIVER.getCode(),
                StatusEnum.WAIT_FOR_RECEIVE.getCode(),
                StatusEnum.FINISH.getCode()
        ).contains(orderEntity.getStatus());
    }

    // 已取消
    public Boolean hasCanceled(OrderOrderEntity orderEntity) {

        return orderEntity.getStatus().equals(StatusEnum.CANCELED.getCode());
    }

    public List<EffectOrderResponse> getEffectOrderListByUid(EffectOrderListDTO orderListDTO) {

        List<OrderOrderEntity> orderList = orderService.getUserEffectOrderOrActivatedOrder(orderListDTO.getUid());
        //检查订单里面
        if (ObjectUtils.isEmpty(orderList)) {
            return null;
        }
        // 获取订单的card 信息，判定订单是否是货车，是否是储值卡
        cardsService.bindToMasterEntityList(orderList, OrderOrderBindCardsRelation.class);

        List<EffectOrderResponse> returnList = new ArrayList<>();
        orderList.forEach(order -> {
            EffectOrderResponse orderResponse = BeanUtil.copyProperties(order, EffectOrderResponse.class);
            if (ObjectUtils.isNotNull(order.getCardsEntity())) {
                orderResponse.setCardType(order.getCardsEntity().getCardType());
                orderResponse.setTruck(order.getCardsEntity().getIsTruck());
            }
            returnList.add(orderResponse);
        });
        return returnList;
    }

    public GetUnpaidOrderInfoVO getUnpaidOrderInfo(Long uid) {

        try {
            // 异常错误处理 TODO
            List<Integer> truckIds = cardsService.getAllTruckIdsWithCache();
            OrderOrderEntity order;
            // 不是指定货车的，都是客车  -- 先不要管客车货车
            order = orderService.getUnpaidCarOrderWithConditions(uid, truckIds);

            if (ObjectUtils.isEmpty(order)) {
                return null;
            }
            GetUnpaidOrderInfoVO getUnpaidOrderInfoVO = new GetUnpaidOrderInfoVO();
            BeanUtils.copyProperties(order, getUnpaidOrderInfoVO);

            if (StringUtils.isEmpty(order.getPackageSn())) {
                return null;
            }
            ProductPackageEntity productPackage = productPackageService.getBySnWithCache(order.getPackageSn());
            if (ObjectUtils.isEmpty(productPackage)) {
                return null;
            }

            // 产品包单号非空，则获取产品包信息
            GetUnpaidOrderInfoVO.ProductPackage productPackageVO = new GetUnpaidOrderInfoVO.ProductPackage();
            BeanUtils.copyProperties(productPackage, productPackageVO);
            // packageInfo String 转object
            productPackageVO.setPackageInfo(productPackage.getPackageInfoObj());
            getUnpaidOrderInfoVO.setProductPackage(productPackageVO);
            // 根据产品包信息判定是否能否继续申办
            getUnpaidOrderInfoVO.setCanContinueApply(allowApplyProductPackage(productPackage) ?
                    YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());

            return getUnpaidOrderInfoVO;
        } catch (Exception e) {
            log.warn("获取最新未支付订单信息失败", e);
            return null;
        }
    }

        public GetUnpaidOrderDetailVO getUnpaidOrderDetail(GetUnpaidOrderDetailDTO unpaidOrderDTO,Long uid){

            try{
                // 异常错误处理 TODO
                OrderOrderEntity order = orderService.getByOrderSn(unpaidOrderDTO.getOrderSn());
                if(ObjectUtils.isEmpty(order)){
                    return null;
                }
                if(!order.getUid().equals(uid)){
                    ToolsHelper.throwException("订单不属于该用户");
                }
                GetUnpaidOrderDetailVO getUnpaidOrderInfoVO = new GetUnpaidOrderDetailVO();
                BeanUtils.copyProperties(order,getUnpaidOrderInfoVO);

                GetUnpaidOrderDetailVO.AddressInfo addressInfo = new GetUnpaidOrderDetailVO.AddressInfo();
                BeanUtils.copyProperties(order,addressInfo);
                getUnpaidOrderInfoVO.setAddressInfo(addressInfo);

                if(StringUtils.isEmpty(order.getPackageSn())){
                    getUnpaidOrderInfoVO.setCanContinueApply(YesOrNoEnum.NO.getCode());
                    return getUnpaidOrderInfoVO;
                }
                ProductPackageEntity productPackage = productPackageService.getBySn(order.getPackageSn());
                if(ObjectUtils.isEmpty(productPackage)){
                    getUnpaidOrderInfoVO.setCanContinueApply(YesOrNoEnum.NO.getCode());
                    return getUnpaidOrderInfoVO;
                }

                // 产品包单号非空，则获取产品包信息
                GetUnpaidOrderDetailVO.ProductPackage productPackageVO = new GetUnpaidOrderDetailVO.ProductPackage();
                BeanUtils.copyProperties(productPackage,productPackageVO);
                // packageInfo String 转object
                productPackageVO.setPackageInfo(productPackage.getPackageInfoObj());
                getUnpaidOrderInfoVO.setProductPackage(productPackageVO);

                // 根据产品包信息判定是否能否继续申办
                getUnpaidOrderInfoVO.setCanContinueApply(allowApplyProductPackage(productPackage) ? YesOrNoEnum.YES.getCode():YesOrNoEnum.NO.getCode());
                return getUnpaidOrderInfoVO;
            }catch (Exception e){
                log.error("获取最未支付订单信息失败"+unpaidOrderDTO.getOrderSn(),e);
                ToolsHelper.throwException("获取订单信息失败，请稍后重试");
            }

            return null;


    }


    /**
     * 检查产品包是否允许继续申办-未支付订单是否能够继续支付
     * TODO 上线前减少日志打印
     */
    public Boolean allowApplyProductPackage(ProductPackageEntity productPackage) {
        try {
            // 检查 productPackage 和 allowApplyProductPackageRule 是否为 null
            if (productPackage == null || allowApplyProductPackageRule == null) {
                return false;
            }
            if (productPackage.getPackageFee() == null || productPackage.getPackageInfoObj() == null || productPackage.getMarketConfigBO() == null) {
                return false;
            }
            // 产品包下架状态不允许继续申办
            if (!ProductPackageStatusEnum.NORMAL.getValue().equals(productPackage.getStatus())) {
                return false;
            }
            // 产品包价格需要大于0
            if (productPackage.getPackageFee().compareTo(BigDecimal.ZERO) < 0) {
                return false;
            }

            Map<String, Object> productPackageMap = new HashMap<>();
            productPackageMap.put("packageFee", productPackage.getPackageFee());
            productPackageMap.put("bizType", productPackage.getBizType());
            productPackageMap.put("issuerId", productPackage.getPackageInfoObj().getInteger("issuer_id"));
            productPackageMap.put("term", productPackage.getMarketConfigBO().getTerm());
            productPackageMap.put("manufacturer", productPackage.getPackageInfoObj().getInteger("manufacturer"));
            productPackageMap.put("deviceType", productPackage.getPackageInfoObj().getInteger("device_type"));

            // 公共匹配规则
            AllowApplyProductPackageRule.AllowRule allowCommonRule = allowApplyProductPackageRule.getAllowCommonRule();
            Field[] fields = allowCommonRule.getClass().getDeclaredFields();
            for (Field field : fields) {
                if (!checkFieldValue(productPackage.getPackageSn(),field, productPackageMap, allowCommonRule)) {
                    return false;
                }
            }

            // 只有在匹配到规则并且符合规则，则返回 true
            return true;

        } catch (IllegalArgumentException e) {
            log.error("非法参数异常", e);
        } catch (Exception e) {
            log.error("检查产品包是否允许继续申办异常", e);
        }
        return false;
    }

    public Boolean checkAllowProductPackage(ProductPackageEntity productPackage,
                                            AllowApplyProductPackageRule.AllowRule allowRule) {
        try {
            // 检查 productPackage 和 allowApplyProductPackageRule 是否为 null
            if (productPackage == null || allowApplyProductPackageRule == null) {
                return false;
            }
            if (productPackage.getPackageFee() == null || productPackage.getPackageInfoObj() == null || productPackage.getMarketConfigBO() == null) {
                return false;
            }
            // 产品包下架状态不允许继续申办
            if (!ProductPackageStatusEnum.NORMAL.getValue().equals(productPackage.getStatus())) {
                return false;
            }

            Map<String, Object> productPackageMap = new HashMap<>();
            productPackageMap.put("packageFee", productPackage.getPackageFee());
            productPackageMap.put("bizType", productPackage.getBizType());
            productPackageMap.put("issuerId", productPackage.getPackageInfoObj().getInteger("issuer_id"));
            productPackageMap.put("term", productPackage.getMarketConfigBO().getTerm());
            productPackageMap.put("manufacturer", productPackage.getPackageInfoObj().getInteger("manufacturer"));
            productPackageMap.put("deviceType", productPackage.getPackageInfoObj().getInteger("device_type"));

            // 公共匹配规则
            Field[] fields = allowRule.getClass().getDeclaredFields();
            for (Field field : fields) {
                if (!checkFieldValue(productPackage.getPackageSn(), field, productPackageMap, allowRule)) {
                    return false;
                }
            }

            // 只有在匹配到规则并且符合规则，则返回 true
            return true;

        } catch (IllegalArgumentException e) {
            log.error("非法参数异常", e);
        } catch (Exception e) {
            log.error("检查产品包是否允许继续申办异常", e);
        }
        return false;
    }

    private boolean checkFieldValue(String packageSn, Field field, Map<String, Object> productPackageMap,
                                    AllowApplyProductPackageRule.AllowRule allowCommonRule) {
        try {
            field.setAccessible(true);

            Object fieldValue = field.get(allowCommonRule);
            if (ObjectUtils.isEmpty(fieldValue)) {
                log.info(field.getName() + "为空,不进行限制判定");
                return true;
            }
            if (!((ArrayList<?>) fieldValue).contains(productPackageMap.get(field.getName()))) {
                log.info(" 产品包：" + packageSn + field.getName() + "不符合要求：产品包配置信息:" +
                        productPackageMap.get(field.getName()) +
                        "配置规则要求：" + fieldValue);
                return false;
            }
        } catch (Exception e) {
            log.warn("fieldName 异常: " + field.getName(), e);
            return false;
        }
        return true;
    }



    public Boolean checkOrderCanBePaid(OrderOrderEntity order) {
        try {
            // 货车的不能直接支付
            if(order.getScene() == SceneEnum.TRUCK.getCode()){
                return false;
            }
            // 订单状态为待支付的单需要进行特殊处理
            if (!Objects.equals(StatusEnum.WAIT_FOR_PAY.getCode(),order.getStatus())) {
                // 订单状态为待支付的单，需要比对是否能够继续跳支付页进行支付
                return false;
            }
            // 支付金额等于小于0 的不能继续支付
            if(order.getNeedPay() == null || order.getNeedPay().compareTo(BigDecimal.ZERO) < 0){
                return false;
            }

            // 保证金订单、前装订单订单不能直接支付
            if(!Objects.equals(order.getDepositStatus(),DepositStatusEnum.NONE.getValue()) || Objects.equals(order.getIsFront(),IsFrontEnum.FRONT.getCode())){
                return false;
            }


            return true;
        }catch (Exception e){
            log.error("订单状态为待支付的单，需要比对是否能够继续跳支付页进行支付 异常:"+e.getMessage());
        }
        return false;
    }

    // 获取用户未完成订单列表，订单状态为待支付的单需要进行特殊处理

    public List<GetUnfinishedOrderListVO> getUnfinishedOrderList(Long uid) {
        ArrayList<GetUnfinishedOrderListVO> unfinishedOrderListVOList = new ArrayList<>();

        try {
            List<Integer> truckIds = cardsService.getAllTruckIdsWithCache();
            // 限制一次最多返回10 个单
            List<OrderOrderEntity> unfinishedOrderList = orderService.getUnfinishedOrderListByConditions(uid,
                    truckIds, 10);
            if (CollectionUtils.isEmpty(unfinishedOrderList)) {
                return unfinishedOrderListVOList;
            }
            // 如果订单是未支付的单，需要比对是否能够继续跳支付页进行支付
            return unfinishedOrderList.stream()
                    .filter(order -> {
                        // 如果不是待支付状态，不进行过滤，目的是过滤掉待支付且不符合支付条件的单
                        if (!Objects.equals(order.getStatus(), StatusEnum.WAIT_FOR_PAY.getCode())) {
                            return true;
                        } else {
                            return checkOrderCanBePaid(order);
                        }
                    })
                    .map(order -> {
                        GetUnfinishedOrderListVO getUnfinishedOrderListVO = new GetUnfinishedOrderListVO();
                        BeanUtils.copyProperties(order, getUnfinishedOrderListVO);
                        ProductPackageEntity productPackage = null;
                        if (ObjectUtils.isNotEmpty(order.getPackageSn())) {
                            productPackage = productPackageService.getBySnWithCache(order.getPackageSn());
                            if (ObjectUtils.isNotEmpty(productPackage)) {
                                GetUnfinishedOrderListVO.ProductPackage productPackageVO =
                                        new GetUnfinishedOrderListVO.ProductPackage();
                                BeanUtils.copyProperties(productPackage, productPackageVO);
                                productPackageVO.setFrontendConfig(JSONObject.parseObject(productPackage.getFrontedConfig()));
                                getUnfinishedOrderListVO.setProductPackage(productPackageVO);
                            }
                        }

                        if (StatusEnum.WAIT_FOR_PAY.getCode() != order.getStatus()) {
                            getUnfinishedOrderListVO.setCanContinuePayApply(YesOrNoEnum.NO.getCode());
                        } else {
                            // 需要判定订单自身信息是否能够继续支付
                            // 判定订单产品包是否能够继续支付
                            getUnfinishedOrderListVO.setCanContinuePayApply(allowApplyProductPackage(productPackage)
                                    ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
                        }
                        return getUnfinishedOrderListVO;
                    })
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取用户未完成订单列表 异常:" + e.getMessage());
        }
        return unfinishedOrderListVOList;
    }


    public List<AuthorizeActivateOrderDetailVO> getAuthorizeActivateOrderListVO(AuthorizeActivateOrderListDTO waitingActivateOrderListDTO) {
        try {
            // 获取待激活的用户订单列表：订单状态为已发货、已签收，无售后，未激活，业务类型为限定的类型
            List<OrderOrderEntity> orderList =
                    orderService.getAuthorizeActivateOrders(waitingActivateOrderListDTO.getUid());
            // 空的话返回null
            if (CollectionUtils.isEmpty(orderList)) {
                return Collections.emptyList();
            }
            // 组合审核单
            reviewOrderService.bindToMasterEntityList(orderList, OrderOrderBindReviewOrderRelation.class);

            // 组合cardEntity
            cardsService.bindToMasterEntityList(orderList, OrderOrderBindCardsRelation.class);

            // 过滤没有审核单或者不是审核通过的订单，过滤订单对应cardId 对应的CardsEntity  的issuerId 如果不在配置的配置中，过滤掉
            List<OrderOrderEntity> filterOrderList = orderList.stream()
                    .filter(this::isAuthorizeActivateOrderStatusValid)
                    .filter(order -> Objects.equals(order.getActivatedStatus(), ActivatedStatusEnum.DEFAULT.getCode()))
                    .collect(Collectors.toList());

            // 过滤后如果是空的返回null
            if (CollectionUtils.isEmpty(filterOrderList)) {
                return Collections.emptyList();
            }

            // 批量查询订单是否有进行中的售后单，有的话过滤掉
            filterOrderList = filterSaleafterOrder(filterOrderList);
            // 过滤后如果是空的返回null
            if (CollectionUtils.isEmpty(filterOrderList)) {
                return Collections.emptyList();
            }

            // 组装产品包数据
            productPackageService.bindToMasterEntityList(filterOrderList, OrderOrderBindProductPackageRelation.class);

            // 通过订单组装返回数据
            return filterOrderList.stream().map(this::buildAuthorizeActivateOrderDetailVO).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取授权激活订单详情异常:{},异常信息：{}", e.getMessage(), e.getStackTrace());
        }
        return Collections.emptyList();

    }


    public AuthorizeActivateOrderDetailVO getAuthorizeActivateOrderDetailVO(AuthorizeActivateOrderDetailDTO waitingActivateOrderDetailDTO) {

        // 通过订单号、uid 获取订单信息
        OrderOrderEntity order = getByOrderSn(waitingActivateOrderDetailDTO.getOrderSn());
        // 订单uid 和 传入的uid 不一致，返回错误提示
        if (!Objects.equals(order.getUid(), waitingActivateOrderDetailDTO.getUid())) {
            // 这里是返回null 还是错误提示可以根据业务需求来
            ToolsHelper.throwException("当前用户无该订单操作权限", ExceptionEnum.ACTIVATE_OTHERS_NOT_ALLOW.getCode());
        }

        // 组合审核单数据
        reviewOrderService.bindToMasterEntity(order, OrderOrderBindReviewOrderRelation.class);

        // 组合card entity
        cardsService.bindToMasterEntity(order, OrderOrderBindCardsRelation.class);
        productPackageService.bindToMasterEntity(order, OrderOrderBindProductPackageRelation.class);

        if (!isAuthorizeActivateOrderStatusValid(order)) {
            ToolsHelper.throwException("订单状态不允许进行激活操作", ExceptionEnum.ACTIVATE_OTHERS_NOT_ALLOW.getCode());
        }

        // 订单判定是否有进行了售后 TODO
        if (checkOrderHasSaleafter(order.getOrderSn())) {
            ToolsHelper.throwException("订单存在售后记录，不允许进行激活", ExceptionEnum.ACTIVATE_OTHERS_NOT_ALLOW.getCode());
        }

        return buildAuthorizeActivateOrderDetailVO(order);

    }


    /**
     * 授权激活订单是否有效
     */
    private boolean isAuthorizeActivateOrderStatusValid(OrderOrderEntity order) {
        // 先判定订单是否允许激活
        if (!Objects.equals(order.getAllowActivate(), AllowActivateEnum.ALLOW_ACTIVATE.getCode())) {
            return false;
        }

        // 订单状态需要是正常状态：已支付、发货、签收
        if (!Arrays.asList(
                StatusEnum.WAIT_FOR_DELIVER.getCode(),
                StatusEnum.WAIT_FOR_RECEIVE.getCode(),
                StatusEnum.FINISH.getCode()).contains(order.getStatus())) {
            return false;
        }

        // 订单已进行售后，不能操作激活
        if (!Objects.equals(AftersaleStatus.STATUS_NORMAL.getValue(), order.getAftersaleStatus())) {
            return false;
        }

        // 没有签过约不允许操作激活
        if (!ObjectUtil.equals(order.getHasOnceSigned(), YesOrNoEnum.YES.getCode())) {
            return false;
        }

        // 需要审核单审核通过
        if (ObjectUtil.isEmpty(order.getReviewOrderEntity()) || !Objects.equals(order.getReviewOrderEntity().getThirdReviewStatus(),
                ThirdReviewStatus.pass.getCode())) {
            return false;
        }

        // 流程在需要排除的范围内不允许授权激活
        if (authorizeActivateConfig.getExcludeFlowType().contains(order.getFlowType())) {
            return false;
        }

        // 需要 有card Entity,并且卡的卡方在配置的允许范围内
        if (ObjectUtil.isEmpty(order.getCardsEntity()) || !authorizeActivateConfig.getAllowIssuerIdList().contains(order.getCardsEntity().getIssuerId())) {
            return false;
        }

        return true;
    }
    // 检查订单是否有进行售后
    private Boolean checkOrderHasSaleafter(String orderSn) {
        CheckAuthorizeActivateDTO authorizeActivateDTO = new CheckAuthorizeActivateDTO();
        authorizeActivateDTO.setApplyOrderSn(Collections.singletonList(orderSn));
        String aftersaleResult = aftersaleFeign.authorizeActivate(authorizeActivateDTO);
        JSONObject jsonObject = JSON.parseObject(aftersaleResult);
        Integer code = jsonObject.getInteger("code");
        if (!code.equals(0)) {
            String msg = jsonObject.getString("msg");
            ToolsHelper.throwException(msg);
        }
        List<AuthorizeActivateVO> data = jsonObject.getObject("data", new TypeReference<List<AuthorizeActivateVO>>() {
        });

        // 如果没有售后记录，返回false,返回的数据不进行状态校验，沟通确认有数据就是有进行中售后，不会有其他情况
        // 售后完成的情况是不能进行激活的
        return !CollectionUtils.isEmpty(data);

    }

    /**
     * 筛选掉有售后的单（售后接口已处理返回售后中、售后完成的订单）
     */
    private List<OrderOrderEntity> filterSaleafterOrder(List<OrderOrderEntity> orderList) {
        CheckAuthorizeActivateDTO authorizeActivateDTO = new CheckAuthorizeActivateDTO();
        authorizeActivateDTO.setApplyOrderSn(orderList.stream().map(OrderOrderEntity::getOrderSn).collect(Collectors.toList()));
        String aftersaleResult = aftersaleFeign.authorizeActivate(authorizeActivateDTO);
        JSONObject jsonObject = JSON.parseObject(aftersaleResult);
        Integer code = jsonObject.getInteger("code");
        if (!code.equals(0)) {
            String msg = jsonObject.getString("msg");
            ToolsHelper.throwException(msg);
        }
        List<AuthorizeActivateVO> data = jsonObject.getObject("data", new TypeReference<List<AuthorizeActivateVO>>() {
        });
        if(CollectionUtils.isEmpty(data)){
            return orderList;
        }
        return orderList.stream().filter(order -> !data.stream().map(AuthorizeActivateVO::getApplyOrderSn).collect(Collectors.toList()).contains(order.getOrderSn())).collect(Collectors.toList());

    }

    public AuthorizeActivateOrderDetailVO buildAuthorizeActivateOrderDetailVO(OrderOrderEntity order) {
        AuthorizeActivateOrderDetailVO authorizeActivateOrderDetailVO = new AuthorizeActivateOrderDetailVO();
        authorizeActivateOrderDetailVO.setOrderSn(order.getOrderSn());
        authorizeActivateOrderDetailVO.setStatus(order.getStatus());
        authorizeActivateOrderDetailVO.setUid(order.getUid());
        authorizeActivateOrderDetailVO.setPlateNo(order.getPlateNo());
        authorizeActivateOrderDetailVO.setPlateColor(order.getPlateColor());
        authorizeActivateOrderDetailVO.setCreatedAt(order.getCreatedAt());

        CardsEntity cardsEntity = order.getCardsEntity();
        if (ObjectUtils.isNotEmpty(cardsEntity)) {
            authorizeActivateOrderDetailVO.setCardType(cardsEntity.getCardType());
            authorizeActivateOrderDetailVO.setCardTypeStr(CardTypeEnum.getDescByCardType(cardsEntity.getCardType()));
            authorizeActivateOrderDetailVO.setCarType(Objects.equals(cardsEntity.getIsTruck(),
                    YesOrNoEnum.YES.getCode()) ?
                    CarTypeEnum.TYPE_TRUCK.getValue() : CarTypeEnum.TYPE_CAR.getValue());
            authorizeActivateOrderDetailVO.setCarTypeStr(CarTypeEnum.map.getOrDefault(authorizeActivateOrderDetailVO.getCarType()
                    , "小汽车"));
        }

        ProductPackageEntity productPackageEntity = order.getProductPackageEntity();

        if (ObjectUtils.isNotEmpty(productPackageEntity)) {
            // 产品包获取设备类型
            JSONObject jsonObject = productPackageEntity.getPackageInfoObj();
            if (ObjectUtils.isNotEmpty(jsonObject)) {
                Integer deviceType = jsonObject.getInteger("device_type");

                String productImg = jsonObject.getString("product_img");
                if (ObjectUtils.isNotEmpty(productImg)) {
                    authorizeActivateOrderDetailVO.setDeviceImg(productImg);
                }
                String deviceName = jsonObject.getString("device_name");
                if (ObjectUtils.isNotEmpty(deviceName)) {
                    authorizeActivateOrderDetailVO.setDeviceName(deviceName);
                } else {
                    authorizeActivateOrderDetailVO.setDeviceName(DeviceTypeEnum.nameMap.getOrDefault(deviceType,
                            "一代-经典ETC"));
                }
            }
        } else {
            authorizeActivateOrderDetailVO.setDeviceName("一代-经典ETC");
            authorizeActivateOrderDetailVO.setDeviceImg(authorizeActivateConfig.getDefaultDeviceImg());
        }
        return authorizeActivateOrderDetailVO;
    }


    /**
     * 筛选掉有售后的单（售后接口已处理返回售后中、售后完成的订单）
     */
    public GetRefundInfoByOrderSnVo getRefundInfoByOrderSn(OrderFindDTO dto) {
        OrderOrderEntity order = getByOrderSn(dto.getOrderSn());
        if(order == null){
            ToolsHelper.throwException("订单号错误"+dto.getOrderSn());
        }
        GetRefundInfoByOrderSnVo refundInfoByOrderSnVo = new GetRefundInfoByOrderSnVo();
        refundInfoByOrderSnVo.setOrderSn(order.getOrderSn());
        refundInfoByOrderSnVo.setAmount(order.getNeedPay());

        switch (OrderPayeeEnum.getByCode(order.getPayee())){
            //商品收款方
            case ORDER_PAYEE_GOODS:
                JSONObject retResponse1 = callGoodsApplication.getRefundNotCancelResult(order.getGoodsOrderSn(),order.getOrderSn()).getDataWithCheckError();
                if(retResponse1 != null){
                    refundInfoByOrderSnVo.setRefundSn(retResponse1.getString("refundServiceSn"));
                    refundInfoByOrderSnVo.setAmount(retResponse1.getBigDecimal("amount"));
                    refundInfoByOrderSnVo.setStatus(getRefundStatusByGoods(retResponse1.getInteger("status")));
                    refundInfoByOrderSnVo.setReason(retResponse1.getString("reason"));
                    refundInfoByOrderSnVo.setRefundedAt(retResponse1.getString("refundedAt"));
                }
                break;
            default:
                if(StringUtils.isEmpty(order.getPaymentSn())){
                    return refundInfoByOrderSnVo;
                }
                JSONObject retResponse2 = callPhpPayFeign.manualRefundQueryByPaymentSn(new GetInfoByPaymentSnDTO(order.getPaymentSn())).getDataWithCheckError();

                JSONObject refundOrder = retResponse2.getJSONObject("refund_order");
                if(refundOrder != null){
                    refundInfoByOrderSnVo.setRefundSn(refundOrder.getString("refund_sn"));
                    refundInfoByOrderSnVo.setAmount(refundOrder.getBigDecimal("amount"));
                    refundInfoByOrderSnVo.setStatus(refundOrder.getInteger("status"));
                    refundInfoByOrderSnVo.setReason(refundOrder.getString("reason"));
                    refundInfoByOrderSnVo.setRefundedAt(refundOrder.getString("refunded_at"));
                }
                break;
        }
        return refundInfoByOrderSnVo;
    }


    /*
     * 通过city获取可办理的产品包
     */
    public BanksByPackageSnVo getBanksByCity(GetBanksByCityDTO dto,Long uid) {
        BanksByPackageSnVo banksByPackageSnVoList = new BanksByPackageSnVo();
        List<BanksByPackageSnVo.Banks> banksList = new ArrayList<>();
        List<Integer> normalBankIds = creditCardBankConfigService.getBankIdListByBankStatus(
                CreditCardBankConfigBankStatusEnum.NORMAL.getStatus()
        );

        // 城市不传就从大数据获取
        if(ObjectUtil.isEmpty(dto.getCity())){
            // bigDataService.getUserCity(uid) 如果获取到空字符串时，默认返回广州市
            dto.setCity(creditCardBusiness.getUserBankCity(uid));
        }
        for (Integer bank : normalBankIds) {
            // 银行办理限制
            BanksByPackageSnVo.Banks banks = new BanksByPackageSnVo.Banks();
            banks.setBank(bank);

            List<String> listCity = new ArrayList<>();
            switch (bank) {
                case ActivityCreditCardUserInfoWhichBankConstant.GUANG_FA_INTERFACE:
                    listCity = CityConstant.CITY_GDB_LIST;
                    break;
                case ActivityCreditCardUserInfoWhichBankConstant.PING_AN_INTERFACE:
                    listCity = CityConstant.CITY_PAB_LIST;
                    break;
                case ActivityCreditCardUserInfoWhichBankConstant.JIAO_TONG:
                    listCity = CityConstant.CITY_COMM_LIST;
                    break;
                case ActivityCreditCardUserInfoWhichBankConstant.CMBC:
                    listCity = CityConstant.CITY_CMBC_LIST;
                    break;
                case ActivityCreditCardUserInfoWhichBankConstant.SPD:
                    listCity = CityConstant.CITY_SPD_LIST;
                    break;
                case ActivityCreditCardUserInfoWhichBankConstant.CEB:
                    listCity = CityConstant.CITY_CEB_LIST;
                    break;
                case ActivityCreditCardUserInfoWhichBankConstant.CITIC:
                    listCity = CityConstant.CITY_CITIC_LIST;
                    break;
            }
            if(listCity.contains(dto.getCity())){
                banks.setCanApply(true);
            }
            banksList.add(banks);
        }
        banksByPackageSnVoList.setBanks(banksList);
        banksByPackageSnVoList.setVersion("unify");
        return banksByPackageSnVoList;

    }
    public HideInfoVO hideInfo(HideInfoDTO hideInfoDTO) {
        HideInfoVO hideInfoVO = new HideInfoVO();
        // 查询订单信息
        OrderOrderEntity order = orderService.getByOrderSn(hideInfoDTO.getOrderSn());
        if (ObjectUtils.isEmpty(order)) {
            ToolsHelper.throwException("订单不存在");
        }

        // 查询商城订单
        ProductOrderEntity productOrder = productOrderService.getOneByColumn(hideInfoDTO.getOrderSn(), ProductOrderEntity::getApplyOrderSn);
        if (ObjectUtils.isEmpty(productOrder)) {
            return hideInfoVO;
        }

        // 查询发货单 处理一个ERP订单多个商品情况
        String thirdOrderSn = productOrder.getThirdOrderSn();
        if (productOrder.getThirdOrderSn().contains("_")) {
            thirdOrderSn = productOrder.getThirdOrderSn().split("_")[0];
        }
        LogisticsFindByOrderSnDTO findByOrderSnDTO = new LogisticsFindByOrderSnDTO();
        findByOrderSnDTO.setOrderSn(thirdOrderSn);
        JsonResult<LogisticsVO> logisticsResult = deliveryFeign.findByOrderSn(findByOrderSnDTO);
        if (logisticsResult.isSuccess()) {
            if (ObjectUtils.isNotEmpty(logisticsResult.getData())) {
                // ERP订单 收件人带* 隐藏收件人信息
                LogisticsVO logisticsVO = logisticsResult.getData();
                if (logisticsVO.getOrderSource().equals("yunda_erp") && logisticsVO.getSendName().contains("*")) {
                    hideInfoVO.setRecipientHide(true);
                }
            }
        }

        return hideInfoVO;
    }


    /*
     * 订单状态处理
     */
    public String getCombineStatusStr(OrderOrderEntity order){
        String combineStatusStr = "";
        Integer combineStatus = order.getStatus();
        if(
           combineStatus.equals(StatusEnum.WAIT_FOR_RECEIVE.getCode()) &&
           order.getAftersaleStatus().equals(AftersaleStatus.STATUS_APPLY_FINISH.getValue())
        ){
            combineStatus = StatusEnum.FINISH.getCode();
        }
        switch (combineStatus){
            case 1:
                combineStatusStr = "待支付";
                break;
            case 2:
            case 3:
                combineStatusStr = "进行中";
                break;
            case 4:
                combineStatusStr = "已完成";
                break;

        }
        return combineStatusStr;
    }

    /**
     * 筛选掉有售后的单（售后接口已处理返回售后中、售后完成的订单）
     */
    public Integer getRefundStatusByGoods(Integer refundStatus) {
        Integer returnStatus = 0;
        switch (refundStatus){
            //已创建
            case 0:
                returnStatus = 0;
                break;
            //退款中
            case 2:
                returnStatus = 4;
                break;
            //退款成功
            case 3:
                returnStatus = 1;
                break;
            //退款失败
            case 4:
                returnStatus = 2;
                break;
            default:
                returnStatus = 99;
        }
        return returnStatus;
    }



    // 获取用户未完成订单列表，订单状态为待支付的单需要进行特殊处理

    public GetManualSnOrderInfoVO getManualSnOrderInfo(Long uid,String manualSn) {
        //通过manual_sn获取映射的产品包
        ProductManual productManual = productManualService.getOneByColumn(manualSn,ProductManual::getManualSn);
        if(productManual == null){
            ToolsHelper.throwException("manualSn不存在："+manualSn);
        }
        if(StringUtils.isEmpty(productManual.getPackageSn())){
            ToolsHelper.throwException("manualSn对应的packageSn不存在："+manualSn);
        }
        GetManualSnOrderInfoVO vo= new GetManualSnOrderInfoVO();
        BeanUtil.copyProperties(productManual,vo);
        ProductPackageEntity productPackage = productPackageService.getBySn(productManual.getPackageSn());
        if (productPackage == null) {
            ToolsHelper.throwException("manualSn对应的packageSn不存在："+productManual.getPackageSn());
        }
        // 产品包获取设备类型 设备名称、图片
        JSONObject jsonObject = productPackage.getPackageInfoObj();
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(jsonObject)) {
            Integer cardId = jsonObject.getInteger("card_id");
            Integer issuerId = jsonObject.getInteger("issuer_id");
            if (org.apache.commons.lang3.ObjectUtils.isEmpty(issuerId) && org.apache.commons.lang3.ObjectUtils.isNotEmpty(cardId)) {
                CardsEntity cardsEntity = cardsService.getByCardId(cardId);
                issuerId = cardsEntity.getIssuerId();
            }
            vo.setIssuerId(issuerId);
            if(org.apache.commons.lang3.ObjectUtils.isNotEmpty(issuerId)){
                vo.setIssuerServiceName(productPackageBusiness.getIssuerServiceName(issuerId,""));
            }
            JSONObject bindShowInfo = jsonObject.getJSONObject("bindShowInfo");
            String productImg = bindShowInfo.getString("productImg");
            if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(productImg)) {
                vo.setGoodsImgUrl(productImg);
            }
            String deviceName = bindShowInfo.getString("title");
            if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(deviceName)) {
                vo.setDeviceName(deviceName);
            }

        }


        try {
            List<OrderOrderEntity> orderList = orderService.getOrderListByPackageSnAndUid(uid,productManual.getPackageSn());
            if (CollectionUtils.isEmpty(orderList)) {
                return vo;
            }
            List<GetManualSnOrderListVO> listVo = new ArrayList<>();
            orderList.stream().forEach(order -> {

                GetManualSnOrderListVO list  = new GetManualSnOrderListVO();
                list.setOrderSn(order.getOrderSn());
                list.setStatus(order.getStatus());
                list.setPlateNo(order.getPlateNo());
                list.setPlateColor(order.getPlateColor());
                list.setCreatedAt(order.getCreatedAt());
                list.setActivatedStatus(order.getActivatedStatus());
                list.setAftersaleStatus(order.getAftersaleStatus());
                listVo.add(list);
            });
            vo.setOrderList(listVo);
        } catch (Exception e) {
            log.error("获取用户未完成订单列表 异常:" + e.getMessage());
        }

        return vo;
    }

    /**
     * 获取风控的检查信息
     */
    public GetOrderRiskCheckInfoVO getOrderRiskCheckInfo(OrderRiskCheckInfoDTO riskCheckInfoDTO) {
        return RiskCheckFactory.create(riskCheckInfoDTO.getRule()).getInfo(riskCheckInfoDTO.getOrderSn());
    }

    public void uploadRiskCheckInfo(Long uid,UploadRiskCheckInfoDTO uploadRiskCheckInfoDTO) {
        // 获取订单详情
        OrderOrderEntity order = orderService.getByOrderSn(uploadRiskCheckInfoDTO.getOrderSn());
        if (ObjectUtil.isEmpty(order)) {
            ToolsHelper.throwException("订单不存在");
        }
        if(!order.getUid().equals(uid)){
            ToolsHelper.throwException("订单不属于当前用户");
        }
        // 订单需要是正常状态，已支付、已发货、已签收，未售后，未激活
        if (!Arrays.asList(
                StatusEnum.WAIT_FOR_DELIVER.getCode(),
                StatusEnum.WAIT_FOR_RECEIVE.getCode(),
                StatusEnum.WAIT_FOR_PAY.getCode(),
                StatusEnum.FINISH.getCode()
        ).contains(order.getStatus())
                || !Objects.equals(order.getAftersaleStatus(), AftersaleStatus.STATUS_NORMAL.getValue())
                || !Objects.equals(order.getActivatedStatus(), ActivatedStatusEnum.DEFAULT.getCode())) {
            ToolsHelper.throwException("订单状态不能进行资料审核操作");
        }

        // 找对应的审核单
        if (ObjectUtil.isEmpty(order.getReviewOrderSn())) {
            ToolsHelper.throwException("订单不存在对应的审核单");
        }

        // 获取订单对应审核单数据
        ReviewOrderEntity reviewOrder = reviewOrderService.getByReviewOrderSn(order.getReviewOrderSn());
        if (ObjectUtil.isEmpty(reviewOrder)) {
            ToolsHelper.throwException("订单对应的审核单不存在");
        }

        if (!ReviewOrderGuide.GUIDE_RISK_CHECK.getCode().equals(reviewOrder.getGuide()) || Objects.equals(reviewOrder.getIsCanceled(), YesOrNoEnum.YES.getCode())) {
            ToolsHelper.throwException("当前审核无需补充资料");
        }
        try {
            // 创建审核风控资料
            ReviewOrderRiskCheckEntity reviewOrderRiskCheck = new ReviewOrderRiskCheckEntity();
            reviewOrderRiskCheck.setRiskCheckSn(generateSn(GenerateSnEnum.REVIEW_ORDER_RISK_CHECK_SN.getKey()));
            reviewOrderRiskCheck.setReviewOrderSn(order.getReviewOrderSn());
            reviewOrderRiskCheck.setResourceUrl(uploadRiskCheckInfoDTO.getResourceUrl());
            reviewOrderRiskCheck.setRiskStatus(RiskStatus.NORMAL.getCode());
            reviewOrderRiskCheckService.create(reviewOrderRiskCheck);
            // 结果放进task 准备推送
            reviewOrderRiskCheckBusiness.addTask(reviewOrderRiskCheck.getRiskCheckSn());

            // 最后成功后把审核单的guide 置为空，拒绝reject_msg 置为空，需要写入审核单日志
            reviewOrderBusiness.resetRiskCheck(order);

        } catch (Exception e) {
            ToolsHelper.throwException("上传风控资料失败，请稍后重试");
        }

    }
    public List<ProcessingPlateNoVO> getProcessingPlateNoByUid(Long uid) {
        List<OrderOrderEntity> orderList = orderService.getProcessingOrderListByUid(uid);
        if (CollectionUtils.isEmpty(orderList)) {
            return null;
        }

        // 过滤临牌
        return orderList.stream()
                .filter(order -> !order.getPlateNo().contains("临"))
                .map(order -> {
                    ProcessingPlateNoVO vo = new ProcessingPlateNoVO();
                    vo.setPlateNo(order.getPlateNo());
                    vo.setPlateColor(order.getPlateColor());
                    return vo;
        }).collect(Collectors.toList());
    }

    public void riskCheckNotify(OrderRiskCheckNotifyDTO dto) {
        // 获取订单数据，订单已取消或者已有审核单，不能进行创建
        OrderOrderEntity order = orderService.getByOrderSn(dto.getOrderSn());
        if (ObjectUtil.isEmpty(order)) {
            ToolsHelper.throwException("订单不存在", ExceptionEnum.ORDER_RISK_CHECK_NOTIFY_FAILED.getCode());
        }

        // 订单需要是正常状态，已支付、已发货、已签收，未售后，未激活
        if (!Arrays.asList(
                StatusEnum.WAIT_FOR_DELIVER.getCode(),
                StatusEnum.WAIT_FOR_RECEIVE.getCode(),
                StatusEnum.WAIT_FOR_PAY.getCode(),
                StatusEnum.FINISH.getCode()
        ).contains(order.getStatus())
                || !Objects.equals(order.getAftersaleStatus(), AftersaleStatus.STATUS_NORMAL.getValue())
                || !Objects.equals(order.getActivatedStatus(), ActivatedStatusEnum.DEFAULT.getCode())) {
            ToolsHelper.throwException("订单状态不能进行资料审核操作", ExceptionEnum.ORDER_RISK_CHECK_NOTIFY_FAILED.getCode());
        }
        // 找对应的审核单
        if (ObjectUtil.isEmpty(order.getReviewOrderSn())) {
            ToolsHelper.throwException("订单不存在对应的审核单", ExceptionEnum.ORDER_RISK_CHECK_NOTIFY_FAILED.getCode());
        }

        // 获取订单对应审核单数据
        ReviewOrderEntity reviewOrder = reviewOrderService.getByReviewOrderSn(order.getReviewOrderSn());
        if (ObjectUtil.isEmpty(reviewOrder)) {
            ToolsHelper.throwException("订单对应的审核单不存在",ExceptionEnum.ORDER_RISK_CHECK_NOTIFY_FAILED.getCode());
        }
        // 审核单guide == risk_check ,说明是幂等请求，直接返回
        if (ObjectUtil.equals(reviewOrder.getGuide(), ReviewOrderGuide.GUIDE_RISK_CHECK.getCode())) {
            return;
        }
        // 如果风控类型是复审，需要把前一个风控单状态驳回
        if (ObjectUtil.equals(dto.getRiskType(), RiskType.SUSPICIOUS.getCode())) {
            reviewOrderRiskCheckBusiness.rejectRiskCheck(reviewOrder.getReviewOrderSn());
        }

        reviewOrderBusiness.needRiskCheck(reviewOrder, dto.getMsg());
    }
}
