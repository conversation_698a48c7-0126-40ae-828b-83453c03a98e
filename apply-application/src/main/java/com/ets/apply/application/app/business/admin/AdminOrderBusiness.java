package com.ets.apply.application.app.business.admin;

import cn.hutool.core.util.DesensitizedUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.apply.application.app.business.BaseBusiness;
import com.ets.apply.application.app.business.OrderBenefitBusiness;
import com.ets.apply.application.app.thirdservice.feign.CallJavaBaseFeign;
import com.ets.apply.application.app.thirdservice.feign.CallPhpApplyFeign;
import com.ets.apply.application.app.thirdservice.request.javaBase.CosGetSignDTO;
import com.ets.apply.application.app.thirdservice.response.javaBase.CosGetSignVO;
import com.ets.apply.application.common.consts.order.*;
import com.ets.apply.application.common.consts.orderCancelApply.CancelStatusEnum;
import com.ets.apply.application.common.consts.reviewOrder.LocalReviewStatus;
import com.ets.apply.application.common.consts.reviewOrder.ThirdReviewStatus;
import com.ets.apply.application.common.dto.request.admin.orderOrder.AdminOrderCancelDTO;
import com.ets.apply.application.common.dto.request.admin.orderOrder.AdminOrderGetInfoDTO;
import com.ets.apply.application.common.dto.request.admin.orderOrder.AdminOrderOrderGetListDTO;
import com.ets.apply.application.common.vo.OrderBenefitListVO;
import com.ets.apply.application.common.vo.admin.orderOrder.AdminOrderOrderInfoVO;
import com.ets.apply.application.common.vo.admin.orderOrder.AdminOrderOrderListVO;
import com.ets.apply.application.infra.entity.*;
import com.ets.apply.application.infra.relation.*;
import com.ets.apply.application.infra.service.*;
import com.ets.common.JsonResult;
import com.ets.common.ToolsHelper;
import com.ets.common.util.NumberUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.net.URL;
import java.time.LocalDateTime;
import java.util.*;
import java.time.temporal.ChronoUnit;

@Slf4j
@Component
public class AdminOrderBusiness extends BaseBusiness {

    @Autowired
    private OrderBenefitBusiness orderBenefitBusiness;

    @Autowired
    private OrderOrderService orderOrderService;
    @Autowired
    private CardsService cardsService;
    @Autowired
    private ProductPackageService productPackageService;
    @Autowired
    private ReviewOrderService reviewOrderService;
    @Autowired
    private LogisticOrderService logisticOrderService;
    @Autowired
    private OrderCancelApplyService orderCancelApplyService;
    @Autowired
    private ReviewOrderIdCardService reviewOrderIdCardService;
    @Autowired
    private ReviewOrderVehicleService reviewOrderVehicleService;
    @Autowired
    private ReviewOrderBizLicenceService reviewOrderBizLicenceService;
    @Autowired
    private OcrResultRecordService ocrResultRecordService;
    @Autowired
    private CallJavaBaseFeign callJavaBaseFeign;
    @Autowired
    private OrderLogService orderLogService;
    @Autowired
    private ConfigBizFieldValuesService configBizFieldValuesService;
    @Autowired
    private CallPhpApplyFeign callPhpApplyFeign;
    /*
     * 获取全部产品包列表
     */
    public IPage<AdminOrderOrderListVO> getList(AdminOrderOrderGetListDTO dto) {
        // 分页设置
        IPage<OrderOrderEntity> oPage = new Page<>(dto.getPageNum(), dto.getPageSize(), true);
        LocalDateTime beginTime = null;
        LocalDateTime endTime = null;
        if(StringUtils.isNotEmpty(dto.getCreateTimeBegin())){
            beginTime = ToolsHelper.getLocalDateTime(dto.getCreateTimeBegin() + " 00:00:00");
        }
        if(StringUtils.isNotEmpty(dto.getCreateTimeEnd())){
            endTime = ToolsHelper.getLocalDateTime(dto.getCreateTimeEnd() + " 23:59:59");
        }
        // 查询条件设置
        LambdaQueryWrapper<OrderOrderEntity> wrapper = new LambdaQueryWrapper<>();
        //如果有订单号,车牌号，uid,则对创建时间没要求
        if(
           StringUtils.isEmpty(dto.getOrderSn()) &&
           !NumberUtil.isPositive(dto.getUid()) &&
           StringUtils.isEmpty(dto.getPlateNo())
        ){
            // 时间范围限制在一个月内
            if (StringUtils.isEmpty(dto.getCreateTimeBegin()) || StringUtils.isEmpty(dto.getCreateTimeEnd())) {
                ToolsHelper.throwException("请选择开始和结束时间");
            }
            if(ChronoUnit.MONTHS.between(beginTime, endTime) > 1){
                ToolsHelper.throwException("开始和结束时间不可超过一个月");
            }
        }


        wrapper.eq(StringUtils.isNotEmpty(dto.getPlateNo()), OrderOrderEntity::getPlateNo, dto.getPlateNo())
                .eq(NumberUtil.isPositive(dto.getStatus()), OrderOrderEntity::getStatus, dto.getStatus())
                .eq(StringUtils.isNotEmpty(dto.getOrderSn()), OrderOrderEntity::getOrderSn, dto.getOrderSn())
                .eq(NumberUtil.isPositive(dto.getUid()), OrderOrderEntity::getUid, dto.getUid())
                .eq(StringUtils.isNotEmpty(dto.getPackageSn()), OrderOrderEntity::getPackageSn, dto.getPackageSn())
                .eq(StringUtils.isNotEmpty(dto.getSendName()), OrderOrderEntity::getSendName, dto.getSendName())
                .eq(StringUtils.isNotEmpty(dto.getSendPhone()), OrderOrderEntity::getSendPhone, dto.getSendPhone())
                .ge(StringUtils.isNotEmpty(dto.getCreateTimeBegin()), OrderOrderEntity::getCreatedAt, beginTime)
                .le(StringUtils.isNotEmpty(dto.getCreateTimeEnd()), OrderOrderEntity::getCreatedAt, endTime)
                .orderByDesc(OrderOrderEntity::getCreatedAt);

        IPage<OrderOrderEntity> pageList = orderOrderService.getPageListByWrapper(oPage, wrapper);
        if(pageList.getRecords().isEmpty()){
            return null;
        }
        cardsService.bindToMasterEntityList(pageList.getRecords(), OrderBindCardRelation.class);
        productPackageService.bindToMasterEntityList(pageList.getRecords(), OrderBindProductPackageRelation.class);
        //审核单
        reviewOrderService.bindToMasterEntityList(pageList.getRecords(), OrderBindReviewOrderRelation.class);
        //发货单
        logisticOrderService.bindToMasterEntityList(pageList.getRecords(), OrderBindLogisticOrderRelation.class);
        //取消单
        orderCancelApplyService.bindToMasterEntityList(pageList.getRecords(), OrderBindCancelOrderRelation.class);
        Map<Integer,String> bizTypeList = getBizTypeList();
        return pageList.convert(record -> {
            AdminOrderOrderListVO vo = new AdminOrderOrderListVO();
            BeanUtils.copyProperties(record, vo);
            if(record.getCardsEntity() != null){
                vo.setCardName(record.getCardsEntity().getIssuerName()+'('+record.getCardsEntity().getName()+')');
                vo.setCardProvince(record.getCardsEntity().getProvince());
            }
            if (record.getProductPackageEntity() != null) {
                vo.setPackageName(record.getProductPackageEntity().getPackageName());
                if(record.getProductPackageEntity().getApplyConfig() != null){
                    JSONObject applyConfigJsonObject = JSONObject.parseObject(record.getProductPackageEntity().getApplyConfig());
                    vo.setDeviceName(applyConfigJsonObject.getString("device_name"));
                }
            }
            vo.setStatusStr(getCombinedStatus(record));
            vo.setReviewStatusStr(getCombinedReviewStatus(record.getReviewOrderEntity()));
            vo.setLogisticStatusStr(getCombinedLogisticStatus(record.getLogisticOrderEntity()));
            vo.setCancelStatusStr(getCombinedCancelStatus(record.getOrderCancelApplyEntity()));
            //脱敏处理
            vo.setSendPhone(DesensitizedUtil.mobilePhone(record.getSendPhone()));
            vo.setSendName(DesensitizedUtil.chineseName(record.getSendName()));
            vo.setSendAddress(DesensitizedUtil.address(record.getSendAddress(),8));
            vo.setReviewOrder(record.getReviewOrderEntity());
            //业务归属
            vo.setBizTypeStr(bizTypeList.get(record.getBizType()));
            vo.setPlateColorStr(PlateColorEnum.getDescByCode(record.getPlateColor()));
            return vo;
        });
    }
    /*
     * 统一输出状态
     */
     public String getCombinedStatus(OrderOrderEntity orderOrder){
         String combinedStatus = "";
         StatusEnum statusNode = StatusEnum.getNodeByCode(orderOrder.getStatus());
         if(statusNode != null){
             combinedStatus = statusNode.getDescription();
         }
         if(orderOrder.getActivatedStatus() == ActivatedStatusEnum.ACTIVATED.getCode()){
             combinedStatus = ActivatedStatusEnum.ACTIVATED.getDescription();
         }else if (orderOrder.getActivatedStatus() == ActivatedStatusEnum.VOIDED.getCode()) {
             combinedStatus = ActivatedStatusEnum.VOIDED.getDescription();
         }
         if(orderOrder.getAftersaleStatus() == AftersaleStatus.STATUS_APPLY.getValue()){
             combinedStatus = AftersaleStatus.STATUS_APPLY.getDesc();
         }else if (orderOrder.getAftersaleStatus() == AftersaleStatus.STATUS_APPLY_FINISH.getValue()) {
             combinedStatus = AftersaleStatus.STATUS_APPLY_FINISH.getDesc();
         }
         return combinedStatus;
     }
     /*
      * 审核单状态
      */
    public String getCombinedReviewStatus(ReviewOrderEntity reviewOrder){
        String combinedStatus = "";
        if(reviewOrder == null){
            return combinedStatus;
        }
        if(reviewOrder.getIsCanceled() == 1){
            return "已取消";
        }
        //不可以继续提交审核单
        if(reviewOrder.getCanContinue() == CanContinueStatusEnum.CAN_NOT_CONTINUE.getCode()){
            return "拒绝（不可以继续）";
        }
        //我方审核为待审核或者审核失败，直接返回状态
        if(Arrays.asList(LocalReviewStatus.waiting.getCode(),LocalReviewStatus.fail.getCode()).contains(reviewOrder.getLocalReviewStatus())){
            return LocalReviewStatus.getDescByCode(reviewOrder.getLocalReviewStatus());
        }
        //第三方审核状态
        return ThirdReviewStatus.getDescByCode(reviewOrder.getThirdReviewStatus());
    }

    /*
     * 发货单状态
     */
    public String getCombinedLogisticStatus(LogisticOrderEntity logisticOrder){
        String combinedStatus = "";
        if(logisticOrder == null){
            return combinedStatus;
        }
        if(logisticOrder.getIsCanceled() == 1){
            return "已取消";
        }
        if(logisticOrder.getShippedAt() != null){
            return "已发货";
        }
        return "发货中";
    }

    /*
     * 取消单状态
     */
    public String getCombinedCancelStatus(OrderCancelApply orderCancelApply){
        String combinedStatus = "";
        if(orderCancelApply == null){
            return combinedStatus;
        }
        //第三方审核状态
        return CancelStatusEnum.getDescByCode(orderCancelApply.getCancelStatus());
    }

    public AdminOrderOrderInfoVO getInfo(AdminOrderGetInfoDTO dto){
        AdminOrderOrderInfoVO vo = new AdminOrderOrderInfoVO();
        OrderOrderEntity orderOrder = orderOrderService.getById(dto.getOrderSn());
        if(orderOrder == null){
            return vo;
        }
        CosGetSignDTO cosGetSignDTO = new CosGetSignDTO();
        HashMap<String, String> cosGetSignUrls = new HashMap<>();

        productPackageService.bindToMasterEntity(orderOrder, OrderBindProductPackageRelation.class);
        //审核单
        if(StringUtils.isNotEmpty(orderOrder.getReviewOrderSn())){
            reviewOrderService.bindToMasterEntity(orderOrder,OrderBindReviewOrderRelation.class);
            vo.setReviewOrder(orderOrder.getReviewOrderEntity());
            //身份证
            reviewOrderIdCardService.bindToMasterEntity(orderOrder.getReviewOrderEntity(), ReviewOrderBindIdCardRelation.class);
            ReviewOrderIdCardEntity reviewOrderIdCard = orderOrder.getReviewOrderEntity().getReviewOrderIdCardEntity();

            vo.setReviewOrderIdCardFrontOcrRecord(ocrResultRecordService.getBySn(reviewOrderIdCard.getFrontOcrRecordSn()));
            vo.setReviewOrderIdCardBackOcrRecord(ocrResultRecordService.getBySn(reviewOrderIdCard.getBackOcrRecordSn()));
            cosGetSignUrls.put("idCardFront", getWatermarkUrl(reviewOrderIdCard.getFrontImgUrl()));
            cosGetSignUrls.put("idCardBack", getWatermarkUrl(reviewOrderIdCard.getBackImgUrl()));

            //行驶证
            reviewOrderVehicleService.bindToMasterEntity(orderOrder.getReviewOrderEntity(), ReviewOrderBindVehicleRelation.class);
            ReviewOrderVehicleEntity reviewOrderVehicle = orderOrder.getReviewOrderEntity().getReviewOrderVehicleEntity();
            vo.setReviewOrderVehicleFrontOcrRecord(ocrResultRecordService.getBySn(reviewOrderVehicle.getFrontOcrRecordSn()));
            vo.setReviewOrderVehicleBackOcrRecord(ocrResultRecordService.getBySn(reviewOrderVehicle.getBackOcrRecordSn()));
            cosGetSignUrls.put("vehicleFront", getWatermarkUrl(reviewOrderVehicle.getFrontImgUrl()));
            cosGetSignUrls.put("vehicleBack", getWatermarkUrl(reviewOrderVehicle.getBackImgUrl()));

            ReviewOrderBizLicenceEntity reviewOrderBizLicence = null;
            //企业营业执照
            if(StringUtils.isNotEmpty(orderOrder.getReviewOrderEntity().getReviewOrderBizLicenceSn())){
                reviewOrderBizLicenceService.bindToMasterEntity(orderOrder.getReviewOrderEntity(), ReviewOrderBindBizLicenceRelation.class);
                 reviewOrderBizLicence = orderOrder.getReviewOrderEntity().getReviewOrderBizLicenceEntity();
                 if(reviewOrderBizLicence != null){
                     vo.setReviewOrderBizLicenceOcrRecord(ocrResultRecordService.getBySn(reviewOrderBizLicence.getOcrRecordSn()));
                     cosGetSignUrls.put("bizLicence", getWatermarkUrl(reviewOrderBizLicence.getImgUrl()));
                 }
            }

            cosGetSignDTO.setUrls(cosGetSignUrls);
            try {
                String jsonResult = callJavaBaseFeign.getSignUrl(cosGetSignDTO);
                JsonResult<CosGetSignVO> result = JsonResult.convertFromJsonStr(jsonResult, CosGetSignVO.class);
                reviewOrderIdCard.setFrontImgUrl(result.getData().getUrls().get("idCardFront"));
                reviewOrderIdCard.setBackImgUrl(result.getData().getUrls().get("idCardBack"));
                reviewOrderVehicle.setFrontImgUrl(result.getData().getUrls().get("vehicleFront"));
                reviewOrderVehicle.setBackImgUrl(result.getData().getUrls().get("vehicleBack"));
                if(reviewOrderBizLicence != null){
                    reviewOrderBizLicence.setImgUrl(result.getData().getUrls().get("bizLicence"));
                }
            }catch (Exception e) {

            }

            vo.setReviewOrderIdCard(reviewOrderIdCard);
            vo.setReviewOrderVehicle(reviewOrderVehicle);
            vo.setReviewOrderBizLicence(reviewOrderBizLicence);
        }
        //发货单
        if(StringUtils.isNotEmpty(orderOrder.getLogisticOrderSn())){
            logisticOrderService.bindToMasterEntity(orderOrder,OrderBindLogisticOrderRelation.class);
            vo.setLogisticOrder(orderOrder.getLogisticOrderEntity());
        }
        //取消单
        if(StringUtils.isNotEmpty(orderOrder.getCancelApplySn())){
            orderCancelApplyService.bindToMasterEntity(orderOrder, OrderBindCancelOrderRelation.class);
            vo.setOrderCancelApply(orderOrder.getOrderCancelApplyEntity());
        }


        BeanUtils.copyProperties(orderOrder, vo);
        vo.setStatusStr(getCombinedStatus(orderOrder));
        vo.setReviewStatusStr(getCombinedReviewStatus(orderOrder.getReviewOrderEntity()));
        vo.setLogisticStatusStr(getCombinedLogisticStatus(orderOrder.getLogisticOrderEntity()));
        vo.setCancelStatusStr(getCombinedCancelStatus(orderOrder.getOrderCancelApplyEntity()));
        //脱敏处理
        vo.setSendPhone(DesensitizedUtil.mobilePhone(orderOrder.getSendPhone()));
        vo.setSendName(DesensitizedUtil.chineseName(orderOrder.getSendName()));
        vo.setSendAddress(DesensitizedUtil.address(orderOrder.getSendAddress(),8));

        vo.setPlateColorStr(PlateColorEnum.getDescByCode(orderOrder.getPlateColor()));

        if (orderOrder.getProductPackageEntity() != null) {
            vo.setPackageName(orderOrder.getProductPackageEntity().getPackageName());
        }

        // 订单权益
        List<OrderBenefitListVO> orderBenefitList = orderBenefitBusiness.getOrderBenefitList(orderOrder.getOrderSn());
        vo.setOrderBenefitList(orderBenefitList);

        return vo;
    }
    /*
     * 使用带水印的图片路径
     */
    public String getWatermarkUrl(String url) {
        try {
            URL newUrl = new URL(url);
            String query = newUrl.getQuery();
            String str = "watermark/1/image/aHR0cHM6Ly9kZXYtMTI1OTQ0MDA1NS5jb3MuYXAtc2hhbmdoYWkubXlxY2xvdWQuY29tL3dhdGVybWFyay93bS5wbmc=/batch/1";
            if(query != null){
                return url + "&"+str;
            }else{
                return url + "?"+str;
            }
        }catch (Exception e) {
            return url;
        }

    }


    public Boolean resetCanContinue(String orderSn){
        OrderOrderEntity orderOrder = orderOrderService.getById(orderSn);
        if(orderOrder.getReviewOrderSn() == null){
            return false;
        }
        reviewOrderService.bindToMasterEntity(orderOrder,OrderBindReviewOrderRelation.class);
        if(orderOrder.getReviewOrderEntity().getCanContinue() == CanContinueStatusEnum.CAN_CONTINUE.getCode()){
            return true;
        }
        reviewOrderService.resetCanContinue(orderOrder.getReviewOrderSn());
        orderLogService.addLog(orderOrder, "重置审核单can_continue状态");
        return true;
    }

    public Map<Integer,String> getBizTypeList(){
        //获取biz_type的数据
        LambdaQueryWrapper<ConfigBizFieldValuesEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(ConfigBizFieldValuesEntity::getFieldKey,ConfigBizFieldValuesEntity::getName)
                .eq(ConfigBizFieldValuesEntity::getBizField, "biz_type")
                .eq(ConfigBizFieldValuesEntity::getStatus, 1)
                .orderByAsc(ConfigBizFieldValuesEntity::getSort);
        List<ConfigBizFieldValuesEntity> list =  configBizFieldValuesService.getListByWrapper(wrapper);
        Map<Integer,String> collect = new HashMap<>();

        if(list != null){
            for (ConfigBizFieldValuesEntity configBizFieldValues : list) {
                collect.put(Integer.parseInt(configBizFieldValues.getFieldKey()), configBizFieldValues.getName());
            }
        }
        return collect;

    }

    public Boolean forceCancel(AdminOrderCancelDTO dto){
        OrderOrderEntity orderOrder = orderOrderService.getById(dto.getOrderSn());
        if(orderOrder == null){
            ToolsHelper.throwException("订单不存在");
        }
        try {
            String jsonResult =  callPhpApplyFeign.orderCancel(dto);
            JsonResult<Object> result = JsonResult.convertFromJsonStr(jsonResult, Object.class);
            if(result.getCode() != 0){
                ToolsHelper.throwException(result.getMsg());
            }
        }catch (Exception e) {
            ToolsHelper.throwException("取消失败"+e.getMessage());
        }
        return true;
    }
}
