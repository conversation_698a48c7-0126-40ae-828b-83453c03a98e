package com.ets.apply.application.app.thirdservice.response.bigData;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class BigDataOrderSettlementVO {

    @JsonProperty("order_plate_no")
    private String orderPlateNo;

    @JsonProperty("etc_card_province")
    private String etcCardProvince;

    @JsonProperty("order_plate_color_desc")
    private String orderPlateColorDesc;

    @JsonProperty("order_package_sn")
    private String orderPackageSn;

    @JsonProperty("week_num")
    private Integer weekNum;

    @JsonProperty("uid")
    private Long uid;

    @JsonProperty("new_setting_cycle")
    private String newSettingCycle;

    @JsonProperty("return_flag")
    private String returnFlag;

    @JsonProperty("standard_flag")
    private String standardFlag;

    @JsonProperty("order_num")
    private Integer orderNum;

    @JsonProperty("suc_order_num")
    private Integer sucOrderNum;

    @JsonProperty("first_setting_cycle")
    private String firstSettingCycle;

    @JsonProperty("order_sn")
    private String orderSn;
}
