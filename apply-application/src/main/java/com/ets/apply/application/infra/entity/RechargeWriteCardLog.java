package com.ets.apply.application.infra.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 充值写卡日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("etc_recharge_write_card_log")
public class RechargeWriteCardLog extends BaseEntity<RechargeWriteCardLog> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户id
     */
    private Long uid;

    /**
     * 充值订单号
     */
    private String orderSn;

    /**
     * 操作事项
     */
    private String operation;

    /**
     * 充值设备品牌
     */
    private String brand;

    /**
     * 手机型号
     */
    private String mobileType;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;


}
