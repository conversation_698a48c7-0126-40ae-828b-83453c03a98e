package com.ets.apply.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.apply.application.infra.entity.ReviewOrderVehicleEntity;
import com.ets.apply.application.infra.mapper.ReviewOrderVehicleMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 行驶证信息审核资料 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-27
 */
@Slf4j
@Service
@DS("db-order-proxy")
public class ReviewOrderVehicleService extends BaseService<ReviewOrderVehicleMapper, ReviewOrderVehicleEntity> {

    public ReviewOrderVehicleEntity getByVehicleSn(String vehicleSn) {
        return super.baseMapper.selectById(vehicleSn);
    }

    public ReviewOrderVehicleEntity getByReviewOrderSn(String reviewOrderSn) {

        Wrapper<ReviewOrderVehicleEntity> wrapper = Wrappers.<ReviewOrderVehicleEntity>lambdaQuery()
                .eq(ReviewOrderVehicleEntity::getReviewOrderSn, reviewOrderSn)
                .last("LIMIT 1");

        return super.baseMapper.selectOne(wrapper);
    }

}
