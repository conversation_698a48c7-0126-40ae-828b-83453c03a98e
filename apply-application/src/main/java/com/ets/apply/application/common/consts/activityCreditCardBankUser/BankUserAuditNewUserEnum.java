package com.ets.apply.application.common.consts.activityCreditCardBankUser;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum BankUserAuditNewUserEnum {

    NEW_USER_DEFAULT(0, "初始值"),
    NEW_USER_YES(1, "新用户"),
    NEW_USER_NOT(2, "非新户");

    private final int code;
    private final String description;

    public static String getDescByCode(int code) {
        for (BankUserAuditNewUserEnum node : BankUserAuditNewUserEnum.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }

}
