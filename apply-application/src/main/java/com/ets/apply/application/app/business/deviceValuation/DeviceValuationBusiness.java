package com.ets.apply.application.app.business.deviceValuation;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ets.apply.application.app.business.BaseBusiness;
import com.ets.apply.application.common.config.DeviceValuationConfig;
import com.ets.apply.application.common.consts.deviceValuation.ValuationStatusEnum;
import com.ets.apply.application.common.dto.request.deviceValuation.CheckAndLockValuationDTO;
import com.ets.apply.application.common.dto.request.deviceValuation.DoDeviceValuationDTO;
import com.ets.apply.application.common.dto.request.deviceValuation.GetDeviceValuationDTO;
import com.ets.apply.application.common.dto.request.deviceValuation.UseValuationDTO;
import com.ets.apply.application.common.vo.deviceValuation.CheckAndLockValuationVO;
import com.ets.apply.application.common.vo.deviceValuation.DeviceValuationSimpleListVO;
import com.ets.apply.application.common.vo.deviceValuation.DoDeviceValuationVO;
import com.ets.apply.application.common.vo.deviceValuation.GetDeviceValuationVO;
import com.ets.apply.application.infra.entity.DeviceValuationEntity;
import com.ets.apply.application.infra.service.DeviceValuationService;
import com.ets.common.ToolsHelper;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class DeviceValuationBusiness extends BaseBusiness {
    @Resource(name = "redisPermanentTemplate")
    private StringRedisTemplate redisPermanentTemplate;

    @Autowired
    private DeviceValuationService deviceValuationService;

    @Autowired
    private DeviceValuationConfig deviceValuationConfig;

    public DoDeviceValuationVO doDeviceValuation(Long uid, DoDeviceValuationDTO deviceValuationDTO) {
        DeviceValuationEntity existingDeviceValuation =
                deviceValuationService.getOneValidByUidAndPlateNo(uid, deviceValuationDTO.getPlateNo(),
                        deviceValuationDTO.getPlateColor());

        String hashKey = this.getValuationMapKey(deviceValuationDTO);
        HashMap<String, String> valuationConfig = deviceValuationConfig.getGoodsMap().get(hashKey);
        if (ObjectUtil.isEmpty(valuationConfig)) {
            ToolsHelper.throwException("该设备暂无估价");
        }
        DoDeviceValuationVO deviceValuationVO = new DoDeviceValuationVO();

        if (ObjectUtil.isNotEmpty(existingDeviceValuation)) {
            // 修改估价单相关信息
            LambdaUpdateWrapper<DeviceValuationEntity> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(DeviceValuationEntity::getUid, uid)
                    .eq(DeviceValuationEntity::getValuationSn, existingDeviceValuation.getValuationSn())
                    .eq(DeviceValuationEntity::getPlateNo, deviceValuationDTO.getPlateNo())
                    .eq(DeviceValuationEntity::getPlateColor, deviceValuationDTO.getPlateColor())
                    .set(DeviceValuationEntity::getPrice, BigDecimal.valueOf(Long.parseLong(valuationConfig.get(
                            "price"))))
                    .set(DeviceValuationEntity::getReductionConfigSn, valuationConfig.get("reductionConfigSn"))
                    .set(DeviceValuationEntity::getDeviceType, deviceValuationDTO.getDeviceType())
                    .set(DeviceValuationEntity::getUseYear, deviceValuationDTO.getUseYear())
                    .set(DeviceValuationEntity::getUseStatus, deviceValuationDTO.getUseStatus())
                    .set(DeviceValuationEntity::getUpdatedAt, LocalDateTime.now())
            ;

            if (ObjectUtil.isNotEmpty(deviceValuationDTO.getIssuedTime())) {
                wrapper.set(DeviceValuationEntity::getIssuedTime,
                        LocalDateTime.parse(deviceValuationDTO.getIssuedTime(), DateTimeFormatter.ofPattern(
                                "yyyyMMddHHmmss")));
            }else{
                wrapper.set(DeviceValuationEntity::getIssuedTime, null);
            }

            deviceValuationService.updateByWrapper(wrapper);
            DeviceValuationEntity updatedDeviceValuation =
                    deviceValuationService.getOneValidByUidAndPlateNo(uid, deviceValuationDTO.getPlateNo(),
                            deviceValuationDTO.getPlateColor());
            BeanUtils.copyProperties(updatedDeviceValuation, deviceValuationVO);
        } else {
            // 属性设置
            String valuationSn = ToolsHelper.genNum(redisPermanentTemplate, "deviceValuation", appConfig.getEnv(), 8);
            DeviceValuationEntity deviceValuation = new DeviceValuationEntity();
            deviceValuation.setValuationSn(valuationSn);
            deviceValuation.setUid(uid);
            deviceValuation.setPrice(BigDecimal.valueOf(Long.parseLong(valuationConfig.get("price"))));
            deviceValuation.setPlateNo(deviceValuationDTO.getPlateNo());
            deviceValuation.setPlateColor(deviceValuationDTO.getPlateColor());
            deviceValuation.setValuationStatus(ValuationStatusEnum.NORMAL.getStatus());
            deviceValuation.setReductionConfigSn(valuationConfig.get("reductionConfigSn"));
            deviceValuation.setDeviceType(deviceValuationDTO.getDeviceType());
            deviceValuation.setUseYear(deviceValuationDTO.getUseYear());
            deviceValuation.setUseStatus(deviceValuationDTO.getUseStatus());
            if(ObjectUtil.isNotEmpty(deviceValuationDTO.getIssuedTime())){
                deviceValuation.setIssuedTime( LocalDateTime.parse(deviceValuationDTO.getIssuedTime(), DateTimeFormatter.ofPattern(
                        "yyyyMMddHHmmss")));
            }
            deviceValuationService.create(deviceValuation);
            BeanUtils.copyProperties(deviceValuation, deviceValuationVO);
        }
        // 计算getIssuedTime 到当前时间的天数差
        if (ObjectUtil.isNotEmpty(deviceValuationVO.getIssuedTime())) {
            deviceValuationVO.setIssuedDays(DateUtil.betweenDay(DateUtil.parse(String.valueOf(deviceValuationVO.getIssuedTime())),
                    DateUtil.date(), false));
        }
        return deviceValuationVO;
    }

    public GetDeviceValuationVO getDeviceValuation(Long uid) {
        DeviceValuationEntity deviceValuationEntity = deviceValuationService.getLastByUid(uid);
        if (ObjectUtil.isEmpty(deviceValuationEntity)) {
            return null;
        }
        GetDeviceValuationVO deviceValuationVO = new GetDeviceValuationVO();
        BeanUtils.copyProperties(deviceValuationEntity, deviceValuationVO);
        // 计算getIssuedTime 到当前时间的天数差
        if (ObjectUtil.isNotEmpty(deviceValuationVO.getIssuedTime())) {
            deviceValuationVO.setIssuedDays(DateUtil.betweenDay(DateUtil.parse(String.valueOf(deviceValuationVO.getIssuedTime())),
                    DateUtil.date(), false));
        }
        return deviceValuationVO;
    }

    public List<DeviceValuationSimpleListVO> getDeviceValuationSimpleList() {

        Integer listDays = deviceValuationConfig.getLimitListDays();
        if (ObjectUtil.isEmpty(listDays)) {
            listDays = 7;
        }
        LocalDateTime startTime = LocalDateTime.now().minusDays(listDays);
        LocalDateTime endTime = LocalDateTime.now();
        List<DeviceValuationEntity> deviceValuationEntityList =
                deviceValuationService.getValidListByCreatedAt(startTime, endTime);
        if (ObjectUtil.isEmpty(deviceValuationEntityList)) {
            return null;
        }
        List<DeviceValuationSimpleListVO> deviceValuationSimpleListVOList =
                deviceValuationEntityList.stream().map(deviceValuationEntity -> {
                    DeviceValuationSimpleListVO deviceValuationSimpleListVO = new DeviceValuationSimpleListVO();
                    // 对车牌做脱敏
                    deviceValuationSimpleListVO.setPlateNo(deviceValuationEntity.getPlateNo());
                    deviceValuationSimpleListVO.setPrice(deviceValuationEntity.getPrice());
                    return deviceValuationSimpleListVO;
                }).collect(Collectors.toList());
        return deviceValuationSimpleListVOList;

    }

    public String getValuationMapKey(DoDeviceValuationDTO deviceValuationDTO) {
        return deviceValuationDTO.getDeviceType().toString() + deviceValuationDTO.getUseYear().toString();
    }

    public CheckAndLockValuationVO checkAndLockValuation(CheckAndLockValuationDTO checkAndLockValuationDTO) {
        // 通过单号获取估价单信息
        DeviceValuationEntity deviceValuationEntity =
                deviceValuationService.getOneByColumn(checkAndLockValuationDTO.getValuationSn(),
                        DeviceValuationEntity::getValuationSn);
        if (ObjectUtil.isEmpty(deviceValuationEntity)) {
            ToolsHelper.throwException("该设备暂无估价");
        }
        if (!Objects.equals(deviceValuationEntity.getUid(), checkAndLockValuationDTO.getUid())) {
            ToolsHelper.throwException("该用户无此估价单权限");
        }
        // 估价单是否有效
        if (!ValuationStatusEnum.NORMAL.getStatus().equals(deviceValuationEntity.getValuationStatus())) {
            ToolsHelper.throwException("该估价单不能使用");
        }
        // 估价单绑定申办单号
        deviceValuationService.bindReferSn(deviceValuationEntity.getUid(), deviceValuationEntity.getValuationSn(),
                checkAndLockValuationDTO.getReferSn());

        CheckAndLockValuationVO checkAndLockValuationVO = new CheckAndLockValuationVO();
        BeanUtils.copyProperties(deviceValuationEntity, checkAndLockValuationVO);
        return checkAndLockValuationVO;
    }

    public void useValuation(UseValuationDTO useValuationDTO) {
        // 通过单号获取估价单信息
        DeviceValuationEntity deviceValuationEntity =
                deviceValuationService.getOneByColumn(useValuationDTO.getValuationSn(),
                        DeviceValuationEntity::getValuationSn);
        if (ObjectUtil.isEmpty(deviceValuationEntity)) {
            ToolsHelper.throwException("该设备暂无估价");
        }
        if (!Objects.equals(deviceValuationEntity.getUid(), useValuationDTO.getUid())) {
            ToolsHelper.throwException("该用户无此估价单权限");
        }
        // 估价单是否有效
        if (!ValuationStatusEnum.NORMAL.getStatus().equals(deviceValuationEntity.getValuationStatus())) {
            ToolsHelper.throwException("该估价单不能使用");
        }
        // 估价单绑定使用申办单号
        deviceValuationService.use(deviceValuationEntity.getUid(), deviceValuationEntity.getValuationSn(),
                useValuationDTO.getReferSn());

    }


    public GetDeviceValuationVO getDeviceValuation( GetDeviceValuationDTO getDeviceValuationDTO){
        DeviceValuationEntity deviceValuationEntity = deviceValuationService.getOneByUidAndDeviceValuationSn(getDeviceValuationDTO.getUid()
                ,getDeviceValuationDTO.getValuationSn());
        if (ObjectUtil.isEmpty(deviceValuationEntity)) {
            return null;
        }
        GetDeviceValuationVO deviceValuationVO = new GetDeviceValuationVO();
        BeanUtils.copyProperties(deviceValuationEntity, deviceValuationVO);
        return deviceValuationVO;
    }

}
