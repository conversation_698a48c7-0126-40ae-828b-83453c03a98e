package com.ets.apply.application;

import com.ets.apply.application.app.business.location.LocationBusiness;
import com.ets.apply.application.common.consts.location.ProvinceLocationEnum;
import com.ets.apply.application.common.vo.location.GetLocationVO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@SpringBootTest(classes = ApplyApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringRunner.class)
@ActiveProfiles(value = "test")
public class LocationBusinessTest {

    @Autowired
    private LocationBusiness locationBusiness;

    @Test
    public void testGetLocationWithProvinceList() {
        GetLocationVO locationVO = locationBusiness.getLocation("113.108.164.34");
        
        // 验证provinceList不为空
        Assert.assertNotNull("省份列表不应为空", locationVO.getProvinceList());
        
        // 验证省份列表数量正确（34个省级行政区）
        Assert.assertEquals("省份列表数量应为34", 34, locationVO.getProvinceList().size());
        
        // 验证包含北京市
        boolean hasBeijing = locationVO.getProvinceList().stream()
                .anyMatch(province -> "110000".equals(province.getId()) && "北京市".equals(province.getValue()));
        Assert.assertTrue("应包含北京市", hasBeijing);
        
        // 验证包含广东省
        boolean hasGuangdong = locationVO.getProvinceList().stream()
                .anyMatch(province -> "440000".equals(province.getId()) && "广东省".equals(province.getValue()));
        Assert.assertTrue("应包含广东省", hasGuangdong);
        
        // 验证包含香港特别行政区
        boolean hasHongKong = locationVO.getProvinceList().stream()
                .anyMatch(province -> "810000".equals(province.getId()) && "香港特别行政区".equals(province.getValue()));
        Assert.assertTrue("应包含香港特别行政区", hasHongKong);
        
        // 验证所有枚举值都被包含
        List<GetLocationVO.Province> provinceList = locationVO.getProvinceList();
        for (ProvinceLocationEnum provinceEnum : ProvinceLocationEnum.values()) {
            boolean found = provinceList.stream()
                    .anyMatch(province -> 
                        provinceEnum.getId().equals(province.getId()) && 
                        provinceEnum.getValue().equals(province.getValue()));
            Assert.assertTrue("应包含省份: " + provinceEnum.getValue(), found);
        }
        
        System.out.println("✅ 省份列表验证通过，共包含 " + locationVO.getProvinceList().size() + " 个省级行政区");
        
        // 打印前几个省份作为示例
        System.out.println("前5个省份示例：");
        locationVO.getProvinceList().stream()
                .limit(5)
                .forEach(province -> 
                    System.out.println("  " + province.getId() + " - " + province.getValue()));
    }
    
    @Test
    public void testProvinceListOrder() {
        GetLocationVO locationVO = locationBusiness.getLocation("113.108.164.34");
        List<GetLocationVO.Province> provinceList = locationVO.getProvinceList();
        
        // 验证列表顺序与枚举定义顺序一致
        ProvinceLocationEnum[] enumValues = ProvinceLocationEnum.values();
        for (int i = 0; i < enumValues.length; i++) {
            GetLocationVO.Province province = provinceList.get(i);
            ProvinceLocationEnum enumValue = enumValues[i];
            
            Assert.assertEquals("第" + (i+1) + "个省份ID应匹配", 
                enumValue.getId(), province.getId());
            Assert.assertEquals("第" + (i+1) + "个省份名称应匹配", 
                enumValue.getValue(), province.getValue());
        }
        
        System.out.println("✅ 省份列表顺序验证通过");
    }
}
