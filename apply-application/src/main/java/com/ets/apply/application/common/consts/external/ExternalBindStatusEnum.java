package com.ets.apply.application.common.consts.external;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ExternalBindStatusEnum {

    DEFAULT(0, "默认"),
    NORMAL(1, "正常"),
    DELETED(2, "已删除"),// 被覆盖
    CANCELED(9, "已取消");// 取消订单后解除绑定

    private final int code;
    private final String description;

    public static String getDescByCode(int code) {
        for (ExternalBindStatusEnum node : ExternalBindStatusEnum.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }
}
