package com.ets.apply.application.app.factory.productPartner.impl;

import com.ets.apply.application.common.bo.productOrder.ProductOrderRefundBO;
import com.ets.apply.application.common.bo.productOrder.ProductOrderShipBO;
import com.ets.apply.application.common.bo.productOrder.ProductOrderThirdSyncBO;
import com.ets.apply.application.common.dto.external.ExternalOrderAfterSaleStatusChangeDTO;
import com.ets.apply.application.infra.entity.ProductOrderEntity;

public abstract class ProductPartnerBase implements IProductPartner {
    public String refund(ProductOrderRefundBO productOrderRefundBO){
        return "";
    }

    public  void ship(ProductOrderShipBO productOrderShipBO){
    }

    /*
     * 订单同步
     */
    public  void orderSync(ProductOrderEntity productOrder, ProductOrderThirdSyncBO bo){
    }

    /**
     * 售后状态变更通知
     * @param dto
     */
    public void afterSaleStatusChange(ProductOrderEntity productOrder, ExternalOrderAfterSaleStatusChangeDTO dto) {

    }
}
