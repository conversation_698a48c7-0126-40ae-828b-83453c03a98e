package com.ets.apply.application.controller.admin;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.apply.application.app.business.creditCard.CreditCardBusiness;
import com.ets.apply.application.common.dto.intensive.IntensiveCreditCardListDTO;
import com.ets.apply.application.common.dto.request.creditCard.CreditCardActivateDTO;
import com.ets.apply.application.common.dto.request.creditCard.CreditCardListDTO;
import com.ets.apply.application.common.dto.request.creditCard.CreditCardLogDTO;
import com.ets.apply.application.common.vo.creditCard.CreditCardListVO;
import com.ets.apply.application.common.vo.creditCard.CreditCardLogVO;
import com.ets.apply.application.controller.BaseController;
import com.ets.common.JsonResult;
import com.ets.common.ToolsHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.HashMap;

@RequestMapping("/admin/creditCard")
@RestController
@Slf4j
public class CreditCardAdminController extends BaseController {

    @Autowired
    private CreditCardBusiness creditCardBusiness;

    @RequestMapping("/getList")
    @ResponseBody
    public JsonResult<IPage<CreditCardListVO>> getList(@RequestBody(required = false) @Valid CreditCardListDTO request) {

        if (request == null) {
            ToolsHelper.throwException("请传入JSON格式的参数");
        }

        return JsonResult.ok(creditCardBusiness.getListV2(request));
    }
    
    @PostMapping("/getIntensiveList")
    public JsonResult<?> getIntensiveList(@RequestBody @Valid IntensiveCreditCardListDTO dto) {

        if (StringUtils.isEmpty(dto.getPlateNo()) && dto.getUid() == null) {
            ToolsHelper.throwException("车牌号码或者uid必传其一");
        }

        CreditCardListDTO listDTO = BeanUtil.copyProperties(dto, CreditCardListDTO.class);

        return JsonResult.ok(creditCardBusiness.getListV2(listDTO));
    }


    @RequestMapping("/getSelectOptions")
    @ResponseBody
    public JsonResult<HashMap<String, Object>> getSelectOptions() {

        return JsonResult.ok(creditCardBusiness.getSelectOptions());
    }

    @RequestMapping("/manualActivate")
    public JsonResult<?> manualActivate(@RequestBody @Valid CreditCardActivateDTO activateDTO) {
        creditCardBusiness.manualActivate(activateDTO);
        return JsonResult.ok();
    }

    @RequestMapping("/getLog")
    public JsonResult<IPage<CreditCardLogVO>> getLog(@RequestBody @Valid CreditCardLogDTO logDTO) {
        IPage<CreditCardLogVO> list = creditCardBusiness.getLog(logDTO);
        return JsonResult.ok(list);
    }
}
