package com.ets.apply.feign.request;

import com.ets.common.annotation.PhoneAnnotation;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class ProductOrderExternalCreateDTO {

    /**
     * 第三方订单号
     */
    @NotBlank(message = "第三方订单号不能为空")
    private String thirdOrderSn;

    /**
     * 产品包sn
     */
    @NotBlank(message = "产品包不能为空")
    private String packageSn;

    /**
     * 收货人
     */
    private String sendName;

    /**
     * 收货手机
     */
    @PhoneAnnotation(message = "收货手机号校验错误")
    private String sendPhone;

    @PhoneAnnotation(message = "申办手机号校验错误")
    private String etcPhone;

    private String sendArea;

    /**
     * 收货详细地址
     */
    private String sendAddress;

    /**
     * 实际销售价格
     */
    @Max(value = 99999999, message = "实际销售价格不能超过99999999")
    @Min(value = 0, message = "实际销售价格不能小于0")
    @NotNull(message = "实际销售价不能为空")
    @Digits(integer = 8, fraction = 2, message = "实际销售价格小数点需在两位以内")
    private BigDecimal paidAmount;

    /**
     * 快递公司
     */
    private String logisticCompany;

    /**
     * 快递单号
     */
    private String logisticNumber;

    /**
     * 发货时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime sendTime;

}
