package com.ets.apply.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.apply.application.infra.entity.Source;
import com.ets.apply.application.infra.mapper.SourceMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 外部渠道值记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-23
 */
@Service
@DS("db-apply")
public class SourceService extends BaseService<SourceMapper, Source> {

    public List<Source> getSourceListBySource(List<String> sourceCodeList, Integer exclude) {
        Wrapper<Source> wrapper = Wrappers.<Source>lambdaQuery()
                .eq(ObjectUtils.isNotNull(exclude), Source::getExclude, exclude)
                .in(ObjectUtils.isNotEmpty(sourceCodeList), Source::getSource, sourceCodeList)
                .orderByAsc(Source::getSort);
        return list(wrapper);
    }

    public List<Source> getChildSourceListBySource(List<String> sourceCodeList, Integer exclude) {
        Wrapper<Source> wrapper = Wrappers.<Source>lambdaQuery()
                .eq(ObjectUtils.isNotNull(exclude), Source::getExclude, exclude)
                .in(ObjectUtils.isNotEmpty(sourceCodeList), Source::getSource, sourceCodeList)
                .ne(Source::getSource, 0)
                .orderByAsc(Source::getSort);
        return list(wrapper);
    }
}
