package com.ets.apply.application.app.thirdservice.feign;

import com.ets.apply.application.app.thirdservice.fallback.CgbFallbackFactory;
import feign.Response;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.net.URI;
import java.util.Map;

@FeignClient(name = "CbgFeign", url = "url", fallbackFactory = CgbFallbackFactory.class)
public interface CgbFeign {

    @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE)
    Response postReq(URI uri, @RequestBody String body, @RequestHeader Map<String, String> headers);
}
