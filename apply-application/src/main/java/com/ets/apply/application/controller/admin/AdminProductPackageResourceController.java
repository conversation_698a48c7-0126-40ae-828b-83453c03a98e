package com.ets.apply.application.controller.admin;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.apply.application.app.business.productPackageResource.ProductPackageResourceBusiness;
import com.ets.apply.application.common.dto.request.applyPage.ApplyPageModifyDTO;
import com.ets.apply.application.common.dto.request.applyPage.ApplyPageSetStatusDTO;
import com.ets.apply.application.common.dto.request.productPackageResource.ProductPackageResourceCreateDTO;
import com.ets.apply.application.common.dto.request.productPackageResource.ProductPackageResourceModifyDTO;
import com.ets.apply.application.common.dto.request.productPackageResource.ProductPackageResourceSetStatusDTO;
import com.ets.apply.application.common.dto.request.productPackageResource.ResourceListDTO;
import com.ets.apply.application.common.vo.productPackageResource.ProductPackageResourceListVO;
import com.ets.apply.application.controller.BaseController;
import com.ets.common.JsonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Controller
@RestController
@RequestMapping("/admin/product-package-resource")
public class AdminProductPackageResourceController extends BaseController {
    @Autowired
    ProductPackageResourceBusiness productPackageResourceBusiness;

    @RequestMapping("/create")
    JsonResult<?> create(@RequestBody @Valid ProductPackageResourceCreateDTO creatDTO) {
        productPackageResourceBusiness.create(creatDTO);
        return JsonResult.ok();
    }

    /**
     * 获取列表信息
     */
    @RequestMapping("/get-list")
    JsonResult<IPage<ProductPackageResourceListVO>> getList(@RequestBody @Valid ResourceListDTO listDTO) {

        return JsonResult.ok(productPackageResourceBusiness.getList(listDTO));
    }

    @RequestMapping("/get-detail")
    JsonResult<?> getDetail(@RequestParam String resourceSn){
        return JsonResult.ok(productPackageResourceBusiness.getDetail(resourceSn));
    }


    @RequestMapping("/modify")
    JsonResult<?> modify(@RequestBody @Valid ProductPackageResourceModifyDTO modifyDTO) {
        productPackageResourceBusiness.modify(modifyDTO);
        return JsonResult.ok();
    }

    @RequestMapping("/set-status")
    JsonResult<?> setStatus(@RequestBody @Valid ProductPackageResourceSetStatusDTO setStatusDTO){
        productPackageResourceBusiness.setStatus(setStatusDTO);
        return JsonResult.ok();
    }
}

