---
description:
globs:
alwaysApply: false
---
# Java 开发规范指南

## API 设计规范

### 接口设计原则
1. 不使用 Swagger API 文档注解
2. 请求方式选择：
   - POST 方式：适用于带有请求参数的接口，使用 `@RequestBody` 传参
   - GET 方式：仅适用于少量单一 ID 参数的数据获取接口，使用 `@RequestParams` 传参

### 命名规范
1. 接口路由：使用连字符（-）方式命名，禁止使用驼峰
   - 正确示例：`/api/user-profile`
   - 错误示例：`/api/userProfile`

2. DTO 命名：
   - 以 DTO 结尾
   - 存放位置：`common/dto` 目录下
   - 可按业务类型分类存放

3. VO 命名：
   - 以 VO 结尾
   - 存放位置：`common/vo` 目录下
   - 可按业务类型分类存放

## 开发规范

### 技术要求
1. 基于 Java 17 开发
2. Controller 层日志使用规范：
   - 尽量避免在 Controller 层添加日志
   - 如需使用日志，必须使用 SLF4J 的注解方式

### 代码示例

#### 接口示例
```java
@RestController
@RequestMapping("/api/user")
public class UserController {
    // GET 示例 - 单一参数
    @GetMapping("/profile/{id}")
    public ResponseEntity<UserProfileVO> getProfile(@PathVariable Long id) {
        // 实现逻辑
    }

    // POST 示例 - 复杂参数
    @PostMapping("/update-profile")
    public ResponseEntity<Void> updateProfile(@RequestBody UserProfileUpdateDTO dto) {
        // 实现逻辑
    }
}
```

#### DTO 示例
```java
public class UserProfileUpdateDTO {
    private Long userId;
    private String username;
    private String email;
    // getter 和 setter
}
```

#### VO 示例
```java
public class UserProfileVO {
    private Long userId;
    private String username;
    private String email;
    // getter 和 setter
}
```
