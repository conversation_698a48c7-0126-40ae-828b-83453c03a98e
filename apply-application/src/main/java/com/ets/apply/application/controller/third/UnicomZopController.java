package com.ets.apply.application.controller.third;

import com.ets.apply.application.app.business.szUnicomZop.SzUnicomZopBusiness;
import com.ets.apply.application.app.service.thirdPartner.SzUnicomZopService;
import com.ets.apply.application.common.dto.request.szUnicomZop.*;
import com.ets.apply.application.common.vo.szUnicomZop.*;
import com.ets.common.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import jakarta.validation.Valid;

@RequestMapping("/third/unicomZop")
@RefreshScope
@RestController
@Slf4j
public class UnicomZopController {

    @Autowired
    private SzUnicomZopService szUnicomZopService;
    @Autowired
    private SzUnicomZopBusiness szUnicomZopBusiness;


    @PostMapping("/checkUser")
    public JsonResult<SzUnicomZopCheckUserVo> checkUser(@RequestBody @Valid SzUnicomZopCheckUserDTO dto) throws Exception {
        return JsonResult.ok(szUnicomZopBusiness.checkUser(dto));
    }

    @PostMapping("/checkRisk")
    public JsonResult<SzUnicomZopNormalVo> checkRisk(@RequestBody @Valid SzUnicomZopCheckRiskDTO dto) throws Exception {
        return JsonResult.ok(szUnicomZopService.checkRisk(dto));
    }

    @PostMapping("/resource")
    public JsonResult<SzUnicomZopNormalVo> resource() throws Exception {
        return JsonResult.ok(szUnicomZopService.resourceV1());
    }


    @PostMapping("/createOrderIntention")
    public JsonResult<SzUnicomZopCreateOrderIntentionVo> createOrderIntention(@RequestBody @Valid SzUnicomZopCreateOrderIntentionDTO dto) throws Exception {
        return JsonResult.ok(szUnicomZopService.createOrderIntention(dto));
    }

    @PostMapping("/createOrderIntentionFormal")
    public JsonResult<SzUnicomZopCreateOrderIntentionFormalVo> createOrderIntentionFormal(@RequestBody @Valid SzUnicomZopCreateOrderIntentionFormalDTO dto) throws Exception {
        return JsonResult.ok(szUnicomZopService.createOrderIntentionFormal(dto));
    }

    @PostMapping("/orderGetCard")
    public JsonResult<SzUnicomZopOrderGetCardVo> orderGetCard(@RequestBody @Valid SzUnicomZopOrderGetCardDTO dto) throws Exception {
        return JsonResult.ok(szUnicomZopService.orderGetCard(dto));
    }

    @PostMapping("/selectNum")
    public JsonResult<SzUnicomZopPhoneListVo> selectNum(@RequestBody @Valid SzUnicomZopSelectNumDTO dto) throws Exception {
        return JsonResult.ok(szUnicomZopService.selectNum(dto));
    }
}
