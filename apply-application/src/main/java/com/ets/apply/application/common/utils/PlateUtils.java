package com.ets.apply.application.common.utils;

import com.ets.common.ToolsHelper;

public class PlateUtils {

    /**
     * 根据车牌号获取车牌颜色，仅限客车
     * @param plateNo
     * @return
     */
    public static int getPlateColorByPlateNo(String plateNo) {
        int plateColor = 0;
        if (plateNo.length() == 8) {
            plateColor = 4;
        } else if (plateNo.length() != 7) {
            ToolsHelper.throwException("车牌号格式不正确");
        }

        return plateColor;
    }

}
