package com.ets.apply.application.common.consts.recharge;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
@AllArgsConstructor
public enum RechargeCardTypeEnum {
    PRE_PAY_CARD("22", "储值卡"),
    DEBIT_CARD("23", "记账卡");

    private final String value;
    private final String desc;
    public static final Map<String, String> map;

    static {
        RechargeCardTypeEnum[] enums = RechargeCardTypeEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
