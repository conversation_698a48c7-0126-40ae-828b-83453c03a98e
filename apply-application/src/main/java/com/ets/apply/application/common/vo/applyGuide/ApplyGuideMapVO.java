package com.ets.apply.application.common.vo.applyGuide;

import lombok.Data;

import java.util.List;

@Data
public class ApplyGuideMapVO {
    /**
     * 节点ID
     */
    private Integer id;

    /**
     * 层级
     */
    private Integer level;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 问题ID
     */
    private Integer questionId;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 标签
     */
    private String tag;

    /**
     * 问题icon图片
     */
    private String iconUrl;

    /**
     * 标签icon图片
     */
    private String tagIconUrl;

    /**
     * 选择方式[1-单选 2-多选]
     */
    private Integer selectType;

    /**
     * 默认选中[0-不选 1-选中]
     */
    private Integer defaultChoose;

    /**
     * 跳转地址链接
     */
    private String pageUrl;

    /**
     * 子节点列表
     */
    private List<ApplyGuideMapVO> children;
} 