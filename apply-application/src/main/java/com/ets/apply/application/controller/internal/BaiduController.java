package com.ets.apply.application.controller.internal;

import com.ets.apply.application.common.dto.BaiduTplMsgDto;
import com.ets.apply.application.app.business.BaiduBusiness;
import com.ets.apply.application.controller.BaseController;
import com.ets.common.BizException;
import com.ets.common.JsonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping("/baidu")
@RestController
public class BaiduController extends BaseController {

    @Autowired
    BaiduBusiness baiduBusiness;

    @RequestMapping("/sendTplMsg")
    public JsonResult<?> sendTplMsg(@Validated @RequestBody BaiduTplMsgDto baiduTplMsgDto) throws BizException {
        baiduBusiness.sendTplMsg(baiduTplMsgDto);
        return JsonResult.ok();
    }
}
