package com.ets.apply.application.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.List;


/**
 * <AUTHOR>
 */
@RefreshScope
@Configuration
@Data
@ConfigurationProperties(prefix = "params.dynamic-recommend")
public class DynamicRecommendConfig {
    // 未支付订单跳转地址
    private String unpaidOrderRecommendPath;
    private HashMap<String,Object> unpaidOrderRecommendParams;
    private String unpaidOrderRecommendTitle;
    private String unpaidOrderRecommendDescription;
    private HashMap<Integer,Object> unpaidOrderTabSourceMap;


    // 详情页浏览推荐
    private String detailPageRecommendPath;
    private HashMap<String,Object> detailPageRecommendParams;
    private String detailPageRecommendTitle;
    private String detailPageRecommendDescription;

    // 订单取消推荐
    private String orderCancelRecommendPath;
    private HashMap<String,Object> orderCancelRecommendParams;
    private String orderCancelRecommendTitle;
    private String orderCancelRecommendDescription;

    // 信用卡推荐
    private String creditCardOrderRecommendPath;
    private HashMap<String,Object> creditCardOrderRecommendParams;
    private String creditCardOrderRecommendTitle;
    private String  creditCardOrderRecommendDescription;
    // 信用卡订单允许的bizType
    private List<Integer> creditCardOrderRecommendBizType;


    /**
     * 已支付订单推荐
     */
    private String paidOrderRecommendPath;
    private HashMap<String,Object> paidOrderRecommendParams;
    private String paidOrderRecommendTitle;
    private String paidOrderRecommendDescription;

}
