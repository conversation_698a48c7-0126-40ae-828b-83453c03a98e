package com.ets.apply.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.apply.application.infra.entity.mapModule.MapModuleEntity;
import com.ets.apply.application.infra.mapper.MapModuleMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 映射模块 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-27
 */
@Service
@DS("db-apply")
public class MapModuleService extends BaseService<MapModuleMapper, MapModuleEntity> {

    /*
     * 通过module获取信息
     */
    public MapModuleEntity getInfoByModule(String module){
        Wrapper<MapModuleEntity> wrapper = Wrappers.<MapModuleEntity>lambdaQuery()
            .eq(MapModuleEntity::getModule, module)
            .last("limit 1");
        return super.baseMapper.selectOne(wrapper);
    }


}
