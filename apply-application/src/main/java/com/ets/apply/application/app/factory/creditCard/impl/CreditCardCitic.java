package com.ets.apply.application.app.factory.creditCard.impl;
import com.ets.apply.application.app.business.creditCard.CreditCardBusiness;
import com.ets.apply.application.app.service.bank.CiticService;
import com.ets.apply.application.common.config.creditBank.CiticCreditBankConfig;
import com.ets.apply.application.common.consts.service.ServerErrorCodeEnum;
import com.ets.apply.application.common.dto.request.creditCard.ApplyOrderDTO;
import com.ets.apply.application.common.vo.creditCard.CreditCardApplyOrderVO;
import com.ets.apply.application.common.vo.creditCard.citic.CiticResponseVO;
import com.ets.common.ToolsHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.Arrays;

@Component
@Slf4j
public class CreditCardCitic extends CreditCardBase{
    @Resource(name = "redisPermanentTemplate")
    private StringRedisTemplate redisPermanentTemplate;
    @Autowired
    private CiticService citicService;
    @Autowired
    private CiticCreditBankConfig citicCreditBankConfig;
    @Autowired
    private CreditCardBusiness creditCardBusiness;

    public CreditCardApplyOrderVO applyOrder(ApplyOrderDTO applyOrderDTO) {
        //内部版本号
        applyOrderDTO.setInnerVersion(citicCreditBankConfig.getInnerVersion());
        //银行申办流水号
        if(applyOrderDTO.getBankApplyNumber().length() < 1){
            //新增记录
            applyOrderDTO.setBankApplyNumber(creditCardBusiness.applyCreditCard(applyOrderDTO));
        }else{
            //查询一下申请流水的状态
            CiticResponseVO ret = citicService.queryOrder(applyOrderDTO.getBankApplyNumber());
            if(ret == null){
                ToolsHelper.throwException("系统繁忙，请稍后重试");
            }
            if(
                !ret.getReturnCode().equals("00") ||
                    ret.getData().isEmpty()
            ){
                ToolsHelper.throwException("系统繁忙，请稍后重试");
            }
            ret.getData().forEach(data -> {
                if(data.getPaId().equals(applyOrderDTO.getBankApplyNumber())){
                    //111 状态未知，允许用户申请
                    //112是无申卡记录，允许申请
                    if(!Arrays.asList("111","112").contains(data.getApplyStatus())){
                        ToolsHelper.throwException("申请结果处理中，请耐心等待", ServerErrorCodeEnum.SERVICE_ERROR_CODE_CREDIT_CARD_SUBMITTED.getCode());
                    }
                }
            });


        }
        CreditCardApplyOrderVO applyOrderVO = new CreditCardApplyOrderVO();
        applyOrderVO.setUrl(citicService.getApplyUrl(applyOrderDTO));
        applyOrderVO.setAppId(citicCreditBankConfig.getAppId());
        return applyOrderVO;
    }
}
