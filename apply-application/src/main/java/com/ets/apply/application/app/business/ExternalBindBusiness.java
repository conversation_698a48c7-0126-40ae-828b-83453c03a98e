package com.ets.apply.application.app.business;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.ets.apply.application.app.thirdservice.feign.BaseApplicationFeign;
import com.ets.apply.application.app.thirdservice.feign.CallPhpUserFeign;
import com.ets.apply.application.app.thirdservice.feign.UserApplicationFeign;
import com.ets.apply.application.common.consts.external.ExternalBindStatusEnum;
import com.ets.apply.application.common.consts.external.ExternalThirdTypeEnum;
import com.ets.apply.application.common.dto.external.ExternalUpdateReservedPhoneDTO;
import com.ets.apply.application.common.dto.external.ExternalBindDTO;
import com.ets.apply.application.common.dto.user.GetListByApplyOrderSnDTO;
import com.ets.apply.application.common.dto.usercard.UserCardFindByOrderSnDTO;
import com.ets.apply.application.common.vo.external.ExternalBindDetailVO;
import com.ets.apply.application.common.vo.external.ExternalBindListVO;
import com.ets.apply.application.common.vo.user.UsersCardsEntityVO;
import com.ets.apply.application.common.vo.user.UsersEntityVO;
import com.ets.apply.application.common.vo.usercard.UserCardFindByOrderSnVO;
import com.ets.apply.application.infra.entity.CardsEntity;
import com.ets.apply.application.infra.entity.CardsIssuersEntity;
import com.ets.apply.application.infra.entity.ExternalBindEntity;
import com.ets.apply.application.infra.entity.ProductOrderEntity;
import com.ets.apply.application.infra.service.CardsIssuersService;
import com.ets.apply.application.infra.service.CardsService;
import com.ets.apply.application.infra.service.ExternalBindService;
import com.ets.apply.application.infra.service.ProductOrderService;
import com.ets.base.feign.request.sms.SendCodeRequestDTO;
import com.ets.base.feign.request.sms.VerifyCodeRequestDTO;
import com.ets.common.RequestHelper;
import com.ets.common.ToolsHelper;
import com.ets.common.util.ListUtils;
import com.ets.common.util.NumberUtil;
import com.ets.starter.config.AppConfig;
import com.ets.user.feign.feign.UsersPhoneFeign;
import com.ets.user.feign.request.UpdateReservedPhoneByPlateNoDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
public class ExternalBindBusiness {

    @Autowired
    private UserApplicationFeign userApplicationFeign;

    @Autowired
    private ExternalBindService externalBindService;

    @Autowired
    private ProductOrderService productOrderService;

    @Autowired
    private CardsService cardsService;

    @Autowired
    private BaseBusiness baseBusiness;

    @Autowired
    private BaseApplicationFeign baseApplicationFeign;

    @Autowired
    private AppConfig appConfig;

    @Autowired
    private UsersPhoneFeign usersPhoneFeign;

    @Autowired
    private CallPhpUserFeign callPhpUserFeign;

    @Autowired
    private CardsIssuersService cardsIssuersService;

    public List<ExternalBindListVO> getList(Long uid) {

        UsersEntityVO usersEntity = userApplicationFeign.getById(uid).getDataWithCheckError();

        List<ExternalBindEntity> list = externalBindService.getListByExternalUnionId(usersEntity.getThirdType(), usersEntity.getThirdCode());

        if (list == null || list.isEmpty()) {
            return null;
        }

        Map<String, ExternalBindEntity> bindMap = ListUtils.listToMap(list, ExternalBindEntity::getBindOrderSn);

        // 查询productOrder列表，提前车牌号，申办单号
        List<ProductOrderEntity> orderList = productOrderService.getValidListBySns(ListUtils.getColumnList(list, ExternalBindEntity::getBindOrderSn));

        if (orderList == null || orderList.isEmpty()) {
            return null;
        }

        List<String> applyOrderSnList = ListUtils.getColumnList(orderList, ProductOrderEntity::getApplyOrderSn);
        // 查询userCard列表, 设置卡签数据，过滤已注销记录

        GetListByApplyOrderSnDTO orderSnDTO = new GetListByApplyOrderSnDTO();
        orderSnDTO.setApplyOrderSnList(applyOrderSnList);
        List<UsersCardsEntityVO> usersCards = userApplicationFeign.getListByApplyOrderSn(orderSnDTO).getDataWithCheckError();
        if (usersCards == null) {
            usersCards = new ArrayList<>();
        }

        Map<String, UsersCardsEntityVO> usersCardsMap = ListUtils.listToMap(usersCards, UsersCardsEntityVO::getApplyOrderSn);

        List<ExternalBindListVO> result = new ArrayList<>();

        orderList.forEach(order -> {

            UsersCardsEntityVO userCard = usersCardsMap.get(order.getApplyOrderSn());

            ExternalBindListVO vo = new ExternalBindListVO();
            vo.setPlateNo(order.getPlateNo());
            vo.setPlateColor(order.getPlateColor());
            vo.setApplyOrderSn(order.getApplyOrderSn());
            vo.setBindSn(bindMap.get(order.getProductOrderSn()).getBindSn());
            vo.setBindId(bindMap.get(order.getProductOrderSn()).getId());
            vo.setHasUserCard(userCard != null);
            vo.setIsActivated(userCard != null && userCard.getFirstActivatedAt() != null);

            // userCard不存在 或 未注销 才新增到列表
            if (userCard == null || userCard.getIsDel() == 0) {
                result.add(vo);
            }
        });

        result.sort(Comparator.comparing(ExternalBindListVO::getBindId));

        return result;
    }

    public ExternalBindDetailVO getDetail(String bindSn, Long uid) {

        UsersEntityVO usersEntity = userApplicationFeign.getById(uid).getDataWithCheckError();

        ExternalBindEntity entity = externalBindService.getByBindSn(bindSn);
        if (entity == null) {
            ToolsHelper.throwException("绑定记录不存在");
        }

        if (! usersEntity.getThirdType().equals(entity.getThirdType())) {
            ToolsHelper.throwException("用户和绑定记录渠道类型不一致");
        }

        ExternalBindDetailVO vo = new ExternalBindDetailVO();
        vo.setBindSn(bindSn);
        // 请求接口前缀 todo 有其他渠道后需单独设置
        vo.setUrlPrefix("/transfer/cq");
        // 查询商城订单
        ProductOrderEntity bindOrderEntity = productOrderService.getByProductOrderSn(entity.getBindOrderSn());
        vo.setPlateNo(bindOrderEntity.getPlateNo());
        vo.setPlateColor(bindOrderEntity.getPlateColor());
        vo.setApplyOrderSn(bindOrderEntity.getApplyOrderSn());

        // 查询卡签信息
        UserCardFindByOrderSnVO userCardVo = null;
        if (StringUtils.isNotEmpty(bindOrderEntity.getApplyOrderSn())) {

            UserCardFindByOrderSnDTO dto = new UserCardFindByOrderSnDTO();
            dto.setUid(bindOrderEntity.getUid());
            dto.setOrderSn(bindOrderEntity.getApplyOrderSn());
            userCardVo = callPhpUserFeign.findByOrderSn(dto).getData();
        }
        if (userCardVo != null) {
            vo.setHasUserCard(true);
            vo.setCardNo(userCardVo.getCardNo());
            UserCardFindByOrderSnVO.VehicleObu vehicleObu = userCardVo.getVehicleObu();
            vo.setWarrantyExpire(getWarrantyExpire(vehicleObu));
            vo.setIsActivated(StringUtils.isNotEmpty(userCardVo.getFirstActivatedAt()));

            CardsEntity card = cardsService.getByCardId(userCardVo.getCardId());
            vo.setIssuerName(getCardIssueName(card));
            vo.setProvince(getIssuerProvince(card));
        } else {
            // 也可能接口请求异常
            vo.setHasUserCard(false);
        }

        return vo;
    }

    public String getIssuerProvince(CardsEntity card) {

        if (card == null) {
            return "";
        }
        CardsIssuersEntity issuer = cardsIssuersService.getByIssuerId(card.getIssuerId());

        return issuer.getProvince();
    }

    public String getCardIssueName(CardsEntity card) {

        if (card != null) {
            return card.getIssuerName() + "（" + card.getProvince() + "）";
        }

        return "";
    }

    public String getWarrantyExpire(UserCardFindByOrderSnVO.VehicleObu vehicleObu) {

        String warrantyExpire = "无";

        if (vehicleObu != null) {
            // 质保期
            if (vehicleObu.getWarrantyStartAt() != null
                    && vehicleObu.getWarrantyEndAt() != null
            ) {
                warrantyExpire = vehicleObu.getWarrantyStartAt().substring(0,10)
                                + "至" + vehicleObu.getWarrantyEndAt().substring(0,10);
            }
        }

        return warrantyExpire;
    }

    public void sendVerifyCode(String phone, Long uid) {

        SendCodeRequestDTO dto = new SendCodeRequestDTO();
        dto.setSendPhone(phone);
        dto.setUid(uid);
        dto.setIp(RequestHelper.getUserIp());

        baseApplicationFeign.sendCode(dto);
    }

    public void verifyCode(String phone, String code) {

        if (appConfig.getEnv().equals("dev")) {
            return;
        }

        VerifyCodeRequestDTO dto = new VerifyCodeRequestDTO();
        dto.setSendPhone(phone);
        dto.setCode(code);

        baseApplicationFeign.verifyCode(dto).checkError();
    }

    @DSTransactional
    public void bind(ExternalBindDTO dto, Long uid) {

        // 查询用户的thirdCode
        UsersEntityVO usersEntityVO = userApplicationFeign.getById(uid).getDataWithCheckError();
        if (usersEntityVO == null) {
            ToolsHelper.throwException("用户不存在");
        }
        Integer thirdType = usersEntityVO.getThirdType();
        String thirdCode = usersEntityVO.getThirdCode();

        //todo
        String source = ExternalThirdTypeEnum.getSourceByType(thirdType);
        if (StringUtils.isEmpty(source)) {
            ToolsHelper.throwException("用户渠道类型错误");
        }

        // 查询商城订单
        ProductOrderEntity productOrderEntity = productOrderService.getExternalCanBindOrder(source, dto.getPhone(), dto.getPlateNo());
        if (productOrderEntity == null) {
            ToolsHelper.throwException("该手机号/车牌号未获取到ETC办理记录");
        }
        if (! NumberUtil.isPositive(productOrderEntity.getUid())) {
            ToolsHelper.throwException("请先到ETC助手小程序绑单激活！");
        }

        GetListByApplyOrderSnDTO orderSnDTO = new GetListByApplyOrderSnDTO();
        orderSnDTO.setApplyOrderSnList(Arrays.asList(productOrderEntity.getApplyOrderSn()));
        List<UsersCardsEntityVO> usersCards = userApplicationFeign.getListByApplyOrderSn(orderSnDTO).getDataWithCheckError();
        if (usersCards != null && ! usersCards.isEmpty()) {
            if (usersCards.get(0).getIsDel() == 1) {
                ToolsHelper.throwException("该ETC设备已注销，不能申请绑定");
            }
        }

        // 验证码校验
        verifyCode(dto.getPhone(), dto.getVerifyCode());

        // 查询是否已存在绑定记录
        ExternalBindEntity existBindEntity = externalBindService.getValidByOrderSn(thirdType, productOrderEntity.getProductOrderSn());
        if (existBindEntity != null) {
            if (existBindEntity.getExternalUnionId().equals(thirdCode)) {
                // 相同的用户已经绑定过了
                return;
            }

            //删除旧绑定记录
            externalBindService.setDeleted(existBindEntity.getBindSn());
        }

        // 新增绑定记录
        ExternalBindEntity bindEntity = new ExternalBindEntity();
        bindEntity.setBindUid(productOrderEntity.getUid());
        bindEntity.setBindOrderSn(productOrderEntity.getProductOrderSn());
        bindEntity.setBindSn(generateBindSn());
        bindEntity.setStatus(ExternalBindStatusEnum.NORMAL.getCode());
        bindEntity.setExternalUnionId(thirdCode);
        bindEntity.setThirdType(thirdType);

        externalBindService.create(bindEntity);
    }

    public String generateBindSn() {

        String sn = baseBusiness.generateSn("externalBindSn");
        sn = sn + RandomUtil.randomNumbers(32);
        sn = DigestUtil.sha256Hex(sn);
        sn = DigestUtil.md5Hex(sn);

        return sn;
    }

    public void updateReservedPhone(ExternalUpdateReservedPhoneDTO dto, Long uid) {

        UsersEntityVO usersEntity = userApplicationFeign.getById(uid).getDataWithCheckError();

        ExternalBindEntity entity = externalBindService.getByBindSn(dto.getBindSn());
        if (entity == null) {
            ToolsHelper.throwException("绑定记录不存在");
        }

        if (! usersEntity.getThirdType().equals(entity.getThirdType())) {
            ToolsHelper.throwException("用户和绑定记录渠道类型不一致");
        }

        ProductOrderEntity bindOrderEntity = productOrderService.getByProductOrderSn(entity.getBindOrderSn());
        if (bindOrderEntity != null) {
            ToolsHelper.throwException("商城订单不存在");
        }

        verifyCode(dto.getPhone(), dto.getVerifyCode());

        UpdateReservedPhoneByPlateNoDTO updateDTO = new UpdateReservedPhoneByPlateNoDTO();
        updateDTO.setPlateNo(bindOrderEntity.getPlateNo());
        updateDTO.setPlateColor(bindOrderEntity.getPlateColor());
        updateDTO.setApplyOrderSn(bindOrderEntity.getApplyOrderSn());
        updateDTO.setUid(uid);
        updateDTO.setPhone(dto.getPhone());
        // 其他字段不更新
        usersPhoneFeign.updateReservedPhoneByPlateNo(updateDTO);
    }

    public void cancel(String bindOrderSn) {

        externalBindService.cancelByBindOrderSn(bindOrderSn);
    }

}
