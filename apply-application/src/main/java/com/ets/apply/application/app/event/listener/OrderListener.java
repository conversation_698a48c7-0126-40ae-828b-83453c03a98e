package com.ets.apply.application.app.event.listener;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ets.apply.application.app.business.ProductOrderBusiness;
import com.ets.apply.application.app.event.OrderActivatedEvent;
import com.ets.apply.application.app.event.OrderCreateEvent;
import com.ets.apply.application.app.event.OrderPaidEvent;
import com.ets.apply.application.app.factory.task.TaskFactory;
import com.ets.apply.application.common.bo.task.OrderActivatedBO;
import com.ets.apply.application.common.consts.productOrder.ProductOrderStatusEnum;
import com.ets.apply.application.common.consts.productOrder.SourceEnum;
import com.ets.apply.application.common.consts.taskRecord.TaskRecordReferTypeEnum;
import com.ets.apply.application.common.dto.task.TaskRecordDTO;
import com.ets.apply.application.infra.entity.OrderOrderEntity;
import com.ets.apply.application.infra.entity.ProductOrderEntity;
import com.ets.apply.application.infra.entity.ProductPackageEntity;
import com.ets.apply.application.infra.service.OrderOrderService;
import com.ets.apply.application.infra.service.ProductOrderService;
import com.ets.apply.application.infra.service.ProductPackageService;
import com.ets.common.ToolsHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
public class OrderListener {
    @Autowired
    private OrderOrderService orderService;
    @Autowired
    private ProductPackageService productPackageService;
    @Autowired
    private ProductOrderBusiness productOrderBusiness;
    @Autowired
    private ProductOrderService productOrderService;


    @EventListener
    @Async
    public void activated(OrderActivatedEvent event){

        System.out.println("订单激活：" + ToolsHelper.getDateTime() + " " + JSON.toJSON(event.getSource()));

        JSONObject params = (JSONObject) event.getSource();

        OrderActivatedBO contentBO = JSON.parseObject(params.toJSONString(), OrderActivatedBO.class);
        // 激活事件处理
        OrderOrderEntity order = orderService.getByOrderSn(contentBO.getOrderSn());
        if(!StringUtils.isEmpty(order.getPackageSn())){
            ProductPackageEntity productPackage = productPackageService.getBySn(order.getPackageSn());
            //腾讯出行激活通知
            if(productPackage.getSource().equals("2396")){
                //塞队列进行发货操作
                TaskRecordDTO taskRecordDTO = new TaskRecordDTO();
                taskRecordDTO.setReferSn(order.getOrderSn());
                taskRecordDTO.setReferType(TaskRecordReferTypeEnum.TASK_ACTIVATED_NOTIFY_WECAR.getType());
                taskRecordDTO.setNextExecTime(LocalDateTime.now());
                taskRecordDTO.setNotifyContent(JSON.toJSONString(contentBO));
                TaskFactory.create(TaskRecordReferTypeEnum.TASK_ACTIVATED_NOTIFY_WECAR.getType()).addAndPush(taskRecordDTO);
            }
            //吉利通知状态，通用版本 903101
            if(productPackage.getSource().equals(SourceEnum.GEELY.getCode())){
                //组装通知参数
                ProductOrderEntity productOrderEntity = productOrderService.findByApplyOrderSn(contentBO.getOrderSn());
                productOrderBusiness.thirdOrderStatusNotify(productOrderEntity,ProductOrderStatusEnum.ACTIVATED.getCode());
            }
        }

    }

    @EventListener
    @Async
    public void orderCreate(OrderCreateEvent event) throws InterruptedException {

        Thread.sleep(3000);

        System.out.println("订单创建：" + ToolsHelper.getDateTime() + " " + JSON.toJSON(event.getSource()));
    }

    @EventListener
    @Async
    public void orderPaid(OrderPaidEvent event) {

        System.out.println("订单支付：" + ToolsHelper.getDateTime() + " " + JSON.toJSON(event.getSource()));
    }

}
