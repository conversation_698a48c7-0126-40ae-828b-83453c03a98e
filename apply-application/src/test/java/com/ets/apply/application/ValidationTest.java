package com.ets.apply.application;

import com.ets.apply.application.app.business.amap.AmapBusiness;
import com.ets.apply.application.common.bo.amap.CreateOrderBO;
import com.ets.common.BizException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@SpringBootTest(classes = ApplyApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringRunner.class)
@ActiveProfiles(value = "test")
public class ValidationTest {

    @Autowired
    private AmapBusiness amapBusiness;

    @Test
    public void testReceiverValidation() {
        // 测试正确的收货人信息
        CreateOrderBO.Receiver validReceiver = new CreateOrderBO.Receiver();
        validReceiver.setName("张三");
        validReceiver.setMobile("***********");
        validReceiver.setProvince("广东省");
        validReceiver.setCity("深圳市");
        validReceiver.setDistrict("南山区");
        validReceiver.setAddress("科技园南区");

        try {
            amapBusiness.checkReceiver(validReceiver);
            System.out.println("✅ 正确的收货人信息验证通过");
        } catch (BizException e) {
            System.out.println("❌ 验证失败: " + e.getMessage());
        }

        // 测试错误的收货人信息 - 姓名包含特殊字符
        CreateOrderBO.Receiver invalidReceiver = new CreateOrderBO.Receiver();
        invalidReceiver.setName("&*^%%**&2432"); // 包含特殊字符，不符合中文格式
        invalidReceiver.setMobile("***********");
        invalidReceiver.setProvince("广东省");
        invalidReceiver.setCity("深圳市");
        invalidReceiver.setDistrict("南山区");
        invalidReceiver.setAddress("科技园南区");

        try {
            amapBusiness.checkReceiver(invalidReceiver);
            System.out.println("❌ 应该验证失败但却通过了");
        } catch (BizException e) {
            System.out.println("✅ 预期的验证失败: " + e.getMessage());
        }

        // 测试空字段验证
        CreateOrderBO.Receiver emptyReceiver = new CreateOrderBO.Receiver();
        // 不设置任何字段，触发 @NotBlank 验证

        try {
            amapBusiness.checkReceiver(emptyReceiver);
            System.out.println("❌ 应该验证失败但却通过了");
        } catch (BizException e) {
            System.out.println("✅ 空字段验证失败: " + e.getMessage());
        }

        // 测试手机号格式错误
        CreateOrderBO.Receiver invalidPhoneReceiver = new CreateOrderBO.Receiver();
        invalidPhoneReceiver.setName("李四");
        invalidPhoneReceiver.setMobile("123"); // 错误的手机号格式
        invalidPhoneReceiver.setProvince("广东省");
        invalidPhoneReceiver.setCity("深圳市");
        invalidPhoneReceiver.setDistrict("南山区");
        invalidPhoneReceiver.setAddress("科技园南区");

        try {
            amapBusiness.checkReceiver(invalidPhoneReceiver);
            System.out.println("❌ 应该验证失败但却通过了");
        } catch (BizException e) {
            System.out.println("✅ 手机号格式验证失败: " + e.getMessage());
        }
    }

    @Test
    public void testAddressValidation() {
        // 测试地址包含特殊字符
        CreateOrderBO.Receiver invalidAddressReceiver = new CreateOrderBO.Receiver();
        invalidAddressReceiver.setName("王五");
        invalidAddressReceiver.setMobile("***********");
        invalidAddressReceiver.setProvince("广东省");
        invalidAddressReceiver.setCity("深圳市");
        invalidAddressReceiver.setDistrict("南山区");
        invalidAddressReceiver.setAddress("科技园@#$%"); // 包含特殊字符

        try {
            amapBusiness.checkReceiver(invalidAddressReceiver);
            System.out.println("❌ 应该验证失败但却通过了");
        } catch (BizException e) {
            System.out.println("✅ 地址特殊字符验证失败: " + e.getMessage());
        }
    }
}
