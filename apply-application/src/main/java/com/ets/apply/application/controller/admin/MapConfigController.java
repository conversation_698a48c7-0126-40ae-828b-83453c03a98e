package com.ets.apply.application.controller.admin;

import com.ets.apply.application.app.business.map.MapConfigBusiness;
import com.ets.apply.application.common.dto.map.MapConfigDTO;
import com.ets.apply.application.common.vo.map.MapConfigVO;
import com.ets.apply.application.controller.BaseController;
import com.ets.common.JsonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import jakarta.validation.Valid;
import java.util.HashMap;

@Controller
@RequestMapping("/admin/mapConfig")
public class MapConfigController extends BaseController {
    @Autowired
    private MapConfigBusiness mapConfigBusiness;

    /**
     * 模块列表查询
     * @return
     */
    @RequestMapping("/get-info-by-key")
    @ResponseBody
    public JsonResult<MapConfigVO> getInfoByKey(@RequestBody(required = false) @Valid MapConfigDTO dto) {
        return JsonResult.ok(mapConfigBusiness.getInfoByKey(dto));
    }

    @RequestMapping("/getSelectOptions")
    @ResponseBody
    public JsonResult<HashMap<String, Object>> getSelectOptions() {

        return JsonResult.ok(mapConfigBusiness.getSelectOptions());
    }



}
