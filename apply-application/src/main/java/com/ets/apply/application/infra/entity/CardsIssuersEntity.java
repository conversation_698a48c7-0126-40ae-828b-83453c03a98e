package com.ets.apply.application.infra.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 发卡方
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("etc_cards_issuers")
public class CardsIssuersEntity extends BaseEntity<CardsIssuersEntity> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 名称
     */
    private String name;

    /**
     * 描述
     */
    private String description;

    /**
     * 行政省份
     */
    private String province;

    /**
     * 城市代码
     */
    private String code;

    /**
     * 图标URL
     */
    private String iconUrl;

    /**
     * 排序（越大越前）
     */
    private Integer sort;

    /**
     * 客服电话
     */
    private String phone;

    /**
     * 是否可用
     */
    private Integer status;

    /**
     * 功能开关
     */
    private String switches;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;


}
