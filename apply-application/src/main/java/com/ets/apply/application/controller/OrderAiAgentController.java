package com.ets.apply.application.controller;

import com.ets.apply.application.app.business.AiAgentBusiness;
import com.ets.apply.application.common.dto.aiagent.AiAgentOrderInfoDTO;
import com.ets.apply.application.common.dto.aiagent.AiAgentPlateNoValidateDTO;
import com.ets.apply.application.common.vo.aiagent.AiAgentOrderInfoVO;
import com.ets.apply.application.common.vo.aiagent.AiAgentPlateNoValidateVO;
import com.ets.common.JsonResult;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 智能体订单信息
 */
@RestController
@RequestMapping("/order-ai-agent")
public class OrderAiAgentController extends BaseController {

    @Autowired
    private AiAgentBusiness aiAgentBusiness;

    /**
     * 获取订单信息
     * @param orderInfoDTO 车牌+uid
     * @return 订单信息
     */
    @PostMapping("/get-order-info")
    public JsonResult<AiAgentOrderInfoVO> getOrderInfo(@RequestBody @Valid AiAgentOrderInfoDTO orderInfoDTO) {
        return JsonResult.ok(aiAgentBusiness.getOrderInfo(orderInfoDTO));
    }

    @PostMapping("/get-plate-no-validate")
    public JsonResult<AiAgentPlateNoValidateVO> getPlateNoValidate(@RequestBody @Valid AiAgentPlateNoValidateDTO plateNoValidateDTO) {
        return JsonResult.ok(aiAgentBusiness.getPlateNoValidate(plateNoValidateDTO));
    }

}
