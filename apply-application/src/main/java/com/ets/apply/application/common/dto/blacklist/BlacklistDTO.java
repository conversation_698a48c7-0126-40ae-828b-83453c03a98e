package com.ets.apply.application.common.dto.blacklist;


import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

@Data
public class BlacklistDTO {
    @NotBlank(message = "用户id不能为空")
    private Integer uid;
    private String reason;
    @NotBlank(message = "拉黑来源不能为空")
    private String source;
    @NotBlank(message = "车牌号码不能为空")
    private String plateNo;
    @NotNull(message = "车牌颜色不能为空")
    private Integer plateColor;
    private String cardNo;
    private Integer addType = 0;
    private Integer delay = 0;
}