package com.ets.apply.application.common.config.external;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Data
@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "external.chuanqi")
public class ChuanQiConfig {

    private String key;

    private String shippedNotifyUrl;

    private String afterSaleFinishNotifyUrl;

    private Boolean checkSign = true;
}
