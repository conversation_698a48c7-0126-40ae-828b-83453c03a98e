package com.ets.apply.application.controller.frontend;

import com.ets.apply.application.app.business.location.LocationBusiness;
import com.ets.apply.application.common.vo.location.GetLocationVO;
import com.ets.common.JsonResult;
import com.ets.common.RequestHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/frontend/location")
public class LocationController {

    @Autowired
    private LocationBusiness locationBusiness;
    @RequestMapping("/get-location-list")
    JsonResult<GetLocationVO> getLocationList() {
        String ip = RequestHelper.getHttpServletRequest().getRemoteAddr();
        return JsonResult.ok(locationBusiness.getLocation(ip));
    }
}
