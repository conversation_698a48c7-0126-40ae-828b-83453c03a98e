package com.ets.apply.application.controller.frontend;

import com.ets.apply.application.app.business.OrderBusiness;
import com.ets.apply.application.app.business.OrderCenterBusiness;
import com.ets.apply.application.common.dto.request.applyOrder.GetBanksByCityDTO;
import com.ets.apply.application.common.dto.request.applyOrder.GetBanksByPackageSnDTO;
import com.ets.apply.application.common.utils.UserUtil;
import com.ets.apply.application.common.vo.apply.BanksByPackageSnVo;
import com.ets.apply.application.common.vo.orderCenter.OrderCenterApplyOrderListVO;
import com.ets.apply.application.controller.BaseController;
import com.ets.common.JsonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/frontend/apply")
public class ApplyController extends BaseController {

    @Autowired
    OrderCenterBusiness orderCenterBusiness;

    @Autowired
    private OrderBusiness orderBusiness;

    @RequestMapping("/applyOrderList")
    public JsonResult<List<OrderCenterApplyOrderListVO>> applyOrderList() {
        return JsonResult.ok(orderCenterBusiness.getApplyOrderList());
    }

    @RequestMapping("/getBanksByPackageSn")
    public JsonResult<BanksByPackageSnVo> getBanksByPackageSn(@RequestBody @Valid GetBanksByPackageSnDTO dto) {

        return JsonResult.ok(orderBusiness.getBanksByPacakgeSn(dto, UserUtil.getUid()));
    }

    @PostMapping("/checkUserHasSatisfyOrder")
    public JsonResult<?> checkUserHasSatisfyOrder() {
        return JsonResult.ok(orderBusiness.checkUserHasSatisfyOrder());
    }


    @RequestMapping("/getBanksByCity")
    public JsonResult<BanksByPackageSnVo> getBanksByCity(@RequestBody @Valid GetBanksByCityDTO dto) {
        return JsonResult.ok(orderBusiness.getBanksByCity(dto,UserUtil.getUid()));
    }
}
