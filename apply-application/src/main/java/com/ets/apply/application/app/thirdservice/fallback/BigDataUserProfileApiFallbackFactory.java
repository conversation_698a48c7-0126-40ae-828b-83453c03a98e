package com.ets.apply.application.app.thirdservice.fallback;

import com.ets.apply.application.app.thirdservice.feign.BigDataUserProfileApiFeign;
import com.ets.apply.application.app.thirdservice.request.bigData.*;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

@Component
public class BigDataUserProfileApiFallbackFactory implements FallbackFactory<BigDataUserProfileApiFeign> {

    @Override
    public BigDataUserProfileApiFeign create(Throwable throwable) {
        return new BigDataUserProfileApiFeign() {

            @Override
            public String atomcmpsTagList(AtomcmpsTagListDTO dto) {
                return "请求big-data 服务失败: " + throwable.getCause().getMessage();
            }

        };
    }
}
