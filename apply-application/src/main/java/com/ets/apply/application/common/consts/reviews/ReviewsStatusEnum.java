package com.ets.apply.application.common.consts.reviews;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum ReviewsStatusEnum {

    STATUS_NORMAL(1, "正常"),
    STATUS_CANCEL(2, "取消"),
    STATUS_PAUSE(3, "暂停");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;

    static {
        map = Arrays.stream(ReviewsStatusEnum.values()).collect(Collectors.toMap(ReviewsStatusEnum::getValue, ReviewsStatusEnum::getDesc));
    }
}
