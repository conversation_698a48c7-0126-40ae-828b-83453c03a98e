package com.ets.apply.application.controller.admin;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.apply.application.app.business.ApplyPageBusiness;
import com.ets.apply.application.common.dto.request.applyPage.*;
import com.ets.apply.application.common.vo.applyPage.ApplyPageVO;
import com.ets.apply.application.controller.BaseController;
import com.ets.common.JsonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-02
 */
@Controller
@RestController
@RequestMapping("/admin/applyPage")
public class ApplyPageController extends BaseController {

    @Autowired
    private ApplyPageBusiness applyPageBusiness;

    /**
     * 获取列表信息
     *
     * @param pageListDTO
     * @return
     */
    @RequestMapping("/getList")
    JsonResult<IPage<ApplyPageVO>> getList(@RequestBody @Valid ApplyPageListDTO pageListDTO) {

        return JsonResult.ok(applyPageBusiness.getList(pageListDTO));
    }

    // 新建
    @RequestMapping("/create")
    JsonResult<?> create(@RequestBody @Valid ApplyPageCreateDTO creatDTO) {
        applyPageBusiness.create(creatDTO);
        return JsonResult.ok();
    }


    @RequestMapping("/modify")
    JsonResult<?> modify(@RequestBody @Valid ApplyPageModifyDTO applyPageModifyDTO) {
        applyPageBusiness.modify(applyPageModifyDTO);
        return JsonResult.ok();
    }

    // 上下架
    @RequestMapping("/setStatus")
    JsonResult<?> setStatus(@RequestBody @Valid ApplyPageSetStatusDTO setStatusDTO){
        applyPageBusiness.setStatus(setStatusDTO);
        return JsonResult.ok();
    }

    // 获取详情
    @RequestMapping("/getDetail")
    JsonResult<?> getDetail(@RequestParam String pageSn){
        return JsonResult.ok(applyPageBusiness.getDetail(pageSn));
    }


    @RequestMapping("/setPageType")
    JsonResult<?> setApplyType(@RequestBody @Valid ApplyPageSetPageTypeDTO setPageTypeDTO){
        applyPageBusiness.setPageType(setPageTypeDTO);
        return JsonResult.ok();
    }


}

