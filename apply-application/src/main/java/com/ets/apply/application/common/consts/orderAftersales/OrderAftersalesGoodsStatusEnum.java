package com.ets.apply.application.common.consts.orderAftersales;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum OrderAftersalesGoodsStatusEnum {
    /*
     * 商品售后单的状态：
        0：没有商品售后单或者非仅取消/退货退款的已完成的商品售后单【不展示售后状态】
        1：存在未完成的商品售后单【展示售后中】
        2：仅取消，退货退款的已完成的商品售后单（换货那些类型已完成的不需要展示售后状态）【售后完成】
     */
    AFTERSALES_GOODS_STATUS_INIT(0, "初始状态"),

    AFTERSALES_GOODS_STATUS_PROCESSING(1, "售后中"),

    AFTERSALES_GOODS_STATUS_FINISH(2, "售后完成");

    private final Integer status;
    private final String desc;
}
