package com.ets.apply.application.controller.frontend;

import com.ets.apply.application.app.business.OrderBusiness;
import com.ets.apply.application.common.dto.order.CheckHasOrderAndGiftRightsDTO;
import com.ets.apply.application.common.dto.order.GetUnpaidOrderDetailDTO;
import com.ets.apply.application.common.dto.request.order.ManualSnOrderListDTO;
import com.ets.apply.application.common.dto.request.order.OrderFindDTO;
import com.ets.apply.application.common.dto.order.HideInfoDTO;
import com.ets.apply.application.common.dto.order.OrderBeforeActivateImgDTO;
import com.ets.apply.application.common.dto.request.order.UploadRiskCheckInfoDTO;
import com.ets.apply.application.common.utils.UserUtil;
import com.ets.apply.application.common.vo.order.*;
import com.ets.apply.application.infra.entity.OrderOrderEntity;
import com.ets.apply.application.infra.service.OrderOrderService;
import com.ets.common.JsonResult;
import com.ets.common.annotation.RateLimiterAnnotation;
import com.ets.common.annotation.CosSignAnnotation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/frontend/order")
public class OrderFrontendController {

    @Autowired
    private OrderBusiness orderBusiness;

    @Autowired
    private OrderOrderService orderService;

    @RequestMapping("/check-has-order-and-gift-rights")
    public JsonResult<OrderGiftRightVO> checkHasOrderAndGiftRights(@RequestBody @Valid CheckHasOrderAndGiftRightsDTO checkHasOrderAndGiftRightsDTO){
        return JsonResult.ok(orderBusiness.getUserEffectOrderOrActivatedOrderAndCheckGiftRight(UserUtil.getUid(),checkHasOrderAndGiftRightsDTO.getGiftRights()));
    }

    @CosSignAnnotation
    @PostMapping("/get-order-before-activate-img")
    public JsonResult<OrderBeforeActivateImgVO> getOrderBeforeActivateImg(@RequestBody OrderBeforeActivateImgDTO orderBeforeActivateImgDTO) {
        return JsonResult.ok(orderBusiness.getOrderBeforeActivateImg(orderBeforeActivateImgDTO));
    }

    /**
     * @deprecated 已弃用,新的前端接口不使用该接口
     */
    @PostMapping("/get-latest-unpaid-order")
    @RateLimiterAnnotation(qps = 2000,msg = "请求繁忙超过限制，请稍后再试")
    public JsonResult<Object> getLatestUnpaidOrder(){

        return JsonResult.ok(orderBusiness.getUnpaidOrderInfo(UserUtil.getUid()));
    }

    @PostMapping("/get-unpaid-order-detail")
    @RateLimiterAnnotation(qps = 2000,msg = "请求繁忙超过限制，请稍后再试")
    public JsonResult<Object> getUnpaidOrderDetail(@RequestBody @Valid GetUnpaidOrderDetailDTO unpaidOrderDTO){

        return JsonResult.ok(orderBusiness.getUnpaidOrderDetail(unpaidOrderDTO,UserUtil.getUid()));
    }

    /**
     *  @deprecated 已弃用,个人中心不使用该接口
     *  <a href="https://yapi.etczs.net/project/404/interface/api/30045">获取未完成订单列表</a>
     */
    @PostMapping("/get-unfinished-order-list")
    @RateLimiterAnnotation(qps = 2000,msg = "请求繁忙超过限制，请稍后再试")
    public JsonResult<List<GetUnfinishedOrderListVO>> getUnfinishedOrderList(){
        return JsonResult.ok(orderBusiness.getUnfinishedOrderList(UserUtil.getUid()));
    }

    @RequestMapping("/getOrderInfoByOrderSn")
    public JsonResult<OrderInfoVo> getOrderInfoByOrderSn(@RequestBody @Valid OrderFindDTO dto) {
        OrderOrderEntity order = orderService.findByOrderSn(dto.getOrderSn());
        if(order == null){
            return JsonResult.ok(null);
        }
        return JsonResult.ok(orderBusiness.getOrderInfoByOrderSn(order));
    }

    @PostMapping("/hide-info")
    public JsonResult<HideInfoVO> hideInfo(@RequestBody @Valid HideInfoDTO hideSendInfoDTO) {
        return JsonResult.ok(orderBusiness.hideInfo(hideSendInfoDTO));
    }

    @PostMapping("/getManualSnOrderInfo")
    public JsonResult<GetManualSnOrderInfoVO> getManualSnOrderInfo(@RequestBody @Valid ManualSnOrderListDTO dto) {
        return JsonResult.ok(orderBusiness.getManualSnOrderInfo(UserUtil.getUid(),dto.getManualSn()));
    }

    @PostMapping("/upload-risk-check-info")
    public JsonResult<?> uploadRiskCheckInfo(@RequestBody @Valid UploadRiskCheckInfoDTO dto) {
        orderBusiness.uploadRiskCheckInfo(UserUtil.getUid(),dto);
        return JsonResult.ok();
    }

}
