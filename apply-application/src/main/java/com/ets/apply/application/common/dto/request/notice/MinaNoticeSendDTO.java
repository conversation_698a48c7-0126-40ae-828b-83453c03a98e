package com.ets.apply.application.common.dto.request.notice;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Map;

@Data
public class MinaNoticeSendDTO {
    private Long uid;

    @JsonProperty(value = "page_path")
    private String path;
    //'uid', 'tpl_id', 'keywords'

    private Map<String,Object> keywords;

    @JsonProperty(value = "tpl_id")
    private String templateId;

    @JsonProperty(value = "wechat_mina")
    private Integer wechatMina;

    @JsonProperty(value = "msg_type")
    private Integer MsgType;


}
