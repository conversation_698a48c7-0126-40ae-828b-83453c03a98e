package com.ets.apply.application.controller;

import com.ets.apply.application.app.business.ProductOrderBusiness;
import com.ets.apply.application.common.dto.productOrder.NotifyActivateDTO;
import com.ets.apply.application.common.dto.productOrder.ProductOrderByOrderSnDTO;
import com.ets.apply.application.infra.entity.ProductOrderEntity;
import com.ets.apply.application.infra.service.ProductOrderService;
import com.ets.common.JsonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

@Controller
@RefreshScope
@RestController
@RequestMapping("/product-order")
public class ProductOrderController {

    @Autowired
    private ProductOrderBusiness productOrderBusiness;
    @Autowired
    private ProductOrderService productOrderService;
    @RequestMapping("/notify-activate")
    public JsonResult<?> notifyActivate(@RequestBody @Valid NotifyActivateDTO notifyActivateDTO) {
        productOrderBusiness.activateNotify(notifyActivateDTO);
        return JsonResult.ok();
    }

    /*
     *  通过订单号获取到电商订单详情
     */
    @RequestMapping("/getInfoByApplyOrderSn")
    public JsonResult<ProductOrderEntity> getInfoByApplyOrderSn(@RequestBody @Valid ProductOrderByOrderSnDTO dto) {
        return JsonResult.ok(productOrderService.findByOrderSnAndSource(dto));
    }
}
