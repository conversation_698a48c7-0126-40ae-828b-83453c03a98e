package com.ets.apply.application.common.dto.request.productPackage;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.util.List;

@Data
public class ProductPackageListDTO {

    @Min(1)
    private Integer pageNum = 1;

    @Min(1)
    private Integer pageSize = 20;

    @Length(max = 50,message = "最大长度为50")
    private String source;

    private List<String> sourceList;

    @Length(max = 50,message = "最大长度为50")
    private String packageSn;

    private String packageName;

    // 是否展示 不是布尔值，遵循数据库设计
    @Min(value = 0,message = "最小值为0")
    @Max(value = 1,message = "最大值为1")
    private Integer isShowAdd;

    @Min(value = 1,message = "最小值为1")
    @Max(value = 2,message = "最大值为2")
    private Integer status;


}
