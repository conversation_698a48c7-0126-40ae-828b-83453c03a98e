package com.ets.apply.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ets.apply.application.infra.entity.ProductPackageTemplateLogEntity;
import com.ets.apply.application.infra.mapper.ProductPackageTemplateLogMapper;
import com.ets.common.ToolsHelper;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 模板操作日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Service
@DS("db-apply")
public class ProductPackageTemplateLogService extends BaseService<ProductPackageTemplateLogMapper, ProductPackageTemplateLogEntity> {
    @Value("${spring.profiles.active}")
    private String ACTIVE;

    @Resource(name = "redisPermanentTemplate")
    private StringRedisTemplate redisPermanentTemplate;
    /*
     * 新增日志
     */
    public void addLog(String operateUser,Integer templateId,String operateType,String msg, String preOperate,String afterOperate){
        ProductPackageTemplateLogEntity packageTemplateLog = new ProductPackageTemplateLogEntity();
        packageTemplateLog.setLogSn(ToolsHelper.genNum(redisPermanentTemplate, "task_record", ACTIVE, 8));
        packageTemplateLog.setTemplateId(templateId);
        packageTemplateLog.setType(operateType);
        packageTemplateLog.setPreOperate(preOperate);
        packageTemplateLog.setAfterOperate(afterOperate);
        packageTemplateLog.setOperator(operateUser);
        packageTemplateLog.setMsg(msg);
        this.create(packageTemplateLog);
    }
}
