package com.ets.apply.application.common.vo.productPackagePopup;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class ProductPackagePopupListVO {
    private String popupSn;
    private String name;
    private Integer popupType;

    private String popupTypeStr;

    private Integer size;

    private Integer style;

    private String styleStr;

    private Integer popupStatus;

    private Integer firstChoose;
    private String remark;
    private String operator;

    private String url;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;


}
