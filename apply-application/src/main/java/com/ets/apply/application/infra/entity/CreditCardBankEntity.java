package com.ets.apply.application.infra.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <p>
 * 信用卡银行
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-27
 */
@Getter
@Setter
@TableName("etc_credit_card_bank")
public class CreditCardBankEntity extends BaseEntity<CreditCardBankEntity> {

    private static final long serialVersionUID = 1L;

    private Integer id;

    /**
     * 银行id
     */
    private Integer bankId;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 标签
     */
    private String tagList;

    /**
     * 主权益
     */
    private String welfare;

    /**
     * 权益说明
     */
    private String welfareDetail;

    /**
     * 附加信息
     */
    private String extraInfo;

    /**
     * 上下架状态[0-下架 1-上架]
     */
    private Integer bankStatus;

    /**
     * 银行类型[1-申办 2-活动]
     */
    private Integer bankType;

    private String operator;
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;
}
