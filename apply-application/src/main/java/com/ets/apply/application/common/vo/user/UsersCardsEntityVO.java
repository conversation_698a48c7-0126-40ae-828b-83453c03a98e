package com.ets.apply.application.common.vo.user;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class UsersCardsEntityVO {

    private Integer id;

    private Long uid;

    /**
     * 卡种（对应 etc_cards.id）
     */
    private Integer cardId;

    /**
     * 车牌号（冗余）
     */
    private String plateNo;

    /**
     * ETC通行卡号
     */
    private String cardNo;

    /**
     * OBU设备电子标签号
     */
    private String obuDeviceSn;

    /**
     * 申请订单流水号
     */
    private String applyOrderSn;

    /**
     * 激活时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime activatedAt;

    /**
     * 首次激活时间，二次激活不更新
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime firstActivatedAt;

    private Integer isDel;

    /**
     * 卡片状态 0:正常 2:挂失 3:补办中 4:作废 8:注销中 9:已注销
     */
    private Integer status;

    /**
     * 通行扣费支付渠道 1、我方自己发起的扣费 2、米大师发起的扣费 3、招行代扣
     */
    private Integer tollPayChannel;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     *  设备商类型（埃特斯1、金溢2、聚力3、万集4、成谷5）
     */
    private Integer manufacturer;

    /**
     * 设备类型[0-普通 1-可充电设备]
     */
    private Integer deviceType;

    /**
     * 车牌颜色：0、蓝色 1、黄色 2、黑色 3、白色 4、渐变绿色 5、黄绿双拼色 6、蓝白渐变色 7、临时牌照 9、未确定 11、绿色 12、红色
     */
    private Integer plateColor;

    /**
     * 通行债务
     */
    private BigDecimal tollDebtAmount;

    /**
     * 结算周期：1日结2周结3月结
     */
    private Integer settleCycle;

    /**
     * 是否已修改签约[0-未改签 1-已改签]
     */
    private Integer hasChangeSign;

    /**
     * 是否为前装
     */
    private Integer isFront;

    private Integer obuStopStatus;

}
