package com.ets.apply.application.controller.third;

import com.alibaba.fastjson.JSONObject;
import com.ets.apply.application.app.business.external.ChuanQiBusiness;
import com.ets.apply.application.app.business.external.ExternalOrderBusiness;
import com.ets.apply.application.common.consts.productOrder.ReferValueEnum;
import com.ets.apply.application.common.dto.external.ExternalOrderAfterSaleCreateDTO;
import com.ets.apply.application.common.dto.external.ExternalOrderCancelDTO;
import com.ets.apply.application.common.dto.external.ExternalOrderCreateDTO;
import com.ets.apply.application.common.bo.external.ExternalSubmitSendBackBO;
import com.ets.apply.application.common.dto.external.ExternalSubmitSendBackDTO;
import com.ets.apply.application.common.dto.external.chuanqi.ChuanQiAfterSaleCreateCheckDTO;
import com.ets.apply.application.common.dto.external.chuanqi.ChuanQiAfterSaleCreateDTO;
import com.ets.apply.application.common.dto.external.chuanqi.ChuanQiCancelDTO;
import com.ets.apply.application.common.dto.external.chuanqi.ChuanQiCreateDTO;
import com.ets.common.BizException;
import com.ets.common.JsonResult;
import com.ets.common.ToolsHelper;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping("/third/chuanqi")
@RestController
@Slf4j
public class ThirdChuanQiController {

    @Autowired
    private ExternalOrderBusiness externalOrderBusiness;

    @Autowired
    private ChuanQiBusiness chuanQiBusiness;

    @PostMapping("/create")
    public JsonResult<Object> create(@RequestBody @Valid ChuanQiCreateDTO createDTO, HttpServletRequest request) {

        chuanQiBusiness.checkSign(request);

        ExternalOrderCreateDTO dto = new ExternalOrderCreateDTO();

        dto.setThirdOrderSn(createDTO.getOrderNo());
        dto.setProductId(createDTO.getGoods().getThreeGoodNo());
        dto.setReceiver(createDTO.getReceiveInfo().getReceiver());
        dto.setPhone(createDTO.getReceiveInfo().getReceiverMobile());
        dto.setAddress(createDTO.getReceiveInfo().getAddress());

        return JsonResult.ok(externalOrderBusiness.create(dto, ReferValueEnum.PARTNER_VALUE_CHUANQI.getCompanyId()));
    }

    @PostMapping("/cancel")
    public JsonResult<Object> cancel(@RequestBody @Valid ChuanQiCancelDTO cancelDTO, HttpServletRequest request) {

        chuanQiBusiness.checkSign(request);

        ExternalOrderCancelDTO dto = new ExternalOrderCancelDTO();

        dto.setThirdOrderNo(cancelDTO.getOrderNo());
        dto.setReason("外部接口调用发起取消");

        externalOrderBusiness.cancel(dto, ReferValueEnum.PARTNER_VALUE_CHUANQI.getCompanyId());

        return JsonResult.ok();
    }

    @PostMapping("/afterSaleCreate")
    public JsonResult<Object> afterSaleCreate(@RequestBody @Valid ChuanQiAfterSaleCreateDTO afterSaleCreateDTO, HttpServletRequest request) {

        chuanQiBusiness.checkSign(request);

        if (afterSaleCreateDTO.getOperateType().equals("returnApprove")) {
            if (StringUtils.isEmpty(afterSaleCreateDTO.getReturnReason())) {
                ToolsHelper.throwException("售后原因不能为空");
            }

            // 只创建售后单
            ExternalOrderAfterSaleCreateDTO dto = new ExternalOrderAfterSaleCreateDTO();
            dto.setServiceType(1);
            dto.setThirdOrderSn(afterSaleCreateDTO.getOrderNo());
            dto.setProblemDescription(afterSaleCreateDTO.getReturnReason());

            JSONObject result = externalOrderBusiness.afterSaleCreate(dto, ReferValueEnum.PARTNER_VALUE_CHUANQI.getCompanyId());

        } else if (afterSaleCreateDTO.getOperateType().equals("returning")) {

            if (afterSaleCreateDTO.getReturnInfo() == null || StringUtils.isEmpty(afterSaleCreateDTO.getReturnInfo().getLogisticsNo())) {
                ToolsHelper.throwException("寄回的快递单号不能为空");
            }

            // 提交快递信息
            ExternalSubmitSendBackDTO dto = new ExternalSubmitSendBackDTO();
            dto.setThirdOrderSn(afterSaleCreateDTO.getOrderNo());
            dto.setSendbackExpressNumber(afterSaleCreateDTO.getReturnInfo().getLogisticsNo());
            dto.setSendbackExpressCompany(afterSaleCreateDTO.getReturnInfo().getLogisticsCompany());
            dto.setServiceType(1);

            externalOrderBusiness.submitSendBack(dto, ReferValueEnum.PARTNER_VALUE_CHUANQI.getCompanyId());
        } else {
            ToolsHelper.throwException("不支持该操作类型");
        }

        return JsonResult.ok();
    }

    @PostMapping("/afterSaleCreateCheck")
    public JsonResult<Object> afterSaleCreateCheck(@RequestBody @Valid ChuanQiAfterSaleCreateCheckDTO checkDTO, HttpServletRequest request) {

        chuanQiBusiness.checkSign(request);

        ExternalOrderAfterSaleCreateDTO dto = new ExternalOrderAfterSaleCreateDTO();
        dto.setServiceType(1);
        dto.setThirdOrderSn(checkDTO.getOrderNo());

        int result = 0;
        try {
            externalOrderBusiness.afterSaleCreateCheck(dto, ReferValueEnum.PARTNER_VALUE_CHUANQI.getCompanyId());
            result = 1;
        } catch (BizException e) {
            // 返回 0
            log.info("传祺售后单创建检查失败：" + e.getMessage());
        }

        return JsonResult.ok(result);
    }
}
