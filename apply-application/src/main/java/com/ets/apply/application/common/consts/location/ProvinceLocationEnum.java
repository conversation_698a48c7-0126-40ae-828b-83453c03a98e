package com.ets.apply.application.common.consts.location;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ProvinceLocationEnum {
    //[{"id":"110000","value":"北京市"},
    // {"id":"120000","value":"天津市"},
    // {"id":"130000","value":"河北省"},
    // {"id":"140000","value":"山西省"},
    // {"id":"150000","value":"内蒙古自治区"},
    // {"id":"210000","value":"辽宁省"},
    // {"id":"220000","value":"吉林省"},
    // {"id":"230000","value":"黑龙江省"},
    // {"id":"310000","value":"上海市"},
    // {"id":"320000","value":"江苏省"},{"id":"330000","value":"浙江省"},{"id":"340000","value":"安徽省"},{"id":"350000","value":"福建省"},{"id":"360000","value":"江西省"},{"id":"370000","value":"山东省"},{"id":"410000","value":"河南省"},{"id":"420000","value":"湖北省"},{"id":"430000","value":"湖南省"},{"id":"440000","value":"广东省"},{"id":"450000","value":"广西壮族自治区"},{"id":"460000","value":"海南省"},{"id":"500000","value":"重庆市"},{"id":"510000","value":"四川省"},{"id":"520000","value":"贵州省"},{"id":"530000","value":"云南省"},{"id":"540000","value":"西藏自治区"},{"id":"610000","value":"陕西省"},{"id":"620000","value":"甘肃省"},{"id":"630000","value":"青海省"},{"id":"640000","value":"宁夏回族自治区"},{"id":"650000","value":"新疆维吾尔自治区"},{"id":"710000","value":"台湾省"},{"id":"810000","value":"香港特别行政区"},{"id":"820000","value":"澳门特别行政区"}]
    // 直辖市
    BEIJING("110000", "北京市"),
    TIANJIN("120000", "天津市"),
    SHANGHAI("310000", "上海市"),
    CHONGQING("500000", "重庆市"),

    // 省份
    HEBEI("130000", "河北省"),
    SHANXI("140000", "山西省"),
    LIAONING("210000", "辽宁省"),
    JILIN("220000", "吉林省"),
    HEILONGJIANG("230000", "黑龙江省"),
    JIANGSU("320000", "江苏省"),
    ZHEJIANG("330000", "浙江省"),
    ANHUI("340000", "安徽省"),
    FUJIAN("350000", "福建省"),
    JIANGXI("360000", "江西省"),
    SHANDONG("370000", "山东省"),
    HENAN("410000", "河南省"),
    HUBEI("420000", "湖北省"),
    HUNAN("430000", "湖南省"),
    GUANGDONG("440000", "广东省"),
    HAINAN("460000", "海南省"),
    SICHUAN("510000", "四川省"),
    GUIZHOU("520000", "贵州省"),
    YUNNAN("530000", "云南省"),
    SHAANXI("610000", "陕西省"),
    GANSU("620000", "甘肃省"),
    QINGHAI("630000", "青海省"),
    TAIWAN("710000", "台湾省"),

    // 自治区
    INNER_MONGOLIA("150000", "内蒙古自治区"),
    GUANGXI("450000", "广西壮族自治区"),
    TIBET("540000", "西藏自治区"),
    NINGXIA("640000", "宁夏回族自治区"),
    XINJIANG("650000", "新疆维吾尔自治区"),

    // 特别行政区
    HONG_KONG("810000", "香港特别行政区"),
    MACAO("820000", "澳门特别行政区")

    ;

    private String id;
    private String value;

    /**
     * 根据省份代码获取省份枚举
     * @param id 省份代码
     * @return 省份枚举，如果未找到返回null
     */
    public static ProvinceLocationEnum getById(String id) {
        if (id == null) {
            return null;
        }
        for (ProvinceLocationEnum province : values()) {
            if (province.getId().equals(id)) {
                return province;
            }
        }
        return null;
    }

    /**
     * 根据省份名称获取省份枚举
     * @param value 省份名称
     * @return 省份枚举，如果未找到返回null
     */
    public static ProvinceLocationEnum getByValue(String value) {
        if (value == null) {
            return null;
        }
        for (ProvinceLocationEnum province : values()) {
            if (province.getValue().equals(value)) {
                return province;
            }
        }
        return null;
    }

    /**
     * 检查是否为直辖市
     * @return true如果是直辖市
     */
    public boolean isMunicipality() {
        return this == BEIJING || this == TIANJIN || this == SHANGHAI || this == CHONGQING;
    }

    /**
     * 检查是否为自治区
     * @return true如果是自治区
     */
    public boolean isAutonomousRegion() {
        return this == INNER_MONGOLIA || this == GUANGXI || this == TIBET ||
               this == NINGXIA || this == XINJIANG;
    }

    /**
     * 检查是否为特别行政区
     * @return true如果是特别行政区
     */
    public boolean isSpecialAdministrativeRegion() {
        return this == HONG_KONG || this == MACAO;
    }
}
