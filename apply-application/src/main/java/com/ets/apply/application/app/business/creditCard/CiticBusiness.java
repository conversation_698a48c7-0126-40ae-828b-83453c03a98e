package com.ets.apply.application.app.business.creditCard;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ets.apply.application.app.disposer.CreditCardActivateNotifyDisposer;
import com.ets.apply.application.app.thirdservice.feign.CallCiticFeign;
import com.ets.apply.application.common.bo.creditCard.CreditCardLogBO;
import com.ets.apply.application.common.config.creditBank.CiticCreditBankConfig;
import com.ets.apply.application.common.consts.activityCreditCardBankUser.*;
import com.ets.apply.application.common.consts.activityCreditCardOperateLog.CreditCardOperateLogTypeEnum;
import com.ets.apply.application.common.consts.activityCreditCardOriginalLog.CreditCardOriginalDataType;
import com.ets.apply.application.common.consts.activityCreditCardUsersInfo.ActivityCreditCardUserInfoWhichBankEnum;
import com.ets.apply.application.common.consts.citic.CiticApplyStatusConst;
import com.ets.apply.application.common.consts.citic.CiticFirstCardEnum;
import com.ets.apply.application.common.consts.citic.CiticNewCustFlagEnum;
import com.ets.apply.application.common.dto.bank.CreditCardActivateNotifyDTO;
import com.ets.apply.application.common.dto.request.citic.CiticOrderReceiveDTO;
import com.ets.apply.application.common.dto.request.citic.CiticOrderReceiveDataDTO;
import com.ets.apply.application.common.dto.request.citic.CiticOrderStatusQueryDTO;
import com.ets.apply.application.infra.entity.ActivityCreditCardBankUsersEntity;
import com.ets.apply.application.infra.entity.ActivityCreditCardUsersInfoEntity;
import com.ets.apply.application.infra.service.ActivityCreditCardBankUsersService;
import com.ets.apply.application.infra.service.ActivityCreditCardLogService;
import com.ets.apply.application.infra.service.ActivityCreditCardUsersInfoService;
import com.ets.common.ToolsHelper;
import com.ets.starter.queue.QueueDefault;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Component
@Slf4j
public class CiticBusiness extends CreditCardBaseBusiness {

    @Autowired
    private ActivityCreditCardUsersInfoService activityCreditCardUsersInfoService;

    @Autowired
    private ActivityCreditCardBankUsersService activityCreditCardBankUsersService;

    @Autowired
    private ActivityCreditCardLogService creditCardLogService;

    @Autowired
    private CallCiticFeign callCiticFeign;



    @Autowired
    private CreditCardUsersInfoBusiness creditCardUsersInfoBusiness;

    @Autowired
    private CiticCreditBankConfig citicCreditBankConfig;

    @Autowired
    private QueueDefault queueDefault;
    /**
     * 中信银行状态回调
     *
     * @param dto
     */
    public void receiveOrder(CiticOrderReceiveDTO dto) {
        log.info("中信银行回传数据：" + dto.getData());
        String unescapeData = StringEscapeUtils.unescapeJava(dto.getData());
        CiticOrderReceiveDataDTO receiveDto = JSONObject.parseObject(unescapeData, CiticOrderReceiveDataDTO.class);
        if (StringUtils.isEmpty(receiveDto.getPaId())) {
            ToolsHelper.throwException("未识别到合作订单号，请确认");
        }
        // 记录原始数据
        this.saveOriginalDataLog(
                receiveDto.getPaId(),
                unescapeData,
                ActivityCreditCardUserInfoWhichBankEnum.CREDIT_BANK_CITIC.getCode(),
                CreditCardOriginalDataType.TYPE_RECEIVE.getCode(),
                Thread.currentThread().getStackTrace()[1].getMethodName()
        );

        this.bankStatusHandle(receiveDto);
    }

    /**
     * 银行状态处理
     *
     * @param receiveDto
     */
    public void bankStatusHandle(CiticOrderReceiveDataDTO receiveDto) {
        // 获取信用卡申请记录
        ActivityCreditCardUsersInfoEntity activityCreditCardUsersInfoEntity = activityCreditCardUsersInfoService.getOneByColumn(receiveDto.getPaId(), ActivityCreditCardUsersInfoEntity::getOrderSn);
        if (activityCreditCardUsersInfoEntity == null) {
            ToolsHelper.throwException("记录不存在");
        }

        ActivityCreditCardBankUsersEntity activityCreditCardBankUsersEntity = activityCreditCardBankUsersService.getOneByColumn(receiveDto.getPaId(), ActivityCreditCardBankUsersEntity::getApplyNumber);
        if (activityCreditCardBankUsersEntity == null) {
            ToolsHelper.throwException("记录不存在");
        }
        if (ObjectUtil.isNull(receiveDto.getApplyTime())) {
            receiveDto.setApplyTime(receiveDto.getPushTime());
        }
        String failReason = citicCreditBankConfig.getFailReason();

        // 记录银行回调结果
        switch (receiveDto.getApplyStatus()) {
            case CiticApplyStatusConst.APPLY_SUBMIT:
                this.applySubmit(activityCreditCardUsersInfoEntity, activityCreditCardBankUsersEntity, receiveDto);
                break;
            case CiticApplyStatusConst.AUDIT_REFUSE:
                this.auditRefuse(activityCreditCardUsersInfoEntity, activityCreditCardBankUsersEntity, receiveDto, failReason);
                break;
            case CiticApplyStatusConst.AUDIT_CHECK_REFUSE:
                this.checkRefuse(activityCreditCardUsersInfoEntity, activityCreditCardBankUsersEntity, receiveDto, failReason);
                break;
            case CiticApplyStatusConst.AUDIT_PASS:
                this.auditPass(activityCreditCardUsersInfoEntity, activityCreditCardBankUsersEntity, receiveDto);
                break;
            case CiticApplyStatusConst.AUDIT_CHECK_SUCCESS:

                if (StringUtils.isEmpty(receiveDto.getNewCustFlag())) {
                    ToolsHelper.throwException("首签节点需要接收新户标志");
                }
                this.auditCheckSuccess(activityCreditCardUsersInfoEntity, activityCreditCardBankUsersEntity, receiveDto);
                break;
            case CiticApplyStatusConst.ACTIVATE_SUCCESS:
                this.activate(activityCreditCardUsersInfoEntity, activityCreditCardBankUsersEntity, receiveDto);
                break;
            case CiticApplyStatusConst.PURCHASE_SUCCESS:
                this.purchaseSuccess(activityCreditCardUsersInfoEntity,activityCreditCardBankUsersEntity, receiveDto);
                break;
            case CiticApplyStatusConst.UNKNOWN_STATUS:
            case CiticApplyStatusConst.ACTIVATE_FAILED:
                break;
            default:
                ToolsHelper.throwException("未定义的状态，请业务核对");
        }
    }

    /**
     * 订单提交待审核
     *
     * @param activityCreditCardUsersInfoEntity
     */
    private void applySubmit(ActivityCreditCardUsersInfoEntity activityCreditCardUsersInfoEntity, ActivityCreditCardBankUsersEntity activityCreditCardBankUsersEntity, CiticOrderReceiveDataDTO receiveDto) {
        // 重复通知状态未变化不进行修改
        if (ActivityCreditCardBankUserApplyCompleteEnum.APPLY_COMPLETED.getCode() == activityCreditCardBankUsersEntity.getApplyCompleted()) {
            log.info(activityCreditCardUsersInfoEntity.getOrderSn() + "申请单已经进件，不能重复进件");
            return;
        }
        creditCardUsersInfoBusiness.applySubmit(activityCreditCardUsersInfoEntity);

        LambdaUpdateWrapper<ActivityCreditCardBankUsersEntity> activityCreditCardBankUsersEntityLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        activityCreditCardBankUsersEntityLambdaUpdateWrapper
                .eq(ActivityCreditCardBankUsersEntity::getId, activityCreditCardBankUsersEntity.getId())
                .set(ActivityCreditCardBankUsersEntity::getSubmitTime,receiveDto.getApplyTime())
                .set(ActivityCreditCardBankUsersEntity::getApplyCompleted, ActivityCreditCardBankUserApplyCompleteEnum.APPLY_COMPLETED.getCode());
        activityCreditCardBankUsersService.updateByWrapper(activityCreditCardBankUsersEntityLambdaUpdateWrapper);

        // 记录申请日志
        String content = "申请完成，申请时间：" + receiveDto.getApplyTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        CreditCardLogBO logBO = new CreditCardLogBO();
        logBO.setOrderSn(activityCreditCardUsersInfoEntity.getOrderSn());
        logBO.setOperateType(CreditCardOperateLogTypeEnum.TYPE_APPLY.getValue());
        logBO.setOperateContent(content);
        creditCardLogService.addLog(logBO);

        /**
         * 进件通知
         */
        this.creditCardSubmitNotify(activityCreditCardUsersInfoEntity.getUid(),
                activityCreditCardUsersInfoEntity.getWhichBank(),
                activityCreditCardUsersInfoEntity.getClassify(),
                activityCreditCardBankUsersEntity.getApplyNumber()
        );
    }

    /**
     * 审核不通过
     *
     * @param activityCreditCardUsersInfoEntity
     * @param activityCreditCardBankUsersEntity
     * @param receiveDto
     * @param failReason
     */
    private void auditRefuse(ActivityCreditCardUsersInfoEntity activityCreditCardUsersInfoEntity,
                             ActivityCreditCardBankUsersEntity activityCreditCardBankUsersEntity,
                             CiticOrderReceiveDataDTO receiveDto,
                             String failReason
    ) {
        // 重复通知状态未变化不进行修改
        // 已审核通过不能处理失败
        if (Arrays.asList(
                ActivityCreditCardBankUserStatusEnum.STATUS_FAIL_AUDIT.getCode(),
                ActivityCreditCardBankUserStatusEnum.STATUS_PASS_AUDIT.getCode()
        ).contains(activityCreditCardBankUsersEntity.getStatus())) {
            return;
        }
        // 用户申办单进行驳回
        creditCardUsersInfoBusiness.auditRefuse(activityCreditCardUsersInfoEntity, failReason);

        // 银行信息记录表更新
        LambdaUpdateWrapper<ActivityCreditCardBankUsersEntity> bankUsersEntityLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        bankUsersEntityLambdaUpdateWrapper.eq(ActivityCreditCardBankUsersEntity::getId, activityCreditCardBankUsersEntity.getId())
                .set(ActivityCreditCardBankUsersEntity::getStatus, ActivityCreditCardBankUserStatusEnum.STATUS_FAIL_AUDIT.getCode())
                .set(ActivityCreditCardBankUsersEntity::getApplicationStatus, ActivityCreditCardBankUserApplyStatusEnum.APPLY_SUCCESS.getCode())
                .set(ActivityCreditCardBankUsersEntity::getApplyCompleted, ActivityCreditCardBankUserApplyCompleteEnum.APPLY_COMPLETED.getCode())
                .set(ObjectUtil.isNotNull(receiveDto.getApplyTime()), ActivityCreditCardBankUsersEntity::getAuditTime, receiveDto.getApplyTime())
        ;
        if (ObjectUtil.isNotNull(receiveDto.getFirstCard())) {
            boolean isNewUser = CiticFirstCardEnum.FIRST_CARD.getCode().equals(receiveDto.getFirstCard());
            bankUsersEntityLambdaUpdateWrapper.set(ActivityCreditCardBankUsersEntity::getAuditNewUser, isNewUser ? BankUserAuditNewUserEnum.NEW_USER_YES.getCode() : BankUserAuditNewUserEnum.NEW_USER_NOT.getCode());
        }
        // 进件时间补充
        if(ObjectUtil.isNull(activityCreditCardBankUsersEntity.getSubmitTime())){
            bankUsersEntityLambdaUpdateWrapper.set(ActivityCreditCardBankUsersEntity::getSubmitTime, receiveDto.getApplyTime());
        }
        activityCreditCardBankUsersService.updateByWrapper(bankUsersEntityLambdaUpdateWrapper);

        // 记录日志审核不通过
        String content = "审核拒绝，拒绝原因：" + failReason + "，拒绝时间：" + receiveDto.getApplyTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        CreditCardLogBO logBO = new CreditCardLogBO();
        logBO.setOrderSn(activityCreditCardUsersInfoEntity.getOrderSn());
        logBO.setOperateType(CreditCardOperateLogTypeEnum.TYPE_AUDIT.getValue());
        logBO.setOperateContent(content);
        creditCardLogService.addLog(logBO);

        // 审核失败通知
         this.creditCardAuditFailedNotify(activityCreditCardUsersInfoEntity.getUid(),
                activityCreditCardUsersInfoEntity.getWhichBank(),
                activityCreditCardUsersInfoEntity.getClassify(),
                activityCreditCardBankUsersEntity.getApplyNumber());
    }

    /**
     * 面签拒绝，银行记录为审核通过，我方保持审核驳回
     */
    private void checkRefuse(ActivityCreditCardUsersInfoEntity activityCreditCardUsersInfoEntity,
                             ActivityCreditCardBankUsersEntity activityCreditCardBankUsersEntity,
                             CiticOrderReceiveDataDTO receiveDto,
                             String failReason)
    {
        // 用户申办单进行驳回
        creditCardUsersInfoBusiness.refuse(activityCreditCardUsersInfoEntity, failReason);

        // 银行信息记录表更新
        // 审核通过是是否新用户是未知的
        LambdaUpdateWrapper<ActivityCreditCardBankUsersEntity> bankUsersEntityLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        bankUsersEntityLambdaUpdateWrapper.eq(ActivityCreditCardBankUsersEntity::getId, activityCreditCardBankUsersEntity.getId())
                .set(ActivityCreditCardBankUsersEntity::getStatus, ActivityCreditCardBankUserStatusEnum.STATUS_PASS_AUDIT.getCode())
                .set(ActivityCreditCardBankUsersEntity::getApplyCompleted, ActivityCreditCardBankUserApplyCompleteEnum.APPLY_COMPLETED.getCode())
        ;

        activityCreditCardBankUsersService.updateByWrapper(bankUsersEntityLambdaUpdateWrapper);

        // 记录日志审核不通过
        String content = "面签拒绝，拒绝原因：" + failReason + "，拒绝时间：" + receiveDto.getApplyTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        CreditCardLogBO logBO = new CreditCardLogBO();
        logBO.setOrderSn(activityCreditCardUsersInfoEntity.getOrderSn());
        logBO.setOperateType(CreditCardOperateLogTypeEnum.TYPE_AUDIT.getValue());
        logBO.setOperateContent(content);
        creditCardLogService.addLog(logBO);

    }

    /**
     * 首刷
     *
     * @param activityCreditCardBankUsersEntity
     * @param receiveDto
     */
    private void purchaseSuccess(
            ActivityCreditCardUsersInfoEntity activityCreditCardUsersInfoEntity,
            ActivityCreditCardBankUsersEntity activityCreditCardBankUsersEntity,
            CiticOrderReceiveDataDTO receiveDto) {
        // 重复通知状态未变化不进行修改
        if (ActivityCreditCardBankUserIsFirstUseEnum.FIRST_USE_YES.getCode() == activityCreditCardBankUsersEntity.getIsFirstUse()) {
            log.info(activityCreditCardUsersInfoEntity.getOrderSn() + "申请单已经首刷，不重复处理");
            return;
        }
        // 银行信息记录表更新
        LambdaUpdateWrapper<ActivityCreditCardBankUsersEntity> bankUsersEntityLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        bankUsersEntityLambdaUpdateWrapper.eq(ActivityCreditCardBankUsersEntity::getId, activityCreditCardBankUsersEntity.getId())
                .set(ActivityCreditCardBankUsersEntity::getIsFirstUse, ActivityCreditCardBankUserIsFirstUseEnum.FIRST_USE_YES.getCode())
                .set(ActivityCreditCardBankUsersEntity::getIsFirstUserBackMoney, ActivityCreditCardBankUserIsNewUserBackMoneyEnum.NEW_USE_BACK_MONEY_YES.getCode())
                .set(StringUtils.isNotEmpty(receiveDto.getFirstPurchaseTime()), ActivityCreditCardBankUsersEntity::getFirstUseDate, receiveDto.getFirstPurchaseTime())
                .set(StringUtils.isNotEmpty(receiveDto.getFirstPurchaseTime()), ActivityCreditCardBankUsersEntity::getFirstUseTime, receiveDto.getFirstPurchaseTime())
        ;

        if (ObjectUtil.isNotNull(receiveDto.getNewCustFlag())) {
            boolean isNewUser = CiticNewCustFlagEnum.NEW_CUST_FLAG.getCode().equals(receiveDto.getNewCustFlag());
            bankUsersEntityLambdaUpdateWrapper.set(ActivityCreditCardBankUsersEntity::getIsNewUser, isNewUser ?
                    ActivityCreditCardBankUserIsNewUserEnum.NEW_USER_YES.getCode() : ActivityCreditCardBankUserIsNewUserEnum.NEW_USER_NOT.getCode());
        }
        activityCreditCardBankUsersService.updateByWrapper(bankUsersEntityLambdaUpdateWrapper);

        // 首刷日志
        String content = "用户首刷信用卡，首刷时间：" + receiveDto.getFirstPurchaseTime();
        CreditCardLogBO logBO = new CreditCardLogBO();
        logBO.setOrderSn(activityCreditCardBankUsersEntity.getApplyNumber());
        logBO.setOperateType(CreditCardOperateLogTypeEnum.TYPE_FIRST_USE.getValue());
        logBO.setOperateContent(content);
        creditCardLogService.addLog(logBO);
        this.creditCardFirstUsedNotify(activityCreditCardUsersInfoEntity.getUid(),
                activityCreditCardUsersInfoEntity.getWhichBank(),
                activityCreditCardUsersInfoEntity.getClassify(),
                activityCreditCardBankUsersEntity.getApplyNumber());
    }


    /**
     * 审核通过
     *
     * @param activityCreditCardUsersInfoEntity
     */
    private void auditPass(ActivityCreditCardUsersInfoEntity activityCreditCardUsersInfoEntity,
                           ActivityCreditCardBankUsersEntity activityCreditCardBankUsersEntity,
                           CiticOrderReceiveDataDTO receiveDto) {
        // 银行信息记录表更新
        // 审核通过是是否新用户是未知的
        LambdaUpdateWrapper<ActivityCreditCardBankUsersEntity> bankUsersEntityLambdaUpdateWrapper = new LambdaUpdateWrapper<>();

        // 有传用户状态时，在用户是二卡时进行驳回处理
        if (ObjectUtil.isNotNull(receiveDto.getFirstCard())) {
            boolean isNewUser = CiticFirstCardEnum.FIRST_CARD.getCode().equals(receiveDto.getFirstCard());
            // 申请单标记新户审核通过，旧户驳回
            if (!isNewUser) {
                creditCardUsersInfoBusiness.auditRefuse(activityCreditCardUsersInfoEntity, citicCreditBankConfig.getOldUserFailReason());
            }else{
                creditCardUsersInfoBusiness.auditPass(activityCreditCardUsersInfoEntity, citicCreditBankConfig.getInnerVersion());
            }
            bankUsersEntityLambdaUpdateWrapper.set(ActivityCreditCardBankUsersEntity::getAuditNewUser, isNewUser ? BankUserAuditNewUserEnum.NEW_USER_YES.getCode() : BankUserAuditNewUserEnum.NEW_USER_NOT.getCode());
        } else {
            creditCardUsersInfoBusiness.auditPass(activityCreditCardUsersInfoEntity, citicCreditBankConfig.getInnerVersion());
        }

        // 进件时间补充
        if(ObjectUtil.isNull(activityCreditCardBankUsersEntity.getSubmitTime())){
            bankUsersEntityLambdaUpdateWrapper.set(ActivityCreditCardBankUsersEntity::getSubmitTime, receiveDto.getApplyTime());
        }
        bankUsersEntityLambdaUpdateWrapper.eq(ActivityCreditCardBankUsersEntity::getId, activityCreditCardBankUsersEntity.getId())
                .set(ActivityCreditCardBankUsersEntity::getStatus, ActivityCreditCardBankUserStatusEnum.STATUS_PASS_AUDIT.getCode())
                .set(ActivityCreditCardBankUsersEntity::getApplicationStatus, ActivityCreditCardBankUserApplyStatusEnum.APPLY_SUCCESS.getCode())
                .set(ActivityCreditCardBankUsersEntity::getApplyCompleted, ActivityCreditCardBankUserApplyCompleteEnum.APPLY_COMPLETED.getCode())
                .set(ObjectUtil.isNotNull(receiveDto.getApplyTime()), ActivityCreditCardBankUsersEntity::getAuditTime, receiveDto.getApplyTime())
        ;
        activityCreditCardBankUsersService.updateByWrapper(bankUsersEntityLambdaUpdateWrapper);

        // 通知申办审核通过
        this.creditCardAuditNotify(   activityCreditCardUsersInfoEntity.getUid(),
                activityCreditCardUsersInfoEntity.getReferType(),
                activityCreditCardUsersInfoEntity.getClassify(),
                activityCreditCardBankUsersEntity.getApplyNumber());

        // 审核通过日志
        String content = "审核通过，通过时间：" + receiveDto.getApplyTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        CreditCardLogBO logBO = new CreditCardLogBO();
        logBO.setOrderSn(activityCreditCardUsersInfoEntity.getOrderSn());
        logBO.setOperateType(CreditCardOperateLogTypeEnum.TYPE_AUDIT.getValue());
        logBO.setOperateContent(content);
        creditCardLogService.addLog(logBO);
    }





    /**
     * 中信银行查询申办进度
     *
     * @param dto
     * @return
     */
    public String statusQuery(CiticOrderStatusQueryDTO dto) {
        Map<String, Object> map = new HashMap<>();
        map.put("paIds", dto.getPaIds());
        return callCiticFeign.statusQuery(map);
    }


    /**
     * 标记中信激活
     */
    private void activate(ActivityCreditCardUsersInfoEntity activityCreditCardUsersInfoEntity,
                          ActivityCreditCardBankUsersEntity activityCreditCardBankUsersEntity,
                          CiticOrderReceiveDataDTO receiveDto) {
        // 重复通知状态未变化不进行修改
        // 允许银行在激活状态未到最高返佣状态前重复通知
        if (activityCreditCardBankUsersEntity.getActivated() == ActivityCreditCardBankUserActivatedEnum.STATUS_ACTIVATED_YES.getCode()) {
            log.info(activityCreditCardBankUsersEntity.getApplyNumber() + "重复通知激活不进行处理");
            return;
        }
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");


        LambdaUpdateWrapper<ActivityCreditCardBankUsersEntity> bankUsersEntityLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        bankUsersEntityLambdaUpdateWrapper.eq(ActivityCreditCardBankUsersEntity::getId, activityCreditCardBankUsersEntity.getId())
                .set(ActivityCreditCardBankUsersEntity::getActivated, ActivityCreditCardBankUserActivatedEnum.STATUS_ACTIVATED_YES.getCode())
                .set(ActivityCreditCardBankUsersEntity::getStatus, ActivityCreditCardBankUserStatusEnum.STATUS_PASS_AUDIT.getCode())
                .set(ActivityCreditCardBankUsersEntity::getApplyCompleted, ActivityCreditCardBankUserApplyCompleteEnum.APPLY_COMPLETED.getCode())
                .set(ActivityCreditCardBankUsersEntity::getActivateTime,receiveDto.getApplyTime())
                ;
        // 更新银行卡表信息

        // 补充信息记录：如果进件时间、审核时间为空，更新进件时间、审核时间
        if(ObjectUtil.isNull(activityCreditCardBankUsersEntity.getSubmitTime())){
            bankUsersEntityLambdaUpdateWrapper.set(ActivityCreditCardBankUsersEntity::getSubmitTime,receiveDto.getApplyTime());
        }

        if(ObjectUtil.isNull(activityCreditCardBankUsersEntity.getAuditTime())){
            bankUsersEntityLambdaUpdateWrapper.set(ActivityCreditCardBankUsersEntity::getAuditTime,receiveDto.getApplyTime());
        }
        activityCreditCardBankUsersService.updateByWrapper(bankUsersEntityLambdaUpdateWrapper);

        // 激活日志
        String content = "用户激活信用卡，激活时间：" + receiveDto.getApplyTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        CreditCardLogBO logBO = new CreditCardLogBO();
        logBO.setOrderSn(activityCreditCardUsersInfoEntity.getOrderSn());
        logBO.setOperateType(CreditCardOperateLogTypeEnum.TYPE_ACTIVATE.getValue());
        logBO.setOperateContent(content);
        creditCardLogService.addLog(logBO);

        CreditCardActivateNotifyDTO activateNotifyDTO  =  new CreditCardActivateNotifyDTO();
        activateNotifyDTO.setApplyNumber( activityCreditCardBankUsersEntity.getApplyNumber());
        activateNotifyDTO.setUid(activityCreditCardUsersInfoEntity.getUid());
        activateNotifyDTO.setReferType(activityCreditCardUsersInfoEntity.getReferType());
        activateNotifyDTO.setClassify(activityCreditCardUsersInfoEntity.getClassify());
        // 延迟通知60  s
        queueDefault.push(new CreditCardActivateNotifyDisposer(activateNotifyDTO),3);

    }


    /**
     * 首签通过
     *
     * @param activityCreditCardUsersInfoEntity
     */
    private void auditCheckSuccess(ActivityCreditCardUsersInfoEntity activityCreditCardUsersInfoEntity,
                                   ActivityCreditCardBankUsersEntity activityCreditCardBankUsersEntity,
                                   CiticOrderReceiveDataDTO receiveDto)
    {
        // 银行信息记录表更新
        LambdaUpdateWrapper<ActivityCreditCardBankUsersEntity> bankUsersEntityLambdaUpdateWrapper = new LambdaUpdateWrapper<>();

        // 申请单状态标记
        if (ObjectUtil.isNotNull(receiveDto.getNewCustFlag())) {
            boolean isNewUser = CiticNewCustFlagEnum.NEW_CUST_FLAG.getCode().equals(receiveDto.getNewCustFlag());
            // 申请单标记新户审核通过，旧户驳回
            if (isNewUser) {
                creditCardUsersInfoBusiness.auditPass(activityCreditCardUsersInfoEntity,citicCreditBankConfig.getInnerVersion());
            } else {
                creditCardUsersInfoBusiness.auditRefuse(activityCreditCardUsersInfoEntity, citicCreditBankConfig.getOldUserFailReason());
            }

            bankUsersEntityLambdaUpdateWrapper.set(ActivityCreditCardBankUsersEntity::getIsNewUser,
                    isNewUser ? ActivityCreditCardBankUserIsNewUserEnum.NEW_USER_YES.getCode() : ActivityCreditCardBankUserIsNewUserEnum.NEW_USER_NOT.getCode());

        }
        // 银行卡表信息记录更新
        bankUsersEntityLambdaUpdateWrapper.eq(ActivityCreditCardBankUsersEntity::getId, activityCreditCardBankUsersEntity.getId())
                .set(ActivityCreditCardBankUsersEntity::getStatus, ActivityCreditCardBankUserStatusEnum.STATUS_PASS_AUDIT.getCode())
                .set(ActivityCreditCardBankUsersEntity::getApplyCompleted, ActivityCreditCardBankUserApplyCompleteEnum.APPLY_COMPLETED.getCode())
        ;
        activityCreditCardBankUsersService.updateByWrapper(bankUsersEntityLambdaUpdateWrapper);

        // 首签日志
        String content = "完成首签，首签时间：" + receiveDto.getApplyTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        CreditCardLogBO logBO = new CreditCardLogBO();
        logBO.setOrderSn(activityCreditCardUsersInfoEntity.getOrderSn());
        logBO.setOperateType(CreditCardOperateLogTypeEnum.TYPE_AUDIT.getValue());
        logBO.setOperateContent(content);
        creditCardLogService.addLog(logBO);

        CreditCardActivateNotifyDTO activateNotifyDTO  =  new CreditCardActivateNotifyDTO();
        activateNotifyDTO.setApplyNumber( activityCreditCardBankUsersEntity.getApplyNumber());
        activateNotifyDTO.setUid(activityCreditCardUsersInfoEntity.getUid());
        activateNotifyDTO.setReferType(activityCreditCardUsersInfoEntity.getReferType());
        activateNotifyDTO.setClassify(activityCreditCardUsersInfoEntity.getClassify());
        // 延迟通知30  s
        queueDefault.push(new CreditCardActivateNotifyDisposer(activateNotifyDTO),4);
    }

    public void activateCompensation(String startTime,String endTime){
        // 查找最近1小时激活且新户的中信银行，对因首签、激活并发未流转到激活新户的信用卡数据进行重新通知处理
        List<ActivityCreditCardBankUsersEntity> activateList = activityCreditCardBankUsersService.getCiticActivateList(startTime,endTime);
        if (!CollectionUtils.isEmpty(activateList)) {
            log.info("activateCompensation：中信银行待激活总条数：" + activateList.size());
            activateList.forEach(bankInfo -> {
                try {
                    ActivityCreditCardUsersInfoEntity activityCreditCardUsersInfoEntity = activityCreditCardUsersInfoService.getOneByColumn(bankInfo.getApplyNumber(),ActivityCreditCardUsersInfoEntity::getOrderSn);
                    if (ObjectUtil.isNull(activityCreditCardUsersInfoEntity)) {
                        ToolsHelper.throwException("银行用户数据缺失，单号" + bankInfo.getApplyNumber());
                    }

                    if(!bankInfo.getIsNewUser().equals(ActivityCreditCardBankUserIsNewUserEnum.NEW_USER_YES.getCode())){
                        ToolsHelper.throwException("当前用户不是银行新户，不进行处理，单号" + bankInfo.getApplyNumber());
                    }

                    if(!bankInfo.getActivated().equals(ActivityCreditCardBankUserActivatedEnum.STATUS_ACTIVATED_YES.getCode())){
                        ToolsHelper.throwException("当前用户未激活，不进行处理，单号" + bankInfo.getApplyNumber());
                    }
                    CiticOrderReceiveDataDTO receiveDto = new CiticOrderReceiveDataDTO();
                    receiveDto.setActiveTime(bankInfo.getActivateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    receiveDto.setFirstCard(String.valueOf(bankInfo.getIsNewUser()));
                    receiveDto.setApplyStatus(CiticApplyStatusConst.ACTIVATE_SUCCESS);
                    this.activate(activityCreditCardUsersInfoEntity,bankInfo,receiveDto);
                } catch (Exception e) {
                    log.info("中信银行激活补偿处理失败，流水单号：" + bankInfo.getApplyNumber() + e.getMessage());
                }
            });
        }

    }
}
