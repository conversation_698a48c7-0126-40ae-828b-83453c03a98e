package com.ets.apply.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ets.apply.application.infra.entity.ProductPackageResourceEntity;
import com.ets.apply.application.infra.mapper.ProductPackageResourceMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Service
@DS("db-apply")
public class ProductPackageResourceService extends BaseService<ProductPackageResourceMapper,
        ProductPackageResourceEntity> {

}
