package com.ets.apply.application.common.vo.productPackage;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.math.BigDecimal;


/**
 * <p>
 * 商品套餐配置表
 * </p>
 * 商品套餐列表
 * 与ProductPackageGetListVo相比，本vo 才是实际的product_package表的字段,ProductPackageGetListVo是一个tmp的vo
 * @since 2021-11-19
 */
@Data
public class ProductPackageGetInfoByPackageSnsVo {


    /**
     * 商品套餐流水号
     */
    private String packageSn;


    /**
     * 商品套餐名称
     */
    private String packageName;


    /**
     * 设备费（元）
     */
    private BigDecimal packageFee;

    /**
     * 库存
     */
    private Integer packageStock;


    /**
     * 渠道码
     */
    private String source;

    /*
     * 状态 1-有效 2-无效
     */
    private Integer status;

    private Integer bizType;


    private JSONObject packageInfo;
    private JSONObject pageConfig;



}
