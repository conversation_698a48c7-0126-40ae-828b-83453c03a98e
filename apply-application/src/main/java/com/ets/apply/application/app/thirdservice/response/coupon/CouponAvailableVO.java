package com.ets.apply.application.app.thirdservice.response.coupon;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class CouponAvailableVO {

    //{
    //    "code": 0,
    //    "msg": "OK",
    //    "data": [
    //        {
    //            "id": 19421911,
    //            "couponBatchNo": "20231220152649",
    //            "couponName": "10元会员券",
    //            "startTime": "2024-01-23T07:31:08.000+00:00",
    //            "endTime": "2024-02-28T15:59:59.000+00:00",
    //            "maxDeduction": 10.000,
    //            "fullValue": 20.000,
    //            "discountValue": 0.000,
    //            "isSuperposition": null,
    //            "status": 1,
    //            "uid": 19914726,
    //            "goodsTag": null,
    //            "couponType": 3,
    //            "couponNo": "83ed06721a13",
    //            "categoryNo": "A28",
    //            "description": "领取后是",
    //            "useChannel": "2",
    //            "batchCouponType": 1,
    //            "reductionValue": 10.000,
    //            "payValue": 0.000,
    //            "finalDiscountValue": null,
    //            "vipFinalDiscountValue": null
    //        }
    //    ]
    //}
    private Long id;
    private String couponBatchNo;
    private String couponName;
    private String couponNo;
    private String categoryNo;
    private Long uid;
    private BigDecimal fullValue;
}
