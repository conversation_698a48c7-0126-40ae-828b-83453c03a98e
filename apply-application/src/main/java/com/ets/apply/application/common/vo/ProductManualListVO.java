package com.ets.apply.application.common.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ProductManualListVO {

    private Integer id;

    private String manualSn;

    private String storageSku;

    private String source;

    private String sourceName;

    private String packageSn;

    private String customerPhone;

    private String activateTutorialVideoUrl;

    private String activateFailVideoUrl;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdAt;
}
