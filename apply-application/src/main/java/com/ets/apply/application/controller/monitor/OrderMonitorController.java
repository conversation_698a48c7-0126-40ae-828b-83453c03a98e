package com.ets.apply.application.controller.monitor;


import com.ets.apply.application.app.business.monitor.OrderMonitorBusiness;
import com.ets.apply.application.common.dto.monitor.order.CheckNumsDTO;
import com.ets.apply.application.common.dto.monitor.order.OrderCenterCheckDTO;
import com.ets.apply.application.common.vo.monitor.order.CheckNumsListVO;
import com.ets.apply.application.common.vo.monitor.order.OrderCenterCheckVO;
import com.ets.apply.application.controller.BaseController;
import com.ets.apply.application.infra.service.IssueServiceService;
import com.ets.apply.application.infra.service.OrderOrderService;
import com.ets.common.JsonResult;
import com.ets.common.ToolsHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

import java.util.List;

@RequestMapping("/monitor/order")
@RestController
@Slf4j
public class OrderMonitorController extends BaseController {
    @Autowired
    private OrderMonitorBusiness orderMonitorBusiness;

    @Autowired
    private OrderOrderService orderOrderService;

    @Autowired
    private IssueServiceService issueServiceService;

    @PostMapping("/checkNums")
    @ResponseBody
    public JsonResult<List<CheckNumsListVO>> checkNums(@RequestBody @Valid CheckNumsDTO checkNumsDTO) {
        return JsonResult.ok(orderMonitorBusiness.checkNums(checkNumsDTO, ToolsHelper.getDate()+" 00:00:00", ToolsHelper.getDate()+" 23:59:59"));
    }

    @PostMapping("/orderCenterCheck")
    @ResponseBody
    public JsonResult<OrderCenterCheckVO> orderCenterCheck(@RequestBody @Valid OrderCenterCheckDTO dto) {
        return JsonResult.ok(new OrderCenterCheckVO(orderOrderService.countOrderSnByCreatedAt(ToolsHelper.getLocalDateTime(dto.getBeginTime()),ToolsHelper.getLocalDateTime(dto.getEndTime()))));
    }

    @PostMapping("/orderCenterIssueMonitor")
    @ResponseBody
    public JsonResult<OrderCenterCheckVO> orderCenterIssueMonitor(@RequestBody @Valid OrderCenterCheckDTO dto) {

        return JsonResult.ok(new OrderCenterCheckVO(issueServiceService.getCreateCount(dto.getBeginTime(), dto.getEndTime())));
    }
}
