package com.ets.apply.application.infra.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("etc_apply_guide_result")
public class ApplyGuideResult extends BaseEntity<ApplyGuideResult>{

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 指引类型
     */
    private Integer mapType;

    /**
     * 问题组合
     */
    private String questionMapGroup;

    /**
     * 产品包
     */
    private String packageSn;

    /**
     * 下级指引类型
     */
    private Integer itemMapType;

    /**
     * 
     */
    private String chooseQuestionIds;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
