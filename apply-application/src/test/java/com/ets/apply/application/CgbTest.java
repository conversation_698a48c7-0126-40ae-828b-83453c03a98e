package com.ets.apply.application;

import cn.hutool.core.net.url.UrlBuilder;
import com.ets.apply.application.app.business.creditCard.CgbBusiness;
import com.ets.apply.application.common.dto.bank.CgbVerifyDTO;
import com.ets.apply.application.common.utils.bank.SM2SignUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@SpringBootTest(classes = ApplyApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringRunner.class)
@ActiveProfiles(value = "dev")
public class CgbTest {

    @Autowired
    CgbBusiness cgbBusiness;

    @Test
    public void decryptUserData() {
        String userData = "\\U0070\\U0072\\U006F\\U0064\\U0075\\U0063\\U0074\\U0049\\U0064\\U003D\\U0031\\U0030\\U0030\\U0036\\U0026\\U0068\\U0069\\U0067\\U0068\\U0072\\U006F\\U0061\\U0064\\U0043\\U006F\\U0064\\U0065\\U003D\\U0030\\U0039\\U0032\\U0031\\U0039\\U0037\\U0026\\U0047\\U0044\\U0066\\U006C\\U006F\\U0077\\U004E\\U006F\\U003D\\U0032\\U0032\\U0031\\U0030\\U0031\\U0037\\U0031\\U0034\\U0032\\U0039\\U0030\\U0030\\U0030\\U0032\\U0036\\U0036\\U0033\\U0034\\U0030\\U0026\\U0047\\U0044\\U0062\\U0061\\U0063\\U006B\\U0055\\U0072\\U006C\\U003D\\U0068\\U0074\\U0074\\U0070\\U0073\\U0025\\U0033\\U0041\\U0025\\U0032\\U0046\\U0025\\U0032\\U0046\\U0068\\U0035\\U002E\\U0067\\U006F\\U006C\\U0063\\U0065\\U0072\\U002E\\U0063\\U006E\\U0025\\U0032\\U0046\\U0073\\U006F\\U006D\\U0065\\U0025\\U0032\\U0046\\U0063\\U006C\\U006F\\U0073\\U0065\\U0057\\U0056\\U0025\\U0032\\U0046\\U0063\\U006C\\U006F\\U0073\\U0065\\U0057\\U0056\\U002E\\U0068\\U0074\\U006D\\U006C\\U0025\\U0033\\U0046\\U0077\\U0068\\U0069\\U0063\\U0068\\U0025\\U0033\\U0044\\U0033\\U0031\\U0025\\U0032\\U0036\\U006E\\U0061\\U0076\\U0057\\U0061\\U0079\\U0025\\U0033\\U0044\\U0072\\U0065\\U004C\\U0061\\U0075\\U006E\\U0063\\U0068\\U0025\\U0032\\U0036\\U0070\\U0061\\U0074\\U0068\\U0025\\U0033\\U0044\\U0025\\U0032\\U0046\\U0070\\U0061\\U0067\\U0065\\U0073\\U0025\\U0032\\U0046\\U0061\\U0070\\U0070\\U006C\\U0079\\U0025\\U0032\\U0046\\U0070\\U0061\\U0067\\U0065\\U0025\\U0032\\U0046\\U0063\\U0068\\U006F\\U006F\\U0073\\U0065\\U0050\\U0061\\U0079\\U0054\\U0079\\U0070\\U0065\\U0046\\U0061\\U0074\\U0068\\U0065\\U0072\\U0026\\U0074\\U0068\\U0069\\U0072\\U0064\\U0043\\U0068\\U0061\\U006E\\U006E\\U0065\\U006C\\U003D\\U0047\\U0044";
        userData = userData.toLowerCase();
        StringBuffer sb = new StringBuffer();
        String[] hex = userData.split("\\\\u");

        for(int i = 1; i < hex.length; ++i) {
            int index = Integer.parseInt(hex[i], 16);
            sb.append((char)index);
        }

        userData = sb.toString();
        System.out.println(userData);
    }

    @Test
    public void sm2EncryptStr() {
        String userData = "productId=1006&highroadCode=092197&GDflowNo=2210171429000266340&GDbackUrl=https%3A%2F%2Fh5.golcer.cn%2Fsome%2FcloseWV%2FcloseWV.html%3Fwhich%3D31%26navWay%3DreLaunch%26path%3D%2Fpages%2Fapply%2Fpage%2FchoosePayTypeFather&thirdChannel=GD";
        String PUB_KEY = "bank/mobilebankCeshi.puk";
        String PRI_KEY = "bank/mobilebankCeshi.pvk";

        try {
            System.out.println("原字符串：" + userData);
            String encodeStr = SM2SignUtil.encryptString(PUB_KEY, userData, "UTF-8");
            System.out.println("加签加密后密串：" + encodeStr);
            String decodeStr = SM2SignUtil.decryptString(PRI_KEY, encodeStr, "UTF-8");
            System.out.println("解密后密串：" + decodeStr);
        } catch (Exception var7) {
            var7.printStackTrace();
        }
    }

    @Test
    public void verify() {
        CgbVerifyDTO verifyDTO = new CgbVerifyDTO();
        verifyDTO.setKey("HD50220VWOVTGEYH");
        verifyDTO.setDecryptData("96a9dd130ae92d0520a860e13724dd511b96965a675872060eb17e1e9e91cfd7b7f56718b7a3f2e50303e529ce5463e0144403d4908bd6b1c1250ff5fa7d9a7cb214f45d60abd7f7aa6c76e24d36aafb3d2d62dfc30a10a98d95d81be34de95af7317f0b1736e3e02bcb2b831ca2eaa659bbf46b9457314f8a04f435946fc34d82b62591a2062f68fd27a9155ee7159406700fa6185ee3fcd3f511e41f7cc85acbe48eb98a4005148e35efe403e279cd07c278bd5e5050b16315bddcb3faab1ee10bfac3c4d6fcdbd122e2ab2450327eae29e55f59330a057706feeabc9ebf6b6ff3899a0fe0dc0a003b15b14b793bc73f3621624d24fcc1d7f72634876f9dd3aba288e309e2b250f4ebc30c33324bcac9a3c85e2bed2661a6e1158ee363a13a145c672e23c04880699e704e49a6b3f1b1172491cc42bb5eae0f3d780fe0b508db71e4db35af835d7f24dfce549427375097b945c60ad8d58b289a24b262ded0272e0ba7e27bd97ac266226a12e1279e7663c06d22a3a9d6ae1024c1140adea7914b340bff50a7d8347db9b1e5c9142de65ad189c0dd98b52262c479e026086ff4272299836ade78486134ffa50ed918b4c7791d18ced0bc764e56b648ef068eec37dab34f20e07da93793de9c649c633ee41c8357122073b73196a61baf369853aae6bcd74866ec053a5f7918822f49a15274e88c47d69739d3c8eab8d741dd19ab335ec75c8bfd56b76dc109b5d9761c2c6d3489a0e6db384853e6bbae2fa37fafca4545e4e3473c777651b288b8795794d49f87a90462f3cfbdb9d2f9a8582c59162fb9e38bf5279d34652915afb505463b94018236dda0ec348ccd99d5f6ae21ff54e96bd5b676fd7007b160afa17e39aad39e1642a02006077db6c0f71e84dc2798ce16e109e26898dd44b4ea1a21b0b02bdb9fc99974d266e36f4772ad1ff2ba3b09759ac24828e5b78ab05e087242518c3c44911b2394146ba8be41667910f11e3a29dc33af1be7e04c5993adb48368b91c0b6cb8787bb75dbd4566a9b4db45c06f5f576c0c37d4e24d8476c3152234f4660b85d18a82351adc31aa3746342d2d2db4ce396aa3af8f2b54ae20a7166354105f0feb0f76a687f302c2352a9719010d735fb4eba34ecdeb642ca07b7cac84b6f8a7de934696b38d7359b3c387a3e60520dc81950682a761d8e425be46617c258bf2138e1fa968d344651a6b9940dacd6bcfcd43edf0328ce5bec01de9e3342503ec481c7fae27462ac53068f04d65ba5d9790488f7aab56cccac8e415a586f014a288220b7519b48c6025159a9b97f45900c4f9084c9bfb43902b1058728e4ab6d1f324bfa89ef09a82d2dc7f41c16519a4327bfd311e50dd32a8130b9e72706465fcd55c82849ffa54a2cd204dd72bdb6868b3b6987bcbdf5ff757584859d0a4ac3dc97b44cb9f4b4921c5729b0bf8830bd0e9fe0ea7ecf0b09851323d0f50d1d81ebc8889aaa20dd61157b1dac0194e499121986b4d8932a9c3273b30bcc88e2f71b98ce212315aeac9d56c0302dc2433de273c0afd22bea2a15f4b200ca52b7699e8e10f5aea127270e871fcae2adede709b97734833a2e0d7db19fc3fe15790cd169530e054e417d2d74d52664bf98d15fb814a10129a7df603d3a6c0141d10945ac76d264d466ca5eb418c844cb8ef10147f43aabb5276c8175ef8758c97196ab2366772f0af2a1b0882c464283f54c118f89f3aa56f9ad616f2fcbb1c4cd244fed299866f3feeaee2c48ed2362d76bf811921375a793304b60248ad20cd030ad3cd3bcb646441d4302f96af3c15acafadf00090cfb6be718c03cecd283c0db399fa35458eefa376746ef0147392a6fdcc24fb5fd8648e44ab670c579aadf4c49c4974fc869a2323d4cbcb931544e82f684662bb6bf528da5656f007a6d84653bf45c941885320cf193740aa878daaefe1c50d6ed62f32e21cbb5e86e75fa7d3f2f7c04e5172f73126b3ba4d90cd66a7ba5704f882ba9a8a176b0fb1856c9e0aa225af6d8e77429ead15d267dd3afb213e24711e61b81eaf36d3386e2893a390ea8dea14eab65c03151d0fb02e5e1dc43cc9c36262a34f51b6803bac09628039a8fc8c4668af3a57b97cc6b5084e862e9ca3322df6d4265bd405a6f6ddfd470267f5db72ada7609e0015bc7ef1e765de9831dd11cca0a42b73e6a6187786529a7a9e87ec250268f8dbe6ac3a95dfcfb704b64122a5edbba61c34be7e67d7d17aed5a6cb79bba8e995f268d2b5b346bd1c8b5d7678afa964b0538a5719fe846add2d4ad71514bb728f2cf38400d21fda0cd4a3905ee715c4d94e10866e5b6c6101f01b37df13feac7cfabbca5545834e75a28e54e3f3aabe1f03a07da15dc4d14b1c6f9636655520202eea4a4721aab4259bc5840bb794641b4a7714d8235020544b3e77335940fedccf686944c8b0515a7243ef6007b89b5df7e132883b5668621cba4219e4caf2b4bd3136d3a4e898dc92de6c0b9be0594dd7747e43a77c973c24b1cb54d545d5f9764166001926df5475041c7062a6b00bb5a8bd33159358b5d16e206c0cca9c97191181720a7c1cc2739446633ab36b54e7756a40a75fcd50648bb2fd48d5ca71c3c6d8a291c4a33457313f4b5864dff3f5a10d334dccd416227558e6a48195be4461655bebdae62894df779b361fe8f4f299f0c1715365a71e1f3b0b1d75278791bf72768c72917af8c50d3199412bf196fffff4b941a0be1960d96f574bd40e90c2321ec2f9abb0fc28ce49d3c957a43a0adea1e09301b94fcf457cdfbb0e13b6796b1ea64054b03958a263688e45da687201c6a9ee257c3d21c9884f7e2c1d2529569c1930fd6b3f4fa9f97a92808c3c29bd54b6de03244adaa14572688c9c46c316e07b8a501640bb62d13efd244c768f6e8be04ee86fc54a07988c88d3dff16a3872762a3c02323989094a6c04942369dab4655c7130dc3e728d7eabc3e44f8c05849dc6f803fe042c8b7bdaf36a4cd054991ac2cffe06ec4e434b293cc9f7c4f13e552311bbc3f989066661440f1f2fb706827644b5c39ca135b4b8d3d31b3a3984df71910c3a8591c5e71ce0b56a9a2587663acf642a1de4d81da0a5a422d4145613d0405c39dda8d4c23b8cf422e1b7f1ad00eb0697e8d00affe5712");
        verifyDTO.setFileSignedValue("MEUCIGb65S0BDVb+UjOykUhmepkgIAR/DrNpE0wygL1F32MKAiEAsARwjXpZSENsn8pVdXkpsh1MD3OJgMT7dsVG4TvQHsM=");
        String result = cgbBusiness.verify(verifyDTO);
        System.out.println(result);
    }

    @Test
    public void urlTest(){
        // https://www.hutool.cn/aaa/bbb?ie=UTF-8&wd=test
        String buildUrl = UrlBuilder.create()
                .setScheme("https")
                .setHost("www.hutool.cn")
                .addPath("/aaa").addPath("bbb")
                .addQuery("ie", "UTF-8")
                .addQuery("wd", "test")
                .build();
        System.out.println(buildUrl);

    }
}
