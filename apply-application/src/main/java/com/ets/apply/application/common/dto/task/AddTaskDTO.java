package com.ets.apply.application.common.dto.task;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AddTaskDTO {
    /**
     * 任务关联sn,对应order_sn之类
     */
    private String referSn;

    /**
     * 任务类型
     */
    private String referType;
    /**
     * 处理内容
     */
    private String notifyContent;
    /**
     * 下一次执行时间
     */
    private String nextExecTime;
}
