package com.ets.apply.application.controller.internal;

import com.ets.apply.application.app.business.deviceValuation.DeviceValuationBusiness;
import com.ets.apply.application.common.dto.request.deviceValuation.CheckAndLockValuationDTO;
import com.ets.apply.application.common.dto.request.deviceValuation.GetDeviceValuationDTO;
import com.ets.apply.application.common.dto.request.deviceValuation.UseValuationDTO;
import com.ets.apply.application.common.vo.deviceValuation.GetDeviceValuationVO;
import com.ets.common.JsonResult;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping("/device-valuation")
@RefreshScope
@RestController
@Slf4j
public class DeviceValuationController {

    @Autowired
    private DeviceValuationBusiness deviceValuationBusiness;
    @RequestMapping("/check-and-lock-valuation")
    public JsonResult<?> checkAndLockValuation(@RequestBody @Valid CheckAndLockValuationDTO checkAndLockValuationDTO){

        return JsonResult.ok(deviceValuationBusiness.checkAndLockValuation(checkAndLockValuationDTO));
    }

    @RequestMapping("/use-valuation")
    public JsonResult<?> useValuation(@RequestBody @Valid UseValuationDTO useValuationDTO) {
        deviceValuationBusiness.useValuation(useValuationDTO);
        return JsonResult.ok();
    }

    @RequestMapping("/get-valuation-info")
    public JsonResult<GetDeviceValuationVO> getValuationSn(@RequestBody @Valid GetDeviceValuationDTO getDeviceValuationDTO) {
        return JsonResult.ok(deviceValuationBusiness.getDeviceValuation(getDeviceValuationDTO));
    }
}
