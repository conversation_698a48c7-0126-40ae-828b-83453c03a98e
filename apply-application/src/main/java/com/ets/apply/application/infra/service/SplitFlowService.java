package com.ets.apply.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.apply.application.infra.entity.splitFlow.SplitFlowEntity;
import com.ets.apply.application.infra.mapper.SplitFlowMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 映射组合规则 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-27
 */
@Service
@DS("db-apply")
public class SplitFlowService extends BaseService<SplitFlowMapper, SplitFlowEntity> {
    /**
     * 查找分流结果
     */
    public SplitFlowEntity findByCondition(String splitType, String splitTerm,Long uid) {
        Wrapper<SplitFlowEntity> wrapper = Wrappers.<SplitFlowEntity>lambdaQuery()
                .eq(SplitFlowEntity::getSplitType, splitType)
                .eq(SplitFlowEntity::getSplitTerm, splitTerm)
                .eq(SplitFlowEntity::getUid, uid)
                .orderByDesc(SplitFlowEntity::getCreatedAt)
                .last("limit 1");
        return super.baseMapper.selectOne(wrapper);
    }
}
