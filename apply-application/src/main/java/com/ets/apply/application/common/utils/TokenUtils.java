package com.ets.apply.application.common.utils;

import com.ets.common.ToolsHelper;
import org.apache.commons.codec.digest.DigestUtils;

import java.lang.reflect.Field;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.Collectors;

public class TokenUtils {

    /** 获取签名 */
    public static String getSign(Map<String, Object> paramMap, String secret) {
        Map<String, Object> signMap = sortMapByKeyToAsc(paramMap);
        if(signMap == null) {
            signMap = new HashMap<>();
        }
        StringBuilder sbA = new StringBuilder();
        for (Map.Entry<String, Object> map : signMap.entrySet()) {
            sbA.append(map.getKey()).append("=").append(map.getValue()).append("&");
        }
        sbA.append("secret=").append(secret);
        return encode(sbA.toString()).toLowerCase();
    }

    /**
     * 根据Map key进行升序排序
     *
     * @param map 待处理map
     * @return 处理结果
     */
    private static Map<String, Object> sortMapByKeyToAsc(Map<String, Object> map) {
        if (map == null || map.isEmpty()) {
            return null;
        }

        return map.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .filter(mapItem -> !org.springframework.util.StringUtils.isEmpty(mapItem.getValue()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,
                        (oldValue, newValue) -> oldValue, LinkedHashMap::new));
    }

    private static String encode(String plaintext) {
        return DigestUtils.md5Hex(plaintext.getBytes(StandardCharsets.UTF_8));
    }

    public static String getTokenByObject(Object obj, String secret)  {
       try {
           Map<String, Object> map = new HashMap<>();
           Class<?> clazz = obj.getClass();
           for (Field field : clazz.getDeclaredFields()) {
               field.setAccessible(true);
               String fieldName = field.getName();
               Object value = field.get(obj);
               if(!fieldName.equals("token")){
                   map.put(fieldName, value);
               }

           }
           return getSign(map, secret);
       }catch (Exception e){
           ToolsHelper.throwException(e.getMessage());
       }
       return null;
    }

}
