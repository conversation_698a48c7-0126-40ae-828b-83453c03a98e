package com.ets.apply.application.infra.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 产品说明书记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("product_manual")
public class ProductManual extends BaseEntity<ProductManual> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 说明书编码
     */
    private String manualSn;

    /**
     * 仓库sku
     */
    private String storageSku;

    /**
     * 渠道值
     */
    private String source;

    /**
     * 渠道名称
     */
    private String sourceName;

    /**
     * 产品包编码
     */
    private String packageSn;

    /**
     * 客服电话
     */
    private String customerPhone;

    /**
     * 激活教程视频
     */
    private String activateTutorialVideoUrl;

    /**
     * 激活失败引导视频
     */
    private String activateFailVideoUrl;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;


}
