package com.ets.apply.application.common.consts.taskRecord;


import com.ets.apply.application.app.factory.task.impl.*;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@AllArgsConstructor
@Getter
public enum TaskRecordReferTypeEnum {

    TASK_THIRD_PARTNER_SHIP_NOTIFY("third_partner_ship_notify", ThirdPartnerShipNotify.class, "第三方商品发货通知"),
    TASK_ACTIVATED_NOTIFY_WECAR("activated_notify_wecar", ActivatedNotifyWecar.class, "腾讯出行激活通知"),
    TASK_ORDER_CENTER_SYNC("order_center_sync", OrderCenterSyncTask.class, "订单中心同步"),
    TASK_ORDER_AFTERSALES_APPLY("order_aftersales_apply", OrderAftersalesApplyTask.class, "申办售后申请"),
    TASK_UNICOM_APPLY_NOTIFY("unicom_apply_notify", UnicomApplyNotifyTask.class, "联通卡申请通知"),
    TASK_SEGMENT_BENEFIT_SEND("segment_benefit_send", SegmentBenefitSend.class, "号段权益发放"),
    TASK_CLEAN_REALNAME("clean_realname", CleanRealNameTask.class, "清理实名数据"),
    TASK_RISK_CHECK_UPLOAD("risk_check_upload", RiskCheckUploadTask.class, "推送补传资料信息"),
    TASK_THIRD_PARTNER_ORDER_SYNC("third_partner_order_sync", ThirdProductOrderSyncTask.class, "合作方订单同步"),
    ;

    private final String type;
    private final Class<? extends TaskBase> job;
    private final String desc;
    public static final Map<String, Class<?>> map;
    public static final List<String> list;

    static {
        TaskRecordReferTypeEnum[] enums = TaskRecordReferTypeEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getType(), enums[index].getJob()),
                Map::putAll);
        list = Arrays.stream(enums).map(TaskRecordReferTypeEnum::getType).collect(Collectors.toList());
    }

    public static TaskRecordReferTypeEnum getReferTypeEnumByType(String type){
        for(TaskRecordReferTypeEnum taskRecordReferTypeEnum:TaskRecordReferTypeEnum.values()){
            if(taskRecordReferTypeEnum.getType().equals(type)){
                return taskRecordReferTypeEnum;
            }
        }
        return null;
    }

    public static List<String> getTypeList() {

        List<String> list = new ArrayList<>();
        for(TaskRecordReferTypeEnum taskRecordReferTypeEnum:TaskRecordReferTypeEnum.values()){
            list.add(taskRecordReferTypeEnum.getType());
        }

        return list;
    }
}
