package com.ets.apply.application.infra.service;

import java.util.List;

import com.ets.apply.application.common.dto.taxi.CompanyListDto;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import cn.hutool.core.util.ObjectUtil;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.dynamic.datasource.annotation.DS;

import com.ets.apply.application.infra.entity.TaxiCompanyEntity;
import com.ets.apply.application.infra.mapper.TaxiCompanyMapper;

/**
 * <p>
 * 出租车公司表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-28
 */
@Service
@DS("db-etc")
public class TaxiCompanyService extends BaseService<TaxiCompanyMapper, TaxiCompanyEntity> {
    public List<TaxiCompanyEntity> getAllCompanyName() {
        // return this.baseMapper.getAllCompanyName();
        return this.baseMapper.selectList(new LambdaQueryWrapper<TaxiCompanyEntity>());
    }

    public TaxiCompanyEntity getByCompanyName(String companyName) {
        LambdaQueryWrapper<TaxiCompanyEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TaxiCompanyEntity::getCompanyName, companyName);
        return this.baseMapper.selectOne(queryWrapper);
    }

    public TaxiCompanyEntity getById(Integer id) {
        LambdaQueryWrapper<TaxiCompanyEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TaxiCompanyEntity::getId, id);
        return this.baseMapper.selectOne(queryWrapper);
    }

    public IPage<TaxiCompanyEntity> pageData(Integer pageNum, Integer pageSize) {
        LambdaQueryWrapper<TaxiCompanyEntity> queryWrapper = new LambdaQueryWrapper<>();

        queryWrapper.orderByDesc(TaxiCompanyEntity::getId);
        if (ObjectUtil.isNull(pageNum)) {
            pageNum = 1;
        }

        if (ObjectUtil.isNull(pageSize)) {
            pageSize = 10;
        }

        return this.baseMapper.selectPage(new Page<>(pageNum, pageSize), queryWrapper);
    }

    public IPage<TaxiCompanyEntity> list(CompanyListDto dto) {
        LambdaQueryWrapper<TaxiCompanyEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNotEmpty(dto.getCompanyName()), TaxiCompanyEntity::getCompanyName, dto.getCompanyName());

        queryWrapper.orderByDesc(TaxiCompanyEntity::getId);

        if (ObjectUtil.isNull(dto.getPageNum())) {
            dto.setPageNum(1);
        }

        if (ObjectUtil.isNull(dto.getPageSize())) {
            dto.setPageSize(10);
        }

        return this.baseMapper.selectPage(new Page<>(dto.getPageNum(), dto.getPageSize()), queryWrapper);
    }

    public int updateEntity(TaxiCompanyEntity entity, Integer id) {
        UpdateWrapper<TaxiCompanyEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(TaxiCompanyEntity::getId, id);
        return this.baseMapper.update(entity, updateWrapper);
    }
}
