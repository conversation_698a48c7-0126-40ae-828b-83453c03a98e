package com.ets.apply.application.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.apply.application.app.business.productPackagePopup.PopupBusiness;
import com.ets.apply.application.common.dto.productPackagePopup.*;
import com.ets.apply.application.common.vo.productPackagePopup.ProductPackagePopupListVO;
import com.ets.apply.application.controller.BaseController;
import com.ets.common.JsonResult;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 */
@Controller
@RestController
@RequestMapping("/admin/product-package-popup")
public class ProductPackagePopupAdminController extends BaseController {

    @Autowired
    private PopupBusiness popupBusiness;
    @RequestMapping("/add")
    @ResponseBody
    public JsonResult<?> add(@RequestBody @Valid PopupAddDTO popupAddDTO){
        popupBusiness.add(popupAddDTO);
        return JsonResult.ok();
    }

    @RequestMapping("/get-select-options")
    @ResponseBody
    public JsonResult<HashMap<String, Object>> getSelectOptions() {

        return JsonResult.ok(popupBusiness.getSelectOptions());
    }

    @RequestMapping("/get-list")
    @ResponseBody
    JsonResult<IPage<ProductPackagePopupListVO>> getList(@RequestBody @Valid PopupListDTO listDTO) {

        return JsonResult.ok(popupBusiness.getList(listDTO));
    }

    @RequestMapping("/get-detail")
    @ResponseBody
    JsonResult<?> getDetail(@RequestParam String popupSn){
        return JsonResult.ok(popupBusiness.getDetail(popupSn));
    }


    @RequestMapping("/modify")
    @ResponseBody
    JsonResult<?> modify(@RequestBody @Valid PopupModifyDTO modifyDTO) {
        popupBusiness.modify(modifyDTO);
        return JsonResult.ok();
    }

    @RequestMapping("/set-status")
    JsonResult<?> setStatus(@RequestBody @Valid PopupSetStatusDTO setStatusDTO){
        popupBusiness.setStatus(setStatusDTO);
        return JsonResult.ok();
    }

    @RequestMapping("/set-first-choose")
    JsonResult<?> setFirstChoose(@RequestBody @Valid PopupSetFirstChooseDTO dto){
        popupBusiness.setFirstChoose(dto);
        return JsonResult.ok();
    }

    @RequestMapping("/get-popup-list-with-popup-type-group")
    JsonResult<HashMap<Integer, List<HashMap<String, Object>>>> getPopupListWithPopupTypeGroup(){
        return JsonResult.ok(popupBusiness.getPopupListWithPopupTypeGroup());
    }


}
