package com.ets.apply.application.controller.frontend;

import com.ets.apply.application.app.business.UserIpAddressBusiness;
import com.ets.common.JsonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/frontend/user-ip")
public class UserIpController {

    @Autowired
    private UserIpAddressBusiness userIpAddressBusiness;

    @PostMapping("/upload-ip")
    public JsonResult<?> uploadIp() {
        userIpAddressBusiness.userIp();
        return JsonResult.ok();
    }
}
