package com.ets.apply.application.controller.frontend;

import com.ets.apply.application.app.business.ExternalBindBusiness;
import com.ets.apply.application.common.dto.external.ExternalBindDTO;
import com.ets.apply.application.common.dto.external.ExternalUpdateReservedPhoneDTO;
import com.ets.apply.application.common.utils.UserUtil;
import com.ets.apply.application.common.vo.external.ExternalBindDetailVO;
import com.ets.apply.application.common.vo.external.ExternalBindListVO;
import com.ets.apply.application.controller.BaseController;
import com.ets.common.JsonResult;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping("/frontend/externalBind")
@RestController
public class ExternalBindController extends BaseController {

    @Autowired
    private ExternalBindBusiness externalBindBusiness;

    @RequestMapping("/getList")
    JsonResult<List<ExternalBindListVO>> getList() {

        return JsonResult.ok(externalBindBusiness.getList(UserUtil.getUid()));
    }

    @RequestMapping("/getDetail")
    JsonResult<ExternalBindDetailVO> getDetail(@RequestParam(value = "bindSn") String bindSn) {

        return JsonResult.ok(externalBindBusiness.getDetail(bindSn, UserUtil.getUid()));
    }

    @RequestMapping("/sendVerifyCode")
    JsonResult<Object> sendVerifyCode(@RequestParam(value = "phone") String phone) {

        externalBindBusiness.sendVerifyCode(phone, UserUtil.getUid());

        return JsonResult.ok();
    }

    @PostMapping("/bind")
    JsonResult<Object> bind(@RequestBody @Valid ExternalBindDTO dto) {

        externalBindBusiness.bind(dto, UserUtil.getUid());

        return JsonResult.ok();
    }

    @PostMapping("/updateReservedPhone")
    JsonResult<Object> updateReservedPhone(@RequestBody @Valid ExternalUpdateReservedPhoneDTO dto) {
        // 由新网关转发到该接口，此时uid不是当前登录用户
        externalBindBusiness.updateReservedPhone(dto, UserUtil.getUid());

        return JsonResult.ok();
    }

}
