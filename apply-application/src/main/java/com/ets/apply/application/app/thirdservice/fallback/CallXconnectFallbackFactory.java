package com.ets.apply.application.app.thirdservice.fallback;

import com.ets.apply.application.app.thirdservice.feign.CallXconnectFeign;
import com.ets.apply.application.app.thirdservice.request.xconnect.OrderSyncListDTO;
import com.ets.common.JsonResult;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

@Component
public class CallXconnectFallbackFactory implements FallbackFactory<CallXconnectFeign> {

    @Override
    public CallXconnectFeign create(Throwable cause) {
        return new CallXconnectFeign() {
            @Override
            public String etcOrderSave(OrderSyncListDTO dto, @RequestHeader Map<String, String> headers){
                return JsonResult.error("订单状态回调：etcOrderSave 请求第三方服务失败: " + cause.getMessage()).toString();
            }
            @Override
            public String getToken(@RequestParam(value = "grant_type") String grantType, @RequestParam(value = "client_id") String clientId, @RequestParam(value = "client_secret") String clientSecret){
                return JsonResult.error("getToken 请求第三方服务失败: " + cause.getMessage()).toString();
            }

            @Override
            public String etcOrderUpdate(OrderSyncListDTO dto, @RequestHeader Map<String, String> headers){
                return JsonResult.error("订单状态回调：etcOrderUpdate 请求第三方服务失败: " + cause.getMessage()).toString();
            }
        };
    }
}