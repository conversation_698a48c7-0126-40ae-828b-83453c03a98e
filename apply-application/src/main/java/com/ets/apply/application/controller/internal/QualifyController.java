package com.ets.apply.application.controller.internal;

import com.ets.apply.application.app.business.QualifyCheckBusiness;
import com.ets.apply.application.common.dto.request.QualifyCheckQueryDto;
import com.ets.apply.application.controller.BaseController;
import com.ets.common.BizException;
import com.ets.common.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

/**
 * <p>
 *   申办订单校验是否拥有资格
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-07
 */
@RequestMapping("/qualify")
@RefreshScope
@RestController
@Slf4j
public class QualifyController extends BaseController {
    @Autowired
    private QualifyCheckBusiness qualifyCheckBusiness;

    /**
     * 根 传 order_sn, 核销类型，核销值 ，判断是否可以
     * @param qualifyCheckQueryDto 查询条件
     * @return JsonResult
     * @throws BizException 异常
     */
    @RequestMapping("/check")
    public JsonResult<Boolean> check(@RequestBody @Valid QualifyCheckQueryDto qualifyCheckQueryDto) throws BizException {
        return JsonResult.ok(qualifyCheckBusiness.check(qualifyCheckQueryDto));
    }
}
