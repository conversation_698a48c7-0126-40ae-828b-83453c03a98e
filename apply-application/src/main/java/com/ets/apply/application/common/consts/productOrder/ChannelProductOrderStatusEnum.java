package com.ets.apply.application.common.consts.productOrder;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
@AllArgsConstructor
public enum ChannelProductOrderStatusEnum {

    DEFAULT(10, "未支付"),
    PAID(20, "已支付"),
    REFUNDING(60, "退款中"),
    REFUND_FAILED(61, "退款失败"),
    REFUNDED(62, "退款完成"),
    CLOSED(90, "已关闭");


    private final Integer code;

    private final String description;

    public static final Map<String, String> map;

    static {
        ChannelProductOrderStatusEnum[] enums = ChannelProductOrderStatusEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(
                LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getCode().toString(), enums[index].getDescription()),
                Map::putAll
        );
    }

    public static String getDescByCode(Integer code) {
        for (ChannelProductOrderStatusEnum node : ChannelProductOrderStatusEnum.values()) {
            if (node.getCode().equals(code)) {
                return node.getDescription();
            }
        }

        return "";
    }

    public static List<Map<String, String>> getLabelList() {
        List<Map<String, String>> list = new ArrayList<>();

        for (ChannelProductOrderStatusEnum node : ChannelProductOrderStatusEnum.values()) {
            Map<String, String> row = new LinkedHashMap<>();
            row.put("label", node.getDescription());
            row.put("value", node.getCode().toString());

            list.add(row);
        }

        return list;
    }


}
