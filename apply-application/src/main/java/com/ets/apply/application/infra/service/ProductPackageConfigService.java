package com.ets.apply.application.infra.service;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.apply.application.infra.entity.ProductPackageConfigEntity;
import com.ets.apply.application.infra.entity.ProductPackageEntity;
import com.ets.apply.application.infra.mapper.ProductPackageConfigMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 产品包相关配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-05
 */
@Service
@DS("db-apply")
public class ProductPackageConfigService extends BaseService<ProductPackageConfigMapper,ProductPackageConfigEntity> {
    /**
     * 根据订单号获取订单信息
     *
     * @param configKey 订单号
     * @return ProductPackageConfigEntity
     */
    public ProductPackageConfigEntity getByKey(String configKey) {
        Wrapper<ProductPackageConfigEntity> wrapper = Wrappers.<ProductPackageConfigEntity>lambdaQuery()
            .eq(ProductPackageConfigEntity::getConfigKey, configKey)
            .last("limit 1");
        return super.baseMapper.selectOne(wrapper);
    }
}
