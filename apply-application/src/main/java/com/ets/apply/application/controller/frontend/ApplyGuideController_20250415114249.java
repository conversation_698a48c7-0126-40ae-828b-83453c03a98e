package com.ets.apply.application.controller.frontend;

import com.ets.apply.application.app.business.ApplyGuideBusiness;
import com.ets.apply.application.common.vo.applyGuide.ApplyGuideMapVO;
import com.ets.apply.application.common.vo.applyGuide.ApplyGuideResultVO;
import com.ets.apply.application.common.vo.applyGuide.ApplyGuideQuestionGroupVO;
import com.ets.common.base.JsonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@RestController
@RequestMapping("/frontend/apply-guide")
@Validated
@Api(tags = "申请指南接口")
public class ApplyGuideController {

    private static final Logger log = LoggerFactory.getLogger(ApplyGuideController.class);

    @Autowired
    private ApplyGuideBusiness applyGuideBusiness;

    @PostMapping("/get-question-map")
    public JsonResult<List<ApplyGuideMapVO>> getQuestionMap(@RequestParam(required = false) Integer mapType) {
        return JsonResult.ok(applyGuideBusiness.getQuestionMap(mapType));
    }

    @PostMapping("/get-result-map")
    public JsonResult<List<ApplyGuideResultVO>> getResultMap(@RequestParam(required = false) Integer mapType) {
        return JsonResult.ok(applyGuideBusiness.getResultMap(mapType));
    }

    @ApiOperation(value = "获取问题组列表", notes = "根据地图类型和问题类型列表获取相应的问题组")
    @PostMapping("/get-question-group-list")
    public JsonResult<List<ApplyGuideQuestionGroupVO>> getQuestionGroupList(
            @ApiParam(value = "地图类型", required = true)
            @NotNull(message = "地图类型不能为空")
            @RequestParam Integer mapType,
            
            @ApiParam(value = "问题类型列表", required = true)
            @NotEmpty(message = "问题类型列表不能为空")
            @RequestParam List<Integer> questionTypeList) {
        
        log.debug("获取问题组列表请求 - mapType: {}, questionTypeList: {}", mapType, questionTypeList);
        List<ApplyGuideQuestionGroupVO> result = applyGuideBusiness.getQuestionGroupList(mapType, questionTypeList);
        return JsonResult.ok(result);
    }

    // get-result-by-map

}
