package com.ets.apply.application.common.consts.deposit;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 保证金记录状态枚举
 */
@Getter
@AllArgsConstructor
public enum DepositRecordStatusEnum {

    INIT(0, "初始化"),
    SUCCESS(1, "成功"),
    FAIL(2, "失败");

    private final Integer value;
    private final String desc;

    /**
     * 根据状态码获取描述
     *
     * @param code 状态码
     * @return 状态描述
     */
    public static String getDescByCode(Integer code) {
        if (code == null) {
            return "";
        }
        
        for (DepositRecordStatusEnum status : DepositRecordStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status.getDescription();
            }
        }
        return "";
    }

    /**
     * 根据状态码获取枚举对象
     *
     * @param code 状态码
     * @return 枚举对象，未找到返回null
     */
    public static DepositRecordStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        
        for (DepositRecordStatusEnum status : DepositRecordStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 判断是否为成功状态
     *
     * @param code 状态码
     * @return true-成功状态，false-非成功状态
     */
    public static boolean isSuccess(Integer code) {
        return SUCCESS.getCode().equals(code);
    }

    /**
     * 判断是否为失败状态
     *
     * @param code 状态码
     * @return true-失败状态，false-非失败状态
     */
    public static boolean isFail(Integer code) {
        return FAIL.getCode().equals(code);
    }

    /**
     * 判断是否为初始化状态
     *
     * @param code 状态码
     * @return true-初始化状态，false-非初始化状态
     */
    public static boolean isInit(Integer code) {
        return INIT.getCode().equals(code);
    }
}
