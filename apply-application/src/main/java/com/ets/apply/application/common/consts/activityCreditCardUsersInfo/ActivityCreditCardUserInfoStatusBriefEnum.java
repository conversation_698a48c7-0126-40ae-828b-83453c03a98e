package com.ets.apply.application.common.consts.activityCreditCardUsersInfo;

import com.ets.apply.application.common.consts.productOrder.ProductPackageEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 后台展示时简略的状态，原状态包含了旧的很久没有使用的状态
 */
@Getter
@AllArgsConstructor
public enum ActivityCreditCardUserInfoStatusBriefEnum {


    STATUS_NOT_AUDIT(1, "未审核"),
    STATUS_FAIL_AUDIT(2, "审核不通过"),
    STATUS_PASS_AUDIT(3, "审核通过"),
    STATUS_GOT(5, "已领取"),
    STATUS_QUALIFICATION_FINISHED(9, "资格已使用"),
    STATUS_ACTIVATE_BU_NOT_NEW_USER(10, "信用卡激活(非新户)"),
    STATUS_CREDIT_CARD_ACTIVATE(11, "信用卡激活(新户)"),
    STATUS_WAIT(50, "尚未提交材料到银行侧");

    private final int code;
    private final String description;

    public static String getDescByCode(int code) {
        for (ActivityCreditCardUserInfoStatusBriefEnum node : ActivityCreditCardUserInfoStatusBriefEnum.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }

    public static List<Map<String, String>> getLabelList() {
        List<Map<String, String>> list = new ArrayList<>();

        for (ActivityCreditCardUserInfoStatusBriefEnum node : ActivityCreditCardUserInfoStatusBriefEnum.values()) {
            Map<String, String> row = new LinkedHashMap<>();
            row.put("label", node.getDescription());
            row.put("value", String.valueOf(node.getCode()));

            list.add(row);
        }

        return list;
    }
}
