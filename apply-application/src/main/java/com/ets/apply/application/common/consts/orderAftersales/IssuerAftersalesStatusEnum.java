package com.ets.apply.application.common.consts.orderAftersales;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum IssuerAftersalesStatusEnum {
    STATUS_REJECT(1, "拒绝"),

    STATUS_PROCESSING(2, "处理中"),

    STATUS_FINISH(3, "完成");

    private final Integer status;
    private final String desc;
    public static IssuerAftersalesStatusEnum getByStatus(int status) {
        for (IssuerAftersalesStatusEnum node : IssuerAftersalesStatusEnum.values()) {
            if (node.getStatus() == status) {
                return node;
            }
        }
        return null;
    }
}
