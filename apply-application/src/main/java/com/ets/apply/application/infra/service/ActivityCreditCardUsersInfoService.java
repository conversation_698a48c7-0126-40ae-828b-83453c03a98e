package com.ets.apply.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.apply.application.common.consts.activityCreditCardUsersInfo.ActivityCreditCardUserInfoClassifyEnum;
import com.ets.apply.application.common.consts.activityCreditCardUsersInfo.ActivityCreditCardUserInfoStatusEnum;
import com.ets.apply.application.common.consts.activityCreditCardUsersInfo.ActivityCreditCardUserInfoWhichBankEnum;
import com.ets.apply.application.common.vo.creditCard.CreditCardListVO;
import com.ets.apply.application.infra.entity.ActivityCreditCardUsersInfoEntity;
import com.ets.apply.application.infra.mapper.ActivityCreditCardUsersInfoMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <p>
 * 用户申请信用卡领取奖励时提交的数据 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-09
 */
@Service
@DS("db-etc-proxy")
public class ActivityCreditCardUsersInfoService extends BaseService<ActivityCreditCardUsersInfoMapper, ActivityCreditCardUsersInfoEntity> {

    public List<ActivityCreditCardUsersInfoEntity> getGuangFaWaitingRewardList(String updateTimeStart, String updateTimeEnd,List<Integer> classify) {
        // 只允许处理广发银行信用卡
        Wrapper<ActivityCreditCardUsersInfoEntity> wrapper = Wrappers.<ActivityCreditCardUsersInfoEntity>lambdaQuery()
                .in(ActivityCreditCardUsersInfoEntity::getClassify, classify)
                .eq(ActivityCreditCardUsersInfoEntity::getStatus, ActivityCreditCardUserInfoStatusEnum.STATUS_PASS_AUDIT.getCode())
                .gt(ActivityCreditCardUsersInfoEntity::getUpdatedAt, updateTimeStart)
                .le(ActivityCreditCardUsersInfoEntity::getUpdatedAt, updateTimeEnd)
                .eq(ActivityCreditCardUsersInfoEntity::getWhichBank, ActivityCreditCardUserInfoWhichBankEnum.GUANG_FA_INTERFACE.getCode())
                .last("limit 200");
        return this.baseMapper.selectList(wrapper);
    }

    public Boolean hasSendQualification(Long uid) {
        Wrapper<ActivityCreditCardUsersInfoEntity> wrapper = Wrappers.<ActivityCreditCardUsersInfoEntity>lambdaQuery()
                .eq(ActivityCreditCardUsersInfoEntity::getClassify, ActivityCreditCardUserInfoClassifyEnum.TYPE_NORMAL_CREDIT_APPLY.getCode())
                .eq(ActivityCreditCardUsersInfoEntity::getStatus, ActivityCreditCardUserInfoStatusEnum.STATUS_QUALIFICATION_FINISHED.getCode())
                .eq(ActivityCreditCardUsersInfoEntity::getUid, uid)
                .in(ActivityCreditCardUsersInfoEntity::getWhichBank, Arrays.asList(
                        ActivityCreditCardUserInfoWhichBankEnum.GUANG_FA_INTERFACE.getCode(),
                        ActivityCreditCardUserInfoWhichBankEnum.PING_AN_INTERFACE.getCode(),
                        ActivityCreditCardUserInfoWhichBankEnum.JIAO_TONG.getCode()
                ))
                .last("LIMIT 1");
        return this.baseMapper.selectOne(wrapper) != null;
    }

    /**
     * 查找一个申办资格类型 的用户申请记录，状态是驳回的类型不包括在可用的记录中
     * @param uid
     * @param whichBanks
     * @return
     */
    public ActivityCreditCardUsersInfoEntity findNewApplyBankUsersInfo(Long uid, List<Integer> whichBanks, Integer classify, String referSn, String version) {
        Wrapper<ActivityCreditCardUsersInfoEntity> wrapper = Wrappers.<ActivityCreditCardUsersInfoEntity>lambdaQuery()
                .eq(ActivityCreditCardUsersInfoEntity::getClassify, classify)
                .eq(ActivityCreditCardUsersInfoEntity::getUid, uid)
                .ne(ActivityCreditCardUsersInfoEntity::getStatus, ActivityCreditCardUserInfoStatusEnum.STATUS_FAIL_AUDIT.getCode())
                .eq(ActivityCreditCardUsersInfoEntity::getReferSn, referSn)
                .eq(ActivityCreditCardUsersInfoEntity::getVersion, version)
                .in(ActivityCreditCardUsersInfoEntity::getWhichBank, whichBanks)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }

    /**
     * 查找申请记录： uid、referSn、referType、whichBank 参数确定一条记录，不过滤状态
     * @param uid
     * @param whichBanks
     * @param referType
     * @param referSn
     * @return
     */
    public ActivityCreditCardUsersInfoEntity findOneByUidAndReferSn(Long uid, List<Integer> whichBanks, Integer referType, String referSn) {
        Wrapper<ActivityCreditCardUsersInfoEntity> wrapper = Wrappers.<ActivityCreditCardUsersInfoEntity>lambdaQuery()
                .eq(ActivityCreditCardUsersInfoEntity::getReferType, referType)
                .eq(ActivityCreditCardUsersInfoEntity::getUid, uid)
                .eq(ActivityCreditCardUsersInfoEntity::getReferSn, referSn)
                .in(ActivityCreditCardUsersInfoEntity::getWhichBank, whichBanks)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }


    /**
     * 通过uid,银行，银行申请单号获取一条记录
     * @param uid
     * @param creditCardNumber
     * @return
     */
    public ActivityCreditCardUsersInfoEntity getOneCmbcByUidAndCreditNumber(Long uid,String creditCardNumber){
        Wrapper<ActivityCreditCardUsersInfoEntity> wrapper = Wrappers.<ActivityCreditCardUsersInfoEntity>lambdaQuery()
                .eq(ActivityCreditCardUsersInfoEntity::getClassify, ActivityCreditCardUserInfoClassifyEnum.TYPE_NEW_APPLY.getCode())
                .eq(ActivityCreditCardUsersInfoEntity::getUid, uid)
                .eq(ActivityCreditCardUsersInfoEntity::getCreditCardNumber, creditCardNumber)
                .eq(ActivityCreditCardUsersInfoEntity::getWhichBank, ActivityCreditCardUserInfoWhichBankEnum.CREDIT_BANK_CMBC.getCode())
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }

    /**
     * 通过关联单号，uid 查找对应银行的最新申请记录
     *
     * @param uid
     * @param referSn
     * @param whichBank
     * @return
     */
    public ActivityCreditCardUsersInfoEntity getOneByReferSnAndUid(Long uid, String referSn, Integer whichBank) {
        Wrapper<ActivityCreditCardUsersInfoEntity> wrapper = Wrappers.<ActivityCreditCardUsersInfoEntity>lambdaQuery()
                .eq(ActivityCreditCardUsersInfoEntity::getUid, uid)
                .eq(ActivityCreditCardUsersInfoEntity::getReferSn, referSn)
                .eq(ActivityCreditCardUsersInfoEntity::getWhichBank, whichBank)
                .orderByDesc(ActivityCreditCardUsersInfoEntity::getCreatedAt)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }


    /**
     *
     * @param uid
     * @param classify
     * @return
     */
    public Boolean hasSendQualification(Long uid,Integer classify) {
        Wrapper<ActivityCreditCardUsersInfoEntity> wrapper = Wrappers.<ActivityCreditCardUsersInfoEntity>lambdaQuery()
                .eq(ActivityCreditCardUsersInfoEntity::getClassify, classify)
                .eq(ActivityCreditCardUsersInfoEntity::getStatus, ActivityCreditCardUserInfoStatusEnum.STATUS_QUALIFICATION_FINISHED.getCode())
                .eq(ActivityCreditCardUsersInfoEntity::getUid, uid)
                .in(ActivityCreditCardUsersInfoEntity::getWhichBank, Arrays.asList(
                        ActivityCreditCardUserInfoWhichBankEnum.GUANG_FA_INTERFACE.getCode(),
                        ActivityCreditCardUserInfoWhichBankEnum.PING_AN_INTERFACE.getCode(),
                        ActivityCreditCardUserInfoWhichBankEnum.JIAO_TONG.getCode()
                )).last("LIMIT 1");
        return this.baseMapper.selectOne(wrapper) != null;
    }


    /**
     * 有进行中的申请单
     * 非审核驳回/资格已发放/待定状态
     * @param uid
     * @param classify
     * @return
     */
    public Boolean hasProgressingApply(Long uid, Integer classify,Integer whichBank) {
        Wrapper<ActivityCreditCardUsersInfoEntity> wrapper = Wrappers.<ActivityCreditCardUsersInfoEntity>lambdaQuery()
                .eq(ActivityCreditCardUsersInfoEntity::getClassify, classify)
                .eq(ActivityCreditCardUsersInfoEntity::getUid, uid)
                .notIn(ActivityCreditCardUsersInfoEntity::getStatus, Arrays.asList(
                        ActivityCreditCardUserInfoStatusEnum.STATUS_FAIL_AUDIT.getCode(),
                        ActivityCreditCardUserInfoStatusEnum.STATUS_QUALIFICATION_FINISHED.getCode(),
                        ActivityCreditCardUserInfoStatusEnum.STATUS_WAIT.getCode()
                ))
                .eq(ActivityCreditCardUsersInfoEntity::getWhichBank,whichBank)
                .last("LIMIT 1")
              ;
        return this.baseMapper.selectOne(wrapper) != null;
    }


    /**
     * 通过关联单号获取银行申请信息
     * @param referType
     * @param referSn
     * @param whichBank
     * @return
     */
    public ActivityCreditCardUsersInfoEntity getOneByReferSnAndWhichBank(Integer referType, String referSn, Integer whichBank) {
        Wrapper<ActivityCreditCardUsersInfoEntity> wrapper = Wrappers.<ActivityCreditCardUsersInfoEntity>lambdaQuery()
                .eq(ActivityCreditCardUsersInfoEntity::getReferType, referType)
                .eq(ActivityCreditCardUsersInfoEntity::getReferSn, referSn)
                .eq(ActivityCreditCardUsersInfoEntity::getWhichBank, whichBank)
                .orderByDesc(ActivityCreditCardUsersInfoEntity::getCreatedAt)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }
    /*
     *   如果有其他已绑定的申请中订单，提示用户不能申请
     */
    public Boolean checkProcessingApply(Long uid,Integer whichBank,Integer classify,String referSn) {
        Wrapper<ActivityCreditCardUsersInfoEntity> wrapper = Wrappers.<ActivityCreditCardUsersInfoEntity>lambdaQuery()
            .eq(ActivityCreditCardUsersInfoEntity::getClassify, classify)
            .notIn(ActivityCreditCardUsersInfoEntity::getStatus, Arrays.asList(
                ActivityCreditCardUserInfoStatusEnum.STATUS_FAIL_AUDIT.getCode(),
                ActivityCreditCardUserInfoStatusEnum.STATUS_QUALIFICATION_FINISHED.getCode(),
                ActivityCreditCardUserInfoStatusEnum.STATUS_ACTIVATE_BU_NOT_NEW_USER.getCode()
            ))
            .eq(ActivityCreditCardUsersInfoEntity::getUid, uid)
            .notIn(ActivityCreditCardUsersInfoEntity::getReferSn, Arrays.asList(
                "",
                referSn
            ))
            .eq(ActivityCreditCardUsersInfoEntity::getWhichBank, whichBank)
            .last("limit 1");
        return (this.baseMapper.selectOne(wrapper) == null)?false:true;
    }


    /*
     *   获取一条正在进行中的申请记录
     */
    public ActivityCreditCardUsersInfoEntity getProcessingApply(Long uid,Integer whichBank,Integer classify,Integer referType,String referSn,String version) {
        Wrapper<ActivityCreditCardUsersInfoEntity> wrapper = Wrappers.<ActivityCreditCardUsersInfoEntity>lambdaQuery()
            .eq(ActivityCreditCardUsersInfoEntity::getClassify, classify)
            .eq(ActivityCreditCardUsersInfoEntity::getReferType, referType)
            .eq(ActivityCreditCardUsersInfoEntity::getReferSn, referSn)
            .eq(ActivityCreditCardUsersInfoEntity::getVersion, version)
            .notIn(ActivityCreditCardUsersInfoEntity::getStatus, Arrays.asList(
                ActivityCreditCardUserInfoStatusEnum.STATUS_FAIL_AUDIT.getCode(),
                ActivityCreditCardUserInfoStatusEnum.STATUS_QUALIFICATION_FINISHED.getCode(),
                ActivityCreditCardUserInfoStatusEnum.STATUS_ACTIVATE_BU_NOT_NEW_USER.getCode()
            ))
            .eq(ActivityCreditCardUsersInfoEntity::getUid, uid)
            .eq(ActivityCreditCardUsersInfoEntity::getWhichBank, whichBank)
            .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }
    /*
     * 查找没有关联sn的数据
     */
    public ActivityCreditCardUsersInfoEntity getOneWithoutReferSn(Long uid,Integer whichBank,Integer classify,String referSn,String version) {
        Wrapper<ActivityCreditCardUsersInfoEntity> wrapper = Wrappers.<ActivityCreditCardUsersInfoEntity>lambdaQuery()
            .in(ActivityCreditCardUsersInfoEntity::getStatus, Arrays.asList(
                ActivityCreditCardUserInfoStatusEnum.STATUS_PASS_AUDIT.getCode(),
                ActivityCreditCardUserInfoStatusEnum.STATUS_CREDIT_CARD_ACTIVATE.getCode()
            ))
            .in(ActivityCreditCardUsersInfoEntity::getReferSn, Arrays.asList(
                "",
                referSn
            ))
            .eq(ActivityCreditCardUsersInfoEntity::getUid, uid)
            .eq(ActivityCreditCardUsersInfoEntity::getWhichBank, whichBank)
            .eq(ActivityCreditCardUsersInfoEntity::getVersion, version)
            .eq(ActivityCreditCardUsersInfoEntity::getClassify, classify)
            .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }

    /**
     * 获取已申请的银行卡用户信息
     * @param uid
     * @param whichBanks
     * @return
     */
    public List<ActivityCreditCardUsersInfoEntity> getAppliedUsersInfo(Long uid, List<Integer> whichBanks) {
        Wrapper<ActivityCreditCardUsersInfoEntity> wrapper = Wrappers.<ActivityCreditCardUsersInfoEntity>lambdaQuery()
                .in(ActivityCreditCardUsersInfoEntity::getWhichBank, whichBanks
                )
                .eq(ActivityCreditCardUsersInfoEntity::getUid, uid)
                .notIn(ActivityCreditCardUsersInfoEntity::getStatus, Collections.singletonList(
                        ActivityCreditCardUserInfoStatusEnum.STATUS_WAIT.getCode()
                ));
        return this.baseMapper.selectList(wrapper);

    }


    public ActivityCreditCardUsersInfoEntity getOneByUidAndWhichBank(Long uid, String referSn,
                                                                     Integer whichBank) {
        Wrapper<ActivityCreditCardUsersInfoEntity> wrapper = Wrappers.<ActivityCreditCardUsersInfoEntity>lambdaQuery()
                .eq(ActivityCreditCardUsersInfoEntity::getUid, uid)
                .eq(ActivityCreditCardUsersInfoEntity::getReferSn, referSn)
                .eq(ActivityCreditCardUsersInfoEntity::getWhichBank, whichBank)
                .orderByDesc(ActivityCreditCardUsersInfoEntity::getCreatedAt)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }

    public List<ActivityCreditCardUsersInfoEntity> getAllByUidAndReferSn(Long uid, String referSn) {
        Wrapper<ActivityCreditCardUsersInfoEntity> wrapper = Wrappers.<ActivityCreditCardUsersInfoEntity>lambdaQuery()
                .eq(ActivityCreditCardUsersInfoEntity::getUid, uid)
                .eq(ActivityCreditCardUsersInfoEntity::getReferSn, referSn)
                .orderByDesc(ActivityCreditCardUsersInfoEntity::getCreatedAt);
        return this.baseMapper.selectList(wrapper);
    }
}
