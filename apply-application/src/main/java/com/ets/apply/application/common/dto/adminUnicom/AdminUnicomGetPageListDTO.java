package com.ets.apply.application.common.dto.adminUnicom;

import lombok.Data;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;

@Data
public class AdminUnicomGetPageListDTO {

    /**
     * 用户uid
     */
    private Long uid;

    /**
     * 办理手机号码（加密）
     */
    private String phone;

    /**
     * 关联流水号，1申办订单号order_sn
     */
    private String referSn;

    private Integer referType = 1;

    /**
     * 联通订单号
     */
    private String unicomOrderSn;


    /**
     * 号卡订单状态
     */
    private String unicomOrderState;

    /**
     * 用户号卡状态，同步接口状态
     */
    private Integer phoneStatus;

    /**
     * 是否三无
     */
    private Integer isThreeNone;

    /**
     * 更新时间
     */
    private String updatedAtStart;
    private String updatedAtEnd;

    /**
     * 申请时间
     */
    private String applyTimeStart;
    private String applyTimeEnd;

    /**
     * 页码
     */
    @Min(value = 1, message = "当前页码必须大于0")
    private Integer pageNum = 1;

    /**
     * 分页大小
     */
    @Max(value = 100, message = "每页最多100条")
    private Integer pageSize = 20;
}
