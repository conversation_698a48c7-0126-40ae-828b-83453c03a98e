package com.ets.apply.application.controller.frontend;

import com.ets.apply.application.app.business.deviceValuation.DeviceValuationBusiness;
import com.ets.apply.application.common.dto.request.deviceValuation.DoDeviceValuationDTO;
import com.ets.apply.application.common.utils.UserUtil;
import com.ets.apply.application.common.vo.deviceValuation.DeviceValuationSimpleListVO;
import com.ets.apply.application.common.vo.deviceValuation.DoDeviceValuationVO;
import com.ets.apply.application.common.vo.deviceValuation.GetDeviceValuationVO;
import com.ets.common.JsonResult;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping("/frontend/device-valuation")
@RestController
@Slf4j
public class DeviceValuationFrontendController {

    @Autowired
    private DeviceValuationBusiness deviceValuationBusiness;

    @RequestMapping("/get-device-valuation")
    @ResponseBody
    public JsonResult<GetDeviceValuationVO> getDeviceValuation() {
        // 获取用户最新的一条有效的估价
        return JsonResult.ok(deviceValuationBusiness.getDeviceValuation(UserUtil.getUid()));
    }

    @PostMapping("/do-device-valuation")
    @ResponseBody
    public JsonResult<DoDeviceValuationVO> doDeviceValuation(@RequestBody @Valid DoDeviceValuationDTO deviceValuationDTO) {
        return JsonResult.ok(deviceValuationBusiness.doDeviceValuation(UserUtil.getUid(),deviceValuationDTO));
    }


    /**
     * 获取有效估价的简单列表
     */
    @RequestMapping("/get-device-valuation-simple-list")
    @ResponseBody
    public JsonResult<List<DeviceValuationSimpleListVO>> getDeviceValuationSimpleList() {
        return JsonResult.ok(deviceValuationBusiness.getDeviceValuationSimpleList());
    }
}
