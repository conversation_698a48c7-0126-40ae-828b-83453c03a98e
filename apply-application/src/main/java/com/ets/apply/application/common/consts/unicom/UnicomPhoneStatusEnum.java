package com.ets.apply.application.common.consts.unicom;

import com.ets.apply.application.common.vo.SelectOptionsVO;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum UnicomPhoneStatusEnum {

    DEFAULT(0, "默认"),

    OPENED(1, "开通"),

    STATUS_2(2, "申请销号"),
    STATUS_3(3, "欠费停机"),
    STATUS_4(4, "申请预销停机"),
    STATUS_5(5, "欠费半停机"),
    STATUS_6(6, "高额半停机"),
    STATUS_7(7, "欠费停局方半停"),
    STATUS_8(8, "高额半停机加局方半停"),
    STATUS_9(9, "申请停机"),
    STATUS_10(10, "局方停机"),
    STATUS_11(11, "局方半停"),
    STATUS_12(12, "申请停,欠费双停"),
    STATUS_13(13, "欠费半停机局方半停"),
    STATUS_14(14, "挂失停逾期欠费停"),
    STATUS_15(15, "欠费销号"),
    STATUS_16(16, "欠费停机加局方停机"),
    STATUS_17(17, "局方停逾期欠费停"),
    STATUS_18(18, "转网销号停机"),
    STATUS_19(19, "欠费半停机加局方停机"),

    CHANGED(20, "套餐变更");

    private final int code;
    private final String description;

    public static String getDescByCode(int code) {
        for (UnicomPhoneStatusEnum node : UnicomPhoneStatusEnum.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }

    public UnicomPhoneStatusEnum getByCode(int code) {
        for (UnicomPhoneStatusEnum node : UnicomPhoneStatusEnum.values()) {
            if (node.getCode() == code) {
                return node;
            }
        }

        return null;
    }

    public static List<SelectOptionsVO> getSelectOptions() {

        return Stream.of(UnicomPhoneStatusEnum.values())
                .map(item -> new SelectOptionsVO(String.valueOf(item.getCode()), item.getDescription()))
                .collect(Collectors.toList());
    }
}
