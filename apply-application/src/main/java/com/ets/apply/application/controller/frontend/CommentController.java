package com.ets.apply.application.controller.frontend;

import com.ets.apply.application.app.business.CommentBusiness;
import com.ets.apply.application.common.dto.request.comment.CommentSubmitDTO;
import com.ets.apply.application.controller.BaseController;
import com.ets.common.JsonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

@RestController
@RequestMapping("/frontend/comment")
public class CommentController extends BaseController {

    @Autowired
    CommentBusiness commentBusiness;

    @RequestMapping("/submit")
    public JsonResult<?> submit(@RequestBody @Valid CommentSubmitDTO submitDTO) {
        commentBusiness.submit(submitDTO);
        return JsonResult.ok();
    }
}
