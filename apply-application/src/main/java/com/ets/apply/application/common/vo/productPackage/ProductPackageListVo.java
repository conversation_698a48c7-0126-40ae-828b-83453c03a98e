package com.ets.apply.application.common.vo.productPackage;

import com.ets.apply.application.common.consts.productPackage.ProductPackageIsShowAddEnum;
import com.ets.apply.application.common.consts.productPackage.ProductPackageStatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * <p>
 * 商品套餐配置表
 * </p>
 * 商品套餐列表
 * 与ProductPackageGetListVo相比，本vo 才是实际的product_package表的字段,ProductPackageGetListVo是一个tmp的vo
 * @since 2021-11-19
 */
@Data
public class ProductPackageListVo {


    /**
     * 商品套餐流水号
     */
    private String packageSn;


    /**
     * 商品套餐名称
     */
    private String packageName;


    /**
     * 设备费（元）
     */
    private BigDecimal packageFee;

    /**
     * 库存
     */
    private Integer packageStock;

    /**
     * 订单数量
     */
    private Integer orderNums;

    /**
     * 渠道码
     */
    private String source;

    /*
     * 状态 1-有效 2-无效
     */
    private Integer status;

    /**
     * 是否展示
     */
    private Integer isShowAdd;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    private Integer sort;


    public String getStatusStr() {
        return ProductPackageStatusEnum.getDescByCode(status);
    }

    public String getIsShowAddStr() {
        return ProductPackageIsShowAddEnum.getDescByCode(isShowAdd);
    }

}
