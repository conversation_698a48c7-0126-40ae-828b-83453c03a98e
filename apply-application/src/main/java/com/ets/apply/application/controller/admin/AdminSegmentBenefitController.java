package com.ets.apply.application.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.apply.application.app.business.SegmentBenefitBusiness;
import com.ets.apply.application.common.dto.segmentBenefit.*;
import com.ets.apply.application.common.vo.segmentBenefit.SegmentBenefitListVO;
import com.ets.apply.application.common.vo.segmentBenefit.SegmentBenefitLogListVO;
import com.ets.common.JsonResult;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;



@RestController
@RequestMapping("/admin/segment-benefit")
public class AdminSegmentBenefitController {

    @Autowired
    private SegmentBenefitBusiness segmentBenefitBusiness;

    @PostMapping("/get-list")
    public JsonResult<IPage<SegmentBenefitListVO>> getList(@RequestBody @Valid SegmentBenefitListDTO listDTO) {
        return JsonResult.ok(segmentBenefitBusiness.getList(listDTO));
    }

    @PostMapping("/get-log")
    public JsonResult<IPage<SegmentBenefitLogListVO>> getLog(@RequestBody @Valid SegmentBenefitLogListDTO logDTO) {
        return JsonResult.ok(segmentBenefitBusiness.getLog(logDTO));
    }

    @PostMapping("/add")
    public JsonResult<?> add(@RequestBody @Valid SegmentBenefitAddDTO addDTO) {
        segmentBenefitBusiness.add(addDTO);
        return JsonResult.ok();
    }

    @PostMapping("/edit")
    public JsonResult<?> edit(@RequestBody @Valid SegmentBenefitEditDTO editDTO) {
        segmentBenefitBusiness.edit(editDTO);
        return JsonResult.ok();
    }

    @PostMapping("/delete")
    public JsonResult<?> delete(@RequestBody @Valid SegmentBenefitDeleteDTO deleteDTO) {
        segmentBenefitBusiness.delete(deleteDTO.getId());
        return JsonResult.ok();
    }
}
