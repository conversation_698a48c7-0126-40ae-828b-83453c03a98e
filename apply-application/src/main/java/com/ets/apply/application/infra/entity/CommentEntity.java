package com.ets.apply.application.infra.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 用户评论表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("etc_comment")
public class CommentEntity extends BaseEntity<CommentEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户id
     */
    private Long uid;

    /**
     * 车牌号码
     */
    private String plateNo;

    /**
     * 车牌颜色
     */
    private Integer plateColor;

    /**
     * 评星
     */
    private Integer stars;

    /**
     * 评论内容
     */
    private String content;

    /**
     * 匿名状态[0-公开 1-匿名]
     */
    private Integer anonymous;

    /**
     * 使用天数
     */
    private Long usedDay;

    /**
     * 精选状态[0-普通 1-精选]
     */
    private Integer featured;

    /**
     * 分类[1-客车 2-货车]
     */
    private Integer carType;

    /**
     * 状态[0-正常 -1-删除]
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;


}
