package com.ets.apply.application.infra.relation;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.ets.apply.application.infra.entity.OrderOrderEntity;
import com.ets.apply.application.infra.entity.ReviewOrderEntity;
import com.ets.common.base.BaseEntityRelation;

import java.util.function.BiConsumer;

public class OrderBindReviewOrderRelation extends BaseEntityRelation<OrderOrderEntity, ReviewOrderEntity> {

    @Override
    public BiConsumer<OrderOrderEntity, ReviewOrderEntity> getEntityColumn() {
        return OrderOrderEntity::setReviewOrderEntity;
    }

    @Override
    public SFunction<OrderOrderEntity, Object> getMasterColumn() {
        return OrderOrderEntity::getReviewOrderSn;
    }

    @Override
    public SFunction<ReviewOrderEntity, Object> getAffiliatedColumn() {
        return ReviewOrderEntity::getReviewOrderSn;
    }
}
