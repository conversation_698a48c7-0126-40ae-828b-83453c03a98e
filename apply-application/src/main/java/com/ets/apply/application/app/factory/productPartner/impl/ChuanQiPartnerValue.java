package com.ets.apply.application.app.factory.productPartner.impl;

import com.ets.apply.application.app.business.external.ChuanQiBusiness;
import com.ets.apply.application.common.bo.productOrder.ProductOrderShipBO;
import com.ets.apply.application.common.dto.external.ExternalOrderAfterSaleStatusChangeDTO;
import com.ets.apply.application.infra.entity.ProductOrderEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ChuanQiPartnerValue extends ProductPartnerBase {

    @Autowired
    private ChuanQiBusiness chuanQiBusiness;

    public void ship(ProductOrderShipBO productOrderShipBO) {

        chuanQiBusiness.shippedNotify(productOrderShipBO);
    }

    public void afterSaleStatusChange(ProductOrderEntity productOrder, ExternalOrderAfterSaleStatusChangeDTO dto) {
        // 售后完成
        if (dto.getCustomerStatus() == 4) {
            chuanQiBusiness.afterSaleFinishNotify(productOrder, dto);
        }
    }
}
