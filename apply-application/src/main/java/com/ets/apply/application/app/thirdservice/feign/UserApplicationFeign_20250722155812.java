package com.ets.apply.application.app.thirdservice.feign;
import com.ets.apply.application.app.thirdservice.request.user.GetDepositRecordListDTO;
import com.ets.apply.application.app.thirdservice.request.user.UpdateDepositRecordStatusDTO;
import com.ets.apply.application.app.thirdservice.request.user.UpdateUserDepositDTO;
import com.ets.apply.application.app.thirdservice.response.user.UserDepositVO;
import com.ets.apply.application.app.thirdservice.response.user.UsersDepositRecordVO;
import com.ets.apply.application.common.dto.user.GetListByApplyOrderSnDTO;
import com.ets.apply.application.common.vo.user.ChangeWechatApplyVO;
import com.ets.apply.application.common.vo.user.ReservedPhoneVO;
import com.ets.apply.application.common.vo.user.UsersCardsEntityVO;
import com.ets.apply.application.common.vo.user.UsersEntityVO;
import com.ets.common.JsonResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

import com.alibaba.fastjson.JSONObject;

@FeignClient(
        url = "${microUrls.user:http://user-application:20050}",
        name = "UserApplicationFeign"
)
public interface UserApplicationFeign {

    @RequestMapping("/users/getById")
    JsonResult<UsersEntityVO> getById(@RequestParam(value = "uid") Long uid);

    @PostMapping("/usersCards/getListByApplyOrderSn")
    JsonResult<List<UsersCardsEntityVO>> getListByApplyOrderSn(@RequestBody GetListByApplyOrderSnDTO dto);

    @PostMapping("/users/getRealNameByUid")
    JsonResult<JSONObject> getRealNameByUid(@RequestParam(value = "uid") Long uid);

    @PostMapping("/usersPhone/getListBySameReservedPhone")
    JsonResult<List<ReservedPhoneVO>> getListBySameReservedPhone(@RequestParam(value = "applyOrderSn") String applyOrderSn);


    @PostMapping("/changeWechat/getListByUid")
    JsonResult<List<ChangeWechatApplyVO>> getChangeWechatListByUid(@RequestParam(value = "uid") Long uid,
                                                                   @RequestParam(value = "onlyFinish") Boolean onlyFinish);

    @PostMapping("/user-deposit-record/get-list")
    JsonResult<List<UsersDepositRecordVO>> getDepositRecordList(@RequestBody GetDepositRecordListDTO dto);

    /**
     * 更新货车保证金记录状态
     * @param dto 更新参数
     * @return 更新结果
     */
    @PostMapping("/user-deposit-record/update-status")
    JsonResult<Void> updateDepositRecordStatus(@RequestBody UpdateDepositRecordStatusDTO dto);

    @RequestMapping("/userDeposit/get-one-by-user-deposit-sn")
    JsonResult<UserDepositVO> getUserDepositByUserDepositSn(@RequestParam(value = "userDepositSn") String userDepositSn);

    /**
     * 更新货车保证金账户信息（状态、支出金额等）
     * @param dto 更新参数
     * @return 更新结果
     */
    @PostMapping("/userDeposit/update")
    JsonResult<Void> updateUserDeposit(@RequestBody UpdateUserDepositDTO dto);

    @PostMapping("/deposit/record/list")
    JsonResult<List<UsersDepositRecordVO>> getDepositRecordList(@RequestBody GetDepositRecordListDTO dto);

    @PostMapping("/deposit/update")
    JsonResult<Void> updateUserDeposit(@RequestBody UpdateUserDepositDTO dto);

    @GetMapping("/deposit/get_by_sn")
    JsonResult<UserDepositVO> getUserDepositByUserDepositSn(@RequestParam("userDepositSn") String userDepositSn);
    
    @PostMapping("/deposit/list_by_sns")
    JsonResult<List<UserDepositVO>> getUserDepositsByUserDepositSns(@RequestBody List<String> userDepositSns);
    
    @PostMapping("/deposit/record/batch_update_status")
    JsonResult<Void> batchUpdateDepositRecordStatus(@RequestParam("ids") List<Integer> ids, @RequestParam("status") Integer status);
    
    @PostMapping("/deposit/batch_update")
    JsonResult<Void> batchUpdateUserDeposit(@RequestBody List<UpdateUserDepositDTO> dtos);
}
