package com.ets.apply.application.app.factory.task;

import com.ets.apply.application.common.dto.task.TaskRecordDTO;
import com.ets.apply.application.infra.entity.TaskRecordEntity;

public interface ITask {
    void addAndPush(TaskRecordDTO taskRecordDTO);
    void beforeExec(TaskRecordEntity taskRecord,Boolean isDirect);
    void execute(String taskSn,Boolean isDirect);
    //子类实际执行
    void childExec(TaskRecordEntity taskRecord);

    void afterExec(TaskRecordEntity taskRecord, Integer status, String msg);
}
