package com.ets.apply.application.infra.service;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.apply.application.common.consts.productOrder.ChannelProductOrderStatusEnum;
import com.ets.apply.application.common.consts.productOrder.ProductOrderStatusEnum;
import com.ets.apply.application.common.consts.productOrder.ProductPackageEnum;
import com.ets.apply.application.common.consts.productOrder.ReferTypeEnum;
import com.ets.apply.application.common.dto.productOrder.ProductOrderByOrderSnDTO;
import com.ets.apply.application.infra.entity.ProductOrderEntity;
import com.ets.apply.application.infra.mapper.ProductOrderMapper;
import com.ets.apply.feign.request.GetFinishOrdersDTO;
import com.ets.common.ToolsHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 商品订单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-19
 */
@Service
@DS("db-apply-proxy")
public class ProductOrderService extends BaseService<ProductOrderMapper, ProductOrderEntity> {

    public Boolean isExistThirdOrderSn(String thirdOrderSn) {
        ProductOrderEntity entity = getOneByColumn(thirdOrderSn, ProductOrderEntity::getThirdOrderSn);

        return entity != null;
    }

    public Boolean isExistNormalPlateNo(String plateNO) {

        LambdaQueryWrapper<ProductOrderEntity> wrapper = new LambdaQueryWrapper<>();

        wrapper.eq(ProductOrderEntity::getPlateNo, plateNO)
                .notIn(ProductOrderEntity::getOrderStatus, Arrays.asList(
                        ProductOrderStatusEnum.CLOSED.getCode(),
                        ProductOrderStatusEnum.ACTIVATED.getCode()
                ));

        ProductOrderEntity entity = getOneByWrapper(wrapper);

        return entity != null;
    }

    // todo 性能差
    public Long getWarrantyReceivedCount() {
        LambdaQueryWrapper<ProductOrderEntity> wrapper = new LambdaQueryWrapper<>();

        wrapper.in(ProductOrderEntity::getPackageSn, Arrays.asList(
                ProductPackageEnum.WARRANTY.getCode(), ProductPackageEnum.WARRANTY_TOLL.getCode())
        ).in(ProductOrderEntity::getOrderStatus, Arrays.asList(
                ProductOrderStatusEnum.SHIPPED.getCode(),
                ProductOrderStatusEnum.RECEIVED.getCode(),
                ProductOrderStatusEnum.ACTIVATED.getCode()
        ));

        return getBaseMapper().selectCount(wrapper);
    }


    public void channelPaySuccess(String productOrderSn, String paymentSn, String paidTime, String transactionId) {
        LambdaUpdateWrapper<ProductOrderEntity> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(ProductOrderEntity::getProductOrderSn, productOrderSn)
                .set(ProductOrderEntity::getOrderStatus, ChannelProductOrderStatusEnum.PAID.getCode())
                .set(ProductOrderEntity::getPaymentSn, paymentSn)
                .set(ProductOrderEntity::getPaidTime, paidTime)
                .set(ProductOrderEntity::getTransactionId, transactionId);
        updateByWrapper(lambdaUpdateWrapper);
    }

    public void refundChannelProductOrder(String productOrderSn,String refundSn,Integer orderStatus,String refundAt){
        LambdaUpdateWrapper<ProductOrderEntity> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(ProductOrderEntity::getProductOrderSn, productOrderSn)
                .set(ProductOrderEntity::getOrderStatus, orderStatus)
                .set(ProductOrderEntity::getRefundSn, refundSn)
                .set(ProductOrderEntity::getRefundAt, refundAt)
                ;
        updateByWrapper(lambdaUpdateWrapper);
    }

    public void refundingChannelProductOrder(String productOrderSn) {
        LambdaUpdateWrapper<ProductOrderEntity> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(ProductOrderEntity::getProductOrderSn, productOrderSn)
                .set(ProductOrderEntity::getOrderStatus, ChannelProductOrderStatusEnum.REFUNDING.getCode())
        ;
        updateByWrapper(lambdaUpdateWrapper);
    }


    /**
     * 通过第三方订单号查找订单
     */
    public ProductOrderEntity findByThirdOrderSn(String thirdOrderSn) {
        return getOneByColumn(thirdOrderSn, ProductOrderEntity::getThirdOrderSn);
    }

    /**
     * 通过申办订单号查询最后一条有效的商品订单
     */
    public ProductOrderEntity findByApplyOrderSn(String applyOrderSn) {
        LambdaQueryWrapper<ProductOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductOrderEntity::getApplyOrderSn, applyOrderSn)
            .notIn(ProductOrderEntity::getOrderStatus, Arrays.asList(
                ProductOrderStatusEnum.CLOSED.getCode()
            ))
            .orderByDesc(ProductOrderEntity::getCreatedAt);

        return getOneByWrapper(wrapper);
    }

    /**
     * 发票服务获取已完成订单信息
     * @param dto
     * @return
     */
    public List<ProductOrderEntity> getFinishOrders(GetFinishOrdersDTO dto,List<String> excludePackageSns) {
        Wrapper<ProductOrderEntity> wrapper = Wrappers.<ProductOrderEntity>lambdaQuery()
                .select(ProductOrderEntity::getProductOrderSn, ProductOrderEntity::getThirdOrderSn, ProductOrderEntity::getPaidTime,
                        ProductOrderEntity::getPaidAmount, ProductOrderEntity::getPlateNo, ProductOrderEntity::getPlateColor,ProductOrderEntity::getUid
                )
                .eq(ProductOrderEntity::getUid, dto.getUid())
                .eq(ProductOrderEntity::getOrderStatus,ProductOrderStatusEnum.ACTIVATED.getCode())
                .eq(StringUtils.isNotEmpty(dto.getSource()), ProductOrderEntity::getSource, dto.getSource())
                .ne(ProductOrderEntity::getPaymentSn,"")
                .notIn(CollectionUtil.isNotEmpty(excludePackageSns),ProductOrderEntity::getPackageSn,excludePackageSns)
                ;
        return this.baseMapper.selectList(wrapper);
    }

    public ProductOrderEntity findByProductOrderSnAndUid(String productOrderSn, Long uid) {
        LambdaQueryWrapper<ProductOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductOrderEntity::getProductOrderSn, productOrderSn)
                .eq(ProductOrderEntity::getUid, uid)
        ;
        return getOneByWrapper(wrapper);
    }


    public ProductOrderEntity findByOrderSnAndSource(ProductOrderByOrderSnDTO dto) {
        LambdaQueryWrapper<ProductOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductOrderEntity::getApplyOrderSn, dto.getApplyOrderSn())
                .eq(StringUtils.isNotEmpty(dto.getSource()), ProductOrderEntity::getSource, dto.getSource())
                .orderByDesc(ProductOrderEntity::getCreatedAt)
        ;
        return getOneByWrapper(wrapper);
    }
    /*
     * 商品订单发起售后,取消售后，完成售后
     */
    public void markOrderOnAftersale(String productOrderSn,Integer orderStatus){
        LambdaUpdateWrapper<ProductOrderEntity> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(ProductOrderEntity::getProductOrderSn, productOrderSn)
                .set(ProductOrderEntity::getOrderStatus, orderStatus)
                .set(ProductOrderEntity::getUpdatedAt, LocalDateTime.now())
        ;
        updateByWrapper(lambdaUpdateWrapper);
    }

    /**
     * 设置关联值，用于信用卡
     */
    public void setReferValue(String productOrderSn,Integer referValue){
        LambdaUpdateWrapper<ProductOrderEntity> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(ProductOrderEntity::getProductOrderSn, productOrderSn)
                .set(ProductOrderEntity::getReferValue,referValue)
                .set(ProductOrderEntity::getUpdatedAt, LocalDateTime.now())
        ;
        updateByWrapper(lambdaUpdateWrapper);
    }

    public List<ProductOrderEntity> getValidListBySns(List<String> snList) {

        LambdaQueryWrapper<ProductOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ProductOrderEntity::getProductOrderSn, snList)
                .notIn(ProductOrderEntity::getOrderStatus,
                        Arrays.asList(ProductOrderStatusEnum.CLOSED.getCode())
                );

        return getListByWrapper(wrapper);
    }

    public ProductOrderEntity getByProductOrderSn(String productOrderSn) {

        LambdaQueryWrapper<ProductOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductOrderEntity::getProductOrderSn, productOrderSn);

        return getOneByWrapper(wrapper);
    }

    public ProductOrderEntity getExternalCanBindOrder(String source, String phone, String plateNo) {

        LambdaQueryWrapper<ProductOrderEntity> wrapper = new LambdaQueryWrapper<>();

        wrapper.eq(ProductOrderEntity::getSource, source)
                .and(qr -> qr.eq(ProductOrderEntity::getEtcPhone, phone)
                        .or()
                        .eq(ProductOrderEntity::getSendPhone, phone)
                    )
                .eq(ProductOrderEntity::getPlateNo, plateNo)
                .in(ProductOrderEntity::getOrderStatus, Arrays.asList(
                        ProductOrderStatusEnum.PAID.getCode(),
                        ProductOrderStatusEnum.ACTIVATED.getCode(),
                        ProductOrderStatusEnum.SHIPPED.getCode(),
                        ProductOrderStatusEnum.RECEIVED.getCode()
                ))
                .orderByDesc(ProductOrderEntity::getCreatedAt);

        return getOneByWrapper(wrapper);
    }

    @Override
    public int updateByWrapper(LambdaUpdateWrapper<ProductOrderEntity> lambdaUpdateWrapper) {
        lambdaUpdateWrapper.set(ProductOrderEntity::getUpdatedAt, ToolsHelper.getDateTime());

        return super.updateByWrapper(lambdaUpdateWrapper);
    }

    /*
     * 根据创建时间统计数量
     */
    public Long countByTime(String source,LocalDateTime startDateTime) {
        LambdaQueryWrapper<ProductOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductOrderEntity::getSource,source)
                .gt(ProductOrderEntity::getCreatedAt, startDateTime)
        ;
        return this.baseMapper.selectCount(wrapper);
    }

    /**
     * 根据商品订单号和第三方订单号获取商品订单
     */
    public ProductOrderEntity getOneByProductOrderSnAndThirdOrderSn(String productOrderSn, String thirdOrderSn) {
        LambdaQueryWrapper<ProductOrderEntity> wrapper = new LambdaQueryWrapper<>();

        wrapper.eq(ProductOrderEntity::getProductOrderSn, productOrderSn)
                .eq(ProductOrderEntity::getThirdOrderSn, thirdOrderSn)
        ;
        return getOneByWrapper(wrapper);
    }

    public ProductOrderEntity getValidThirdPartnerOrder(String thirdOrderSn, Integer referValue) {

        LambdaQueryWrapper<ProductOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductOrderEntity::getThirdOrderSn, thirdOrderSn)
                .eq(ProductOrderEntity::getReferValue, referValue)
                .eq(ProductOrderEntity::getReferType, ReferTypeEnum.THIRD_PARTNER_TYPE.getCode())
                .ne(ProductOrderEntity::getOrderStatus, ProductOrderStatusEnum.CLOSED.getCode());

        return getOneByWrapper(wrapper);
    }

    public ProductOrderEntity getLastThirdPartnerOrder(String thirdOrderSn, Integer referValue) {

        LambdaQueryWrapper<ProductOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductOrderEntity::getThirdOrderSn, thirdOrderSn)
                .eq(ProductOrderEntity::getReferValue, referValue)
                .eq(ProductOrderEntity::getReferType, ReferTypeEnum.THIRD_PARTNER_TYPE.getCode())
                .orderByDesc(ProductOrderEntity::getCreatedAt);

        return getOneByWrapper(wrapper);
    }

    public void closeOrder(String productOrderSn, String reason) {

        LambdaUpdateWrapper<ProductOrderEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ProductOrderEntity::getProductOrderSn, productOrderSn)
                .set(ProductOrderEntity::getOrderStatus, ProductOrderStatusEnum.CLOSED.getCode())
                .set(ProductOrderEntity::getCancelReason, reason);

        updateByWrapper(wrapper);
    }
}
