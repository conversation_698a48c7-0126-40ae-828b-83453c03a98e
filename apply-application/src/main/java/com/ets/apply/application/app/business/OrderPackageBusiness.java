package com.ets.apply.application.app.business;

import com.ets.apply.application.infra.entity.OrderOrderEntity;
import com.ets.apply.application.infra.entity.ProductPackageEntity;
import com.ets.apply.application.infra.service.ProductPackageService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class OrderPackageBusiness {

    @Autowired
    private ProductPackageService productPackageService;

    public ProductPackageEntity getOrderProductPackage(OrderOrderEntity orderEntity) {

        if (StringUtils.isEmpty(orderEntity.getPackageSn())) {
            return null;
        }

        return productPackageService.getBySn(orderEntity.getPackageSn());
    }
}
