package com.ets.apply.application.app.consumer;

import com.ets.apply.application.app.business.ChannelProductOrderBusiness;
import com.ets.starter.base.BaseConsumer;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class RefundCompleteConsumer extends BaseConsumer implements MessageListenerConcurrently {

    @Autowired
    private ChannelProductOrderBusiness channelProductOrderBusiness;
    /**
     * 默认msg里只有一条消息，可以通过设置consumeMessageBatchMaxSize参数来批量接收消息
     * 不要抛异常，如果没有return CONSUME_SUCCESS ，consumer会重新消费该消息，直到return CONSUME_SUCCESS
     * @param msgList List
     * @param consumeConcurrentlyContext ConsumeConcurrentlyContext
     * @return ConsumeConcurrentlyStatus
     */
    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgList, ConsumeConcurrentlyContext consumeConcurrentlyContext) {

        String body = getMessageBody(msgList);

        log.info("MQ-MESSAGE-PayRefundConsumer:" + body);

        try {
            channelProductOrderBusiness.refundComplete(body);
        } catch (Exception e) {

            log.error("退款回调处理失败：" + e.getMessage());
        }

        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }
}