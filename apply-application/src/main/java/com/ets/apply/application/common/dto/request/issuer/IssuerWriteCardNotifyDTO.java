package com.ets.apply.application.common.dto.request.issuer;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class IssuerWriteCardNotifyDTO {

    @JsonProperty(value = "order_sn")
    private String orderSn;
    private String steps = "writeCardNotify";
    @JsonProperty(value = "card_no")
    private String cardNo;
    @JsonProperty(value = "plate_no")
    private String plateNo;
    @JsonProperty(value = "plate_color")
    private Integer plateColor;
    @JsonProperty(value = "write_card_amount")
    private Integer writeCardAmount;
    @JsonProperty(value = "write_card_after_amount")
    private Integer writeCardAfterAmount;
    @JsonProperty(value = "write_card_accept_no")
    private String writeCardAcceptNo;
    @JsonProperty(value = "term_id")
    private String termId;
    @JsonProperty(value = "trade_no_before")
    private String tradeNoBefore;
    @JsonProperty(value = "trade_no_after")
    private String tradeNoAfter;
    @JsonProperty(value = "tac_no")
    private String tacNo;
    @JsonProperty(value = "callback_url")
    private String callbackUrl;
}
