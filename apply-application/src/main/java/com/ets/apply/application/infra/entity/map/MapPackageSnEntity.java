package com.ets.apply.application.infra.entity.map;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ets.apply.application.infra.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 映射模块关联的产品包
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("map_package_sn")
public class MapPackageSnEntity extends BaseEntity<MapPackageSnEntity> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 模块编码
     */
    private String objectType;
    private Integer objectId;
    /**
     * 类型：普通normal,指定assign
     */
    private String packageSn;

    /**
     * 映射规则json，包含组合combine_rule,rule_items
     */
    private String combineRules;
    private String itemRules;
    /**
     * 状态（有效1，无效2）
     */
    private Integer status;
    private Integer labelId;
    /**
     * 排序，序号越大排前面
     */
    private Integer sort;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    private String operator;


}
