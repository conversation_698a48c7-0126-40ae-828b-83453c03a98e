package com.ets.apply.application.app.disposer;

import java.time.LocalDateTime;
import java.util.List;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ets.apply.application.common.bo.AfterSalesReviewAutoAuditBO;
import com.ets.apply.application.common.consts.aftersalesreviews.AftersalesReviewsDataTypeEnum;
import com.ets.apply.application.common.consts.aftersalesreviews.AftersalesReviewsStatusEnum;
import com.ets.apply.application.infra.entity.aftersalesreview.AftersalesReviews;
import com.ets.apply.application.infra.entity.aftersalesreview.AftersalesReviewsVehicles;
import com.ets.apply.application.infra.service.AftersalesReviewsService;
import com.ets.apply.application.infra.service.AftersalesReviewsVehiclesService;
import com.ets.common.ToolsHelper;
import com.ets.common.queue.BaseDisposer;

import lombok.NoArgsConstructor;

@NoArgsConstructor
@Component(value = "afterSalesReviewAutoAuditJobBean")
public class AfterSalesReviewAutoAuditDisposer extends BaseDisposer {

    @Autowired
    private AftersalesReviewsService aftersalesReviewsService;

    @Autowired
    private AftersalesReviewsVehiclesService aftersalesReviewsVehiclesService;

    public AfterSalesReviewAutoAuditDisposer(Object params) {
        super(params);
    }

    @Override
    public String getJobBeanName() {
        return "afterSalesReviewAutoAuditJobBean";
    }

    @Override
    public void execute(Object content) {
        AfterSalesReviewAutoAuditBO bo = getParamsObject(content, AfterSalesReviewAutoAuditBO.class);

        // 获取审核单数据
        AftersalesReviews aftersalesReviews = aftersalesReviewsService.getByReviewSn(bo.getReviewSn());
        if (aftersalesReviews == null) {
            ToolsHelper.throwException("售后审核单记录不存在");
        }

        // 获取行驶证数据
        List<AftersalesReviewsVehicles> vehiclesList = aftersalesReviewsVehiclesService.getByReviewSn(aftersalesReviews.getReviewSn());
        if (ObjectUtils.isEmpty(vehiclesList)) {
            ToolsHelper.throwException("行驶证数据不存在");
        }


        // 获取审核资料和申办资料的数据
        AftersalesReviewsVehicles reviewData = vehiclesList.stream()
            .filter(vehicle -> AftersalesReviewsDataTypeEnum.REVIEW_DATA.getValue().equals(vehicle.getDataType()))
            .findFirst()
            .orElse(null);
        
        AftersalesReviewsVehicles orderData = vehiclesList.stream()
            .filter(vehicle -> AftersalesReviewsDataTypeEnum.ORDER_DATA.getValue().equals(vehicle.getDataType()))
            .findFirst()
            .orElse(null);

        if (ObjectUtils.isEmpty(reviewData) || ObjectUtils.isEmpty(orderData)) {
            ToolsHelper.throwException("行驶证数据不完整，需要同时存在审核资料和申办资料的数据");
        }

        // 判断是否可以自动审核
            
        // 对比行驶证数据是否一致
        boolean isDataMatch = compareVehicleData(reviewData, orderData);
        
        if (isDataMatch) {
            // 更新审核状态为通过
            aftersalesReviews.setReviewStatus(AftersalesReviewsStatusEnum.APPROVED.getValue());
            aftersalesReviews.setReviewTime(LocalDateTime.now());
            aftersalesReviews.setAutoAudit(1);
            aftersalesReviews.set
            aftersalesReviews.setReviewRemark("自动审核通过");
            aftersalesReviewsService.updateById(aftersalesReviews);
        } else {
            ToolsHelper.throwException("行驶证数据不一致，无法自动审核通过");
        }


    }
}
