package com.ets.apply.application.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.apply.application.app.business.ChangePhoneBusiness;
import com.ets.apply.application.common.dto.changePhone.ChangePhoneCreateDTO;
import com.ets.apply.application.common.dto.intensive.IntensiveChangePhoneListDTO;
import com.ets.apply.application.common.vo.intensive.IntensiveChangePhoneListVO;
import com.ets.common.JsonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

@RestController
@RequestMapping("/admin/changePhone")
public class ChangePhoneController {

    @Autowired
    private ChangePhoneBusiness changePhoneBusiness;

    @PostMapping("/getIntensiveList")
    public JsonResult<IPage<IntensiveChangePhoneListVO>> getIntensiveList(@RequestBody @Valid IntensiveChangePhoneListDTO listDTO) {
        return JsonResult.ok(changePhoneBusiness.getIntensiveList(listDTO));
    }

    @PostMapping("/create")
    public JsonResult<?> create(@RequestBody @Valid ChangePhoneCreateDTO createDTO) {
        changePhoneBusiness.create(createDTO);
        return JsonResult.ok();
    }
}
