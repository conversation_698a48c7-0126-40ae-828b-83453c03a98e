package com.ets.apply.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ets.apply.application.infra.entity.StatisticDataLogEntity;
import com.ets.apply.application.infra.mapper.StatisticDataLogMapper;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <p>
 * 卡片种类 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-07
 */
@Service
@DS("db-etc")
public class StatisticDataLogService extends BaseService<StatisticDataLogMapper, StatisticDataLogEntity> {

    public Integer getLastNums(Integer module, String type, LocalDateTime startAt, LocalDateTime endAt) {
        LambdaQueryWrapper<StatisticDataLogEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StatisticDataLogEntity::getModule, module)
                .eq(StatisticDataLogEntity::getType,type)
                .ge(StatisticDataLogEntity::getStartAt,startAt )
                .le(StatisticDataLogEntity::getEndAt,endAt)
                .orderByDesc(StatisticDataLogEntity::getEndAt);
        StatisticDataLogEntity entity = this.getOneByWrapper(wrapper);
        return (entity != null)?entity.getNums():0;
    }

    public StatisticDataLogEntity getLastRecord(Integer module, String type, LocalDateTime startAt, LocalDateTime endAt) {
        LambdaQueryWrapper<StatisticDataLogEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StatisticDataLogEntity::getModule, module)
                .eq(StatisticDataLogEntity::getType,type)
                .ge(StatisticDataLogEntity::getStartAt,startAt )
                .le(StatisticDataLogEntity::getEndAt,endAt)
                .orderByDesc(StatisticDataLogEntity::getEndAt);
        return this.getOneByWrapper(wrapper);
    }
}
