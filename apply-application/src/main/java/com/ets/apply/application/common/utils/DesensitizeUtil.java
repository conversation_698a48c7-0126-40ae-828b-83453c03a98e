package com.ets.apply.application.common.utils;

import com.ets.apply.application.common.consts.idCard.IdCardEnum;
import com.ets.common.ToolsHelper;
import org.apache.commons.lang3.StringUtils;

public class DesensitizeUtil {

    public static String phone(String phone) {

        if (StringUtils.isEmpty(phone)) {
            return "";
        }

        return ToolsHelper.desensitize(phone, 4, 4);
    }

    public static String name(String name) {

        if (StringUtils.isEmpty(name)) {
            return "";
        }

        return ToolsHelper.desensitize(
                name,
                2,
                Math.max(1, name.length() - 2)
        );
    }

    public static String idCard(String number, Integer idCardType) {

        if (StringUtils.isEmpty(number)) {
            return "";
        }

        if (idCardType.equals(IdCardEnum.ID.getCode())) {

            return ToolsHelper.desensitize(number, 5, 10);

        } else {

            return ToolsHelper.desensitize(number, 3, number.length() - 4);
        }
    }
}
