package com.ets.apply.application.app.thirdservice.fallback;

import com.alibaba.fastjson.JSONObject;
import com.ets.apply.application.app.thirdservice.feign.CallGoodsApplication;
import com.ets.apply.application.app.thirdservice.response.goods.CheckStallSkuAttrVO;
import com.ets.apply.application.common.bo.external.ExternalCreateCustomerBO;
import com.ets.apply.application.common.bo.external.ExternalSubmitSendBackBO;
import com.ets.apply.application.common.bo.goods.GetSkuByAttrBO;
import com.ets.apply.application.common.bo.orderCenter.OrderCenterApplyBO;
import com.ets.apply.application.common.dto.goods.OrderCenterUpdateDTO;
import com.ets.apply.application.common.dto.request.goods.CheckStallSkuAttrDTO;
import com.ets.common.JsonResult;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;

@Component
public class CallJavaGoodsFallbackFactory implements FallbackFactory<CallGoodsApplication> {

    @Override
    public CallGoodsApplication create(Throwable cause) {
        return new CallGoodsApplication() {

            @Override
            public JsonResult<Object> updateInfo(OrderCenterUpdateDTO dto) {
                return JsonResult.error("请求java goods 服务失败: " + cause.getMessage());
            }

            @Override
            public String getCustomerServiceOrder(@RequestParam(value = "businessSn") String businessSn, @RequestParam(value = "serviceType") Integer serviceType) {

                return JsonResult.error("请求java goods 服务失败: " + cause.getMessage()).toString();
            }
            @Override
            public String orderCenterUpdateInfo(@RequestBody OrderCenterApplyBO dto) {

                return JsonResult.error("请求java goods 服务失败: " + cause.getMessage()).toString();
            }
            @Override
            public JsonResult<JSONObject>  getRefundNotCancelResult(@RequestParam(value = "goodsOrderSn") String goodsOrderSn, @RequestParam(value = "referSn") String referSn) {

                return JsonResult.error("请求java goods getRefundNotCancelResult 服务失败: " + cause.getMessage());
            }
            @Override
            public JsonResult<JSONObject>  getSkuByAttr(@RequestBody GetSkuByAttrBO dto) {

                return JsonResult.error("请求java goods recommendEtcByGoodsAttr 服务失败: " + cause.getMessage());
            }
            @Override
            public JsonResult<JSONObject> refundNotCancel(String goodsOrderSn, String reason, BigDecimal amount, String referSn, Boolean isBackCoupon) {
                return JsonResult.error("请求java goods 退款不取消接口失败：" + cause.getMessage());
            }

            @Override
            public JsonResult<CheckStallSkuAttrVO> checkStallSkuAttr(CheckStallSkuAttrDTO dto) {
                return JsonResult.error("请求java goods 校验sku接口失败：" + cause.getMessage());
            }

            @Override
            public JsonResult<JSONObject> customerServiceCreateOrder(ExternalCreateCustomerBO dto) {
                return JsonResult.error("请求java goods 创建商品售后单接口失败：" + cause.getMessage());
            }

            @Override
            public JsonResult<JSONObject> submitSendBack(ExternalSubmitSendBackBO dto) {
                return JsonResult.error("请求java goods 提交商品售后寄回接口失败：" + cause.getMessage());
            }

        };
    }
}