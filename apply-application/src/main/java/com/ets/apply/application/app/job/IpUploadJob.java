package com.ets.apply.application.app.job;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ets.apply.application.app.disposer.UserIpAddressUploadDisposer;
import com.ets.apply.application.common.bo.UserIpAddressUploadBO;
import com.ets.apply.application.infra.entity.UserIp;
import com.ets.apply.application.infra.service.UserIpService;
import com.ets.starter.queue.QueueDefault;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

@Component
public class IpUploadJob {

    @Autowired
    private QueueDefault queueDefault;

    @Autowired
    private UserIpService userIpService;

    @XxlJob("historyUserIpLUploadHandler")
    public ReturnT<String> historyUserIpLUploadHandler(String param) {
        int dealNum = Integer.parseInt(param);
        if (dealNum <= 0) {
            XxlJobLogger.log("历史用户ip数量不能小于0");
            return ReturnT.FAIL;
        }
        if (dealNum > 20000) {
            XxlJobLogger.log("历史用户ip数量不能大于20000");
            return ReturnT.FAIL;
        }

        // 查询表数据
        LambdaQueryWrapper<UserIp> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserIp::getStatus, 0)
                .last("limit " + dealNum);
        List<UserIp> userIpList = userIpService.getListByWrapper(queryWrapper);

        if (ObjectUtils.isNotEmpty(userIpList)) {
            XxlJobLogger.log("历史用户ip数量：" + userIpList.size());

            userIpList.forEach(v -> {
                // 114代表补充上报
                UserIpAddressUploadBO bo = new UserIpAddressUploadBO();
                bo.setActionId(114);
                bo.setIp(v.getIp());
                bo.setUid(v.getUid());
                queueDefault.push(new UserIpAddressUploadDisposer(bo));

                // 更新为已处理
                LambdaUpdateWrapper<UserIp> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(UserIp::getId, v.getId())
                        .set(UserIp::getStatus, 1)
                        .set(UserIp::getUpdatedAt, LocalDateTime.now());
                userIpService.updateByWrapper(updateWrapper);
            });
        }

        return ReturnT.SUCCESS;
    }
}
