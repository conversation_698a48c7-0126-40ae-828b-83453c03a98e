package com.ets.apply.application.app.thirdservice.feign;

import com.ets.apply.application.app.thirdservice.fallback.CallCiticFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

/**
 * 调用中信银行接口
 */
@FeignClient(
        url = "${microUrls.third-interface:http://third-interface-main:22020}",
        name = "third-interface",  fallbackFactory = CallCiticFallbackFactory.class)
public interface CallCiticFeign {

    @PostMapping("/api/citic/statusQuery")
    String statusQuery(@RequestBody Map<String, Object> map);

}
