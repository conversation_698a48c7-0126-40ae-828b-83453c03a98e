package com.ets.apply.application.controller.third;

import cn.hutool.extra.validation.BeanValidationResult;
import cn.hutool.extra.validation.ValidationUtil;
import com.ets.apply.application.app.business.ProductOrderBusiness;
import com.ets.apply.application.app.business.ThirdInterfaceLogBusiness;
import com.ets.apply.application.app.business.wecar.WecarBusiness;
import com.ets.apply.application.common.dto.request.ThirdInterfaceLogDTO;
import com.ets.apply.application.common.dto.request.wecar.*;
import com.ets.apply.application.common.vo.wecar.*;
import com.ets.common.BeanHelper;
import com.ets.common.BizException;
import com.ets.common.JsonResult;
import com.ets.common.ToolsHelper;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@RequestMapping("/third/wecar")
@RefreshScope
@RestController
@Slf4j
public class WecarController {

    @Autowired
    private WecarBusiness wecarBusiness;
    @Autowired
    private ThirdInterfaceLogBusiness thirdInterfaceLogBusiness;

    @Autowired
    private ProductOrderBusiness productOrderBusiness;


    @PostMapping("/getProductList")
    public JsonResult<List<WecarProductVo>> getProductList() throws Exception {
        return JsonResult.ok(productOrderBusiness.getProductListByCompanyId("wecar"));
    }
    /*
     * 创建商城订单
     */
    @PostMapping("/addOrder")
    public JsonResult<WecarAddOrderVo> addOrder(@RequestBody @Valid WecarAddOrderDTO dto) throws Exception {
        return JsonResult.ok(wecarBusiness.addOrder(dto));
    }

    /*
     * ⽀付/退款结果通知
     */
    @PostMapping("/notifyPaidOrRefund")
    public JsonResult<Boolean> notifyPaidOrRefund(@RequestBody @Valid WecarNotifyPaidOrRefundDTO dto) throws Exception {
        //补充日志记录
        ThirdInterfaceLogDTO thirdInterfaceLogDTO = new ThirdInterfaceLogDTO();
        thirdInterfaceLogDTO.setLogMethod("wecar:notifyPaidOrRefund");
        thirdInterfaceLogDTO.setLogParams(dto.toString());
        thirdInterfaceLogDTO.setLogRequest("notifyPaidOrRefund");
        thirdInterfaceLogDTO.setLogRespone("");
        thirdInterfaceLogBusiness.addLog(thirdInterfaceLogDTO);

        switch (dto.getNotifyType()){
            case 0:
                WecarNotifyPaidDTO wecarNotifyPaidDTO = BeanHelper.copy(WecarNotifyPaidDTO.class,dto);

                BeanValidationResult result  = ValidationUtil.warpValidate(wecarNotifyPaidDTO);
                if (! result.isSuccess()) {
                    String errorMsg = result.getErrorMessages().stream().map(BeanValidationResult.ErrorMessage::getMessage).collect(Collectors.joining(";"));

                    ToolsHelper.throwException(errorMsg);
                }

                wecarBusiness.notifyPaid(wecarNotifyPaidDTO);
                break;
            case 1:
                WecarNotifyRefundDTO wecarNotifyRefundDTO = BeanHelper.copy(WecarNotifyRefundDTO.class,dto);

                BeanValidationResult validateResult  = ValidationUtil.warpValidate(wecarNotifyRefundDTO);
                if (! validateResult.isSuccess()) {
                    String errorMsg = validateResult.getErrorMessages().stream().map(BeanValidationResult.ErrorMessage::getMessage).collect(Collectors.joining(";"));

                    ToolsHelper.throwException(errorMsg);
                }

                wecarBusiness.notifyRefund(wecarNotifyRefundDTO);
                break;
            default:
                ToolsHelper.throwException("通知类型不正确");
                break;
        }
        return JsonResult.ok(true);
    }

    /*
     * 创建商城订单
     */
    @PostMapping("/getOrderList")
    public JsonResult<List<WecarOrderVo>> getOrderList(@RequestBody @Valid WecarGetOrderListDTO dto){
        return JsonResult.ok(wecarBusiness.getOrderList(dto));
    }

    /*
     * 创建商城订单
     */
    @PostMapping("/getOrderInfo")
    public JsonResult<WecarOrderInfoVo> getOrderInfo(@RequestBody @Valid WecarGetOrderInfoDTO dto){
        return JsonResult.ok(wecarBusiness.getOrderInfo(dto));
    }
    /*
     * 取消商城订单
     */
    @PostMapping("/cancelOrder")
    public JsonResult<WecarCancelOrderVo> cancelOrder(@RequestBody @Valid WecarCancelOrderDTO dto) throws BizException, JsonProcessingException {
        return JsonResult.ok(wecarBusiness.cancelOrder(dto));
    }
    /*
     * 修改商城订单
     */
    @PostMapping("/modifyOrder")
    public JsonResult<Boolean> modifyOrder(@RequestBody @Valid WecarModifyOrderDTO dto){
        return JsonResult.ok(wecarBusiness.modifyOrder(dto));
    }
}
