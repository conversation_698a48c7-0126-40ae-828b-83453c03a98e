package com.ets.apply.application.infra.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("logistic_order")
public class LogisticOrderEntity extends BaseEntity<LogisticOrderEntity> {

    private static final long serialVersionUID = 1L;

    @TableId
    private String logisticOrderSn;

    /**
     * 对应的订单order_sn
     */
    private String orderSn;

    private Long uid;

    /**
     * 1、未发货 2 待揽收 3、在途 4、送货中、5、已签收、6、拒收、7、异常件
     */
    private Integer status;

    private Integer isCanceled;

    /**
     * 1、待推送 2、推送中 3、推送成功 4、无需推送
     */
    private Integer pushStatus;

    /**
     * 快递单号
     */
    private String logisticCompany;

    private String logisticNumber;

    /**
     * 发货时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime shippedAt;

    /**
     * 签收时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime receivedAt;

    /**
     * 发货单拒绝原因
     */
    private String rejectMsg;

    /**
     * 用于后续数据同步，业务中禁止使用
     */
    private Integer id;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;


}
