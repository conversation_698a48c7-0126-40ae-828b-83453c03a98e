package com.ets.apply.application.app.business.unicom;

import com.alibaba.fastjson.JSON;
import com.ets.apply.application.app.business.BaseBusiness;
import com.ets.apply.application.app.business.MissionRewardBusiness;
import com.ets.apply.application.app.disposer.UnicomSelectNumberDisposer;
import com.ets.apply.application.app.disposer.UnicomSyncOrderDisposer;
import com.ets.apply.application.app.factory.task.TaskFactory;
import com.ets.apply.application.app.service.thirdPartner.SzUnicomZopService;
import com.ets.apply.application.app.thirdservice.feign.UnicomApplyNotifyFeign;
import com.ets.apply.application.common.bo.missionReward.MissionRewardResultBO;
import com.ets.apply.application.common.bo.unicom.UnicomApplyNotifyBO;
import com.ets.apply.application.common.bo.unicom.UnicomNotifyUrlBO;
import com.ets.apply.application.common.bo.unicom.UnicomSelectNumberDisposerBO;
import com.ets.apply.application.common.bo.unicom.UnicomSyncOrderDisposerBO;
import com.ets.apply.application.common.config.UnicomApplyConfig;
import com.ets.apply.application.common.consts.taskRecord.TaskRecordReferTypeEnum;
import com.ets.apply.application.common.consts.unicom.*;
import com.ets.apply.application.common.dto.request.szUnicomZop.*;
import com.ets.apply.application.common.dto.unicom.*;
import com.ets.apply.application.common.utils.ChooseNumberUtil;
import com.ets.apply.application.common.utils.ParamUtil;
import com.ets.apply.application.common.vo.szUnicomZop.*;
import com.ets.apply.application.common.vo.unicom.*;
import com.ets.apply.application.infra.entity.MissionUnicomApplyEntity;
import com.ets.apply.application.infra.service.MissionUnicomApplyService;
import com.ets.apply.application.infra.service.MissionUnionApplyLogService;
import com.ets.common.BeanHelper;
import com.ets.common.JsonResult;
import com.ets.common.ToolsHelper;
import com.ets.starter.queue.QueueDefault;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.net.URI;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;

@Component
@Slf4j
public class UnicomApplyBusiness {

    @Autowired
    private MissionUnicomApplyService missionUnicomApplyService;

    @Autowired
    private UnicomApplyConfig unicomApplyConfig;

    @Autowired
    private BaseBusiness baseBusiness;

    @Autowired
    private QueueDefault queueDefault;

    @Autowired
    private MissionRewardBusiness missionRewardBusiness;

    @Autowired
    private UnicomApplyNotifyFeign notifyFeign;

    @Autowired
    private SzUnicomZopService szUnicomZopService;

    @Autowired
    private MissionUnionApplyLogService logService;

    @Resource(name = "redisPermanentTemplate")
    private StringRedisTemplate redisPermanentTemplate;

    public List<UnicomChooseNumberVO> chooseNumber(UnicomChooseNumberDTO dto) {

        if (StringUtils.isNotEmpty(dto.getSearchNumber()) || StringUtils.isNotEmpty(dto.getRule())) {
            List<UnicomChooseNumberVO> redisResult = chooseNumberFromRedis(dto);
            if (redisResult != null && ! redisResult.isEmpty()) {
                return redisResult;
            }
        }

        SzUnicomZopSelectNumDTO selectNumDTO = new SzUnicomZopSelectNumDTO();
        selectNumDTO.setGoodsId(unicomApplyConfig.getUnicomGoodsId());
        selectNumDTO.setProvinceCode(unicomApplyConfig.getProvinceCode());
        selectNumDTO.setCityCode(unicomApplyConfig.getCityCode());
        if (StringUtils.isNotEmpty(dto.getSearchNumber())) {
            String search = "";
            if (dto.getSearchNumber().length() <= 4) {
                search = StringUtils.rightPad(dto.getSearchNumber(), 4, "1");
                selectNumDTO.setSearchType("02");
            } else if (dto.getSearchNumber().length() <= 8) {
                search = StringUtils.rightPad(dto.getSearchNumber(), 8, "1");
                selectNumDTO.setSearchType("01");
            } else {
                search = dto.getSearchNumber().substring(dto.getSearchNumber().length() - 8);
                selectNumDTO.setSearchType("01");
            }

            selectNumDTO.setSearchValue(search);
        } else {
            selectNumDTO.setSearchValue("");
        }

        SzUnicomZopPhoneListVo listVo = szUnicomZopService.selectNum(selectNumDTO);

        if (! listVo.getCode().equals("0") && ! listVo.getCode().equals("90")) {
            ToolsHelper.throwException("号码查询失败：" + listVo.getMessage());
        }

        List<SzUnicomZopPhoneListContentVo> phoneList = listVo.getPhoneList();

        if (phoneList == null) {
            return null;
        }

        List<UnicomChooseNumberVO> result = new ArrayList<>();
        phoneList.forEach(phoneBO -> {

            UnicomChooseNumberVO vo = getNumberVO(phoneBO.getPhoneNum(), dto);

            if (vo != null) {
                // 其他属性设置
                vo.setAdvanceLimit(phoneBO.getAdvanceLimit());
                vo.setMonthlimit(phoneBO.getMonthlimit());
                vo.setMonthFeeLimit(phoneBO.getMonthFeeLimit());
                vo.setNicerule(phoneBO.getNicerule());

                result.add(vo);
            }
        });

        return result;
    }

    public List<UnicomChooseNumberVO> chooseNumberFromRedis(UnicomChooseNumberDTO dto) {

        List<String> phoneList = getListFromRedis();

        if (phoneList == null || phoneList.isEmpty()) {
            return null;
        }

        List<UnicomChooseNumberVO> result = new ArrayList<>();

        List<String> phones = new ArrayList<>();

        for (String phoneStr : phoneList) {

            List<String> rows = Arrays.asList(phoneStr.split(","));

            UnicomChooseNumberVO vo = getNumberVO(rows.get(0), dto);

            if (vo != null) {

                if (result.size() >= 100) {
                    break;
                }

                if (phones.contains(rows.get(0))) {
                    continue;
                } else {
                    phones.add(rows.get(0));
                }

                // 其他属性设置
                vo.setAdvanceLimit(rows.get(1));
                vo.setMonthFeeLimit(rows.get(2));
                vo.setMonthlimit(rows.get(3));
                vo.setNicerule(rows.get(4));

                result.add(vo);
            }
        }

        return result;
    }

    public UnicomChooseNumberVO getNumberVO(String phone, UnicomChooseNumberDTO dto) {

        List<String> numbers = new ArrayList<>();

        if (StringUtils.isNotEmpty(dto.getRule())) {
            switch (dto.getRule()) {
                case "AAA":
                    numbers = ChooseNumberUtil.sameNumber(phone, 3);
                    break;
                case "ABC":
                    numbers = ChooseNumberUtil.nearNumber(phone, 3, true);
                    break;
                case "DCBA":
                    numbers = ChooseNumberUtil.nearNumber(phone, 4, false);
                    break;
                case "ABAB":
                    numbers = ChooseNumberUtil.repeatTwoNumber(phone);
                    break;
                case "AABBB":
                    numbers = ChooseNumberUtil.twoThreeNumber(phone);
                    break;
                case "AABB":
                    numbers = ChooseNumberUtil.doubleTwoNumber(phone);
                    break;
                case "AABCC":
                    numbers = ChooseNumberUtil.nearDoubleTwoNumber(phone);
                    break;
                case "AABA":
                    numbers = ChooseNumberUtil.aABA(phone);
                    break;
                case "ABAA":
                    numbers = ChooseNumberUtil.aBAA(phone);
                    break;
                default:
                    break;
            }

            if (numbers == null || numbers.isEmpty()) {
                return null;
            }
        }

        if (dto.getIsNoFour() && ChooseNumberUtil.containsNumber(phone, "4")) {
            return null;
        }

        if (dto.getIsGoodEnd() && ! ChooseNumberUtil.endWith(phone, Arrays.asList("6","8","9"))) {
            return null;
        }

        // 搜索的字符高亮
        if (StringUtils.isNotEmpty(dto.getSearchNumber())) {
            numbers = ChooseNumberUtil.getHitSearchNumberList(phone, dto.getSearchNumber());
            if (numbers == null || numbers.isEmpty()) {
                return null;
            }
        }

        UnicomChooseNumberVO vo = new UnicomChooseNumberVO();
        vo.setPhone(phone);

        if (! numbers.isEmpty()) {
            vo.setPrefix(numbers.get(0));
            vo.setHitNumber(numbers.get(1));
            vo.setExt(numbers.get(2));
        }

        return vo;
    }

    public UnicomCheckHasOrderVO checkHasOrder(UnicomExistOrderDTO dto) {

        UnicomCheckHasOrderVO vo = new UnicomCheckHasOrderVO();

        MissionUnicomApplyEntity existOrder = missionUnicomApplyService.getDoingByReferSn(dto.getReferType(), dto.getReferSn());

        if (existOrder == null) {
            vo.setHasOrder(false);
        } else {
            vo.setHasOrder(true);
            vo.setPhone(existOrder.getPhone());
            vo.setExpireDate(existOrder.getExpireDate());
            vo.setUnExpiredDay(getUnExpiredDay(existOrder));
        }

        return vo;
    }

    public UnicomExistOrderVO getExistOrder(UnicomExistOrderDTO dto) {

        UnicomExistOrderVO vo = new UnicomExistOrderVO();

        vo.setType(2);

        MissionUnicomApplyEntity existOrder = missionUnicomApplyService.getByReferSn(dto.getReferType(), dto.getReferSn());
        if (existOrder != null
                && ! Arrays.asList(
                        UnicomApplyStatusEnum.CANCELED.getCode(),
                        UnicomApplyStatusEnum.DEFAULT.getCode()
                ).contains(existOrder.getStatus())
        ) {
            // 排除未创建成功和已取消的订单

            if (! dto.getUid().equals(existOrder.getUid())) {
                ToolsHelper.throwException("数据异常，uid不一致");
            }

            // 已存在订单
            vo.setExistApplySn(existOrder.getUnicomApplySn());
            vo.setType(1);
            vo.setPhone(existOrder.getPhone());

            return vo;
        }

        // 查找是否可复用订单
        MissionUnicomApplyEntity reuseOrder = missionUnicomApplyService.getCanReuseOrder(dto.getUid(), dto.getReferType(), unicomApplyConfig.getMissionConfigSn());
        if (reuseOrder != null) {
            // 复用
            vo.setReuseApplySn(reuseOrder.getUnicomApplySn());
            vo.setPhone(reuseOrder.getPhone());
            vo.setType(3);
        }

        return vo;
    }

    public Boolean checkCanReuse(MissionUnicomApplyEntity order, Long uid) {

        if (order == null) {
            return false;
        }

        if (! order.getUid().equals(uid)) {
            ToolsHelper.throwException("没有操作权限");
        }

        // 未同步到联通的订单不可复用
        if (
            ! Arrays.asList(UnicomApplyStatusEnum.SYNCED.getCode(),
                    UnicomApplyStatusEnum.COMPLETED.getCode()
            ).contains(order.getStatus())
        ) {
            return false;
        }

        if (order.getExpireDate() == null || order.getExpireDate().isBefore(LocalDateTime.now())) {
            return false;
        }

        if (! order.getMissionConfigSn().equals(unicomApplyConfig.getMissionConfigSn())) {
            return false;
        }

        return true;
    }

    public UnicomBindReuseOrderVO bindReuseOrder(UnicomBindReuseOrderDTO dto) {

        UnicomBindReuseOrderVO vo = new UnicomBindReuseOrderVO();

        MissionUnicomApplyEntity reuseOrder = missionUnicomApplyService.getByApplySn(dto.getReuseApplySn());

        if (! checkCanReuse(reuseOrder, dto.getUid())) {
            vo.setBindResult(false);
            vo.setErrorMsg("待绑定订单已失效，需重新创建订单");
        } else {
            // 绑定操作
            missionUnicomApplyService.bindReuseOrder(dto.getUid(), dto.getReuseApplySn(), dto.getReferSn());

            vo.setBindResult(true);

            logService.addLog(reuseOrder.getUnicomApplySn(),
                    "申请订单复用绑定，旧业务单号：" + reuseOrder.getReferSn() + "，新业务单号：" + dto.getReferSn(),
                    "用户");
        }

        return vo;
    }

    public void getVerifyCode(UnicomGetVerifyCodeDTO dto) {
        // 调联通接口
        SzUnicomZopSafeCodeDTO codeDTO = new SzUnicomZopSafeCodeDTO();
        codeDTO.setCertNo(dto.getCertNo());
        codeDTO.setContactNum(dto.getSendPhone());

        SzUnicomZopNormalVo result = szUnicomZopService.safeCode(codeDTO);
        if (! result.getCode().equals("0")) {
            ToolsHelper.throwException("发送验证码失败：" + result.getMessage());
        }
    }

    public UnicomCreateOrderVO createOrder(UnicomCreateOrderDTO dto) {

        UnicomCreateOrderVO vo = new UnicomCreateOrderVO();

        // 地区编号校验和处理
        List<String> areaCodeList =  Arrays.asList(dto.getAreaCode().split(" "));
        if (areaCodeList.size() < 3) {
            ToolsHelper.throwException("省市区编号格式不正确");
        }

        String provinceCode = areaCodeList.get(0);
        String cityCode = areaCodeList.get(1);
        String districtCode = areaCodeList.get(2);

        if (unicomApplyConfig.getCodeMap() != null && unicomApplyConfig.getCodeMap().containsKey(districtCode)) {
            districtCode = unicomApplyConfig.getCodeMap().get(districtCode);
            cityCode = districtCode.substring(0, 4) + "00";
        }

        List<String> unSupportProvinceCode = unicomApplyConfig.getUnSupportProvinceCode();
        if (unSupportProvinceCode == null) {
            // 香港，澳门
            unSupportProvinceCode = Arrays.asList("810000", "820000");
        }
        if (unSupportProvinceCode.contains(provinceCode)) {
            ToolsHelper.throwException("选择的省份暂不支持上门派件");
        }

        int age = ParamUtil.getYearOfIdCard(dto.getCertNo());
        if (age > 60 || age < 16) {
            ToolsHelper.throwException("仅限16岁以上60岁以下人员申请");
        }

        // 一个身份证只能申请一次
        Long hasCount = missionUnicomApplyService.getCountByCertNo(dto.getCertNo());
        if (hasCount >= unicomApplyConfig.getOneCertLimit()) {
            ToolsHelper.throwException("一个身份证号只能申请"+unicomApplyConfig.getOneCertLimit()+"个号码");
        }

        if (dto.getReferSn().length() < 10) {
            log.error("联通卡申请申办订单号长度异常：" + dto.getReferSn());
        }

        // 身份证校验 调联通接口
        SzUnicomZopCheckUserDTO checkUserDTO = new SzUnicomZopCheckUserDTO();
        checkUserDTO.setCertName(dto.getSendName());
        checkUserDTO.setCertNum(dto.getCertNo());
        checkUserDTO.setProvinceCode(unicomApplyConfig.getProvinceCode());
        checkUserDTO.setCityCode(unicomApplyConfig.getCityCode());

        SzUnicomZopCheckUserVo checkUserVo = szUnicomZopService.checkUser(checkUserDTO);
        if (! checkUserVo.getCode().equals("0")) {
            log.info("身份证校验失败：" + checkUserVo.getMessage());
            ToolsHelper.throwException(checkUserVo.getMessage());
        }

        // 风控 调联通接口
        SzUnicomZopCheckRiskDTO checkRiskDTO = new SzUnicomZopCheckRiskDTO();
        checkRiskDTO.setAddress(dto.getAddress());
        checkRiskDTO.setCertName(dto.getSendName());
        checkRiskDTO.setCertNo(dto.getCertNo());
        checkRiskDTO.setProvinceCode(unicomApplyConfig.getProvinceCode());
        checkRiskDTO.setCityCode(unicomApplyConfig.getCityCode());
        checkRiskDTO.setContactNum(dto.getPhone());
        checkRiskDTO.setPostProvinceCode(provinceCode);
        checkRiskDTO.setPostCityCode(cityCode);
        checkRiskDTO.setPostDistrictCode(districtCode);

        SzUnicomZopNormalVo normalVo = szUnicomZopService.checkRisk(checkRiskDTO);
        Integer normalCode = Integer.valueOf(normalVo.getCode());
        if (! normalCode.equals(UnicomRiskErrorFixEnum.PASS.getCode())) {

            log.info("风控校验失败：" + normalVo.getMessage());

            if (unicomApplyConfig.getVerifyOpen()) {
                String normalError = UnicomRiskErrorFixEnum.getDescByCode(normalCode);
                if (StringUtils.isNotEmpty(normalError)) {
                    ToolsHelper.throwException(normalError);
                } else {
                    ToolsHelper.throwException(normalVo.getMessage());
                }
            }
        }

        // 验证码校验 调联通接口
        SzUnicomZopCheckCodeDTO codeDTO = new SzUnicomZopCheckCodeDTO();
        codeDTO.setCertNo(dto.getCertNo());
        codeDTO.setContactNum(dto.getSendPhone());
        codeDTO.setSafeCode(dto.getVerifyCode());
        SzUnicomZopCheckCodeVo checkCodeVo = szUnicomZopService.checkCode(codeDTO);
        if (! checkCodeVo.getCode().equals("0")) {
            if (unicomApplyConfig.getVerifyOpen()) {
                log.info("联通验证码校验失败：" + checkCodeVo.getMessage());
                ToolsHelper.throwException(checkCodeVo.getMessage());
            }
        }

        // 创建申请单
        MissionUnicomApplyEntity entity = new MissionUnicomApplyEntity();
        entity.setUnicomApplySn(baseBusiness.generateSn("UnicomApplySn"));
        entity.setUid(dto.getUid());
        entity.setPhone(dto.getPhone());
        entity.setReferType(dto.getReferType());
        entity.setReferSn(dto.getReferSn());
        entity.setUnicomGoodsId(unicomApplyConfig.getUnicomGoodsId());
        entity.setMissionConfigSn(unicomApplyConfig.getMissionConfigSn());
        entity.setSendName(dto.getSendName());
        entity.setSendPhone(dto.getSendPhone());
        entity.setArea(dto.getArea());
        entity.setAreaCode(dto.getAreaCode());
        entity.setAddress(dto.getAddress());
        entity.setCertNo(dto.getCertNo());
        UnicomNotifyUrlBO notifyUrlBO = new UnicomNotifyUrlBO();
        notifyUrlBO.setStatusChange(unicomApplyConfig.getNotifyUrl());
        entity.setNotifyUrl(JSON.toJSONString(notifyUrlBO));

        missionUnicomApplyService.create(entity);
        vo.setUnicomApplySn(entity.getUnicomApplySn());

        try {
            // 同步意向单 调联通接口
            SzUnicomZopCreateOrderIntentionDTO intentionDTO = new SzUnicomZopCreateOrderIntentionDTO();
            intentionDTO.setCertName(dto.getSendName());
            intentionDTO.setCertNo(dto.getCertNo());
            intentionDTO.setContactNum(dto.getSendPhone());
            intentionDTO.setPostProvinceCode(provinceCode);
            intentionDTO.setPostCityCode(cityCode);
            intentionDTO.setPostDistrictCode(districtCode);
            intentionDTO.setPostAddr(dto.getAddress());

            SzUnicomZopCreateOrderIntentionVo intentionVo = szUnicomZopService.createOrderIntention(intentionDTO);
            if (! intentionVo.getCode().equals("0")) {
                log.info("创建意向单失败：" + intentionVo.getMessage());
                ToolsHelper.throwException(intentionVo.getMessage());
            }

            String intentionToken = intentionVo.getDataObj().getString("token");

            // 同步正式单 调联通接口
            SzUnicomZopCreateOrderIntentionFormalDTO formalDTO = new SzUnicomZopCreateOrderIntentionFormalDTO();
            formalDTO.setProvinceCode(unicomApplyConfig.getProvinceCode());
            formalDTO.setCityCode(unicomApplyConfig.getCityCode());
            formalDTO.setPhoneNum(dto.getPhone());
            formalDTO.setToken(intentionToken);

            String unicomOrderSn = "";
            if (StringUtils.isNotEmpty(dto.getUnicomOrderSn())) {
                unicomOrderSn = dto.getUnicomOrderSn();
            } else if (unicomApplyConfig.getRealSubmit()) {
                SzUnicomZopCreateOrderIntentionFormalVo formalVo = szUnicomZopService.createOrderIntentionFormal(formalDTO);
                if (! formalVo.getCode().equals("0")) {
                    log.info("同步正式单失败：" + formalVo.getMessage());
                    ToolsHelper.throwException(formalVo.getMessage());
                }

                unicomOrderSn = formalVo.getDataObj().getString("orderNo");
            } else {
                unicomOrderSn = "uSn" + entity.getId();
            }

            // 更新联通订单号，状态, 申请时间
            missionUnicomApplyService.syncUnicomOrder(entity.getUnicomApplySn(), unicomOrderSn);

            logService.addLog(entity.getUnicomApplySn(), "创建申请单并同步", "用户");

        } catch (Exception e) {

            missionUnicomApplyService.cancel(entity.getUnicomApplySn(), e.getMessage());

            if (e.getMessage().equals("所填省份、地市或区县编码有误")) {
                log.error("行政区划编号异常：" + dto.getAreaCode());
            }

            throw e;
        }

        return vo;
    }

    public void businessOrderCancel(UnicomBusinessOrderCancelDTO dto) {
        // 业务单取消
        MissionUnicomApplyEntity existOrder = missionUnicomApplyService.getByReferSn(dto.getReferType(), dto.getReferSn());

        if (existOrder == null) {
            // 未创建对应的联通申请单，则无需处理
            return;
        }

        if (Arrays.asList(
                UnicomApplyStatusEnum.CANCELED.getCode()
        ).contains(existOrder.getStatus())) {
            // 已取消的单，则不需要设置为可复用
            return;
        }

        missionUnicomApplyService.setCanReuse(existOrder.getUnicomApplySn());

        logService.addLog(existOrder.getUnicomApplySn(), "业务单取消，置为可复用", "业务微服务接口调用");
    }

    public Boolean isExpired(MissionUnicomApplyEntity order) {

        if (Arrays.asList(
                UnicomOrderStateEnum.ACTIVATED.getCode(),
                UnicomOrderStateEnum.CHARGED.getCode()
        ).contains(order.getUnicomOrderState())) {
            return false;
        }

        if (order.getExpireDate() == null || order.getExpireDate().isBefore(LocalDateTime.now())) {
            return true;
        } else {
            return false;
        }
    }

    public long getUnExpiredDay(MissionUnicomApplyEntity order) {

        if (Arrays.asList(
                UnicomOrderStateEnum.ACTIVATED.getCode(),
                UnicomOrderStateEnum.CHARGED.getCode()
        ).contains(order.getUnicomOrderState())) {
            return 30;
        }

        if (order.getExpireDate() == null || order.getExpireDate().isBefore(LocalDateTime.now())) {
            return 0;
        } else {
            return Math.max(0, ChronoUnit.DAYS.between(LocalDateTime.now(), order.getExpireDate()) - 1);
        }
    }

    public UnicomProcessingOrderVO getProcessingOrder(UnicomGetProcessingOrderDTO dto, MissionUnicomApplyEntity order) {
        // 获取进行中订单
        UnicomProcessingOrderVO vo;

        if (order == null) {
            order = missionUnicomApplyService.getByApplySn(dto.getUnicomApplySn());
            if (order == null) {
                ToolsHelper.throwException("联通申请单不存在");
            }
        }

        if (! order.getUid().equals(dto.getUid())) {
            ToolsHelper.throwException("没有权限");
        }

        vo = BeanHelper.copy(UnicomProcessingOrderVO.class, order);

        if (order.getStatus().equals(UnicomApplyStatusEnum.DEFAULT.getCode())) {
            return null;
        }

        if (order.getStatus().equals(UnicomApplyStatusEnum.SYNCED.getCode())) {

            if (Arrays.asList(
                    UnicomOrderStateEnum.DEFAULT.getCode(),// 默认为待上门
                    UnicomOrderStateEnum.UN_REGISTER.getCode(),
                    UnicomOrderStateEnum.ACTIVATED.getCode()
            ).contains(order.getUnicomOrderState())) {
                vo.setFrontendStatus(UnicomFrontendStatusEnum.WAIT_HOME.getCode());
            }

            if (order.getUnicomOrderState().equals(UnicomOrderStateEnum.CHARGED.getCode())) {
                vo.setFrontendStatus(UnicomFrontendStatusEnum.ACTIVATED.getCode());
            }

            if (isExpired(order)) {
                // 已过期失效
                vo.setFrontendStatus(UnicomFrontendStatusEnum.CANCELED.getCode());
            }
        }

        // 注销状态
        if (
                Arrays.asList(
                        UnicomApplyStatusEnum.VOID.getCode(),
                        UnicomApplyStatusEnum.CANCELED.getCode()
                ).contains(order.getStatus())
        ) {
            vo.setFrontendStatus(UnicomFrontendStatusEnum.CANCELED.getCode());
        }

        if (order.getStatus().equals(UnicomApplyStatusEnum.COMPLETED.getCode())) {
            vo.setRechargeComplete(true);
            vo.setFrontendStatus(UnicomFrontendStatusEnum.ACTIVATED.getCode());
        }

        return vo;
    }

    public void cancel(String applySn, Long uid) {

        MissionUnicomApplyEntity order = missionUnicomApplyService.getByApplySn(applySn);
        if (order == null) {
            ToolsHelper.throwException("联通申请单不存在");
        }

        if (! order.getUid().equals(uid)) {
            ToolsHelper.throwException("没有权限");
        }

        if (Arrays.asList(
                UnicomApplyStatusEnum.CANCELED.getCode()
        ).contains(order.getStatus())) {
            return;
        }

        missionUnicomApplyService.cancel(applySn, "用户选择重新办理");

        logService.addLog(order.getUnicomApplySn(), "取消重新办理", "用户");
    }

    public void syncUnicomOrderOfUnFinished() {
        // 定时同步
        List<MissionUnicomApplyEntity> list = missionUnicomApplyService.getUnFinishedList();
        if (list == null || list.isEmpty()) {
            return;
        }

        list.forEach(entity -> {

            UnicomSyncOrderDisposerBO bo = new UnicomSyncOrderDisposerBO();
            bo.setUnicomApplySn(entity.getUnicomApplySn());

            queueDefault.push(new UnicomSyncOrderDisposer(bo));
        });
    }

    public void syncOrderByData(String unicomApplySn, SzUnicomZopOrderGetCardVo.ReturnData row) {

        MissionUnicomApplyEntity entity = missionUnicomApplyService.getByApplySn(unicomApplySn);

        if (! entity.getStatus().equals(UnicomApplyStatusEnum.SYNCED.getCode())) {
            return;
        }

        String state = "";
        int amountCent = 0;

        if (Arrays.asList(
                UnicomOrderStateEnum.CANCELED.getCode(),
                UnicomOrderStateEnum.VOIDED.getCode(),
                UnicomOrderStateEnum.CHARGED.getCode()
        ).contains(row.getState())) {
            state = row.getState();
            if (row.getLgtsId() != null) {
                amountCent = Integer.parseInt(row.getLgtsId());
            }
        }

        if (Arrays.asList(
                UnicomOrderStateEnum.ACTIVATED.getCode(),
                UnicomOrderStateEnum.UN_REGISTER.getCode()
        ).contains(row.getState())) {
            state = row.getState();
        }

        updateInfoByUnicomState(entity, state, amountCent);
    }

    public void doSyncOrder(String unicomApplySn) {

        MissionUnicomApplyEntity entity = missionUnicomApplyService.getByApplySn(unicomApplySn);

        // 状态判断
        if (! entity.getStatus().equals(UnicomApplyStatusEnum.SYNCED.getCode()) || StringUtils.isEmpty(entity.getUnicomOrderSn())) {
            return;
        }

        // 调联通接口
        SzUnicomZopOrderGetCardDTO getCardDTO = new SzUnicomZopOrderGetCardDTO();
        getCardDTO.setType("4");
        getCardDTO.setOrderNo(entity.getUnicomOrderSn());
        SzUnicomZopOrderGetCardVo getCardVo = szUnicomZopService.orderGetCard(getCardDTO);

        if (getCardVo.getData() == null || getCardVo.getData().isEmpty()) {
            return;
        }

        String state = "";
        int amountCent = 0;
        for (SzUnicomZopOrderGetCardVo.ReturnData row : getCardVo.getData()) {
            if (Arrays.asList(
                    UnicomOrderStateEnum.CANCELED.getCode(),
                    UnicomOrderStateEnum.VOIDED.getCode(),
                    UnicomOrderStateEnum.CHARGED.getCode()
            ).contains(row.getState())) {
                state = row.getState();
                if (StringUtils.isNotEmpty(row.getLgtsId())) {
                    amountCent = Integer.parseInt(row.getLgtsId());
                }

                break;
            }

            if (Arrays.asList(
                    UnicomOrderStateEnum.ACTIVATED.getCode(),
                    UnicomOrderStateEnum.UN_REGISTER.getCode()
            ).contains(row.getState())) {
                state = row.getState();
            }

            // todo 转套餐
        }

        updateInfoByUnicomState(entity, state, amountCent);
    }

    public void updateInfoByUnicomState(MissionUnicomApplyEntity entity, String state, Integer amountCent) {

        if (entity.getUnicomOrderState().equals(state) && entity.getFirstRecharge().equals(amountCent)) {
            return;
        }

        // 更新状态
        missionUnicomApplyService.updateUnicomInfo(entity, state, amountCent);

        if (entity.getStatus().equals(UnicomApplyStatusEnum.COMPLETED.getCode())) {
            logService.addLog(entity.getUnicomApplySn(), "首充达标", "系统同步");
        } else {
            logService.addLog(entity.getUnicomApplySn(), "联通订单状态更新：" + state, "系统同步");
        }

        // 销户，退单状态处理
        if (state.equals(UnicomOrderStateEnum.CANCELED.getCode())
                || state.equals(UnicomOrderStateEnum.VOIDED.getCode())
        ) {
            missionUnicomApplyService.voidOrder(entity.getUnicomApplySn());

            logService.addLog(entity.getUnicomApplySn(), "退单或注销", "系统同步");
        }

        // 首充达标后
        if (entity.getStatus().equals(UnicomApplyStatusEnum.COMPLETED.getCode())
                && entity.getHasReward() == UnicomHasRewardEnum.NOT.getCode()) {
            // 发放奖励
            MissionRewardResultBO rewardResultBO = missionRewardBusiness.reward(entity.getMissionConfigSn(), entity.getUid(), entity.getUnicomApplySn());

            // 更新奖励内容
            if (StringUtils.isNotEmpty(rewardResultBO.getError())) {
                missionUnicomApplyService.updateRewardInfo(entity.getUnicomApplySn(), JSON.toJSONString(rewardResultBO), false);
                log.error("奖励发放失败：" + rewardResultBO.getError());
                return;
            }

            missionUnicomApplyService.updateRewardInfo(entity.getUnicomApplySn(), JSON.toJSONString(rewardResultBO), true);

            // 日志
            logService.addLog(entity.getUnicomApplySn(), "发放奖励", "系统");

            UnicomApplyNotifyBO notifyBO = new UnicomApplyNotifyBO();
            notifyBO.setStatus(entity.getStatus());
            notifyBO.setUnicomApplySn(entity.getUnicomApplySn());
            notifyBO.setReferSn(entity.getReferSn());

            // 通知业务
            TaskFactory.createByRefer(notifyBO, TaskRecordReferTypeEnum.TASK_UNICOM_APPLY_NOTIFY, entity.getUnicomApplySn());
        }
    }

    public void notifyBusiness(UnicomApplyNotifyBO bo) {

        MissionUnicomApplyEntity entity = missionUnicomApplyService.getByApplySn(bo.getUnicomApplySn());

        if (StringUtils.isEmpty(entity.getNotifyUrlBO().getStatusChange())) {
            return;
        }

        if (entity.getNotifyStatus().equals(UnicomNotifyStatusEnum.SUCCESS.getCode())) {
            return;
        }

        JsonResult<Object> result = notifyFeign.completeRewardNotify(URI.create(entity.getNotifyUrlBO().getStatusChange()), bo);

        result.checkError();

        missionUnicomApplyService.notifySuccess(bo.getUnicomApplySn());

        logService.addLog(entity.getUnicomApplySn(), "通知业务成功", "系统");
    }

    /**
     * 异步查询号码，存储到redis
     * @param keyIndex
     * @param pageCount
     */
    public void asyncSelectNumber(String keyIndex, Integer pageCount) {

        redisPermanentTemplate.opsForHash().delete(UnicomApplyConst.SELECT_NUMBER_REDIS_KEY, keyIndex);

        List<String> result = new ArrayList<>();

        for (int i=1;i<=pageCount;i++) {

            SzUnicomZopSelectNumDTO selectNumDTO = new SzUnicomZopSelectNumDTO();
            selectNumDTO.setGoodsId(unicomApplyConfig.getUnicomGoodsId());
            selectNumDTO.setProvinceCode(unicomApplyConfig.getProvinceCode());
            selectNumDTO.setCityCode(unicomApplyConfig.getCityCode());
            SzUnicomZopPhoneListVo listVo = szUnicomZopService.selectNum(selectNumDTO);

            if (! listVo.getCode().equals("0")) {
                break;
            }

            List<SzUnicomZopPhoneListContentVo> phoneList = listVo.getPhoneList();

            phoneList.forEach(vo -> {

                result.add(
                        vo.getPhoneNum()
                        + "," + vo.getAdvanceLimit()
                        + "," + vo.getMonthFeeLimit()
                        + "," + vo.getMonthlimit()
                        + "," + vo.getNicerule()
                );
            });
        }

        redisPermanentTemplate.opsForHash().put(UnicomApplyConst.SELECT_NUMBER_REDIS_KEY, keyIndex, JSON.toJSONString(result));

    }

    public void autoRefreshNumbers(Integer keyCount, Integer pageCount) {

        for (int i=1;i<=keyCount;i++) {
            UnicomSelectNumberDisposerBO bo = new UnicomSelectNumberDisposerBO();
            bo.setPageCount(pageCount);
            bo.setKeyIndex(String.valueOf(i));

            queueDefault.push(new UnicomSelectNumberDisposer(bo));
        }
    }

    public List<String> getListFromRedis() {

        Map<Object, Object> map = redisPermanentTemplate.opsForHash().entries(UnicomApplyConst.SELECT_NUMBER_REDIS_KEY);

        if (map == null || map.isEmpty()) {
            return null;
        }

        List<String> result = new ArrayList<>();

        map.keySet().forEach(key -> {
            String listStr = (String) map.get(key);
            List<String> phoneList = JSON.parseArray(listStr, String.class);

            result.addAll(phoneList);
        });

        return result;
    }

    public List<String> getListFromRedisByIndex(String keyIndex) {

        String listStr = (String) redisPermanentTemplate.opsForHash().get(UnicomApplyConst.SELECT_NUMBER_REDIS_KEY, keyIndex);

        return JSON.parseArray(listStr, String.class);
    }

}
