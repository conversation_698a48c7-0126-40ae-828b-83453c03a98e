package com.ets.apply.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.apply.application.infra.entity.ReviewOrderIdCardEntity;
import com.ets.apply.application.infra.mapper.ReviewOrderIdCardMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户身份证审核资料 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-26
 */
@Slf4j
@Service
@DS("db-order-proxy")
public class ReviewOrderIdCardService extends BaseService<ReviewOrderIdCardMapper, ReviewOrderIdCardEntity> {

    public ReviewOrderIdCardEntity getByIdCardSn(String idCardSn) {
        return super.baseMapper.selectById(idCardSn);
    }

    public ReviewOrderIdCardEntity getByReviewOrderSn(String reviewOrderSn) {

        Wrapper<ReviewOrderIdCardEntity> wrapper = Wrappers.<ReviewOrderIdCardEntity>lambdaQuery()
                .eq(ReviewOrderIdCardEntity::getReviewOrderSn, reviewOrderSn)
                .last("LIMIT 1");

        return super.baseMapper.selectOne(wrapper);
    }


}
