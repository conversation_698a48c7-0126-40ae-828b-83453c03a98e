package com.ets.apply.application.common.consts.orderAftersales;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum OrderAftersalesBussinessStatusEnum {
    STATUS_WAIT(0, "待处理"),
    STATUS_FINISH(1, "完成");

    private final Integer status;
    private final String desc;

    public static String getDescByStatus(int status) {
        for (OrderAftersalesBussinessStatusEnum node : OrderAftersalesBussinessStatusEnum.values()) {
            if (node.getStatus().equals(status)) {
                return node.getDesc();
            }
        }
        return "";
    }
}
