package com.ets.apply.application.common.vo;

import com.ets.apply.application.common.consts.orderBenefitRecord.OrderBenefitRecordStatusEnum;
import com.ets.apply.application.common.consts.segmentBenefit.BenefitTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class OrderBenefitListVO {
    private Integer id;
    private String benefitSn;
    private String orderSn;

    private String benefitType;
    private String benefitTypeStr;

    private String benefitContent;
    private String benefitContentStr;

    private Integer status;
    private String statusStr;

    private String errorMsg;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdAt;

    public String getBenefitTypeStr() {
        return BenefitTypeEnum.map.getOrDefault(benefitType, "-");
    }

    public String getStatusStr() {
        return OrderBenefitRecordStatusEnum.map.getOrDefault(status, "未知");
    }
}
