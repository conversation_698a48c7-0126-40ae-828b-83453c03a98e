package com.ets.apply.application;

import com.ets.apply.application.app.disposer.RechargeNotifyDisposer;
import com.ets.apply.application.common.bo.recharge.RechargeNotifyBO;
import com.ets.starter.queue.QueueDefault;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@SpringBootTest(classes = ApplyApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringRunner.class)
@ActiveProfiles(value = "dev")
public class IssuerTest {

    @Autowired
    QueueDefault queue;

    @Test
    public void rechargeNotify() {
        RechargeNotifyBO notifyBO = new RechargeNotifyBO();
        notifyBO.setOrderSn("2212071638000000148");
        notifyBO.setNeedNotify(true);
        queue.push(new RechargeNotifyDisposer(notifyBO));
    }
}
