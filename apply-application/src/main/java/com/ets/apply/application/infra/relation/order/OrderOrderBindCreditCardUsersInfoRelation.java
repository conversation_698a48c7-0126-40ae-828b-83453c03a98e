package com.ets.apply.application.infra.relation.order;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.ets.apply.application.infra.entity.ActivityCreditCardUsersInfoEntity;

import com.ets.apply.application.infra.entity.OrderOrderEntity;
import com.ets.common.base.BaseEntityRelation;

import java.util.function.BiConsumer;

public class OrderOrderBindCreditCardUsersInfoRelation extends BaseEntityRelation<OrderOrderEntity,
        ActivityCreditCardUsersInfoEntity> {
    @Override
    public BiConsumer<OrderOrderEntity, ActivityCreditCardUsersInfoEntity> getEntityColumn() {

        return OrderOrderEntity::setActivityCreditCardUsersInfoEntity;
    }

    @Override
    public SFunction<OrderOrderEntity, Object> getMasterColumn() {
        return OrderOrderEntity::getOrderSn;
    }

    @Override
    public SFunction<ActivityCreditCardUsersInfoEntity, Object> getAffiliatedColumn() {
        return ActivityCreditCardUsersInfoEntity::getReferSn;
    }

}
