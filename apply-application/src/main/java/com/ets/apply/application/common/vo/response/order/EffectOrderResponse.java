package com.ets.apply.application.common.vo.response.order;

import lombok.Data;

@Data
public class EffectOrderResponse {
    private String orderSn;
    private Long uid;
    private String packageSn;
    // 售后状态
    private Integer aftersaleStatus;

    private String plateNo;

    private Integer plateColor;

    // 卡种类型：1记账卡，2储值卡
    private Integer cardType;

    // 是否货车:0-非货车 1-货车
    private Integer truck;

    private Integer bizType;
}
