package com.ets.apply.application.controller;

import com.ets.apply.application.app.business.external.ExternalOrderBusiness;
import com.ets.apply.application.common.dto.external.*;
import com.ets.common.JsonResult;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping("/externalOrderNotify")
@RestController
@Slf4j
public class ExternalOrderNotifyController {

    @Autowired
    private ExternalOrderBusiness externalOrderBusiness;

    @PostMapping("/afterSaleStatusChange")
    public JsonResult<Object> afterSaleStatusChange(@RequestBody @Valid ExternalOrderAfterSaleStatusChangeDTO dto) {

        if (dto.getCustomerStatus() == 4) {
            // 目前只处理售后完成
            externalOrderBusiness.afterSaleFinish(dto);
        }

        return JsonResult.ok();
    }

}
