package com.ets.apply.application.common.consts.card;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
@AllArgsConstructor
public enum CarTypeEnum {

    TYPE_CAR(1, "小汽车"),
    TYPE_TRUCK(2, "货车");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;

    static {
        CarTypeEnum[] enums = CarTypeEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
