package com.ets.apply.application.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@RefreshScope
@Configuration
@Data
@ConfigurationProperties(prefix = "bank.ceb")
public class CebBankConfig {

    /**
     * 请求地址
     */
    private String requestUrl;

    /**
     * 卡片ID
     */
    private String cardId;

    /**
     * 项目来源码
     */
    private String proCode;

    /**
     * 数据来源(合作方代码)
     */
    private String corpId;



    private String aesSecretKey;

    private String cebRsaPublicKey;

    private String etcRsaPrivateKey;

    private String etcRsaPublicKey;






}
