package com.ets.apply.application.common.consts.activityCreditCardUsersInfo;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum ActivityCreditCardUserInfoWhichBankEnum {

    PING_AN_INTERFACE(30, "平安银行"),
    GUANG_FA_INTERFACE(31, "广发银行"),
    JIAO_TONG(32, "交通银行"),
    CREDIT_BANK_CITIC(80, "中信银行"),

    CREDIT_BANK_CMBC(161, "民生银行"),
    CREDIT_BANK_SPD(162, "浦发银行"),
    CREDIT_BANK_CEB(183, "光大银行");

    private final int code;
    private final String description;

    public static String getDescByCode(int code) {
        for (ActivityCreditCardUserInfoWhichBankEnum node : ActivityCreditCardUserInfoWhichBankEnum.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }
        return "";
    }

    public static List<Map<String, String>> getLabelList() {
        List<Map<String, String>> list = new ArrayList<>();

        for (ActivityCreditCardUserInfoWhichBankEnum node : ActivityCreditCardUserInfoWhichBankEnum.values()) {
            Map<String, String> row = new LinkedHashMap<>();
            row.put("label", node.getDescription());
            row.put("value", String.valueOf(node.getCode()));

            list.add(row);
        }

        return list;
    }

    /**
     * 1：广发，2：交通，3：平安
     * @param whichBank
     * @return
     */
    public static Integer getCoopBankByWhichBank(Integer whichBank) {
        int coopBank = 0;
        switch (whichBank) {
            case ActivityCreditCardUserInfoWhichBankConstant.GUANG_FA_INTERFACE:
                coopBank = 1;
                break;
            case ActivityCreditCardUserInfoWhichBankConstant.JIAO_TONG:
                coopBank = 2;
                break;
            case ActivityCreditCardUserInfoWhichBankConstant.PING_AN_INTERFACE:
                coopBank = 3;
                break;
        }
        return coopBank;
    }
}
