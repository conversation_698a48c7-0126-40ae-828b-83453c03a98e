package com.ets.apply.application.app.thirdservice.fallback;

import com.ets.apply.application.app.thirdservice.feign.CallCouponFeign;
import com.ets.apply.application.app.thirdservice.request.coupon.CouponSettleDTO;
import com.ets.common.JsonResult;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

@Component
public class CallCouponFallBackFactory implements FallbackFactory<CallCouponFeign> {

    @Override
    public CallCouponFeign create(Throwable cause) {
        return new CallCouponFeign() {
            @Override
            public String listAvailableBySettle(@RequestBody CouponSettleDTO couponSettleDTO) {
                return JsonResult.error("请求java coupon 服务 anyCoupon失败: " + cause.getMessage()).toString();

            }

        };
    }
}