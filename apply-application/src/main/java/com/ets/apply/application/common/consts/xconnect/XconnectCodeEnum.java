package com.ets.apply.application.common.consts.xconnect;

import lombok.AllArgsConstructor;
import lombok.Getter;
@Getter
@AllArgsConstructor
public enum XconnectCodeEnum {

    XCONNECT_CODE_SUCCESS("0000", "正常"),
    XCONNECT_CODE_TOKEN_FAIL("4401",  "token失效"),
    XCONNECT_CODE_SAVE_EXIST("6007", "保存的数据已存在"),
    XCONNECT_CODE_SAVE_NOT_EXIST("6001", "更新的数据不存在"),
    ;

    private final String code;
    private final String description;


}
