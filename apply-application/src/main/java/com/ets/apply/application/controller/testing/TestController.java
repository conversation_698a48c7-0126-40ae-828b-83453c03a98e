package com.ets.apply.application.controller.testing;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ets.apply.application.app.business.ChannelProductOrderBusiness;
import com.ets.apply.application.app.business.job.CosSyncJobBusiness;
import com.ets.apply.application.app.job.OrderCenterSyncJob;
import com.ets.apply.application.app.job.StatisticJob;
import com.ets.apply.application.app.service.thirdPartner.XconnectService;
import com.ets.apply.application.app.thirdservice.feign.CallPhpIssuerAdminFeign;
import com.ets.apply.application.app.thirdservice.feign.CallTestFeign;
import com.ets.apply.application.common.config.CosConfig;
import com.ets.apply.application.common.config.WecarConfig;
import com.ets.apply.application.common.consts.productOrder.ProductOrderStatusEnum;
import com.ets.apply.application.common.dto.cosJob.CosDelJobDTO;
import com.ets.apply.application.common.dto.cosJob.CosSyncJobDTO;
import com.ets.apply.application.common.dto.request.orderAftersales.OrderAftersalesOrderChangeDTO;
import com.ets.apply.application.common.vo.szUnicomZop.SzUnicomZopCreateOrderIntentionFormalVo;
import com.ets.apply.application.controller.BaseController;
import com.ets.apply.application.app.event.OrderCreateEvent;
import com.ets.apply.application.app.event.OrderPaidEvent;
import com.ets.apply.application.infra.entity.LogCosClearEntity;
import com.ets.apply.application.infra.entity.ProductOrderEntity;
import com.ets.apply.application.infra.service.LogCosClearService;
import com.ets.apply.application.infra.service.ProductOrderService;
import com.ets.common.JsonResult;
import com.ets.common.ToolsHelper;
import com.ets.common.BizException;
import com.ets.apply.application.common.dto.request.test.UserRequest;
import com.ets.starter.queue.QueueDefault;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.exception.CosClientException;
import com.qcloud.cos.exception.CosServiceException;
import com.qcloud.cos.model.COSObjectSummary;
import com.qcloud.cos.model.ListObjectsRequest;
import com.qcloud.cos.model.ObjectListing;
import com.qcloud.cos.region.Region;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.ApplicationContext;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.web.bind.annotation.*;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import jakarta.mail.internet.MimeMessage;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;
import java.util.Properties;


@RefreshScope
@RequestMapping("/test")
@RestController
@Slf4j
public class TestController extends BaseController {

    @Autowired
    private ApplicationContext publisher;

    @Autowired
    private QueueDefault queueDefault;

    @Autowired
    private CallTestFeign callTestFeign;

    @Autowired
    private OrderCenterSyncJob orderCenterSyncJob;


    @Autowired
    private CallPhpIssuerAdminFeign callPhpIssuerAdminFeign;

    @Autowired
    private StatisticJob statisticJob;

    @Autowired
    private XconnectService xconnectService;
    @Autowired
    private ProductOrderService productOrderService;
    @Autowired
    private ChannelProductOrderBusiness channelProductOrderBusiness;


    @Autowired
    private CosSyncJobBusiness cosSyncJobBusiness;
    @RequestMapping("/event")
    @ResponseBody
    public JsonResult<Object> event() throws BizException {

        Properties properties = new Properties();
        properties.setProperty("orderSn", "111111");
        properties.setProperty("type", "2");

        publisher.publishEvent(new OrderCreateEvent(properties));

        properties.setProperty("fee", "100.0");

        publisher.publishEvent(new OrderPaidEvent(properties));

        return JsonResult.ok();
    }


    @RequestMapping("/echo-json")
    @ResponseBody
    public JsonResult<Object> echoJson(@RequestParam(value = "arg", required = false) String arg) throws BizException {
        ToolsHelper.throwException("超出请求频率限制！");
        if (rateLimiterHelper.checkConcurrency("test-echo-json", 1, false)) {
            ToolsHelper.throwException("超出请求频率限制！");
        }

        if (! ToolsHelper.addLock(defaultRedisTemplate, "echo-json", 5)) {
            ToolsHelper.throwException("请稍后再试！");
        }

        if (arg == null) {
            return JsonResult.error("arg 参数不能为空！" + appParamsConfig.getName());
        }
        if (arg.equals("1")) {
            emailAlarm.send("参数错误", "arg 参数不能为1！");

            ToolsHelper.throwException("arg 参数不能为1！");
        }

        String mailSendResult;
        try {
            MimeMessage mimeMessage = mailSender.createMimeMessage();
            // 设置utf-8或GBK编码，否则邮件会有乱码
            MimeMessageHelper messageHelper = new MimeMessageHelper(mimeMessage, true, "UTF-8");
            messageHelper.setFrom("<EMAIL>", "高灯科技");
            messageHelper.setTo("<EMAIL>");
            messageHelper.setSubject(StringUtils.upperCase(appParamsConfig.getEnv()) + ": java email test");
            messageHelper.setText("hello email" + arg, true);
            mailSender.send(mimeMessage);

            mailSendResult = "发送成功";
        } catch (Exception e) {
            log.error(e.getMessage());

            mailSendResult = "发送失败：" + e.getMessage();
        }

        return JsonResult.ok("provider controller:" + arg + mailSendResult);
    }

    @PostMapping("/post-json")
    @ResponseBody
    public JsonResult<Object> postJson(@RequestBody(required = false) UserRequest user) {

        if (user == null) {
            return JsonResult.error("arg 参数不能为空！" + appParamsConfig.getName());
        }

        return JsonResult.ok(user);
    }

    @RequestMapping("/request")
    @ResponseBody
    public JsonResult<Object> request(
            @RequestParam(value = "arg", required = false) String arg,
            HttpServletRequest request,
            HttpServletResponse response)
            throws ServletException, IOException {

        log.info("forward begin" + arg);

        response.sendRedirect("/user-entrust/get/10");

        //request.getRequestDispatcher("/user-entrust/get/10").forward(request, response);

        log.info("forward end" + arg);

        return JsonResult.ok(request.getParameterMap());
    }

    @PostMapping("/cipher")
    @ResponseBody
    public JsonResult<Object> cipher(
            @RequestParam(value = "cipherValue") String cipherValue
    ) {
        String ret = "";
        try {
            String secretKey = "gd@123";
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5PADDING");
            byte[] key = Arrays.copyOf(DigestUtils.sha1(secretKey), 16);
            //IvParameterSpec iv = new IvParameterSpec(key);
            cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(key,"AES"));

            log.info(DigestUtils.sha1Hex(secretKey));
            log.info(new String(key, StandardCharsets.UTF_8));
            byte[] result = cipher.doFinal(Base64.getDecoder().decode(cipherValue.trim()));

            ret = new String(result, StandardCharsets.UTF_8);

        } catch (Exception e) {
            ToolsHelper.throwException(e.getMessage());
        }

        return JsonResult.ok(ret);
    }

    @PostMapping("/unicomJson")
    @ResponseBody
    public JsonResult<SzUnicomZopCreateOrderIntentionFormalVo> unicomJson(@RequestParam(value = "result") String result) throws JsonProcessingException {

        SzUnicomZopCreateOrderIntentionFormalVo vo = new ObjectMapper().readValue(result, SzUnicomZopCreateOrderIntentionFormalVo.class);

        if (vo.getCode().equals("0")) {
            vo.setDataObj(JSON.parseObject((String) vo.getData()));
        }
        vo.setData(null);

        return JsonResult.ok(vo);
    }

    @PostMapping("/responseMock")
    @ResponseBody
    public JsonResult<?> responseMock(@RequestBody String response) {

        return JSON.parseObject(response, JsonResult.class);
    }

    @PostMapping("/refundResponse")
    @ResponseBody
    public JsonResult<Object> refundResponse(@RequestBody String response) {

        JSONObject refundResponse = callTestFeign.responseMock(response).getDataWithCheckError();

        String refundSn = refundResponse.getJSONObject("refund_order").getString("refund_sn");

        return JsonResult.ok(refundSn);
    }
    @PostMapping("/orderCenterSync")
    @ResponseBody
    public JsonResult<Object> orderCenterSync(@RequestBody String response) {

        orderCenterSyncJob.orderCenterSyncOnceHandler(response);

        return JsonResult.ok();
    }

    @PostMapping("/orderChange")
    @ResponseBody
    public JsonResult<Object> orderChange() {
        String orderSn = "240527144600006567A2";
        try {
            OrderAftersalesOrderChangeDTO dto = new OrderAftersalesOrderChangeDTO("240527144600006567A2","cancel_success","test");
          String ret = callPhpIssuerAdminFeign.orderChange("JSuTong",dto);
        } catch (Throwable e) {
            log.error("请求审核发货平台失败:"+orderSn+e.getMessage(), e.getMessage());
        }

        return JsonResult.ok();
    }

    @PostMapping("/statisticJob")
    @ResponseBody
    public JsonResult<Object> statisticJob() {
        statisticJob.StatisticDataHandler("");
        return JsonResult.ok();
    }

    @PostMapping("/saveOrder")
    @ResponseBody
    public JsonResult<Object> saveOrder(@RequestParam(value = "productOrderSn", required = false) String productOrderSn) {
        ProductOrderEntity orderEntity = productOrderService.getByProductOrderSn(productOrderSn);
        if(orderEntity.getOrderStatus().equals(ProductOrderStatusEnum.DEFAULT.getCode())){
            xconnectService.etcOrderSave(orderEntity);
        }else{
            xconnectService.etcOrderUpdate(orderEntity);
        }
        return JsonResult.ok();
    }
    @PostMapping("/paySuccess")
    @ResponseBody
    public JsonResult<Object> paySuccess(@RequestParam(value = "productOrderSn", required = false) String productOrderSn) {
        String data = "{\"amount\":0.01,\"cardType\":\"\",\"contractCode\":\"\",\"couponFee\":0.00,\"couponId\":\"\",\"debtStatus\":0,\"discountAmount\":0.00,\"errCode\":\"\",\"errMsg\":\"订单已关闭\",\"goodsTag\":\"\",\"mchType\":33,\"openId\":\"oo4Ia60PPhwiQNSg3iZ0sPF3TErM\",\"orderNo\":"+productOrderSn+",\"payWay\":28,\"paymentNo\":\"14222208040900000025\",\"paymentType\":3,\"provinceId\":0,\"serviceAmount\":0.00,\"status\":2,\"type\":42,\"transactionId\":1991826815}";
        channelProductOrderBusiness.PaySuccess(data);
        return JsonResult.ok();
    }

    @PostMapping("/cos")
    @ResponseBody
    public JsonResult<Object> cos(@RequestBody String params) {
        CosSyncJobDTO dto = JSONObject.parseObject(params, CosSyncJobDTO.class);
        cosSyncJobBusiness.syncByDate(dto);
        return JsonResult.ok();
    }

    @PostMapping("/delCos")
    @ResponseBody
    public JsonResult<Object> delCos(@RequestBody String params) {
        CosDelJobDTO dto = JSONObject.parseObject(params, CosDelJobDTO.class);
        cosSyncJobBusiness.delCos(dto);
        return JsonResult.ok();
    }
}
