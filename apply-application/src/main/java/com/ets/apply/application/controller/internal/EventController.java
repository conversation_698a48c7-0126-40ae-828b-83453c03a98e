package com.ets.apply.application.controller.internal;

import com.ets.apply.application.app.business.EventBusiness;
import com.ets.apply.application.app.thirdservice.request.mqProducer.MqEventTriggerDTO;
import com.ets.apply.application.common.dto.event.EventTriggerDTO;
import com.ets.apply.application.controller.BaseController;
import com.ets.common.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

@RequestMapping("/event")
@RefreshScope
@RestController
@Slf4j
public class EventController extends BaseController {

    @Autowired
    private EventBusiness eventBusiness;

    @PostMapping("/trigger")
    public JsonResult<Object> trigger(@RequestBody @Valid EventTriggerDTO dto) {

        eventBusiness.trigger(dto.getEventBeanName(), dto.getParams());

        return JsonResult.ok();
    }

    @PostMapping("/mqTrigger")
    public JsonResult<Object> mqTrigger(@RequestBody @Valid MqEventTriggerDTO dto) {

        eventBusiness.mqTrigger(dto);

        return JsonResult.ok();
    }
}
