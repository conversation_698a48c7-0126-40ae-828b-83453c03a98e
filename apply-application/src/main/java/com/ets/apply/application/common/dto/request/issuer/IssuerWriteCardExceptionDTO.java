package com.ets.apply.application.common.dto.request.issuer;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class IssuerWriteCardExceptionDTO {

    @JsonProperty(value = "order_sn")
    private String orderSn;
    private String steps = "writeCardExceptionHandling";
    @JsonProperty(value = "card_no")
    private String cardNo;
    @JsonProperty(value = "plate_no")
    private String plateNo;
    @JsonProperty(value = "plate_color")
    private Integer plateColor;
    private String file_name = "aaa";
    @JsonProperty(value = "trade_records_list")
    private String tradeRecordsList;
    @JsonProperty(value = "callback_url")
    private String callbackUrl = "";

    @Data
    public static class TradeRecord {
        @JSONField(name = "InPay")
        private String InPay;
        @JSONField(name = "InTransData")
        private String InTransData;
        @JSONField(name = "InTransTime")
        private String InTransTime;
        @JSONField(name = "LinkNo")
        private String LinkNo;
        @JSO<PERSON>ield(name = "TerminalId")
        private String TerminalId;
        @JSONField(name = "TransId")
        private String TransId;
        @JSONField(name = "TransMoney")
        private String TransMoney;
    }
}