package com.ets.apply.application.common.consts.taskRecord;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ProcessingEnum {

    IS_PROCESSING_NO(0, "非处理中"),
    IS_PROCESSING_YES(1, "处理中");

    private final int code;
    private final String description;

    public static String getDescByCode(int code) {
        for (ProcessingEnum node : ProcessingEnum.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }
}
