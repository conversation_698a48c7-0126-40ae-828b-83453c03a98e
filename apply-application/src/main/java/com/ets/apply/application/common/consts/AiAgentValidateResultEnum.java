package com.ets.apply.application.common.consts;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum AiAgentValidateResultEnum {

    // 无占用（0）；我司车牌占用（1）；非我司车牌占用（2）
    NULL(null, ""),
    NO_USE(0, "无占用"),
    OUR_COMPANY(1, "我司占用"),
    NOT_OUR_COMPANY(2, "非我司占用");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;
    static {
        map = Arrays.stream(AiAgentValidateResultEnum.values()).collect(Collectors.toMap(AiAgentValidateResultEnum::getValue, AiAgentValidateResultEnum::getDesc));
    }
    
}
