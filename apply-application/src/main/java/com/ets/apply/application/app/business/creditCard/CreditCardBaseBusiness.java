package com.ets.apply.application.app.business.creditCard;


import com.ets.apply.application.app.thirdservice.feign.CallPhpApplyFeign;
import com.ets.apply.application.common.consts.activityCreditCardPassLog.ActivityCreditCardPassLogForcePassEnum;
import com.ets.apply.application.common.consts.activityCreditCardPassLog.ActivityCreditCardPassLogIsPassEnum;
import com.ets.apply.application.common.dto.bank.CreditCardActivateNotifyDTO;
import com.ets.apply.application.common.dto.request.creditCard.CreditCardChangeNotifyDTO;
import com.ets.apply.application.common.dto.request.creditCard.CreditCardFirstAuditNotifyDTO;
import com.ets.apply.application.infra.entity.ActivityCreditCardBankUsersEntity;
import com.ets.apply.application.infra.entity.ActivityCreditCardOriginalDataLogEntity;
import com.ets.apply.application.infra.entity.ActivityCreditCardPassLogEntity;
import com.ets.apply.application.infra.service.ActivityCreditCardBankUsersService;
import com.ets.apply.application.infra.service.ActivityCreditCardOriginalDataLogService;
import com.ets.apply.application.infra.service.ActivityCreditCardPassLogService;
import com.ets.common.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class CreditCardBaseBusiness {
    @Autowired
    private ActivityCreditCardPassLogService activityCreditCardPassLogService;

    @Autowired
    private CallPhpApplyFeign callPhpApplyFeign;

    @Autowired
    private ActivityCreditCardOriginalDataLogService activityCreditCardOriginalDataLogService;

    @Autowired
    private ActivityCreditCardBankUsersService activityCreditCardBankUsersService;

    /**
     * 创建审核通过日志，旧系统的兼容逻辑
     */
    public void createPassLog(Long uid, Integer userInfoId, Integer bankInfoId, Boolean isPass) {
        ActivityCreditCardPassLogEntity creditCardPassLogEntity = new ActivityCreditCardPassLogEntity();
        creditCardPassLogEntity.setUid(uid);
        creditCardPassLogEntity.setCreditCardBankUserId(bankInfoId);
        creditCardPassLogEntity.setCreditCardUserInfoId(userInfoId);
        creditCardPassLogEntity.setOperatorId(uid);
        creditCardPassLogEntity.setIsForcePass(ActivityCreditCardPassLogForcePassEnum.FORCE_PASS_NOT.getCode());
        creditCardPassLogEntity.setIsPass(isPass ? ActivityCreditCardPassLogIsPassEnum.PASS_YES.getCode() : ActivityCreditCardPassLogIsPassEnum.PASS_NOT.getCode());
        activityCreditCardPassLogService.create(creditCardPassLogEntity);
    }


    /**
     * 进件通知
     * @param uid
     * @param referType
     * @param classify
     * @param applyNumber
     */
    public void creditCardSubmitNotify(Long uid, Integer referType, Integer classify,String applyNumber) {
        // 成功通知申办进行发货
        ActivityCreditCardBankUsersEntity bankUsersEntity = activityCreditCardBankUsersService.getOneByColumn(applyNumber, ActivityCreditCardBankUsersEntity::getApplyNumber);
        if (bankUsersEntity == null) {
            log.info("通知申办信用卡提交失败，未找到申办信息:" + applyNumber);
            return;
        }
        CreditCardChangeNotifyDTO dto = new CreditCardChangeNotifyDTO();
        BeanUtils.copyProperties(bankUsersEntity, dto);
        dto.setUid(uid);
        dto.setClassify(classify);
        dto.setReferType(referType);

        try {
            String jsonResult = callPhpApplyFeign.creditCardSubmittedNotify(dto);
            JsonResult<Object> result = JsonResult.convertFromJsonStr(jsonResult, Object.class);
            result.checkError();
        } catch (Exception e) {
            log.info("通知申办信用卡提交失败:" + uid);
        }
    }


    /**
     * 信用卡激活通知
     */
    public void creditCardActivateNotify(Long uid, Integer referType, Integer classify,String applyNumber) {
        // 激活成功通知申办进行发货
        ActivityCreditCardBankUsersEntity bankUsersEntity = activityCreditCardBankUsersService.getOneByColumn(applyNumber, ActivityCreditCardBankUsersEntity::getApplyNumber);
        if (bankUsersEntity == null) {
            log.info("通知申办信用卡激活失败，未找到申办信息:" + applyNumber);
            return;
        }
        CreditCardChangeNotifyDTO dto = new CreditCardChangeNotifyDTO();
        BeanUtils.copyProperties(bankUsersEntity, dto);
        dto.setUid(uid);
        dto.setClassify(classify);
        dto.setReferType(referType);

        try {
            String jsonResult = callPhpApplyFeign.creditCardActivateNotify(dto);
            JsonResult<Object> result = JsonResult.convertFromJsonStr(jsonResult, Object.class);
            result.checkError();
        } catch (Exception e) {
            log.info("通知申办信用激活失败:" + dto.getApplyNumber());
        }
    }

    public void creditCardActivateNotify(CreditCardActivateNotifyDTO notifyDTO) {
        // 激活成功通知申办进行发货
        ActivityCreditCardBankUsersEntity bankUsersEntity = activityCreditCardBankUsersService.getOneByColumn(notifyDTO.getApplyNumber(), ActivityCreditCardBankUsersEntity::getApplyNumber);
        if (bankUsersEntity == null) {
            log.info("通知申办信用卡激活失败，未找到申办信息:" + notifyDTO.getApplyNumber());
            return;
        }
        CreditCardChangeNotifyDTO dto = new CreditCardChangeNotifyDTO();
        BeanUtils.copyProperties(bankUsersEntity, dto);
        dto.setUid(notifyDTO.getUid());
        dto.setClassify(notifyDTO.getClassify());
        dto.setReferType(notifyDTO.getReferType());

        try {
            String jsonResult = callPhpApplyFeign.creditCardActivateNotify(dto);
            JsonResult<Object> result = JsonResult.convertFromJsonStr(jsonResult, Object.class);
            result.checkError();
        } catch (Exception e) {
            log.info("通知申办信用激活失败:" + dto.getApplyNumber());
        }
    }

    public void creditCardFirstAuditNotify(CreditCardFirstAuditNotifyDTO dto) {
        // 初审通过通知申办
        try {
            String jsonResult = callPhpApplyFeign.creditCardFirstAuditNotify(dto);
            JsonResult<Object> result = JsonResult.convertFromJsonStr(jsonResult, Object.class);
            result.checkError();
        } catch (Exception e) {
            log.info("通知申办信用初审失败:" + dto.getApplyNumber());
        }
    }

    public void creditCardAuditNotify(Long uid, Integer referType, Integer classify,String applyNumber) {
        // 审核通知申办
        ActivityCreditCardBankUsersEntity bankUsersEntity = activityCreditCardBankUsersService.getOneByColumn(applyNumber, ActivityCreditCardBankUsersEntity::getApplyNumber);
        if (bankUsersEntity == null) {
            log.info("通知申办信用卡审核失败，未找到申办信息:" + applyNumber);
            return;
        }
        CreditCardChangeNotifyDTO dto = new CreditCardChangeNotifyDTO();
        BeanUtils.copyProperties(bankUsersEntity, dto);
        dto.setUid(uid);
        dto.setClassify(classify);
        dto.setReferType(referType);
        try {
            String jsonResult = callPhpApplyFeign.creditCardAuditNotify(dto);
            JsonResult<Object> result = JsonResult.convertFromJsonStr(jsonResult, Object.class);
            result.checkError();
        } catch (Exception e) {
            log.info("通知申办信用审核失败:" + dto.getApplyNumber());
        }
    }

    /**
     * 通知申办信用卡审核失败
     */
    public void creditCardAuditFailedNotify(Long uid, Integer referType, Integer classify,String applyNumber) {
        ActivityCreditCardBankUsersEntity bankUsersEntity = activityCreditCardBankUsersService.getOneByColumn(applyNumber, ActivityCreditCardBankUsersEntity::getApplyNumber);
        if (bankUsersEntity == null) {
            log.info("通知申办信用卡激活失败，未找到申办信息:" + applyNumber);
            return;
        }
        CreditCardChangeNotifyDTO dto = new CreditCardChangeNotifyDTO();
        BeanUtils.copyProperties(bankUsersEntity, dto);
        dto.setUid(uid);
        dto.setClassify(classify);
        dto.setReferType(referType);
        try {
            String jsonResult = callPhpApplyFeign.creditCardAuditFailedNotify(dto);
            JsonResult<Object> result = JsonResult.convertFromJsonStr(jsonResult, Object.class);
            result.checkError();
        } catch (Exception e) {
            log.info("通知申办信用审核失败:" + dto.getApplyNumber());
        }
    }

    /**
     * 首刷成功通知
     */
    public void creditCardFirstUsedNotify(Long uid, Integer referType, Integer classify,String applyNumber) {
        ActivityCreditCardBankUsersEntity bankUsersEntity = activityCreditCardBankUsersService.getOneByColumn(applyNumber, ActivityCreditCardBankUsersEntity::getApplyNumber);
        if (bankUsersEntity == null) {
            log.info("通知申办信用卡激活失败，未找到申办信息:" + applyNumber);
            return;
        }
        CreditCardChangeNotifyDTO dto = new CreditCardChangeNotifyDTO();
        BeanUtils.copyProperties(bankUsersEntity, dto);
        dto.setUid(uid);
        dto.setClassify(classify);
        dto.setReferType(referType);
        try {
            String jsonResult = callPhpApplyFeign.creditCardFirstUsedNotify(dto);
            JsonResult<Object> result = JsonResult.convertFromJsonStr(jsonResult, Object.class);
            result.checkError();
        } catch (Exception e) {
            log.info("通知申办信用审核失败:" + dto.getApplyNumber());
        }
    }



    /**
     * 信用卡原始数据记录
     * @param orderSn
     * @param originalData
     * @param whichBank
     * @param dateType
     * @param method
     */
    public void saveOriginalDataLog(String orderSn, Object originalData,Integer whichBank,Integer dateType,String method) {
        ActivityCreditCardOriginalDataLogEntity entity = new ActivityCreditCardOriginalDataLogEntity();
        entity.setBankData(originalData.toString());
        entity.setOrderSn(orderSn);
        entity.setDataType(dateType);
        entity.setWhichBank(whichBank);
        entity.setLogMethod(method);
        activityCreditCardOriginalDataLogService.addLog(entity);
    }

}