package com.ets.apply.application.app.business.aftersalesreviews;

import cn.hutool.core.bean.BeanUtil;
import com.ets.apply.application.common.dto.aftersalesreviews.CancelAfterSalesReviewDTO;
import com.ets.apply.application.common.dto.aftersalesreviews.CreateAfterSalesReviewDTO;
import com.ets.apply.application.common.consts.aftersalesreviews.AftersalesReviewsDataTypeEnum;
import com.ets.apply.application.common.consts.aftersalesreviews.AftersalesReviewsStatusEnum;
import com.ets.apply.application.infra.entity.aftersalesreview.AftersalesReviews;
import com.ets.apply.application.infra.entity.aftersalesreview.AftersalesReviewsVehicles;
import com.ets.apply.application.infra.service.AftersalesReviewsService;
import com.ets.apply.application.infra.service.AftersalesReviewsVehiclesService;
import com.ets.common.ToolsHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;

/**
 * 售后审核单业务逻辑
 */
@Slf4j
@Component
public class AfterSalesReviewsBusiness {

    @Autowired
    private AftersalesReviewsService aftersalesReviewsService;

    @Autowired
    private AftersalesReviewsVehiclesService aftersalesReviewsVehiclesService;

    @Resource(name = "redisPermanentTemplate")
    private StringRedisTemplate redisPermanentTemplate;

    /**
     * 创建售后审核单
     *
     * @param dto 创建售后审核单请求参数
     * @return 售后审核单
     */
    @Transactional(rollbackFor = Exception.class)
    public AftersalesReviews createAfterSalesReview(CreateAfterSalesReviewDTO dto) {
                // 创建审核单
        AftersalesReviews aftersalesReviews = BeanUtil.copyProperties(dto, AftersalesReviews.class);

        // 生成审核单流水号
        String reviewSn = ToolsHelper.genNum(redisPermanentTemplate, "AftersalesReviews", "prod", 8);

        // 设置属性
        aftersalesReviews.setReviewSn(reviewSn);
        aftersalesReviews.setApplyTime(LocalDateTime.now());

        // 设置状态
        aftersalesReviews.setReviewStatus(AftersalesReviewsStatusEnum.PENDING.getValue()); // 待审核

        // 保存审核单
        aftersalesReviewsService.create(aftersalesReviews);

        // 保存审核行驶证信息
        if (dto.getReviewVehicleInfo() != null) {
            saveVehicleInfo(reviewSn, dto.getReviewVehicleInfo(), AftersalesReviewsDataTypeEnum.REVIEW_DATA.getValue());
        }

        // 保存订单行驶证信息
        if (dto.getOrderVehicleInfo() != null) {
            saveVehicleInfo(reviewSn, dto.getOrderVehicleInfo(), AftersalesReviewsDataTypeEnum.ORDER_DATA.getValue());
        }

        log.info("创建售后审核单成功，审核单号：{}", reviewSn);

        return aftersalesReviews;
    }

    /**
     * 保存行驶证信息
     *
     * @param reviewSn 审核单号
     * @param vehicleInfo 行驶证信息
     * @param dataType 数据类型 [1-审核资料 2-申办资料]
     */
    private void saveVehicleInfo(String reviewSn, CreateAfterSalesReviewDTO.VehicleInfoDTO vehicleInfo, Integer dataType) {
        AftersalesReviewsVehicles vehicle = BeanUtil.copyProperties(vehicleInfo, AftersalesReviewsVehicles.class);
        vehicle.setReviewSn(reviewSn);
        vehicle.setDataType(dataType);
        vehicle.setCreatedAt(LocalDateTime.now());
        vehicle.setUpdatedAt(LocalDateTime.now());

        aftersalesReviewsVehiclesService.create(vehicle);
    }

    /**
     * 取消售后审核单
     *
     * @param dto 取消售后审核单请求参数
     * @return 是否取消成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelAfterSalesReview(CancelAfterSalesReviewDTO dto) {
        // 查询审核单
        AftersalesReviews aftersalesReviews = aftersalesReviewsService.getById(dto.getReviewSn());
        if (aftersalesReviews == null) {
            ToolsHelper.throwException("审核单不存在");
        }

        // 检查审核单状态
        if (aftersalesReviews.getReviewStatus().equals(AftersalesReviewsStatusEnum.CANCELED.getValue())) {
            ToolsHelper.throwException("审核单已取消");
        }

        if (aftersalesReviews.getReviewStatus().equals(AftersalesReviewsStatusEnum.APPROVED.getValue())) {
            ToolsHelper.throwException("审核单已通过，无法取消");
        }

        // 更新审核单状态
        aftersalesReviews.setReviewStatus(AftersalesReviewsStatusEnum.CANCELED.getValue()); // 已取消
        aftersalesReviews.setReviewTime(LocalDateTime.now());

        // 保存审核单
        aftersalesReviewsService.updateById(aftersalesReviews);

        log.info("取消售后审核单成功，审核单号：{}", dto.getReviewSn());

        return true;
    }
}
