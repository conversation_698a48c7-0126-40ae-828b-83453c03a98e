package com.ets.apply.application.common.consts.blacklist;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

@Getter
@AllArgsConstructor
public enum BlacklistSourceEnum {
    CCB("ccb", "建行", new String[]{"toll", "ccb"}),
    REVOKE_CONFIRM("revoke_confirm", "注销确认", new String[]{"entrust", "revoke_confirm", "revoke"}),
    TOLL("toll", "通行扣费", new String[]{"toll"}),
    ENTRUST("entrust", "签约", new String[]{"entrust"}),
    REVOKE("revoke", "注销", new String[]{"revoke"}),
    AUDIT("audit", "稽核", new String[]{"audit"});
    private final String code;

    private final String description;

    private final String[] list;
}
