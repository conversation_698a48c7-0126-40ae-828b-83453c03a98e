package com.ets.apply.application.app.service.thirdPartner;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.ets.apply.application.common.bo.amap.ResponseBO;
import com.ets.apply.application.common.config.AmapConfig;
import com.ets.apply.application.common.vo.amap.BizResponseVO;
import com.ets.common.ToolsHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.*;

@Slf4j
@Service
public class AmapService {

    @Autowired
    private AmapConfig amapConfig;
    private static final RestTemplate restTemplate = new RestTemplate();

    /**
     * 使用商家私钥生成签名
     *
     */
    public String generateSign(Map<String, String> paramMap) throws Exception {
        //使用商家私钥进行加签，请在高德云店「接入准备及配置」页面生成并获取商家私钥
        String signContent = getSignContent(paramMap);
        return getSign(signContent, this.amapConfig.getEtcRsaPrivateKey());
    }

    /**
     * 使用高德公钥验证签名
     *
     */
    public boolean checkSign(Map<String, String> paramFromAmap) {
        try {
            //使用高德公钥进行验签，请在高德云店「API对接信息」页面直接获取高德公钥
            String signContent = getSignContent(paramFromAmap);
            return checkSign(signContent, paramFromAmap.get("sign"), this.amapConfig.getAmapPublicKey());
        } catch (Exception e) {
            ToolsHelper.throwException("签名验证失败:" + e.getMessage());
        }
        return false;
    }


    /**
     * 参数转换为待加签字符串
     *
     * @param paramMap 待生成加密sign的参数集合
     */
    private static String getSignContent(Map<String, String> paramMap) {
        StringBuilder content = new StringBuilder();
        List<String> keys = new ArrayList<>(paramMap.keySet());
        // 将参数集合排序
        Collections.sort(keys);
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            //排除不需要参与签名的公共参数
            if ("sign_type".equals(key) || "sign".equals(key) || "need_encrypt".equals(key)) {
                continue;
            }
            String value = paramMap.get(key);
            // 拼装所有非空参数
            if (key != null && !"".equalsIgnoreCase(key) && value != null && !"".equalsIgnoreCase(value)) {
                content.append(i == 0 ? "" : "&").append(key).append("=").append(value);
            }
        }
        return content.toString();
    }

    /**
     * 字符串加签
     *
     */
    private static String getSign(String signContent, String merchantPrivateKey) throws IOException, GeneralSecurityException {
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        byte[] encodedKey = readText(new ByteArrayInputStream(merchantPrivateKey.getBytes())).getBytes();
        encodedKey = Base64.getDecoder().decode(encodedKey);
        PrivateKey priKey = keyFactory.generatePrivate(new PKCS8EncodedKeySpec(encodedKey));

        Signature signature = Signature.getInstance("SHA256WithRSA");
        signature.initSign(priKey);
        signature.update(signContent.getBytes(StandardCharsets.UTF_8));
        byte[] signed = signature.sign();
        return new String(Base64.getEncoder().encode(signed));
    }

    private static String readText(InputStream in) throws IOException {
        Reader reader = new InputStreamReader(in);
        StringWriter writer = new StringWriter();

        int bufferSize = 4096;
        char[] buffer = new char[bufferSize];
        int amount;
        while ((amount = reader.read(buffer)) >= 0) {
            writer.write(buffer, 0, amount);
        }
        return writer.toString();
    }

    /**
     * 对加密字符串进行验签
     *
     */
    private static boolean checkSign(String content, String sign, String amapPublicKey) throws IOException, GeneralSecurityException {
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        StringWriter writer = new StringWriter();
        io(new InputStreamReader(new ByteArrayInputStream(amapPublicKey.getBytes())), writer);
        byte[] encodedKey = writer.toString().getBytes();
        encodedKey = org.apache.commons.codec.binary.Base64.decodeBase64(encodedKey);
        PublicKey pubKey = keyFactory.generatePublic(new X509EncodedKeySpec(encodedKey));
        Signature signature = Signature.getInstance("SHA256WithRSA");
        signature.initVerify(pubKey);
        signature.update(content.getBytes(StandardCharsets.UTF_8));
        return signature.verify(Base64.getDecoder().decode(sign.getBytes()));
    }

    public static void io(Reader in, Writer out) throws IOException {
        int bufferSize = 4096;
        char[] buffer = new char[bufferSize];
        int amount;
        while ((amount = in.read(buffer)) >= 0) {
            out.write(buffer, 0, amount);
        }
    }


    public BizResponseVO successResponse(Object respData) {
        // 商家成功响应
        BizResponseVO.Response successResp = new BizResponseVO.Response();
        // 成功响应code固定为10000
        successResp.setCode("10000");
        successResp.setMsg("请求成功");
        successResp.setData(respData);
        return new BizResponseVO(successResp);
    }


    /**
     * 失败响应 格式化返回数据
     */
    public BizResponseVO failResponse(String code,String message) {
        // 商家失败响应
        BizResponseVO.Response failResp = new BizResponseVO.Response();
        // 业务异常响应code默认为40004，可以根据具体接口文档定义
        // 成功响应code固定为10000
        failResp.setCode("40004");
        failResp.setMsg(message);
        HashMap<String,String> successData = new HashMap<>();
        successData.put("subMsg",message);
        successData.put("subCode",code);
        failResp.setData(successData);
        return new BizResponseVO(failResp);
    }


    public String requestAmap(String method, Map<String, String> paramMap) throws Exception {
        // 构造参与加签的公共参数
        paramMap.put("app_id", amapConfig.getAppId());
        paramMap.put("utc_timestamp", String.valueOf(System.currentTimeMillis()));
        paramMap.put("version", "1.0");
        paramMap.put("charset", "UTF-8");
        paramMap.put("method", method);
        paramMap.put("sign", this.generateSign(paramMap));
        paramMap.put("sign_type", "RSA2");
        log.info("请求高德参数：{}", JSON.toJSONString(paramMap));
        // 定义Content-type、初始化HttpEntity
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        MultiValueMap<String, Object> formBody = convertToMultiValueMap(paramMap);
        HttpEntity<MultiValueMap<String, Object>> entity = new HttpEntity<>(formBody, headers);

        // 调用高德接口
        ResponseEntity<String> resp = restTemplate.postForEntity(amapConfig.getUrl(), entity, String.class);
        log.info("请求高德"+method+"返回：{}", resp.getBody());
        if (resp.getBody() != null && !"".equals(resp.getBody())) {
            // ResonseBO
            ResponseBO responseBO = JSON.parseObject(resp.getBody(), ResponseBO.class);
            if(ObjectUtil.isEmpty(responseBO) || ObjectUtil.isEmpty(responseBO.getResponse())){
                ToolsHelper.throwException("请求高德异常，接口返回为空");
            }
            if (!"10000".equals(responseBO.getResponse().getCode())) {
                ToolsHelper.throwException("请求高德接口失败："+"错误码"+responseBO.getResponse().getCode() + "错误信息" + responseBO.getResponse().getMsg());
            }
            if(!"200".equals(responseBO.getResponse().getData().getSubCode())){
                ToolsHelper.throwException("请求高德接口业务处理失败："+"错误码"+responseBO.getResponse().getData().getSubCode() + "错误信息" + responseBO.getResponse().getData().getSubMsg());
            }

            return responseBO.getResponse().getData().toString();
        }
        ToolsHelper.throwException("请求高德异常，接口返回为空");
        return null;
    }

    private static MultiValueMap<String, Object> convertToMultiValueMap(Map<String, String> paramMap) {
        MultiValueMap<String, Object> multiValueMap = new LinkedMultiValueMap<>();
        for (Map.Entry<String, String> entry : paramMap.entrySet()) {
            multiValueMap.add(entry.getKey(), entry.getValue());
        }
        return multiValueMap;
    }

}
