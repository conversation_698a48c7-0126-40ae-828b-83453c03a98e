package com.ets.apply.application.common.dto.creditCardBank;

import jakarta.validation.constraints.Min;
import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@Data
public class CreditCardBankListDTO {
    @Min(1)
    private Integer pageNum = 1;
    @Min(1)
    private Integer pageSize = 20;

    private String bankName;
    private Integer bankType;
    private LocalDate updatedAtStart;
    private LocalDate updatedAtEnd;
}
