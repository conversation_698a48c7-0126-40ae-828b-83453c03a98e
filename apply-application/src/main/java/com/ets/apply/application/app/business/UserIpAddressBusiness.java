package com.ets.apply.application.app.business;

import com.alibaba.fastjson.JSON;
import com.ets.apply.application.app.disposer.UserIpAddressUploadDisposer;
import com.ets.apply.application.app.thirdservice.feign.BaseApplicationFeign;
import com.ets.apply.application.app.thirdservice.feign.BigDataLogFeign;
import com.ets.apply.application.app.thirdservice.request.bigData.UserIpAddressEtcLogDTO;
import com.ets.apply.application.app.thirdservice.response.IpAddressInfoVO;
import com.ets.apply.application.common.bo.UserIpAddressUploadBO;
import com.ets.common.RequestHelper;
import com.ets.common.util.UserUtil;
import com.ets.starter.queue.QueueDefault;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class UserIpAddressBusiness {

    @Autowired
    private BaseApplicationFeign baseApplicationFeign;

    @Autowired
    private BigDataLogFeign bigDataLogFeign;

    @Autowired
    private QueueDefault queueDefault;

    public void userIp() {
        Long uid = UserUtil.getUid();
        String ip = RequestHelper.getHttpServletRequest().getRemoteAddr();

        try {
            UserIpAddressUploadBO bo = new UserIpAddressUploadBO();
            bo.setUid(uid);
            bo.setIp(ip);
            queueDefault.push(new UserIpAddressUploadDisposer(bo));
        } catch (Throwable e) {
            log.error("上报用户ip地址信息推送队列异常", e);
        }
    }

    public void uploadUserIpAddress(UserIpAddressUploadBO bo) {
        Long uid = bo.getUid();
        String ip = bo.getIp();

        try {
            // 获取ip地址信息
            IpAddressInfoVO ipAddressInfoVO = baseApplicationFeign.getIpAddressInfo(ip).getDataWithCheckError();
            if (ObjectUtils.isEmpty(ipAddressInfoVO)) {
                return;
            }

            // 上传大数据埋点
            UserIpAddressEtcLogDTO logDTO = new UserIpAddressEtcLogDTO();
            logDTO.setUserId(uid);
            logDTO.setActionId(bo.getActionId());
            logDTO.setIpAddr(ipAddressInfoVO.getIp());
            logDTO.setIpToCountry(ipAddressInfoVO.getCountry());
            logDTO.setIpToProvince(ipAddressInfoVO.getProvince());
            logDTO.setIpToCity(ipAddressInfoVO.getCity());
            logDTO.setPagesTimestamp(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());

            Map<String, String> pathQuery = genPathQuery(ipAddressInfoVO);
            logDTO.setPathQuery(URLEncoder.encode(JSON.toJSONString(pathQuery), "UTF-8"));

            Map<String, Object> params = new HashMap<>();
            params.put("data", Collections.singletonList(logDTO));
            bigDataLogFeign.etcLog(JSON.toJSONString(params));
        } catch (Throwable e) {
            log.error("查询上报用户ip地址信息异常", e);
        }
    }

    private static Map<String, String> genPathQuery(IpAddressInfoVO ipAddressInfoVO) {
        Map<String, String> pathQuery = new HashMap<>();
        pathQuery.put("ip", ipAddressInfoVO.getIp());
        pathQuery.put("country", ipAddressInfoVO.getCountry());
        pathQuery.put("province", ipAddressInfoVO.getProvince());
        pathQuery.put("city", ipAddressInfoVO.getCity());
        pathQuery.put("districts", ipAddressInfoVO.getDistricts());
        pathQuery.put("provinceCode", ipAddressInfoVO.getProvinceCode());
        pathQuery.put("cityCode", ipAddressInfoVO.getCityCode());
        pathQuery.put("districtsCode", ipAddressInfoVO.getDistrictCode());
        return pathQuery;
    }
}
