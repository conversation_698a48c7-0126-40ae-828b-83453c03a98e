package com.ets.apply.application.app.thirdservice.fallback;

import com.ets.apply.application.app.thirdservice.feign.CallDongFengFeign;
import com.ets.common.JsonResult;

import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.Map;

@Component
public class CallDongFengFallbackFactory implements FallbackFactory<CallDongFengFeign> {

    @Override
    public CallDongFengFeign create(Throwable cause) {

        return new CallDongFengFeign() {
            @Override
            public String orderNotify(@RequestBody String body, @RequestHeader Map<String, String> headers) {
                return JsonResult.error("通知东风日产订单信息失败：orderNotify 请求第三方服务失败: " + cause.getMessage()).toString();
            }
        };
    }
}