package com.ets.apply.application.app.thirdservice.fallback;

import com.ets.apply.application.app.thirdservice.feign.DeliveryFeign;
import com.ets.apply.application.app.thirdservice.request.LogisticsFindByOrderSnDTO;
import com.ets.apply.application.app.thirdservice.request.delivery.GetSkuSummaryDTO;
import com.ets.apply.application.app.thirdservice.request.delivery.RiskCheckInfoUploadDTO;
import com.ets.apply.application.app.thirdservice.response.LogisticsVO;
import com.ets.apply.application.app.thirdservice.response.delivery.GetSkuSummaryByExpressNumberVO;
import com.ets.common.JsonResult;
import com.ets.delivery.feign.request.logistics.LogisticsAttemptCancelDTO;
import com.ets.delivery.feign.response.logistics.AttemptCancelVO;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

import jakarta.validation.Valid;

@Component
public class DeliveryFallbackFactory implements FallbackFactory<DeliveryFeign> {
    @Override
    public DeliveryFeign create(Throwable throwable) {
        return new DeliveryFeign() {
            @Override
            public JsonResult<LogisticsVO> findByOrderSn(@RequestBody @Valid LogisticsFindByOrderSnDTO findByOrderSnDTO) {
                return JsonResult.error("调用DELIVERY服务【获取发货单】失败：" + findByOrderSnDTO.toString());
            }

            @Override
            public JsonResult<GetSkuSummaryByExpressNumberVO> getSkuSummaryByExpressNumber(GetSkuSummaryDTO getSkuSummaryDTO) {
                return JsonResult.error("调用DELIVERY服务【获取发货总数】失败：" + throwable.getMessage());
            }

            @Override
            public JsonResult<?> riskInfoUpload(RiskCheckInfoUploadDTO riskCheckInfoU) {
                return JsonResult.error("调用DELIVERY服务【上传风控补传资料】失败：" + throwable.getMessage());
            }

            @Override
            public JsonResult<AttemptCancelVO> attemptCancel(LogisticsAttemptCancelDTO logisticsAttemptCancelDTO) {
                return JsonResult.error("调用DELIVERY服务【取消发货单】失败：" + throwable.getMessage());
            }
        };
    }
}
