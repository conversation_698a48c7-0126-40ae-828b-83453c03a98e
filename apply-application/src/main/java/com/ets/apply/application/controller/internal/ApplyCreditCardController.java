package com.ets.apply.application.controller.internal;

import com.ets.apply.application.app.business.applyCreditCard.ApplyCreditCardBusiness;
import com.ets.apply.application.app.business.creditCard.CreditCardBusiness;
import com.ets.apply.application.common.dto.request.bank.common.CheckCanApply;
import com.ets.apply.application.common.dto.request.creditCard.ApplyOrderDTO;
import com.ets.apply.application.common.dto.request.creditCard.CreditCardBankInfoV2DTO;
import com.ets.apply.application.common.vo.creditCard.CheckCanApplyVO;
import com.ets.apply.application.common.dto.request.creditCard.CreditCardLimitDTO;
import com.ets.apply.application.common.vo.creditCard.CreditCardApplyOrderVO;
import com.ets.apply.application.common.vo.creditCard.CreditCardBankInfoVO;
import com.ets.apply.application.common.vo.creditCard.CreditCardLimitBankVO;
import com.ets.apply.application.controller.BaseController;
import com.ets.common.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import jakarta.validation.Valid;
import java.util.List;

@RequestMapping("/applyCreditCard")
@RefreshScope
@RestController
@Slf4j
public class ApplyCreditCardController extends BaseController {

    @Autowired
    private ApplyCreditCardBusiness applyCreditCardBusiness;

    @Autowired
    private CreditCardBusiness creditCardBusiness;

    /**
     * 申请信用卡
     */
    @PostMapping("/applyOrder")
    public JsonResult<CreditCardApplyOrderVO> applyOrder(@RequestBody @Valid ApplyOrderDTO applyOrderDTO) {
        return JsonResult.ok(applyCreditCardBusiness.applyOrder(applyOrderDTO));
    }

    /**
     *  <a href="https://yapi.etczs.net/project/312/interface/api/31764">...</a>
     */
    @PostMapping("/checkCanApply")
    public JsonResult<List<CheckCanApplyVO>> checkCanApply(@RequestBody @Valid CheckCanApply checkCanApply) {

        return JsonResult.ok(creditCardBusiness.checkCanApply(checkCanApply));
    }


    /**
     * yapi:<a href="https://yapi.etczs.net/project/312/interface/api/33594"></a>
     * 查询信用卡办理限制
     */
    @PostMapping("/checkCreditCardLimit")
    public JsonResult<CreditCardLimitBankVO> checkCreditCardLimit(@RequestBody @Valid CreditCardLimitDTO creditCardLimitDTO) {

        return JsonResult.ok(creditCardBusiness.checkCreditCardLimit(creditCardLimitDTO));
    }

    @PostMapping("/bankInfo")
    public JsonResult<CreditCardBankInfoVO> bankInfo(@RequestBody @Valid CreditCardBankInfoV2DTO dto) {
        return JsonResult.ok(creditCardBusiness.bankInfo(dto));
    }

}
