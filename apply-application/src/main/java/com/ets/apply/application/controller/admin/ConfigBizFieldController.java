package com.ets.apply.application.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.apply.application.app.business.ConfigBizFieldBusiness;
import com.ets.apply.application.common.dto.configBizField.*;
import com.ets.apply.application.common.vo.configBizField.ConfigBizFieldListVO;
import com.ets.apply.application.common.vo.configBizField.ConfigBizFieldValuesListVO;
import com.ets.apply.application.controller.BaseController;
import com.ets.common.JsonResult;
import com.ets.common.RequestHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import jakarta.validation.Valid;

@Controller
@RequestMapping("/admin/configBizField")
public class ConfigBizFieldController extends BaseController {
    @Autowired
    private ConfigBizFieldBusiness configBizFieldBusiness;

    /**
     * 业务字段列表查询
     * @return
     */
    @RequestMapping("/getList")
    @ResponseBody
    public JsonResult<IPage<ConfigBizFieldListVO>> getList(@RequestBody(required = false) @Valid ConfigBizFieldDTO dto) {
        return JsonResult.ok(configBizFieldBusiness.getAllList(dto));
    }


    /**
     * 获取产品包列表
     * @return
     */
    @RequestMapping("/getValueslist")
    @ResponseBody
    public JsonResult<IPage<ConfigBizFieldValuesListVO>> getValueslist(@RequestBody(required = false) @Valid ConfigBizFieldValuesDTO dto) {
        return JsonResult.ok(configBizFieldBusiness.getValueslist(dto));
    }

    /**
     *  添加产品包
     * @return
     */
    @RequestMapping("/addValues")
    @ResponseBody
    public JsonResult<Boolean> addValues(@RequestBody(required = false) @Valid ConfigBizFieldValuesAddDTO dto) {
        configBizFieldBusiness.addValues(dto, RequestHelper.getHttpServletRequest().getHeader("loginCode"));
        return JsonResult.ok(true);
    }
    /**
     *  修改产品包
     * @return
     */
    @RequestMapping("/modifyValues")
    @ResponseBody
    public JsonResult<Boolean> modifyValues(@RequestBody(required = false) @Valid ConfigBizFieldValuesModifyDTO dto) {
        configBizFieldBusiness.modifyValues(dto, RequestHelper.getHttpServletRequest().getHeader("loginCode"));
        return JsonResult.ok(true);
    }

    /**
     *  修改产品包
     * @return
     */
    @RequestMapping("/delValues")
    @ResponseBody
    public JsonResult<Boolean> delValues(@RequestBody(required = false) @Valid ConfigBizFieldValuesDelDTO dto) {
        configBizFieldBusiness.delValues(dto, RequestHelper.getHttpServletRequest().getHeader("loginCode"));
        return JsonResult.ok(true);
    }

}
