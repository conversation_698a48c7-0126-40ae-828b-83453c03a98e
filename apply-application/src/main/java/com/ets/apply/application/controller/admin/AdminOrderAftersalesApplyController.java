package com.ets.apply.application.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.apply.application.app.business.admin.AdminOrderAftersalesApplyBusiness;
import com.ets.apply.application.common.dto.request.admin.orderAftersalesApply.AdminOrderAftersalesApplyGetListDTO;
import com.ets.apply.application.common.vo.admin.orderAftersalesApply.AdminOrderAftersalesApplyVO;
import com.ets.apply.application.controller.BaseController;
import com.ets.common.JsonResult;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 *  申办售后列表
 * </p>
 */
@RequestMapping("/admin/orderAftersalesApply")
@RefreshScope
@RestController
@Slf4j
@Validated
public class AdminOrderAftersalesApplyController extends BaseController {
    @Autowired
    private AdminOrderAftersalesApplyBusiness adminOrderAftersalesApplyBusiness;


    @RequestMapping("/getList")
    @ResponseBody
    public JsonResult<IPage<AdminOrderAftersalesApplyVO>> getList(@RequestBody(required = false) @Valid AdminOrderAftersalesApplyGetListDTO dto) {
        return JsonResult.ok(adminOrderAftersalesApplyBusiness.getList(dto));
    }
    @RequestMapping("/cancel")
    @ResponseBody
    public JsonResult<Boolean> cancel(@RequestParam(value = "applySn") String applySn, @RequestParam(value = "error") String error) {

        return JsonResult.ok( adminOrderAftersalesApplyBusiness.cancel(applySn,error));
    }
}