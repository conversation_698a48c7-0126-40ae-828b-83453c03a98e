package com.ets.apply.application.common.consts.payment;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum PaymentModeEnum {
    PAYMENT_MODE_WEIXIN(1, "普通微信签约代扣", "微信普通免密"),
    PAYMENT_MODE_VEHICLE(2, "车主平台签约代扣", "微信车主普通"),
    PAYMENT_MODE_CCB(4, "建行(北京)签约代扣", "建行直连"),
    PAYMENT_MODE_CMB(5, "招行(广西)签约代扣", "招行直连"),
    PAYMENT_MODE_WEBANK(7, "微众银行签约代扣", "微众银行签约"),
    PAYMENT_MODE_VEHICLE_BIND_BANK(9, "车主平台绑定银行卡签约代扣", "微信指定卡");

    private final int code;
    private final String description;
    private final String signName;

    public static final Map<Integer, String> signMap;

    static {
        signMap = Arrays.stream(PaymentModeEnum.values()).collect(Collectors.toMap(PaymentModeEnum::getCode, PaymentModeEnum::getSignName));
    }

    public static String getDescByCode(int code) {
        for (PaymentModeEnum node : PaymentModeEnum.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }

    public static PaymentModeEnum getEnumByCode(int code) {
        for (PaymentModeEnum node : PaymentModeEnum.values()) {
            if (node.getCode() == code) {
                return node;
            }
        }
        return null;
    }
}
