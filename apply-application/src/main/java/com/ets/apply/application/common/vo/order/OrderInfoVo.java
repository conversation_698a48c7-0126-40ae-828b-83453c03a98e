package com.ets.apply.application.common.vo.order;

import com.ets.apply.application.common.utils.DesensitizeUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class OrderInfoVo {
    /**
     * 用户ID
     */
    private Long uid;
    /**
     * 订单流水号
     */
    private String orderSn;
    /**
     * 1、待支付 2 、待发货 3、待签收 4、已完成 5、已取消
     */
    private Integer status;

    /**
     * 组合状态描述
     */
    private String combineStatusStr;
    /**
     * 审核单状态
     */
    private Integer thirdReviewStatus;
    /**
     * 订单来源：1.didi, 2.weiche, 3.kuaigou
     */
    private Integer thirdType;

    /**
     * 总计需支付（元）
     */
    private BigDecimal needPay;

    /**
     * 设备费（元）
     */
    private BigDecimal productFee;

    /**
     * 运费（元）
     */
    private BigDecimal freightFee;

    /**
     * 保证金金额
     */
    private BigDecimal depositFee;

    /**
     * 服务费金额（元）
     */
    private BigDecimal serviceFee;

    /**
     * 通行费保证金（元）
     */
    private BigDecimal tollDepositFee;

    /**
     * 商品套餐金额（元）
     */
    private BigDecimal packageFee;
    /**
     * 卡种ID
     */
    private Integer cardId;

    /**
     * 场景值 1、普通客车 2、出租车 3、储值卡转化 4、老客户转化 5、货车 6、储值卡申办（旧数据迁移用）
     */
    private Integer scene;

    /**
     * 推送订单号
     */
   // private String pushOrderSn;

    /**
     * 车牌号
     */
    private String plateNo;
    /*
     * 车牌颜色
     */
    private Integer plateColor;
    /**
     * 订单收货人姓名
     */
    private String sendName;
    /**
     * 订单收货人地区
     */
    private String sendArea;

    /**
     * 订单收货人地址
     */
    private String sendAddress;
    /**
     * 订单收货人手机
     */
    private String sendPhone;
    /**
     * 车辆归属
     */
    private Integer vehicleBelong;
    /**
     * 支付方式
     */
    private Integer purchaseType;
    /**
     * 卡所属银行/机构与类别(不仅仅是信用卡）
     */
    private Integer purchaseParty;
    /**
     * 支付模式
     */
    private Integer paymentMode;

    /**
     * 结算模式
     */
    private Integer settlementMode;

    /**
     * 车辆业务类型  1-运政车（江苏）
     */
    private Integer carBusinessType;
    /**
     * 是否自取 0不用发货 1需要发货
     */
    private Integer needOnlineDelivery;
    /**
     * 是否前装 0正常 1前装
     */
    private Integer isFront;

    /**
     * 是否出租车 0 非出租车 1 出租车
     */
    private Integer isTaxi;

    /**
     * 设备类型 0 普通 1 可充电式 2 单片式
     */
    private Integer deviceType;

    /**
     * 发行机构编码
     */
    private String IssuerServiceCode;
    /**
     * 市场营销渠道 普通-0 江苏抖音-1
     */
    private Integer marketSource;

    /*
     *  卡种信息
     */
    private OrderInfoCardVo card;

    /*
     *  卡种信息
     */
    private OrderInfoIssueServiceVo issueService;

    /**
     * 发货时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime shippedAt;

    private Integer flowType;

    private String packageSn;

    /**
     * 支付单号
     */
    private String paymentSn;
    /**
     * 退款单号
     */
    private String refundSn;

    /**
     * 没有保证金 1 使用保证金 2 保证金已使用（退款）
     */
    private Integer depositStatus;
    /**
     * 优惠券号
     */
    private String couponCode;

    /**
     * 推广渠道来源标识
     */
    private String source;

    /**
     * 订单期数[1-默认 2-二期活动支付类型]
     */
    private Integer term;
    private Integer declareReceived;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime declareReceivedTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime paidAt;

    /**
     * 通过package_info
     */
    private Object packageInfo;

    private String goodsOrderSn;

    /**
     * 业务类型
     */
    private Integer bizType;

    private IdCard idCard;

    @Data
    public static class IdCard {
        private String name;
        private String idNumber;
        private Integer idCardType;

        public IdCard(String name, String number, Integer idcardType) {
            this.name = name;
            this.idNumber = number;
            this.idCardType = idcardType;
        }

        /**
         * C 端数据强制脱敏
         */
        public String getName() {
            return DesensitizeUtil.name(this.name);
        }

        /**
         * C 端数据强制脱敏
         */
        public String getIdNumber() {
            return DesensitizeUtil.idCard(this.idNumber, this.idCardType);
        }
    }
}
