package com.ets.apply.application.controller.internal;

import com.ets.apply.application.app.business.OrderBenefitBusiness;
import com.ets.apply.application.common.dto.OrderBenefitAddDTO;
import com.ets.apply.application.controller.BaseController;
import com.ets.common.JsonResult;

import jakarta.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/order-benefit")
public class OrderBenefitController extends BaseController {

    @Autowired
    private OrderBenefitBusiness orderBenefitBusiness;

    @PostMapping("/add")
    public JsonResult<?> add(@RequestBody @Valid OrderBenefitAddDTO addDTO) {
        orderBenefitBusiness.add(addDTO);
        return JsonResult.ok();
    }

}
