package com.ets.apply.application.common.consts.issueService;

import com.ets.apply.application.common.vo.SelectOptionsVO;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum IssueServiceTypeEnum {

    ETC_COMMON(2, "江苏通用模式"),

    DONG_BEN(3, "东本纯签约"),

    SHANG_QI(11, "上汽纯签约"),

    XIN_JIANG(31, "新疆纯签约");

    private final int code;
    private final String description;

    public static String getDescByCode(int code) {
        for (IssueServiceTypeEnum node : IssueServiceTypeEnum.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }

    public static List<SelectOptionsVO> getSelectOptions() {

        return Stream.of(IssueServiceTypeEnum.values())
                .map(item -> new SelectOptionsVO(String.valueOf(item.getCode()), item.getDescription()))
                .collect(Collectors.toList());
    }
}
