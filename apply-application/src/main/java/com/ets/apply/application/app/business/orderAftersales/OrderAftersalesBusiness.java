package com.ets.apply.application.app.business.orderAftersales;

import com.alibaba.fastjson.JSON;
import com.ets.apply.application.app.business.ProductOrderBusiness;
import com.ets.apply.application.app.disposer.TaskDisposer;
import com.ets.apply.application.app.factory.task.TaskFactory;
import com.ets.apply.application.app.thirdservice.feign.*;
import com.ets.apply.application.common.bo.orderAftersales.CheckOrderCanApplyBO;
import com.ets.apply.application.common.config.queue.task.QueueTask;
import com.ets.apply.application.common.consts.issuer.IssuerClassEnum;
import com.ets.apply.application.common.consts.issuer.IssuerErrorEnum;
import com.ets.apply.application.common.consts.order.AftersaleStatus;
import com.ets.apply.application.common.consts.order.StatusEnum;
import com.ets.apply.application.common.consts.orderAftersales.*;
import com.ets.apply.application.common.consts.productOrder.SourceEnum;
import com.ets.apply.application.common.consts.reviewOrder.ThirdReviewStatus;
import com.ets.apply.application.common.consts.taskRecord.TaskRecordReferTypeEnum;
import com.ets.apply.application.common.dto.request.issuerAftersales.IssuerOrderCancelDTO;
import com.ets.apply.application.common.dto.request.issuerAftersales.IssuerOrderResetDTO;
import com.ets.apply.application.common.dto.request.orderAftersales.*;
import com.ets.apply.application.common.dto.task.TaskRecordDTO;
import com.ets.apply.application.common.vo.orderAftersales.GetCustomerServiceOrderVO;
import com.ets.apply.application.common.vo.orderAftersales.OrderAftersalesGetInfoVO;
import com.ets.apply.application.common.vo.response.issuer.*;
import com.ets.apply.application.infra.entity.*;
import com.ets.apply.application.infra.service.*;
import com.ets.common.BizException;
import com.ets.common.JsonResult;
import com.ets.common.ToolsHelper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Objects;
import java.util.List;


@Component
@Slf4j
public class OrderAftersalesBusiness {

    @Autowired
    private OrderAftersalesApplyService orderAftersalesApplyService;

    @Autowired
    private CallPhpIssuerFeign callPhpIssuerFeign;

    @Autowired
    private CallPhpIssuerAdminFeign callPhpIssuerAdminFeign;

    @Autowired
    private CallPhpUserFeign callPhpUserFeign;

    @Autowired
    private OrderOrderService orderOrderService;
    @Autowired
    private ReviewOrderService reviewOrderService;

    @Autowired
    private CardsService cardsService;

    @Autowired
    private TaskRecordService taskRecordService;

    @Autowired
    private CallGoodsApplication callGoodsApplication;

    @Autowired
    private QueueTask queueTask;
    @Autowired
    private ProductOrderBusiness productOrderBusiness;

    @Resource(name = "defaultRedisTemplate")
    private StringRedisTemplate redisTemplate;

    @Autowired
    private ProductPackageService productPackageService;

    /*
     *  判断cardId是否9901的模式，一般是判断是否需要验证码
     */
    public Boolean checkCardIdIsWl(Integer cardId) {
        return Arrays.asList(
                IssuerClassEnum.JSUTONGWL.getClassCode(),
                IssuerClassEnum.WANGLUZL.getClassCode(),
                IssuerClassEnum.JSUTONGWLTRUCK.getClassCode()
        ).contains(cardsService.getIssuerCodeById(cardId));
    }

    /*
     *取消申办前校验
     */
    public Boolean callIssuerOrderCancelCheck(String issuerCode, OrderCancelCheckDTO dto) {
        try {
            String jsonResult = callPhpIssuerFeign.orderCancelCheck(issuerCode,dto);
            JsonResult<IssuerOrderCancelCheckVO> result = JsonResult.convertFromJsonStr(jsonResult, IssuerOrderCancelCheckVO.class);
            if(result.getCode() == 0){
                //处理成功
                if(result.getData().getErrorCode() == null){
                    result.getData().setErrorCode("SUCCESS");
                }
                switch (result.getData().getErrorCode()){
                    //处理成功
                    case "SUCCESS":
                    case "ORDER_NOT_EXIST":
                    case "ORDER_CANCELED":
                        return true;
                    default:
                        ToolsHelper.throwException(result.getData().getErrorMsg());
                        break;
                }
            }else{
                ToolsHelper.throwException(result.getMsg(),result.getCode());
            }
        } catch (BizException e) {
            //9901需要登录
            if(Arrays.asList(
                    IssuerErrorEnum.ISSUER_ERROR_NEED_LOGIN.getCode(),
                    IssuerErrorEnum.ISSUER_ERROR_NEED_CHANGE_PHONE.getCode(),
                    IssuerErrorEnum.ISSUER_ERROR_NEED_CHANGE_REALNAME.getCode()
            ).contains(e.getErrorCode())
            ){
                return true;
            }
            log.error(dto.getOrderSn()+"申请取消检查失败："+e.getMessage());
            ToolsHelper.throwException("申请失败："+e.getMessage());
        }
        ToolsHelper.throwException("申请失败：接口异常");
        return false;
    }
    /*
     *  换货的check
     */
    public Boolean callIssuerOrderResetCheck(String issuerCode, OrderCancelCheckDTO dto) {
        try {
            String jsonResult =  callPhpIssuerFeign.orderReset(issuerCode,new IssuerOrderResetDTO(
                    dto.getOrderSn(),
                    "商品售后换货"
            ));
            JsonResult<IssuerOrderCancelVO> result = JsonResult.convertFromJsonStr(jsonResult, IssuerOrderCancelVO.class);
            if(result.getCode() == 0){
                //处理成功
                if(result.getData().getErrorCode() == null){
                    result.getData().setErrorCode("SUCCESS");
                }
                switch (result.getData().getErrorCode()){
                    //处理成功
                    case "SUCCESS":
                    case "ORDER_NOT_EXIST":
                        //取消成功
                        return true;
                    //拒绝
                    default:
                        ToolsHelper.throwException("设备发行错误，请联系客服处理");
                        break;
                }
            }else {
                ToolsHelper.throwException(result.getMsg());
            }
        } catch (Throwable e) {
            log.error("请求发卡方重置失败", e.getMessage());
            ToolsHelper.throwException("请求发卡方重置失败"+e.getMessage()+"，请稍后重试");
        }
        ToolsHelper.throwException("申请失败：接口异常");
        return false;
    }

    public Boolean cancelPreCheck(OrderAftersalesCheckDTO dto) {
        //检查是否可以取消
        OrderAftersalesApplyEntity orderAftersalesApply = orderAftersalesApplyService.getLatestByOrderSn(dto.getBusinessOrderSn());
        if( orderAftersalesApply == null){
            return true;
        }
        if(
                Arrays.asList(
                        OrderAftersalesStatusEnum.STATUS_PROCESSING.getStatus(),
                        OrderAftersalesStatusEnum.STATUS_SUCCESS.getStatus()
                ).contains(orderAftersalesApply.getStatus()) &&
                        Arrays.asList(
                                OrderAftersalesTypeEnum.TYPE_UNBIND_REFUND.getType(),
                                OrderAftersalesTypeEnum.TYPE_UNBIND_ONLY.getType(),
                                OrderAftersalesTypeEnum.TYPE_UNBIND_CANCEL.getType()
                        ).contains(orderAftersalesApply.getType())
        ){
            ToolsHelper.throwException("售后进行中，不可以取消");
        }
        return true;
    }

    public void checkChannelAllowAfterSale(OrderAftersalesCheckDTO dto) {
        //查询订单数据
        OrderOrderEntity orderOrderEntity = orderOrderService.getByOrderSn(dto.getBusinessOrderSn());
        if(orderOrderEntity == null){
            ToolsHelper.throwException("订单号"+dto.getBusinessOrderSn()+"不存在");
        }
        if (StringUtils.isEmpty(orderOrderEntity.getPackageSn())) {
            return;
        }
        if (StringUtils.isEmpty(dto.getGoodsOrderSn())) {
            // 非商品订单售后，不限制类型
            return;
        }

        ProductPackageEntity productPackageEntity = productPackageService.getBySn(orderOrderEntity.getPackageSn());
        List<Integer> notAllowTypeList = new ArrayList<>();
        if (productPackageEntity.getSource().equals(SourceEnum.CHUANQI.getCode())) {
            // 传祺渠道不支持退货退款和仅退款
            notAllowTypeList = Arrays.asList(1,3);
        }

        if (! notAllowTypeList.isEmpty() && notAllowTypeList.contains(dto.getServiceType())) {
            ToolsHelper.throwException("该渠道的订单不支持此售后类型");
        }
    }

    /*
     * 检查 是否可以做售后
     *  申办是否允许
     *   发卡方是否允许
     */
    public Boolean check(OrderAftersalesCheckDTO dto) {

        // 渠道允许的售后类型检查
        checkChannelAllowAfterSale(dto);

        //申办订单是否允许
        //商品售后类型转换
        CheckOrderCanApplyBO checkOrderCanApplyBO = checkOrderCanApply(dto.getBusinessOrderSn(),OrderAftersalesTypeEnum.getTypeByServiceType(dto.getServiceType()));
        if(Arrays.asList(
                OrderAftersalesTypeEnum.TYPE_UNBIND_REFUND.getServiceType(),
                OrderAftersalesTypeEnum.TYPE_UNBIND_ONLY.getServiceType(),
                OrderAftersalesTypeEnum.TYPE_UNBIND_CANCEL.getServiceType()

        ).contains(dto.getServiceType())){
            //业务方是否允许
            callIssuerOrderCancelCheck(cardsService.getIssuerCodeById(checkOrderCanApplyBO.getOrder().getCardId()),
                    new OrderCancelCheckDTO(checkOrderCanApplyBO.getOrder().getOrderSn())
            );

        } else if(Arrays.asList(
                OrderAftersalesTypeEnum.TYPE_REISSUE_GOODS.getType(),
                OrderAftersalesTypeEnum.TYPE_EXCHANGE_GOODS.getType()
        ).contains(dto.getServiceType())){
            //重置发行接口
            callIssuerOrderResetCheck(cardsService.getIssuerCodeById(checkOrderCanApplyBO.getOrder().getCardId()),
                    new OrderCancelCheckDTO(checkOrderCanApplyBO.getOrder().getOrderSn())
            );
        }
        return true;
    }

    /*
     *  售后申请-初始化（预处理）,
     *  仅取消需要判断是否要验证码
     */
    public IssuerAftersalesApplyInitVO applyInit(OrderAftersalesApplyInitDTO dto) {
        //申办订单是否允许
        CheckOrderCanApplyBO checkOrderCanApplyBO = checkOrderCanApply(dto.getOrderSn(),dto.getType());

        IssuerAftersalesApplyInitVO applyInitVO = new IssuerAftersalesApplyInitVO();
        //仅取消，如果是9901的业务，需要传验证码
        if(
                Objects.equals(dto.getType(),OrderAftersalesTypeEnum.TYPE_UNBIND_ONLY.getType()) &&
                        StringUtils.isNotEmpty(checkOrderCanApplyBO.getOrder().getReviewOrderSn()) &&
                        checkCardIdIsWl(checkOrderCanApplyBO.getOrder().getCardId())
        ){
            applyInitVO.setNeedPhone(1);
        }
        return applyInitVO;
    }
    /*
     * 取消审核单
     */
    public Boolean cancelReviewOrder(String orderSn,String reason) {
        try {
            callPhpIssuerAdminFeign.orderChange("JSuTong",new OrderAftersalesOrderChangeDTO(orderSn,"cancel_success",reason));
        } catch (Throwable e) {
            log.error("请求审核发货平台失败:"+orderSn, e.getMessage());
        }
        return true;
    }
    /*
     * 取消用户卡签数据
     */
    public Boolean cancelUserCard(String orderSn,Long uid) {
        try {
            callPhpUserFeign.cancelUserCard(new OrderAftersalesCancelUserCardDTO(orderSn,uid));
        } catch (Throwable e) {
            log.error("取消用户卡签数据失败"+orderSn, e.getMessage());
        }
        return true;
    }



    /*
     * 申请售后
     *  创建售后单
     *  同步更新申办订单为售后中
     *  同步更新商品订单为售后中
     */
    public OrderAftersalesApplyEntity apply(OrderAftersalesApplyDTO dto,Long uid) {

        if (!ToolsHelper.addLock(redisTemplate, "OrderAftersalesApply:" + dto.getOrderSn(), 5)) {
            ToolsHelper.throwException("申请过于频繁，请稍候");
        }

        //检查类型是否支持
        if(Boolean.FALSE.equals(OrderAftersalesTypeEnum.checkSupportType(dto.getType()))){
            ToolsHelper.throwException("申请类型不支持");
        }

        CheckOrderCanApplyBO checkOrderCanApplyBO = checkOrderCanApply(dto.getOrderSn(),dto.getType());
        //检查uid是否一致
        if(!uid.equals(checkOrderCanApplyBO.getOrder().getUid())){
            ToolsHelper.throwException("订单号非当前用户所有");
        }

        //仅取消，如果是9901的业务，需要传验证码
        if(
                Boolean.TRUE.equals(Objects.equals(dto.getType(),OrderAftersalesTypeEnum.TYPE_UNBIND_ONLY.getType()))&&
                        StringUtils.isNotEmpty(checkOrderCanApplyBO.getOrder().getReviewOrderSn()) &&
                        checkCardIdIsWl(checkOrderCanApplyBO.getOrder().getCardId()) &&
                        StringUtils.isEmpty(dto.getCode())
        ){
            ToolsHelper.throwException("验证码不可为空");
        }

        OrderAftersalesApplyEntity orderAftersalesApply = checkOrderCanApplyBO.getOrderAftersalesApply();
        if( orderAftersalesApply == null){
            //新增申请单
            orderAftersalesApply = orderAftersalesApplyService.addNew(dto,checkOrderCanApplyBO.getOrder());

            //塞队列进行发货操作
            TaskRecordDTO taskRecordDTO = new TaskRecordDTO();
            taskRecordDTO.setReferSn(orderAftersalesApply.getApplySn());
            taskRecordDTO.setReferType(TaskRecordReferTypeEnum.TASK_ORDER_AFTERSALES_APPLY.getType());
            taskRecordDTO.setNextExecTime(LocalDateTime.now().plusSeconds(5));
            taskRecordDTO.setNotifyContent(JSON.toJSONString(orderAftersalesApply));
            TaskFactory.create(TaskRecordReferTypeEnum.TASK_ORDER_AFTERSALES_APPLY.getType()).addAndPush(taskRecordDTO);
        }

        //根据不同的类型进行处理
        //仅取消
        if (Objects.requireNonNull(OrderAftersalesTypeEnum.getByType(dto.getType())) == OrderAftersalesTypeEnum.TYPE_UNBIND_ONLY) {//取消发卡方订单
            IssuerAftersalesApplyVO issuerAftersalesApplyVO = callIssuerOrderCancel(orderAftersalesApply, checkOrderCanApplyBO.getOrder(), dto.getCode());
            //仅取消处理申请结果
            unBindOnlyAfterCallIssuerOrderCancel(orderAftersalesApply, checkOrderCanApplyBO.getOrder(), reviewOrderService.getNeedDealReviewOrderSn(checkOrderCanApplyBO.getOrder()), issuerAftersalesApplyVO);
            //取最新的售后单
            return orderAftersalesApplyService.getLatestByOrderSnAndType(dto.getOrderSn(), dto.getType(), uid);
        }

        if (!Objects.equals(checkOrderCanApplyBO.getOrder().getAftersaleStatus(), AftersaleStatus.STATUS_APPLY.getValue())) {
            //更新申办订单为“售后中”
            String reason = "发起售后【"+OrderAftersalesTypeEnum.getDescByType(dto.getType())+"】,原因： "+dto.getReason();
            orderOrderService.aftersaleStatusUpdate(checkOrderCanApplyBO.getOrder(), AftersaleStatus.STATUS_APPLY.getValue(),reason);
            productOrderBusiness.aftersalesMarkByOrderSn(checkOrderCanApplyBO.getOrder(), 0, orderAftersalesApply.getApplySn());
        }
        return orderAftersalesApply;

    }


    /*
     *  查看订单号是否满足申请条件
     *      查找order_sn的最新一条记录
     存在，则判断类型是否为 仅取消或者退货退款，如果已经完成，则其他类型不允许申请
                                        如果未完成，同一个类型允许，其他类型不允许

                   其他类型，如果未完成，同一个类型允许，其他类型不允许
     */
    public CheckOrderCanApplyBO checkOrderCanApply(String orderSn, Integer type) {
        //查询订单数据
        OrderOrderEntity orderOrderEntity = orderOrderService.getByOrderSn(orderSn);
        if(orderOrderEntity == null){
            ToolsHelper.throwException("订单号"+orderSn+"不存在");
        }
        //订单状态为待支付或者已取消，不可做售后
        if(Arrays.asList(
                StatusEnum.WAIT_FOR_PAY.getCode(),
                StatusEnum.WAIT_FOR_DELIVER.getCode(),
                StatusEnum.CANCELED.getCode()
        ).contains(orderOrderEntity.getStatus())
        ){
            ToolsHelper.throwException("该订单暂无法申请售后，原因:"+StatusEnum.getDescByCode(orderOrderEntity.getStatus())+"，请确认。",OrderAftersalesErrorCodeEnum.AFTERSALES_ERROR_CODE_ORDER_STATUS_INCORRECT.getCode());
        }
        //已激活不允许做售后
        if(orderOrderEntity.getActivatedStatus() != 0){
            ToolsHelper.throwException("订单号"+orderSn+"已激活或者已注销，不允许售后",OrderAftersalesErrorCodeEnum.AFTERSALES_ERROR_CODE_ORDER_STATUS_INCORRECT.getCode());
        }
        //售后已完成
        if(Objects.equals(orderOrderEntity.getAftersaleStatus(), AftersaleStatus.STATUS_APPLY_FINISH.getValue())){
            ToolsHelper.throwException("订单号"+orderSn+"已完成售后",OrderAftersalesErrorCodeEnum.AFTERSALES_ERROR_CODE_NOT_ALLOWED_TO_APPLY.getCode());
        }

        CheckOrderCanApplyBO checkOrderCanApplyBO = new CheckOrderCanApplyBO();
        checkOrderCanApplyBO.setOrder(orderOrderEntity);
        //查找是否存在业务状态未完成的售后单，如果是同一个类型，则复用售后单
        OrderAftersalesApplyEntity orderAftersalesApplyEntity = orderAftersalesApplyService.getLatestByOrderSn(orderSn);
        //兼容历史售后
        if(orderAftersalesApplyEntity == null && Objects.equals(orderOrderEntity.getAftersaleStatus(), AftersaleStatus.STATUS_APPLY.getValue())){
            ToolsHelper.throwException("订单号"+orderSn+"已完成售后",OrderAftersalesErrorCodeEnum.AFTERSALES_ERROR_CODE_ALREADY_COMPLETED.getCode());
        }

        //存在记录
        if(orderAftersalesApplyEntity != null){
            //是否已完成
            Boolean isFinish = (Objects.equals(orderAftersalesApplyEntity.getStatus(), OrderAftersalesStatusEnum.STATUS_SUCCESS.getStatus()) && Objects.equals(orderAftersalesApplyEntity.getBusinessStatus(), OrderAftersalesBussinessStatusEnum.STATUS_FINISH.getStatus()));
            //未完成，同一个类型允许，其他类型不允许
            if(!isFinish && !Objects.equals(type, orderAftersalesApplyEntity.getType())){
                ToolsHelper.throwException("订单号"+orderSn+"存在未完成售后单");
            } else if (
                    isFinish &&
                            Arrays.asList(
                                    OrderAftersalesTypeEnum.TYPE_UNBIND_REFUND.getType(),
                                    OrderAftersalesTypeEnum.TYPE_UNBIND_ONLY.getType()
                            ).contains(orderAftersalesApplyEntity.getType()) &&
                            !Objects.equals(type, orderAftersalesApplyEntity.getType())
            ) {
                ToolsHelper.throwException("订单号"+orderSn+"已完成退货退款或者仅取消，不支持其他售后",OrderAftersalesErrorCodeEnum.AFTERSALES_ERROR_CODE_NOT_ALLOWED_TO_APPLY.getCode());
            } else if (isFinish && Arrays.asList(
                    OrderAftersalesTypeEnum.TYPE_REISSUE_GOODS.getType(),
                    OrderAftersalesTypeEnum.TYPE_EXCHANGE_GOODS.getType()
            ).contains(orderAftersalesApplyEntity.getType())) {
                //补办/换货已完成可以忽略
                checkOrderCanApplyBO.setOrderAftersalesApply(null);
            }else{
                checkOrderCanApplyBO.setOrderAftersalesApply(orderAftersalesApplyEntity);
            }
        }
        return checkOrderCanApplyBO;
    }


    /*
     *    是否存在未完成的售后单
     *   未完成的售后单的状态
     */
    public Boolean checkCanActivate(CheckCanActivateDTO dto) {
        //是否存在未完成的售后单
        OrderAftersalesApplyEntity orderAftersalesApply = orderAftersalesApplyService.getLatestByOrderSn(dto.getOrderSn());
        if( orderAftersalesApply == null){
            return true;
        }
        if(
                orderAftersalesApply.getStatus() == OrderAftersalesStatusEnum.STATUS_SUCCESS.getStatus() &&
                        orderAftersalesApply.getBusinessStatus() == OrderAftersalesBussinessStatusEnum.STATUS_FINISH.getStatus()
        ){
            return true;
        }
        ToolsHelper.throwException("存在未完成的售后："+orderAftersalesApply.getError());
        return false;
    }

    /*
     * 调用发卡方接口,取消申办单
     */
    public IssuerAftersalesApplyVO callIssuerOrderCancel(OrderAftersalesApplyEntity orderAftersalesApply, OrderOrderEntity orderOrder,String code) {
        IssuerAftersalesApplyVO vo = new IssuerAftersalesApplyVO();
        //针对粤通卡card_id，直接返回成功
        if(Arrays.asList(4,5,18).contains(orderOrder.getCardId())){
            vo.setBussinessStatus(IssuerAftersalesStatusEnum.STATUS_FINISH.getStatus());
            vo.setMsg("申请取消发卡方订单：粤通卡直接返回成功");
            return vo;
        }
        //如果审核单号为空，则直接返回成功
        if(StringUtils.isEmpty(orderOrder.getReviewOrderSn())){
            vo.setBussinessStatus(IssuerAftersalesStatusEnum.STATUS_FINISH.getStatus());
            vo.setMsg("申请取消发卡方订单：审核单号为空，直接返回成功");
            return vo;
        }
        //直接请求发卡方操作，返回异常则直接中断，异步回调的当正常处理
        try {
            String cancelReason = orderAftersalesApply.getReason();
            if(StringUtils.isEmpty(cancelReason)){
                cancelReason = "用户取消";
            }
            String jsonResult =  callPhpIssuerFeign.orderCancel(cardsService.getIssuerCodeById(orderOrder.getCardId()),new IssuerOrderCancelDTO(
                    orderAftersalesApply.getOrderSn(),
                    cancelReason,
                    code,
                    "http://apply-application:20070/orderAftersalesApply/notifyStatus"

            ));
            JsonResult<IssuerOrderCancelVO> result = JsonResult.convertFromJsonStr(jsonResult, IssuerOrderCancelVO.class);
            if(result.getCode() == 0){
                //处理成功
                if(result.getData().getErrorCode() == null){
                    result.getData().setErrorCode("SUCCESS");
                }

                switch (result.getData().getErrorCode()){
                    //处理成功
                    case "SUCCESS":
                    case "ORDER_NOT_EXIST":
                    case "ORDER_CANCELED":
                        //取消成功
                        vo.setBussinessStatus(IssuerAftersalesStatusEnum.STATUS_FINISH.getStatus());
                        vo.setMsg("申请取消发卡方订单:"+result.getData().getErrorMsg());
                        break;
                    //处理中
                    case "ORDER_CANCELING":
                        vo.setBussinessStatus(IssuerAftersalesStatusEnum.STATUS_PROCESSING.getStatus());
                        vo.setMsg("申请取消发卡方订单:"+result.getData().getErrorMsg());
                        break;
                    //拒绝
                    case "ORDER_FINISHED":
                    case "ORDER_ISSUED":
                    case "ORDER_CANCEL_REJECT":
                        vo.setBussinessStatus(IssuerAftersalesStatusEnum.STATUS_REJECT.getStatus());
                        vo.setMsg("申请取消发卡方订单:"+result.getData().getErrorMsg());
                        break;
                }
                return vo;
            }else {
                ToolsHelper.throwException("申请取消发卡方订单:"+result.getMsg(), result.getCode());
            }
        } catch (BizException e) {
            //9901需要登录
            if(Arrays.asList(
                    IssuerErrorEnum.ISSUER_ERROR_NEED_LOGIN.getCode(),
                    IssuerErrorEnum.ISSUER_ERROR_NEED_CHANGE_PHONE.getCode(),
                    IssuerErrorEnum.ISSUER_ERROR_NEED_CHANGE_REALNAME.getCode()
            ).contains(e.getErrorCode())
            ){
                ToolsHelper.throwException("申请取消发卡方订单:"+e.getMessage(), e.getErrorCode());
            }
            log.error(orderAftersalesApply.getOrderSn()+"申请取消失败："+e.getMessage());
            ToolsHelper.throwException("调用发卡方取消接口失败"+e.getMessage()+"，请稍后重试");
        }
        return null;
    }

    /*
     *  排除仅取消类型 卡方取消成功需要处理
     */
    public void afterCallIssuerOrderCancel(OrderAftersalesApplyEntity orderAftersalesApply,OrderOrderEntity orderOrder,ReviewOrderEntity reviewOrder,IssuerAftersalesApplyVO issuerAftersalesApplyVO) {
        IssuerAftersalesStatusEnum issuerAftersalesStatusEnum = IssuerAftersalesStatusEnum.getByStatus(issuerAftersalesApplyVO.getBussinessStatus());
        if(issuerAftersalesStatusEnum == null){
            ToolsHelper.throwException("处理状态异常");
        }
        //仅取消类型，报错
//        if(Objects.equals(orderAftersalesApply.getType(), OrderAftersalesTypeEnum.TYPE_UNBIND_ONLY.getType())){
//            ToolsHelper.throwException("处理类型异常，不处理仅取消");
//        }
        switch (issuerAftersalesStatusEnum){
            //拒绝
            case STATUS_REJECT:
                orderAftersalesApplyService.modifyStatus(
                        orderAftersalesApply.getApplySn(),
                        OrderAftersalesStatusEnum.STATUS_PROCESSING.getStatus(),
                        issuerAftersalesApplyVO.getMsg()
                );
                ToolsHelper.throwException("取消发卡方失败，请联系客服处理");
                break;
            //处理中
            case STATUS_PROCESSING:
                //售后申请单
                if(!Objects.equals(orderAftersalesApply.getStatus(), OrderAftersalesStatusEnum.STATUS_PROCESSING.getStatus())){
                    orderAftersalesApplyService.modifyStatus(
                            orderAftersalesApply.getApplySn(),
                            OrderAftersalesStatusEnum.STATUS_PROCESSING.getStatus(),
                            ""
                    );

                }
                break;
            //成功
            case STATUS_FINISH:
                //售后申请单
                if(!Objects.equals(orderAftersalesApply.getStatus(), OrderAftersalesStatusEnum.STATUS_SUCCESS.getStatus())){
                    orderAftersalesApplyService.modifyStatus(
                            orderAftersalesApply.getApplySn(),
                            OrderAftersalesStatusEnum.STATUS_SUCCESS.getStatus(),
                            ""
                    );

                }
                break;
            default:
                orderAftersalesApplyService.modifyStatus(
                        orderAftersalesApply.getApplySn(),
                        OrderAftersalesStatusEnum.STATUS_PROCESSING.getStatus(),
                        issuerAftersalesApplyVO.getMsg()
                );
                ToolsHelper.throwException("取消发卡方失败，请联系客服处理");
                break;
        }
    }
    /*
     *  卡方取消成功需要处理
     */
    public void afterCallIssuerOrderReset(OrderAftersalesApplyEntity orderAftersalesApply,OrderOrderEntity orderOrder,IssuerAftersalesApplyVO issuerAftersalesApplyVO) {
        IssuerAftersalesStatusEnum issuerAftersalesStatusEnum = IssuerAftersalesStatusEnum.getByStatus(issuerAftersalesApplyVO.getBussinessStatus());
        if(issuerAftersalesStatusEnum == null){
            ToolsHelper.throwException("处理状态异常");
        }
        Integer orderBusinessStatus = OrderBusinessStatusEnum.STATUS_PROCESSING.getStatus();
        if(orderAftersalesApply.getType() == OrderAftersalesTypeEnum.TYPE_REISSUE_GOODS.getType()){
            orderBusinessStatus = OrderBusinessStatusEnum.STATUS_FINISHED.getStatus();
        }
        switch (issuerAftersalesStatusEnum){
            //拒绝
            case STATUS_REJECT:
                //取消售后单申请，返回msg
                orderAftersalesApplyService.modify(
                        orderAftersalesApply.getApplySn(),
                        OrderAftersalesStatusEnum.STATUS_FAIL.getStatus(),
                        orderBusinessStatus,
                        ""
                );
                ToolsHelper.throwException(issuerAftersalesApplyVO.getMsg());
                break;
            //处理中
            case STATUS_PROCESSING:
                //售后申请单
                if(orderAftersalesApply.getStatus() != OrderAftersalesStatusEnum.STATUS_PROCESSING.getStatus()){
                    orderAftersalesApplyService.modify(
                            orderAftersalesApply.getApplySn(),
                            OrderAftersalesStatusEnum.STATUS_PROCESSING.getStatus(),
                            orderBusinessStatus,
                            ""
                    );

                }
                //申办订单
                if(orderOrder.getAftersaleStatus() != AftersaleStatus.STATUS_APPLY.getValue()){
                    //更新申办订单为“售后中”
                    orderOrderService.aftersaleStatusUpdate(orderOrder,AftersaleStatus.STATUS_APPLY.getValue(),issuerAftersalesApplyVO.getMsg());
                }

                break;
            //成功
            case STATUS_FINISH:
                //售后申请单
                if(orderAftersalesApply.getStatus() != OrderAftersalesStatusEnum.STATUS_SUCCESS.getStatus()){
                    orderAftersalesApplyService.modify(
                            orderAftersalesApply.getApplySn(),
                            OrderAftersalesStatusEnum.STATUS_SUCCESS.getStatus(),
                            orderBusinessStatus,
                            ""
                    );

                }
                if(orderOrder.getAftersaleStatus() != AftersaleStatus.STATUS_APPLY.getValue()){
                    //更新申办订单为“售后中”
                    orderOrderService.aftersaleStatusUpdate(orderOrder,AftersaleStatus.STATUS_APPLY.getValue(),issuerAftersalesApplyVO.getMsg());
                }
                break;
            default:
                orderOrderService.aftersaleStatusUpdate(orderOrder,AftersaleStatus.STATUS_APPLY_CANCEL.getValue(),"申请异常"+issuerAftersalesApplyVO.getMsg());
                ToolsHelper.throwException("申请失败，请稍后重试");
                break;
        }
    }
    /*
     * 调用发卡方接口,取消申办单
     */
    public IssuerAftersalesApplyVO callIssuerOrderReset(OrderAftersalesApplyEntity orderAftersalesApply, OrderOrderEntity orderOrder) {
        IssuerAftersalesApplyVO vo = new IssuerAftersalesApplyVO();
        //如果审核单号为空，则直接返回成功
        if(StringUtils.isEmpty(orderOrder.getReviewOrderSn())){
            vo.setBussinessStatus(IssuerAftersalesStatusEnum.STATUS_FINISH.getStatus());
            vo.setMsg("重置发卡方数据：审核单号为空，直接返回成功");
            return vo;
        }
        //直接请求发卡方操作，返回异常则直接中断，异步回调的当正常处理
        try {
            String jsonResult =  callPhpIssuerFeign.orderReset(cardsService.getIssuerCodeById(orderOrder.getCardId()),new IssuerOrderResetDTO(
                    orderAftersalesApply.getOrderSn(),
                    orderAftersalesApply.getReason()
            ));
            JsonResult<IssuerOrderCancelVO> result = JsonResult.convertFromJsonStr(jsonResult, IssuerOrderCancelVO.class);
            if(result.getCode() == 0){
                //处理成功
                if(result.getData().getErrorCode() == null){
                    result.getData().setErrorCode("SUCCESS");
                }

                switch (result.getData().getErrorCode()){
                    //处理成功
                    case "SUCCESS":
                    case "ORDER_NOT_EXIST":
                        //取消成功
                        vo.setBussinessStatus(IssuerAftersalesStatusEnum.STATUS_FINISH.getStatus());
                        vo.setMsg("重置发卡方数据："+result.getData().getErrorMsg());
                        break;
                    //拒绝
                    default:
                        vo.setBussinessStatus(IssuerAftersalesStatusEnum.STATUS_REJECT.getStatus());
                        vo.setMsg("设备发行错误，请联系客服处理");
                        break;
                }
                return vo;
            }else {
                ToolsHelper.throwException(result.getMsg());
            }
        } catch (Throwable e) {
            log.error(orderOrder.getOrderSn()+"请求发卡方重置失败"+e.getMessage());
            ToolsHelper.throwException("请求发卡方重置失败"+e.getMessage()+"，请稍后重试");
        }
        return null;
    }


    public Boolean task(OrderAftersalesTaskDTO dto) {
        try {
            //是否存在task
            if(dto.getTaskSn()!= null){
                TaskRecordEntity taskRecord = taskRecordService.getOneByTaskSn(dto.getTaskSn());
                // 执行任务
                TaskFactory.create(taskRecord.getReferType()).execute(taskRecord.getTaskSn(),false);
            }else{
                //塞队列进行发货操作
                TaskRecordDTO taskRecordDTO = new TaskRecordDTO();
                taskRecordDTO.setReferSn(dto.getApplySn());
                taskRecordDTO.setReferType(TaskRecordReferTypeEnum.TASK_ORDER_AFTERSALES_APPLY.getType());
                taskRecordDTO.setNextExecTime(LocalDateTime.now());
                taskRecordDTO.setNotifyContent(dto.getApplySn());
                TaskFactory.create(TaskRecordReferTypeEnum.TASK_ORDER_AFTERSALES_APPLY.getType()).addAndPush(taskRecordDTO);
            }

        } catch (Throwable e) {
            ToolsHelper.throwException("申请失败："+e.getMessage());
        }
        return false;
    }

    /*
     * 调用售后接口，查询售后完成的状态
     */
    public GetCustomerServiceOrderVO getCustomerServiceOrder(GetCustomerServiceOrderDTO dto) {
        GetCustomerServiceOrderVO vo = new GetCustomerServiceOrderVO();
        //直接请求发卡方操作，返回异常则直接中断，异步回调的当正常处理
        try {
            String jsonResult =  callGoodsApplication.getCustomerServiceOrder(dto.getBusinessSn(),dto.getServiceType());
            JsonResult<GetCustomerServiceOrderVO> result = JsonResult.convertFromJsonStr(jsonResult, GetCustomerServiceOrderVO.class);
            if(result.getCode() == 0){
                return result.getData();
            }else {
                ToolsHelper.throwException(result.getMsg());
            }
        } catch (Throwable e) {
            log.error("查询售后完成的状态失败", e.getMessage());
            ToolsHelper.throwException("查询售后完成的状态失败，请稍后重试："+ e.getMessage());
        }
        return null;
    }
    /*
     * 通过订单号获取最新的售后申请记录，过滤掉已取消的售后申请记录
     */
    public OrderAftersalesApplyEntity getLatestByOrderSn(CheckCanActivateDTO dto) {
        return orderAftersalesApplyService.getLatestByOrderSn(dto.getOrderSn());
    }


    /*
     * 通过订单号获取最新的售后申请记录，过滤掉已取消的售后申请记录
     */
    public Boolean updateBusinessStatus(UpdateBussinessStatusDTO dto) {
        OrderAftersalesApplyEntity orderAftersalesApply = orderAftersalesApplyService.getLatestByOrderSnAndType(dto.getOrderSn(),dto.getType());
        if(orderAftersalesApply != null){
            orderAftersalesApplyService.updateBusinessStatus(orderAftersalesApply.getApplySn(),dto.getBussinessStatus());
            //触发申办售后任务
            if(Objects.equals(dto.getBussinessStatus(), OrderAftersalesBussinessStatusEnum.STATUS_FINISH.getStatus())){
                TaskRecordEntity taskRecord = taskRecordService.getOneByReferSn(orderAftersalesApply.getApplySn());
                if(taskRecord != null){
                    queueTask.push(new TaskDisposer(taskRecord));
                }
            }

        }
        return true;
    }


    /*
     *  售后申请-初始化（预处理）
     */
    public Boolean checkNeedPreOperate(OrderAftersalesApplyInitDTO dto) {
        OrderOrderEntity order = orderOrderService.getByOrderSn(dto.getOrderSn());
        //仅取消，退货退款，如果是9901的业务，需要传验证码
        return  Arrays.asList(
                OrderAftersalesTypeEnum.TYPE_UNBIND_REFUND.getType(),
                OrderAftersalesTypeEnum.TYPE_UNBIND_ONLY.getType(),
                OrderAftersalesTypeEnum.TYPE_UNBIND_CANCEL.getType()
        ).contains(dto.getType()) &&
                StringUtils.isNotEmpty(order.getReviewOrderSn()) &&
                checkCardIdIsWl(order.getCardId());
    }


    /*
     * 获取售后详情
     * 如果还没开启任务，则判断是否需要传验证码
     */
    public OrderAftersalesGetInfoVO getInfo(OrderAftersalesGetInfoDTO dto) {
        OrderAftersalesApplyEntity orderAftersalesApply = orderAftersalesApplyService.getLatestByOrderSn(dto.getOrderSn());
        if(orderAftersalesApply == null){
            ToolsHelper.throwException("订单号"+dto.getOrderSn()+"的售后单不存在");
        }
        OrderAftersalesGetInfoVO vo = new OrderAftersalesGetInfoVO();
        BeanUtils.copyProperties(orderAftersalesApply, vo);
        //发卡方售后是否已开启任务
        if(Objects.equals(orderAftersalesApply.getStatus(), OrderAftersalesStatusEnum.STATUS_APPLY.getStatus())){
            //查询订单数据
            OrderOrderEntity orderOrder = orderOrderService.getByOrderSn(orderAftersalesApply.getOrderSn());
            if(orderOrder == null){
                ToolsHelper.throwException("订单号"+orderAftersalesApply.getOrderSn()+"不存在");
            }
            //判断是否需要传验证码
            //仅取消，如果是9901的业务，需要传验证码
            //仅取消，退货退款，如果是9901的业务，需要传验证码
            if(
                    Arrays.asList(
                            OrderAftersalesTypeEnum.TYPE_UNBIND_REFUND.getType(),
                            OrderAftersalesTypeEnum.TYPE_UNBIND_ONLY.getType(),
                            OrderAftersalesTypeEnum.TYPE_UNBIND_CANCEL.getType()
                    ).contains(orderAftersalesApply.getType())  &&
                            StringUtils.isNotEmpty(orderOrder.getReviewOrderSn()) &&
                            checkCardIdIsWl(orderOrder.getCardId())
            ){
                vo.setNeedPhone(1);
            }
        }
        return vo;
    }

    /*
     * 商品售后单状态更新，触发执行task
     * 售后单状态为完成时，则不触发执行task
     */
    public Boolean businessStatusChange(BussinessStatusChangeDTO dto) {
        OrderAftersalesApplyEntity orderAftersalesApply = orderAftersalesApplyService.getLatestByOrderSnAndType(dto.getOrderSn(),dto.getType());
        //兼容处理 历史售后完成才调用申办售后的数据
        if(orderAftersalesApply == null){
            OrderOrderEntity orderOrder  = orderOrderService.getByOrderSn(dto.getOrderSn());
            if(orderOrder == null){
                ToolsHelper.throwException("订单号"+dto.getOrderSn()+"不存在");
            }
            OrderAftersalesApplyDTO orderAftersalesApplyDTO = new OrderAftersalesApplyDTO();
            orderAftersalesApplyDTO.setOrderSn(dto.getOrderSn());
            orderAftersalesApplyDTO.setType(dto.getType());
            orderAftersalesApplyDTO.setUid(orderOrder.getUid());
            orderAftersalesApply = apply(orderAftersalesApplyDTO, orderOrder.getUid());
        }
        //触发申办售后任务
        TaskRecordEntity taskRecord = taskRecordService.getOneByReferSn(orderAftersalesApply.getApplySn());
        if(taskRecord != null){
            taskRecord.setNextExecTime(LocalDateTime.now().minusMinutes(5));
            taskRecordService.saveOrUpdate(taskRecord);
            queueTask.push(new TaskDisposer(taskRecord));
        }
        return true;
    }


    /*
     *   需要验证码，请求发卡方售后申请接口
     */
    public OrderAftersalesApplyEntity applyWithCode(OrderAftersalesApplyWithCodeDTO dto,Long uid) {
        OrderAftersalesApplyEntity orderAftersalesApply = orderAftersalesApplyService.getByApplySn(dto.getApplySn());
        if( orderAftersalesApply == null){
            ToolsHelper.throwException("applySn不存在："+dto.getApplySn());
        }
        OrderOrderEntity orderOrder = orderOrderService.getByOrderSn(orderAftersalesApply.getOrderSn());
        if(orderOrder == null){
            ToolsHelper.throwException("订单号"+orderAftersalesApply.getOrderSn()+"不存在");
        }
        if(
                Arrays.asList(
                        OrderAftersalesTypeEnum.TYPE_UNBIND_REFUND.getType(),
                        OrderAftersalesTypeEnum.TYPE_UNBIND_ONLY.getType(),
                        OrderAftersalesTypeEnum.TYPE_UNBIND_CANCEL.getType()
                ).contains(orderAftersalesApply.getType())
        ){

            IssuerAftersalesApplyVO issuerAftersalesApplyVO = callIssuerOrderCancel(orderAftersalesApply, orderOrder, dto.getCode());
            //处理申请结果
            afterCallIssuerOrderCancel(orderAftersalesApply, orderOrder, reviewOrderService.getNeedDealReviewOrderSn(orderOrder), issuerAftersalesApplyVO);
        }
        //取最新的售后单
        return orderAftersalesApplyService.getLatestByOrderSnAndType(orderOrder.getOrderSn(), orderAftersalesApply.getType(), uid);

    }


    /*
     *  仅取消类型 在请求发卡方售后申请接口的处理
     */
    public void unBindOnlyAfterCallIssuerOrderCancel(OrderAftersalesApplyEntity orderAftersalesApply,OrderOrderEntity orderOrder,ReviewOrderEntity reviewOrder,IssuerAftersalesApplyVO issuerAftersalesApplyVO) {
        IssuerAftersalesStatusEnum issuerAftersalesStatusEnum = IssuerAftersalesStatusEnum.getByStatus(issuerAftersalesApplyVO.getBussinessStatus());
        if(issuerAftersalesStatusEnum == null){
            ToolsHelper.throwException("处理状态异常");
        }

        switch (issuerAftersalesStatusEnum){
            //拒绝
            case STATUS_REJECT:
                //取消售后单申请，返回msg
                orderAftersalesApplyService.modifyStatus(
                        orderAftersalesApply.getApplySn(),
                        OrderAftersalesStatusEnum.STATUS_FAIL.getStatus(),
                        issuerAftersalesApplyVO.getMsg()
                );
                ToolsHelper.throwException("申请失败，"+issuerAftersalesApplyVO.getMsg());
                break;
            //处理中
            case STATUS_PROCESSING:
                //取消审核平台的审核单
                if(reviewOrder != null){
                    cancelReviewOrder(orderOrder.getOrderSn(),issuerAftersalesApplyVO.getMsg());
                }
                //售后申请单
                if(!Objects.equals(orderAftersalesApply.getStatus(), OrderAftersalesStatusEnum.STATUS_PROCESSING.getStatus())){
                    orderAftersalesApplyService.modifyStatus(
                            orderAftersalesApply.getApplySn(),
                            OrderAftersalesStatusEnum.STATUS_PROCESSING.getStatus(),
                            ""
                    );

                }
                //更新申办订单为“售后中”
                orderOrderService.aftersaleStatusUpdate(orderOrder, AftersaleStatus.STATUS_APPLY.getValue(), orderAftersalesApply.getReason());
                productOrderBusiness.aftersalesMarkByOrderSn(orderOrder, 0, orderAftersalesApply.getApplySn());
                break;
            //成功
            case STATUS_FINISH:
                if(reviewOrder != null){
                    //取消审核平台的审核单
                    cancelReviewOrder(orderOrder.getOrderSn(),issuerAftersalesApplyVO.getMsg());
                }
                //售后申请单
                if(!Objects.equals(orderAftersalesApply.getStatus(), OrderAftersalesStatusEnum.STATUS_SUCCESS.getStatus())){
                    orderAftersalesApplyService.modifyStatus(
                            orderAftersalesApply.getApplySn(),
                            OrderAftersalesStatusEnum.STATUS_SUCCESS.getStatus(),
                            ""
                    );

                }
                //更新申办订单为“售后中”
                orderOrderService.aftersaleStatusUpdate(orderOrder, AftersaleStatus.STATUS_APPLY.getValue(), orderAftersalesApply.getReason());
                productOrderBusiness.aftersalesMarkByOrderSn(orderOrder, 0, orderAftersalesApply.getApplySn());
                break;
            default:
                //取消售后单申请，返回msg
                orderAftersalesApplyService.modifyStatus(
                        orderAftersalesApply.getApplySn(),
                        OrderAftersalesStatusEnum.STATUS_FAIL.getStatus(),
                        issuerAftersalesApplyVO.getMsg()
                );
                ToolsHelper.throwException("申请异常，"+issuerAftersalesApplyVO.getMsg());
                break;
        }
    }
}
