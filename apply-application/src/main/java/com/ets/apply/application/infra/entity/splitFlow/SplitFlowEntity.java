package com.ets.apply.application.infra.entity.splitFlow;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ets.apply.application.infra.entity.BaseEntity;
import com.ets.apply.application.infra.entity.ProductPackageEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 分流记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("split_flow")
public class SplitFlowEntity extends BaseEntity<SplitFlowEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 记录流水号
     */
    @TableId(value = "split_sn")
    private String splitSn;

    /**
     * 测试类型[etc_2.0_before_pay-2.0支付前]
     */
    private String splitType;

    /**
     * 测试的周期：20210420
     */
    private String splitTerm;

    /**
     * 测试结果：A/B/C
     */
    private String splitResult;

    /**
     * 用户uid
     */
    private Long uid;

    /**
     * 关联order_sn，可为空
     */
    private String orderSn;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    @TableField(exist = false)
    private SplitFlowConfigEntity splitFlowConfigEntity;
}
