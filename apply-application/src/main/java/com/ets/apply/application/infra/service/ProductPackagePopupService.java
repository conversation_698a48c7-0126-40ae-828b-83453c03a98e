package com.ets.apply.application.infra.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.apply.application.common.consts.productPackagePopup.PopupStatusEnum;
import com.ets.apply.application.infra.entity.ProductPackageEntity;
import com.ets.apply.application.infra.entity.ProductPackagePopupEntity;
import com.ets.apply.application.infra.mapper.ProductPackagePopupMapper;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class ProductPackagePopupService extends BaseService<ProductPackagePopupMapper, ProductPackagePopupEntity> {
    public ProductPackagePopupEntity getByPopupType(Integer popupType, Integer firstChoose) {
        Wrapper<ProductPackagePopupEntity> wrapper = Wrappers.<ProductPackagePopupEntity>lambdaQuery()
                .eq(ProductPackagePopupEntity::getPopupType, popupType)
                .eq(ProductPackagePopupEntity::getFirstChoose, firstChoose)
                .last("limit 1");
        return super.baseMapper.selectOne(wrapper);
    }

    public List<ProductPackagePopupEntity> getGroupList() {
        LambdaQueryWrapper<ProductPackagePopupEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.select()
                .eq(ProductPackagePopupEntity::getPopupStatus, PopupStatusEnum.NORMAL.getStatus())
                .orderByDesc(ProductPackagePopupEntity::getFirstChoose)
        ;
        return super.baseMapper.selectList(wrapper);
    }

    /**
     * 获取所有弹窗信息并缓存5分钟
     */
    @Cacheable(value = "productPackagePopupList:getAll#600")
    public List<ProductPackagePopupEntity> getAll() {
        LambdaQueryWrapper<ProductPackagePopupEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(ProductPackagePopupEntity::getPopupSn, ProductPackagePopupEntity::getUrl)
        ;
        return super.baseMapper.selectList(wrapper);
    }
}
