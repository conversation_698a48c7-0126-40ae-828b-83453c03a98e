package com.ets.apply.application.infra.entity;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import com.ets.apply.application.common.bo.missionReward.MissionRewardContentBO;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>
 * 任务配置
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mission_config")
public class MissionConfigEntity extends BaseEntity<MissionConfigEntity> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 任务编号
     */
    private String missionConfigSn;

    /**
     * 配置名称
     */
    private String configName;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startDate;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endDate;

    /**
     * 任务状态：1正常2无效
     */
    private Integer status;

    /**
     * 满足奖励条件json
     */
    @TableField("rewardCondition")
    private String rewardcondition;

    /**
     * 奖励内容json
     */
    @TableField("rewardContent")
    private String rewardcontent;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    public MissionRewardContentBO getRewardContentBO() {

        if (StringUtils.isEmpty(rewardcontent)) {
            return new MissionRewardContentBO();
        } else {
            return JSON.parseObject(rewardcontent, MissionRewardContentBO.class);
        }
    }

}
