package com.ets.apply.application;

import com.ets.apply.application.common.config.CmbcBankConfig;
import com.ets.apply.application.common.utils.SM2Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;
import org.bouncycastle.crypto.digests.SM3Digest;
import org.bouncycastle.crypto.macs.HMac;
import org.bouncycastle.crypto.params.KeyParameter;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.Base64Utils;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

@SpringBootTest(classes = ApplyApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringRunner.class)
@ActiveProfiles(value = "dev")
@Slf4j
public class SmSignTest {

    @Autowired
    private CmbcBankConfig cmbcBankConfig;



    /**
     * 生成验签
     */
    @Test
    public void hmacSm3Test(){
        SmSignTest.hmacSM3("bd8e47bea18ff1Hj","BMhUm74TU9cvcjWVUCXkm8Bg7MGhOFAV0tBaVPZgrr2gnkfAhq07W1XXJ29WSn63yyxRVnxMLoZuGDhcN3G4Lz%2FLzvnTsiurGYJN0KWnc94u1Mb0qsahxN2F8Jjbmb%2FF3dVhMlskw1%2FOjWMyvQJQaEtzfcQquvFqUSiUmK7DXiXCkf9Jfo4NnHpbW9uPghIjmOb6J2EEhO1rPOkumUWt4LOqsV%2BMrAgPqBPs5qkiZTl7GPnbvDZEuAj9Th4bz5hKiNIc8yU9oJsFMfRKFpAv7tIICNu3vkkZa27Cml6FvevTsqP88HQ03CTaIDjsUkVP7IMRQ3kx3w%3D%3D");
    }
    public static String hmacSM3(String keyStr, String srcStr) {
        log.info("SMCypherUtil hmacSM3 srcStr={}", srcStr);
        byte[] key = keyStr.getBytes(StandardCharsets.UTF_8);
        KeyParameter keyParameter = new KeyParameter(key);
        SM3Digest digest = new SM3Digest();
        HMac mac = new HMac(digest);
        mac.init(keyParameter);
        byte[] srcData = srcStr.getBytes(StandardCharsets.UTF_8);
        mac.update(srcData, 0, srcData.length);
        byte[] result = new byte[mac.getMacSize()];
        mac.doFinal(result, 0);
        String hexRes = Hex.encodeHexString(result);
        log.info(hexRes);
        return hexRes;
    }




    /**
     * 民生银行推荐的加密方法
     * @throws Exception
     */
    @Test
    public void sm2encrypt2() throws Exception {
        //        KeyPair keyPair = generateSm2KeyPair();
        //明文

//        byte[] publicKeyByte = keyPair.getPublic().getEncoded();
        String publicKey  = "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEi4Z/+VKwhcNu8bGcEGdY278+1+AxsEWmnLKbkkdSaQsHvtyR2gwRIClxHk0MCme9Y6bhWYDuwvfCSDT0fR/VTQ==";
        String plaintext = "{\"version\":\"2.0\",\"apply_info\":{\"partner_pin\":\"1991826827\",\"prod_id\":\"137\"},\"request_id\":\"22102417090000000000000000000041\",\"source_code\":\"CJ-BJGDCS1\"}";
        //加密
        byte[] publicKeyByte2 =  Base64Utils.decode(publicKey.getBytes());
        String ciphertext = Base64Utils.encodeToString(SM2Utils.encrypt(plaintext.getBytes("utf-8"),publicKeyByte2));
        //生成签名
//        String signature = Base64Utils.encode(sign(plaintext.getBytes("utf-8"),keyPair.getPrivate().getEncoded()));
        System.out.println("ciphertext: " + ciphertext);
//        System.out.println("signature: " + signature);
        //解密
//        plaintext = new String(decrypt(Base64Utils.decode(ciphertext),keyPair.getPrivate().getEncoded()),"utf-8");
        //验签
//        boolean result = verify(plaintext.getBytes("utf-8"),Base64Utils.decode(signature),keyPair.getPublic().getEncoded());
        System.out.println("plaintext: " + plaintext);

        System.out.println("urlencode :"+ URLEncoder.encode(ciphertext));
//        System.out.println("verify result: " + result);
    }




}
