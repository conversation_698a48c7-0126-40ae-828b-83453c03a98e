package com.ets.apply.application.app.business.amap;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.validation.BeanValidationResult;
import cn.hutool.extra.validation.ValidationUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.ets.apply.application.app.business.BaseBusiness;
import com.ets.apply.application.app.business.external.ExternalOrderBusiness;
import com.ets.apply.application.app.business.plateNo.PlateNoBusiness;
import com.ets.apply.application.app.service.thirdPartner.AmapService;
import com.ets.apply.application.common.bo.amap.AvailableCheckBO;
import com.ets.apply.application.common.bo.amap.CreateOrderBO;
import com.ets.apply.application.common.bo.amap.QueryDetailBO;
import com.ets.apply.application.common.consts.amap.CurrencyEnum;
import com.ets.apply.application.common.consts.amap.OrderStatusEnum;
import com.ets.apply.application.common.consts.amap.ProcessStatusEnum;
import com.ets.apply.application.common.consts.amap.SubCodeEnum;
import com.ets.apply.application.common.consts.common.YesOrNoEnum;
import com.ets.apply.application.common.consts.productOrder.ReferValueEnum;
import com.ets.apply.application.common.dto.external.ExternalOrderCreateDTO;
import com.ets.apply.application.common.dto.request.plateNo.PlateNoCheckUniqueDTO;
import com.ets.apply.application.common.utils.PlateUtils;
import com.ets.apply.application.common.utils.amap.RequestValidationUtil;
import com.ets.apply.application.common.vo.amap.BizResponseVO;
import com.ets.apply.application.common.vo.amap.OrderDetailVO;
import com.ets.apply.application.infra.entity.ProductOrderEntity;
import com.ets.apply.application.infra.entity.ProductPackageEntity;
import com.ets.apply.application.infra.service.ProductOrderService;
import com.ets.apply.application.infra.service.ProductPackageService;
import com.ets.common.ToolsHelper;
import com.ets.common.annotation.RateLimiterAnnotation;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class AmapBusiness extends BaseBusiness {

    // 可提取为静态常量
    private static final BigDecimal HUNDRED = BigDecimal.valueOf(100);
    private static final int SCALE = 2; // 根据业务需要设定保留的小数位数
    private static final RoundingMode ROUNDING_MODE = RoundingMode.HALF_UP;

    @Autowired
    private AmapService amapService;

    @Autowired
    private ProductPackageService productPackageService;

    @Autowired
    private ProductOrderService productOrderService;


    @Autowired
    private PlateNoBusiness plateNoBusiness;


    @Autowired
    private ExternalOrderBusiness externalOrderBusiness;


    @DSTransactional
    public BizResponseVO createOrder(Map<String, String> requestMap) {
        log.info("高德请求创建订单原始参数:{}", requestMap.toString());

        try {
            // 使用通用验证工具进行验签和解析
            RequestValidationUtil.ValidationResult<CreateOrderBO> validationResult =
                    RequestValidationUtil.validateAndParse(
                            requestMap,
                            amapService::checkSign,
                            this::buildFailResponseByMessage,
                            CreateOrderBO.class
                    );

            if (!validationResult.isSuccess()) {
                return validationResult.getFailResponse();
            }

            CreateOrderBO createOrderBO = validationResult.getData();

            // 校验币种
            if (!CurrencyEnum.CNY.getCode().equals(createOrderBO.getCurrency())) {
                return failResponse(SubCodeEnum.FAIL.getCode(), "币种不支持");
            }

            // 校验商品信息
            if (ObjectUtil.isEmpty(createOrderBO.getItems()) || createOrderBO.getItems().length == 0) {
                return failResponse(SubCodeEnum.LACK_PARAM.getCode(), "item为空");
            }

            CreateOrderBO.Item item = createOrderBO.getItems()[0];
            if (ObjectUtil.isEmpty(item.getProduct())) {
                return failResponse(SubCodeEnum.LACK_PARAM.getCode(), "product为空");
            }

            String packageSn = item.getProduct().getSkuId();
            ProductPackageEntity productPackageEntity = productPackageService.getBySn(packageSn);
            if (productPackageEntity == null) {
                return failResponse(SubCodeEnum.LACK_PARAM.getCode(), "sku 商品未可以办理");
            }

            // 商品的售价需要跟系统设置的一样，否则返回错误提醒
            if (!Objects.equals(new BigDecimal(item.getProduct().getSalePrice()).divide(HUNDRED, SCALE, ROUNDING_MODE), productPackageEntity.getPackageFee())) {
                return failResponse(SubCodeEnum.FAIL.getCode(), "商品售价不一致，暂不允许销售");
            }

            // 订单创建
            ExternalOrderCreateDTO addOrderDTO = new ExternalOrderCreateDTO();
            addOrderDTO.setThirdOrderSn(createOrderBO.getAmapOrderId());
            addOrderDTO.setUserCode(createOrderBO.getBuyer().getOpenId());
            addOrderDTO.setProductId(packageSn);

            if (ObjectUtil.isNotEmpty(createOrderBO.getFulfillment())) {
                CreateOrderBO.Identifier target = createOrderBO.getFulfillment().getFulfillmentTarget().getIdentifier();
                if (target != null && StrUtil.isNotBlank(target.getPrimaryIdentifier())) {
                    String plateNo = target.getPrimaryIdentifier();
                    addOrderDTO.setPlateNo(plateNo);
                }
            }

            CreateOrderBO.Delivery delivery = createOrderBO.getDelivery();
            if (delivery != null && delivery.getReceiver() != null) {
                CreateOrderBO.Receiver receiver = delivery.getReceiver();
                addOrderDTO.setPhone(StrUtil.nullToEmpty(receiver.getMobile()).trim());
                //参数校验
                checkReceiver(receiver);
                addOrderDTO.setReceiver(StrUtil.nullToEmpty(receiver.getName()).trim());
                addOrderDTO.setAddress(receiver.getProvince() + receiver.getCity() + receiver.getDistrict() + StrUtil.nullToEmpty(receiver.getAddress()).trim());
            }

            // 订单金额处理
            addOrderDTO.setTotalAmount(new BigDecimal(createOrderBO.getTotalSalePrice()).divide(HUNDRED, SCALE, ROUNDING_MODE));
            // 实付金额
            addOrderDTO.setPaidAmount(new BigDecimal(createOrderBO.getTotalOrderPrice()).divide(HUNDRED, SCALE, ROUNDING_MODE));
            HashMap<String, String> result = externalOrderBusiness.create(addOrderDTO, ReferValueEnum.PARTNER_VALUE_AMAP.getCompanyId());

            HashMap<String, String> successData = new HashMap<>();
            successData.put("cpOrderId", result.get("spOrderId"));
            successData.put("amapOrderId", createOrderBO.getAmapOrderId());
            successData.put("subMsg", "成功");
            successData.put("subCode", "200");
            return amapService.successResponse(successData);
        } catch (Exception e) {
            log.error("高德请求创建订单异常:{}", e.getMessage(), e);
            return failResponse(SubCodeEnum.FAIL.getCode(), "请求失败：" + e.getMessage());
        }
    }


    private BizResponseVO buildSuccessData(ProductOrderEntity entity, String amapOrderId) {
        HashMap<String, String> successData = new HashMap<>();
        successData.put("cpOrderId", entity.getProductOrderSn());
        successData.put("amapOrderId", amapOrderId);
        successData.put("subMsg", "成功");
        successData.put("subCode", "200");
        return amapService.successResponse(successData);
    }

    @RateLimiterAnnotation(qps = 5, msg = "请求频繁，请稍后再试")
    public BizResponseVO availableCheck(Map<String, String> requestMap) {
        // 请求原始参数记录
        log.info("高德请求availableCheck原始参数:{}", requestMap.toString());
        HashMap<String, String> successData = new HashMap<>();

        try {
            // 使用通用验证工具进行验签和解析
            RequestValidationUtil.ValidationResult<AvailableCheckBO> validationResult =
                    RequestValidationUtil.validateAndParse(
                            requestMap,
                            amapService::checkSign,
                            this::buildFailResponseByMessage,
                            AvailableCheckBO.class
                    );

            if (!validationResult.isSuccess()) {
                return validationResult.getFailResponse();
            }

            AvailableCheckBO availableCheckBO = validationResult.getData();
            // 获取车牌号码
            if (ObjectUtil.isEmpty(availableCheckBO.getFulfillment())) {
                return amapService.failResponse(SubCodeEnum.LACK_PARAM.getCode(), "fulfillment为空");
            }
            if (ObjectUtil.isEmpty(availableCheckBO.getFulfillment().getFulfillmentTarget())) {
                return amapService.failResponse(SubCodeEnum.LACK_PARAM.getCode(), "fulfillmentTarget为空");
            }

            String plateNo = availableCheckBO.getFulfillment().getFulfillmentTarget().getIdentifier().getPrimaryIdentifier();
            if (ObjectUtil.isEmpty(plateNo)) {
                return amapService.failResponse(SubCodeEnum.LACK_PARAM.getCode(), "车牌号码为空");
            }

            // 计算车牌颜色
            int plateColor = PlateUtils.getPlateColorByPlateNo(plateNo);
            PlateNoCheckUniqueDTO plateNoCheckUniqueDTO = new PlateNoCheckUniqueDTO();
            plateNoCheckUniqueDTO.setPlateNo(plateNo);
            plateNoCheckUniqueDTO.setPlateColor(plateColor);


            boolean checkUnique = plateNoBusiness.checkIssuerPlateUnique(plateNoCheckUniqueDTO);

            successData.put("subMsg", "成功");
            successData.put("subCode", "200");
            successData.put("status", String.valueOf(checkUnique ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode()));
            return amapService.successResponse(successData);
        } catch (Exception e) {
            log.info("高德请求availableCheck异常:{}", e.getMessage());
            return amapService.failResponse(SubCodeEnum.FAIL.getCode(), e.getMessage());
        }

    }

    public BizResponseVO queryDetail(Map<String, String> requestMap) {
        // 请求原始参数记录
        log.info("高德请求queryDetail原始参数:{}", requestMap.toString());

        try {
            // 使用通用验证工具进行验签和解析
            RequestValidationUtil.ValidationResult<QueryDetailBO> validationResult =
                    RequestValidationUtil.validateAndParse(
                            requestMap,
                            amapService::checkSign,
                            this::buildFailResponseByMessage,
                            QueryDetailBO.class
                    );

            if (!validationResult.isSuccess()) {
                return validationResult.getFailResponse();
            }

            QueryDetailBO queryDetailBO = validationResult.getData();

            // 获取对应的订单信息
            ProductOrderEntity productOrder = productOrderService.getOneByProductOrderSnAndThirdOrderSn(queryDetailBO.getCpOrderId(), queryDetailBO.getAmapOrderId());

            if (ObjectUtil.isEmpty(productOrder)) {
                return amapService.failResponse(SubCodeEnum.DATA_NOT_EXIST.getCode(), "订单数据不存在");
            }

            OrderDetailVO orderDetailVO = new OrderDetailVO();
            // 第三方id
            orderDetailVO.setAmapOrderId(productOrder.getThirdOrderSn());
            // 供应商id
            orderDetailVO.setCpOrderId(productOrder.getProductOrderSn());
            // totalSalePrice 优惠前总价
            orderDetailVO.setTotalSalePrice(productOrder.getTotalAmount().multiply(BigDecimal.valueOf(100)).longValue());
            // 实际支付金额
            orderDetailVO.setTotalOrderPrice(productOrder.getPaidAmount().multiply(BigDecimal.valueOf(100)).longValue());
            // 订单状态
            OrderStatusEnum orderStatusEnum = OrderStatusEnum.getByProductOrderStatus(productOrder.getOrderStatus());
            orderDetailVO.setOrderStatus(ObjectUtil.isNotEmpty(orderStatusEnum) ? Objects.requireNonNull(orderStatusEnum).getCode() : 0);
            // 供应商订单状态
            ProcessStatusEnum processStatusEnum = ProcessStatusEnum.getByProductOrderStatus(productOrder.getOrderStatus());
            if (ObjectUtil.isEmpty(processStatusEnum)) {
                processStatusEnum = ProcessStatusEnum.PENDING;
            }
            assert processStatusEnum != null;
            orderDetailVO.setCpOrderStatus(processStatusEnum.getCpOrderStatus());
            // 商品信息
            OrderDetailVO.Product product = new OrderDetailVO.Product();
            product.setQuantity(1);
            product.setSkuId(productOrder.getPackageSn());
            orderDetailVO.setProduct(product);

            OrderDetailVO.Delivery delivery = new OrderDetailVO.Delivery();
            OrderDetailVO.Receiver receiver = new OrderDetailVO.Receiver();
            receiver.setName(productOrder.getSendName());
            receiver.setMobile(productOrder.getSendPhone());
            // 收货地址,需要从sendArea 中按空格分隔，示例 ：广东省 广州市 天河区
            // 安全性处理：防止空指针和数组越界
            String sendAreaStr = productOrder.getSendArea();
            if (StrUtil.isBlank(sendAreaStr)) {
                // 处理 sendArea 为空的情况
                receiver.setProvince("");
                receiver.setCity("");
                receiver.setDistrict("");
                receiver.setAddress(productOrder.getSendAddress());
            } else {
                String[] sendArea = sendAreaStr.split(" ", -1); // 使用 -1 限制避免末尾空字符串被丢弃
                // 确保至少有三个元素，不足时使用空字符串填充
                receiver.setProvince(sendArea.length > 0 ? sendArea[0] : "");
                receiver.setCity(sendArea.length > 1 ? sendArea[1] : "");
                receiver.setDistrict(sendArea.length > 2 ? sendArea[2] : "");
            }

            // 发货地址，示例：河南省洛阳市新安县dfhdfhdhd东方红东方红反对恢复和 通过sendArea 去除前面的省市区信息
            String sendArea = productOrder.getSendArea().replace(" ", "");
            receiver.setAddress(productOrder.getSendAddress().replace(sendArea, ""));

            delivery.setReceiver(receiver);
            delivery.setOrderId(productOrder.getLogisticNumber());

            if (ObjectUtil.isNotEmpty(productOrder.getLogisticNumber())) {
                OrderDetailVO.Company company = new OrderDetailVO.Company();
                company.setName(productOrder.getLogisticCompany());
                delivery.setCompany(company);
            }
            orderDetailVO.setDelivery(delivery);

            // 创建时间
            orderDetailVO.setCreateTime(productOrder.getCreatedAt().toInstant(ZoneOffset.of("+8")).toEpochMilli());

            // 币种
            orderDetailVO.setCurrency("CNY");

            // 状态码
            orderDetailVO.setSubCode("200");
            orderDetailVO.setSubMsg("成功");
            return amapService.successResponse(orderDetailVO);
        } catch (Exception e) {
            log.info("高德请求queryDetail异常:{}", e.getMessage());
            return amapService.failResponse(SubCodeEnum.FAIL.getCode(), e.getMessage());
        }
    }

    /**
     * 获取订单进度
     */
    private List<OrderDetailVO.ProcessDetail> getProcessList(ProductOrderEntity productOrder) {

        List<OrderDetailVO.ProcessDetail> processList = new ArrayList<>();
        processList.add(new OrderDetailVO.ProcessDetail(
                ProcessStatusEnum.PENDING.getProcessStatus(),
                ProcessStatusEnum.PENDING.getCpOrderStatus(),
                productOrder.getCreatedAt().toInstant(ZoneOffset.of("+8")).toEpochMilli()));

        ProcessStatusEnum processStatusEnum = ProcessStatusEnum.getByProductOrderStatus(productOrder.getOrderStatus());
        if (ObjectUtil.isEmpty(processStatusEnum) || Objects.equals(processStatusEnum, ProcessStatusEnum.PENDING)) {
            // 固定一个初始状态返回
            return processList;
        }

        // 已发货状态添加发货信息
        if (ObjectUtil.isNotEmpty(productOrder.getSendTime())) {
            OrderDetailVO.ProcessDetail processDetail = new OrderDetailVO.ProcessDetail(
                    ProcessStatusEnum.DELIVERING.getProcessStatus(),
                    ProcessStatusEnum.DELIVERING.getCpOrderStatus(),
                    productOrder.getSendTime().toInstant(ZoneOffset.of("+8")).toEpochMilli());
            processList.add(processDetail);
        }

        if (ObjectUtil.isNotEmpty(productOrder.getReceivedTime())) {
            OrderDetailVO.ProcessDetail processDetail = new OrderDetailVO.ProcessDetail(
                    ProcessStatusEnum.DELIVERY_SUCCESS.getProcessStatus(),
                    ProcessStatusEnum.DELIVERY_SUCCESS.getCpOrderStatus(),
                    productOrder.getReceivedTime().toInstant(ZoneOffset.of("+8")).toEpochMilli());
            processList.add(processDetail);
        }

        // 已激活
        if (ObjectUtil.isNotEmpty(productOrder.getFinishTime())) {
            OrderDetailVO.ProcessDetail processDetail = new OrderDetailVO.ProcessDetail(
                    ProcessStatusEnum.SUCCESS.getProcessStatus(),
                    ProcessStatusEnum.SUCCESS.getCpOrderStatus(),
                    productOrder.getFinishTime().toInstant(ZoneOffset.of("+8")).toEpochMilli());
            processList.add(processDetail);
        }


        return processList;
    }

    public BizResponseVO failResponse(String code, String message) {
        return amapService.failResponse(code, message);
    }

    public void checkReceiver(@Valid CreateOrderBO.Receiver receiver) {
        // 触发校验
        BeanValidationResult result = ValidationUtil.warpValidate(receiver);
        if (!result.isSuccess()) {
            // 收集所有验证错误信息
            String errorMsg = result.getErrorMessages().stream()
                    .map(BeanValidationResult.ErrorMessage::getMessage)
                    .collect(Collectors.joining("; "));
            // 抛出业务异常
            ToolsHelper.throwException(errorMsg);
        }
    }

    /**
     * 根据错误消息构建失败响应
     * 这个方法会根据消息内容判断错误类型并返回相应的失败响应
     */
    private BizResponseVO buildFailResponseByMessage(String message) {
        // 根据消息内容判断错误类型
        if (message.contains("签名校验失败") || message.contains("签名验证异常")) {
            return amapService.failResponse(SubCodeEnum.SIGN_ERROR.getCode(), message);
        } else if (message.contains("为空") || message.contains("格式错误")) {
            return amapService.failResponse(SubCodeEnum.LACK_PARAM.getCode(), message);
        } else {
            return amapService.failResponse(SubCodeEnum.FAIL.getCode(), message);
        }
    }
}
