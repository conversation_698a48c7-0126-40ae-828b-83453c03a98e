package com.ets.apply.application.infra.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 申办召回
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("recall")
public class RecallEntity extends BaseEntity<RecallEntity> {

    private static final long serialVersionUID = 1L;

    @TableId
    private String recallSn;

    /**
     * 召回类型[1-召回送权益]
     */
    private Integer recallType;

    /**
     * 用户uid
     */
    private Integer uid;

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 车牌颜色
     */
    private Integer plateColor;

    /**
     * 操作参数
     */
    private String operateParams;

    /**
     * 操作结果
     */
    private String operateResult;

    /**
     * 召回状态[0-未操作 1-已召回 2-召回失败 3-待召回]
     */
    private Integer recallStatus;

    /**
     * 原始订单号
     */
    private String originOrderSn;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;


}
