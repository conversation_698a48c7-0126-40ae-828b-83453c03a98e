package com.ets.apply.application.common.vo.applyOrder;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class OrderBizTypeStatisticExcelVO {

    /**
     * 包sn
     */
    @ExcelProperty(value = "业务类型（值）")
    private Integer bizType;

    /**
     * 包名
     */
    @ExcelProperty(value = "业务类型名称")
    private String bizTypeName;

    /**
     * 平均总数
     */
    @ExcelProperty(value = "订单总数")
    private Integer total;

    @ExcelProperty(value = "平均订单总数/天")
    private BigDecimal average;
    /**
     * 总计
     */
    @ExcelProperty(value = "区间订单总数")
    private Integer countTotal;

    /**
     * 涨跌幅
     */
    @ExcelProperty(value = "涨跌幅（%）")
    private BigDecimal variation = BigDecimal.ZERO;
}
