package com.ets.apply.application.controller.testing;

import com.ets.apply.application.app.business.creditCard.*;
import com.ets.common.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RequestMapping("/testing/creditCard")
@RefreshScope
@RestController
@Slf4j
public class CreditCardTestController {


    @Autowired
    private CmbcBusiness cmbcBusiness;

    @Autowired
    private SpdBusiness spdBusiness;

    @Autowired
    private CebBusiness cebBusiness;

    @Autowired
    private CgbCreditBusiness cgbBusiness;

    @Autowired
    private CommBusiness commBusiness;

    /**
     * 测试环境获取加密数据用以模拟
     */
    @PostMapping("/sign")
    public JsonResult<HashMap<String, String>> cmbcSign(@RequestBody String data) {
        String encryptData = cmbcBusiness.encryptLocal(data);
        String sign = cmbcBusiness.sign(encryptData);
        HashMap<String, String> map = new HashMap<>();
        map.put("data", encryptData);
        map.put("sign", sign);
        return JsonResult.ok(map);
    }


    /**
     * 浦发测试造数据
     * @param data
     * @return
     * @throws Exception
     */
    @PostMapping("/spdSign")
    public JsonResult<HashMap<String, String>> spdSign(@RequestBody String data) throws Exception {
        return JsonResult.ok(spdBusiness.spdSign(data));
    }


    /**
     * ceb加密
     *
     * @param data 数据
     * @return {@link JsonResult}<{@link HashMap}<{@link String}, {@link Object}>>
     * @throws Exception 异常
     */
    @PostMapping("/cebSign")
    public JsonResult<HashMap<String, Object>> cebSign(@RequestBody String data) throws Exception {
        return JsonResult.ok(cebBusiness.cebSign(data));
    }


    /**
     * 测试验证返回头
     * @return
     */
    @PostMapping("/testHeader")
    public ResponseEntity<HashMap<String, String>> test() {
        HashMap<String, String> body = new HashMap<>();

        body.put("test", "test");
        return ResponseEntity.status(200).header("test", "tes")
                .header("test2", "test2")
                .body(body);
    }

    @PostMapping("/cgbSign")
    public JsonResult<Map<String, Object>> cgbSign(@RequestBody String data) throws Exception {
        return JsonResult.ok(cgbBusiness.cgbRequest(data));
    }


    /**
     * 交通银行模拟加密
     * @param data
     * @return
     * @throws Exception
     */
    @PostMapping("/commSign")
    public JsonResult<Map<String, String>> commSign(@RequestBody String data) throws Exception {
        return JsonResult.ok(commBusiness.sign(data));
    }
}
