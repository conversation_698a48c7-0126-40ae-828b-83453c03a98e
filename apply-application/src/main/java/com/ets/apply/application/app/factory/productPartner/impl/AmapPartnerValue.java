package com.ets.apply.application.app.factory.productPartner.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ets.apply.application.app.business.ProductOrderBusiness;
import com.ets.apply.application.app.service.thirdPartner.AmapService;
import com.ets.apply.application.common.bo.productOrder.ProductOrderRefundBO;
import com.ets.apply.application.common.bo.productOrder.ProductOrderThirdSyncBO;
import com.ets.apply.application.common.config.AmapConfig;
import com.ets.apply.application.common.consts.amap.CurrencyEnum;
import com.ets.apply.application.common.consts.amap.RefundStatusEnum;
import com.ets.apply.application.common.consts.amap.RefundTypeEnum;
import com.ets.apply.application.common.consts.productOrder.ProductOrderLogTypeEnum;
import com.ets.apply.application.common.consts.productOrder.ProductOrderStatusEnum;
import com.ets.apply.application.infra.entity.ProductOrderEntity;
import com.ets.apply.application.infra.service.ProductOrderService;
import com.ets.common.ToolsHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class AmapPartnerValue extends ProductPartnerBase {

    @Autowired
    private ProductOrderService productOrderService;

    @Autowired
    private ProductOrderBusiness productOrderBusiness;

    @Autowired
    private AmapService amapService;

    @Autowired
    private AmapConfig amapConfig;


    /**
     * 退款操作发起
     *
     * @param productOrderRefundBO
     * @return
     */
    @Override
    public String refund(ProductOrderRefundBO productOrderRefundBO) {
        ProductOrderEntity productOrder = productOrderService.getOneByColumn(productOrderRefundBO.getProductOrderSn(), ProductOrderEntity::getProductOrderSn);

        Map<String, String> paramMap = new HashMap<String, String>();

        // 构造业务参数
        JSONObject bizContent = new com.alibaba.fastjson.JSONObject();
        bizContent.put("amapOrderId", productOrder.getThirdOrderSn());
        bizContent.put("cpOrderId", productOrder.getProductOrderSn());
        // 退款状态
        bizContent.put("refundStatus", RefundStatusEnum.REFUND_SUCCESS.getCode());
        // 退款类型
        bizContent.put("refundType", RefundTypeEnum.SUPPLIER_REFUND.getCode());
        bizContent.put("finalRefundPrice", productOrder.getPaidAmount().multiply(BigDecimal.valueOf(100)).longValue());
        // productOrderRefundBO.getRefundAt() String 转成LocalDateTime
        LocalDateTime refundAt = LocalDateTime.parse(productOrderRefundBO.getRefundAt(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        // refundAt  转毫秒时间
        bizContent.put("finalRefundTime", refundAt.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        bizContent.put("currency", CurrencyEnum.CNY.getCode());
        HashMap<String, String> reason = new HashMap<>();
        reason.put("desc", productOrderRefundBO.getRefundReason());
        bizContent.put("reason", reason);

        paramMap.put("biz_content", JSON.toJSONString(bizContent));
        try {
            amapService.requestAmap(amapConfig.getRefundMethod(), paramMap);
            // 添加日志，记录退款成功
            productOrderBusiness.createLog(ProductOrderLogTypeEnum.REFUND.getCode(),
                    productOrder,
                    ProductOrderStatusEnum.CLOSED.getCode(),
                    "订单请求高德退款成功，时间：" + DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(LocalDateTime.now()),
                    productOrderRefundBO.getOperator());
        } catch (Exception e) {
            log.error("请求高德退款失败", e);
            ToolsHelper.throwException("请求高德退款接口失败：" + e.getMessage());
        }
        return "";
    }

    /*
     * 订单状态同步
     */
    public void orderSync(ProductOrderEntity productOrder, ProductOrderThirdSyncBO bo) {
        if (ObjectUtil.isEmpty(productOrder) || ObjectUtil.isEmpty(productOrder.getThirdOrderSn())) {
            return;
        }
        Map<String, String> paramMap = new HashMap<>();
        // 构造业务参数
        JSONObject bizContent = new JSONObject();
        bizContent.put("amapOrderId", productOrder.getThirdOrderSn());
        bizContent.put("cpOrderId", productOrder.getProductOrderSn());
        bizContent.put("bizType", 98);
        bizContent.put("cpCode", "98001");
        paramMap.put("biz_content", JSON.toJSONString(bizContent));
        try {
            amapService.requestAmap(amapConfig.getOrderSyncMethod(), paramMap);
        } catch (Exception e) {
            log.error("请求高德订单同步失败", e);
            ToolsHelper.throwException("请求高德订单同步失败：" + e.getMessage());
        }
    }

}
