package com.ets.apply.application.common.consts.activityCreditCardBankUser;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ActivityCreditCardBankUserApplyStatusEnum {


    APPLY_DEFAULT(0, "默认"),
    APPLY_SUCCESS(1, "成功"),
    APPLY_FAIL(2, "失败"),
    APPLY_USED(3, "已使用资格");

    private final int code;
    private final String description;

    public static String getDescByCode(int code) {
        for (ActivityCreditCardBankUserApplyStatusEnum node : ActivityCreditCardBankUserApplyStatusEnum.values()) {
            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }

}
