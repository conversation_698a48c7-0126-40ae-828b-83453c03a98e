package com.ets.apply.application.app.thirdservice.feign;

import com.ets.apply.application.app.thirdservice.fallback.CallPabFallbackFactory;
import com.ets.apply.application.common.dto.request.creditCard.pab.CommReqDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.net.URI;
import java.util.Map;

/**
 * 调用平安银行接口
 */

@FeignClient(url = "EMPTY", name = "CallPabFeign", fallbackFactory = CallPabFallbackFactory.class)
public interface CallPabFeign {
    @PostMapping()
    String checkNewUserByPhone(URI uri, @RequestBody CommReqDTO dto);

    @PostMapping
    String getApplyUrl(URI uri, @RequestBody Map<String,String> params);

    @GetMapping
    String queryOrder(URI uri, @RequestBody Map<String,String> params);
}
