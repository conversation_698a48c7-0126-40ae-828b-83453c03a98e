package com.ets.apply.application.common.consts.amap;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum RefundTypeEnum {

    /**
     * 退款类型；
     * 1、用户发起
     * 2、供应商发起
     * 3、高德发起
     */
    USER_REFUND(1, "用户发起"),
    SUPPLIER_REFUND(2, "供应商发起"),
    AMAP_REFUND(3, "高德发起");

    private final Integer code;
    private final String description;

    public static String getDescByCode(Integer code) {
        for (RefundTypeEnum node : RefundTypeEnum.values()) {
            if (node.getCode().equals(code)) {
                return node.getDescription();
            }
        }

        return "";
    }

}
