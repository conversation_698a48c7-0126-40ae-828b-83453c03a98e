package com.ets.apply.application.common.consts.writeCard;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
@AllArgsConstructor
public enum WriteCardInitResultEnum {
    NORMAL(1, "正常"),
    ERROR(2, "异常");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;

    static {
        WriteCardInitResultEnum[] enums = WriteCardInitResultEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
