package com.ets.apply.application.controller.admin;


import com.ets.apply.application.app.business.creditCardBankConfig.CreditCardBankConfigBusiness;
import com.ets.apply.application.common.dto.creditCardBank.CreditCardBankSetStatusDTO;
import com.ets.apply.application.common.dto.creditCardBankConfig.CreditCardBankConfigSetStatusDTO;
import com.ets.apply.application.common.vo.creditCardBankConfig.CreditCardBankConfigListVO;
import com.ets.common.JsonResult;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 */
@Controller
@RestController
@RequestMapping("/admin/credit-card-bank-config")
public class CreditCardBankConfigAdminController {

    @Autowired
    private CreditCardBankConfigBusiness creditCardBankConfigBusiness;

    @RequestMapping("/get-list")
    @ResponseBody
    JsonResult<List<CreditCardBankConfigListVO>> getList() {

        return JsonResult.ok(creditCardBankConfigBusiness.getList());
    }


    @RequestMapping("/set-status")
    JsonResult<?> setStatus(@RequestBody @Valid CreditCardBankConfigSetStatusDTO setStatusDTO){
        creditCardBankConfigBusiness.setStatus(setStatusDTO);
        return JsonResult.ok();
    }




}
