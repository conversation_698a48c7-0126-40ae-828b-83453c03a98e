package com.ets.apply.application.controller;

import com.ets.apply.application.app.business.customerService.CustomerServiceBusiness;
import com.ets.apply.application.common.dto.customerService.GetListByUidDTO;
import com.ets.apply.application.common.vo.customerService.GetListByUidVO;
import com.ets.common.JsonResult;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RequestMapping("/customer-service")
@RefreshScope
@RestController
@Slf4j
public class CustomerServiceController extends BaseController{

    @Autowired
    private CustomerServiceBusiness customerServiceBusiness;

    @RequestMapping("/get-list-by-uid")
    public JsonResult<List<GetListByUidVO>> getListByUid(@RequestBody @Valid GetListByUidDTO getListByUidDTO){

        return JsonResult.ok(customerServiceBusiness.getListByUid(getListByUidDTO));
    }

}
