package com.ets.apply.application.controller.internal;

import com.ets.apply.application.app.business.OrderBusiness;
import com.ets.apply.application.controller.BaseController;
import com.ets.apply.feign.request.ApplyOrderSnsDTO;
import com.ets.apply.feign.feign.ApplyOrderFeign;
import com.ets.apply.feign.response.OrderResponse;
import com.ets.common.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.util.List;

@RequestMapping("/apply-order")
@RefreshScope
@RestController
@Slf4j
public class ApplyOrderController extends BaseController implements ApplyOrderFeign {

    @Autowired
    private OrderBusiness orderBusiness;

    @RequestMapping("/getOrderListBySn")
    public JsonResult<List<OrderResponse>> getOrderListBySn(@RequestBody @Valid ApplyOrderSnsDTO dto) {

        return JsonResult.ok(orderBusiness.getOrderListBySn(dto.getOrderSns()));
    }
}
