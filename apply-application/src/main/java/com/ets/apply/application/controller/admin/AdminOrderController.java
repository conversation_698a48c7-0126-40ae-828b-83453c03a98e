package com.ets.apply.application.controller.admin;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.apply.application.app.business.CollectOrderBusiness;
import com.ets.apply.application.app.business.admin.AdminOrderBusiness;
import com.ets.apply.application.common.dto.order.OrderLogListDTO;
import com.ets.apply.application.common.dto.request.admin.orderOrder.AdminOrderCancelDTO;
import com.ets.apply.application.common.dto.request.admin.orderOrder.AdminOrderGetInfoDTO;
import com.ets.apply.application.common.dto.request.admin.orderOrder.AdminOrderOrderGetListDTO;
import com.ets.apply.application.common.vo.LogisticOrderVo;
import com.ets.apply.application.common.vo.OrderVo;
import com.ets.apply.application.common.vo.ReviewOrderVehicleVo;
import com.ets.apply.application.common.vo.UserCardVo;
import com.ets.apply.application.common.vo.admin.orderOrder.AdminOrderOrderInfoVO;
import com.ets.apply.application.common.vo.admin.orderOrder.AdminOrderOrderListVO;
import com.ets.apply.application.common.vo.adminOrder.HistoryOrderVO;
import com.ets.apply.application.common.vo.adminOrder.OrderLogVO;
import com.ets.apply.application.controller.BaseController;
import com.ets.common.JsonResult;
import com.ets.common.annotation.CosSignAnnotation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;

/**
 * <p>
 * 订单 集约管理后台接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-17
 */
@RequestMapping("/admin/order")
@RefreshScope
@RestController
@Slf4j
@Validated
public class AdminOrderController extends BaseController {

    @Autowired
    private CollectOrderBusiness collectBusiness;
    @Autowired
    private AdminOrderBusiness adminOrderBusiness;

    /**
     * 根据uid，车牌，货手机号，卡号 获取订单列表
     * @param uid 用户id
     * @param phone 手机号
     * @param plateNo 车牌号
     * @return JsonResult
     */
    @RequestMapping("/getUserInfos")
    public JsonResult<HashMap<String, Object>> getUserInfos(
            @RequestParam(value = "uid", required = false) @Min(value = 1, message = "请输入大于0的数值") Long uid,
            @RequestParam(value = "phone", required = false) String phone,
            @RequestParam(value = "plateNo", required = false) String plateNo
    ) {
        if (plateNo != null) {
            plateNo = plateNo.trim().toUpperCase(Locale.ROOT);
        }

        HashMap<String, Object> result = collectBusiness.getUserInfo(uid, phone, plateNo);

        return JsonResult.ok(result);
    }

    @RequestMapping("/getUserCardInfo")
    public JsonResult<UserCardVo> getUserCardInfo(
            @RequestParam(value = "uid") @Min(value = 1, message = "请输入大于0的数值") Long uid,
            @RequestParam(value = "plateNo") String plateNo,
            @RequestParam(value = "plateColor") Integer plateColor
    ) {

        return JsonResult.ok(collectBusiness.getUserCardInfo(uid, plateNo, plateColor));
    }

    @RequestMapping("/getOrderInfo")
    public JsonResult<OrderVo> getOrderInfo(
            @RequestParam(value = "orderSn", required = false) @NotBlank(message = "订单号不能为空") String orderSn
    ) {

        return JsonResult.ok(collectBusiness.getOrderInfo(orderSn));
    }

    @RequestMapping("/getOrderFeeInfo")
    public JsonResult<OrderVo> getOrderFeeInfo(
            @RequestParam(value = "orderSn", required = false) @NotBlank(message = "订单号不能为空") String orderSn
    ) {

        return JsonResult.ok(collectBusiness.getOrderFeeInfo(orderSn));
    }

    @RequestMapping("/getHistoryOrderInfo")
    public JsonResult<List<HistoryOrderVO>> getHistoryOrderInfo(
            @RequestParam(value = "orderSn", required = false) @NotBlank(message = "订单号不能为空") String orderSn,
            @RequestParam(value = "plateNo", required = false) @NotBlank(message = "车牌号不能为空") String plateNo,
            @RequestParam(value = "uid", required = false) Long uid
    ) {

        return JsonResult.ok(collectBusiness.getHistoryOrderInfo(orderSn, plateNo, uid));
    }

    @RequestMapping("/getOrderLogisticInfo")
    public JsonResult<LogisticOrderVo> getOrderLogisticInfo(
            @NotBlank(message = "订单号不能为空") @RequestParam(value = "orderSn", required = false) String orderSn
    ) {

        return JsonResult.ok(collectBusiness.getOrderLogisticInfo(orderSn));
    }

    @RequestMapping("/getVehicleInfo")
    @CosSignAnnotation
    public JsonResult<ReviewOrderVehicleVo> getVehicleInfo(
            @RequestParam(value = "orderSn", required = false) @NotBlank(message = "订单号不能为空") String orderSn
    ) {

        return JsonResult.ok(collectBusiness.getVehicleInfo(orderSn));
    }

    @PostMapping("/checkPlateNo")
    @ResponseBody
    public JsonResult<HashMap<String, Object>> checkPlateNo(
            @RequestParam(value = "plateNo", required = true) String plateNo,
            @RequestParam(value = "plateColor", required = true) Integer plateColor
    ) {
        return JsonResult.ok(collectBusiness.checkPlateNo(plateNo, plateColor));
    }

    @PostMapping("/getLog")
    public JsonResult<IPage<OrderLogVO>> getLog(@RequestBody @Valid OrderLogListDTO listDTO) {
        return JsonResult.ok(collectBusiness.getLog(listDTO));
    }

    @RequestMapping("/getList")
    @ResponseBody
    public JsonResult<IPage<AdminOrderOrderListVO>> getList(@RequestBody(required = false) @Valid AdminOrderOrderGetListDTO dto) {
        return JsonResult.ok(adminOrderBusiness.getList(dto));
    }

    @RequestMapping("/getInfo")
    @ResponseBody
    public JsonResult<AdminOrderOrderInfoVO> getInfo(@RequestBody(required = false) @Valid AdminOrderGetInfoDTO dto) {
        return JsonResult.ok(adminOrderBusiness.getInfo(dto));
    }
    /*
     * 审核单重置can_continue=1
     */
    @RequestMapping("/resetCanContinue")
    @ResponseBody
    public JsonResult<Boolean> resetCanContinue(@RequestBody(required = false) @Valid AdminOrderGetInfoDTO dto) {
        return JsonResult.ok(adminOrderBusiness.resetCanContinue(dto.getOrderSn()));
    }
    /*
     * 后台强制取消订单
     */
    @RequestMapping("/forceCancel")
    @ResponseBody
    public JsonResult<Boolean> forceCancel(@RequestBody(required = false) @Valid AdminOrderCancelDTO dto) {
        return JsonResult.ok(adminOrderBusiness.forceCancel(dto));
    }
}