package com.ets.apply.application.common.utils;

import cn.hutool.core.net.URLEncodeUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.ets.common.ToolsHelper;

import java.util.HashMap;

public class SignUtil {

    public static String getExternalSign(String body, String secret, Long timestamp) {

        String str = DigestUtil.sha256Hex(body) + "-" + secret + "-" + timestamp;

        return DigestUtil.sha256Hex(str);
    }

    public static HashMap<String,String> getChuanQiSignInfo(String key) {

        String nonce = ToolsHelper.createRandomStr(16);
        long millisTime = System.currentTimeMillis();

        String signStr = URLEncodeUtil.encode(nonce + millisTime + key).toUpperCase();

        String signature = DigestUtil.md5Hex(signStr).toUpperCase();

        HashMap<String,String> map = new HashMap<>();
        map.put("nonce", nonce);
        map.put("timestamp", String.valueOf(millisTime));
        map.put("signature", signature);

        return map;
    }

}
