<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 读取spring 上下文环境信息-->
    <springProperty scope="context" name="springAppName" source="spring.application.name" defaultValue="init"/>
    <springProperty scope="context" name="log.level" source="logging.root.level" defaultValue="INFO"/>
    <conversionRule conversionWord="ip" converterClass="com.ets.common.config.LogIpConfig"/>
    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter"/>
    <conversionRule conversionWord="wex" converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter"/>
    <conversionRule conversionWord="wEx" converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter"/>
    <conversionRule conversionWord="cm" converterClass="com.ets.common.log.EtcMessageConverter"/>
    <property name="log.dir" value="/data/logs"/>

    <!-- console日志 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>
                %clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr([%t]){faint} %clr([%-5level]) %clr([%ip]) %clr(${springAppName}) %clr([%logger:%L]){cyan} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}
            </pattern>
        </encoder>
    </appender>

    <appender name="ROLLING" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>
            ${log.dir}/${springAppName}.log
        </file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>
                ${log.dir}%d{yyyy-MM,aux}/${springAppName}.%d{yyyy-MM-dd}.log.gz
            </fileNamePattern>
            <maxHistory>30</maxHistory> <!-- 保留30天日志 -->
        </rollingPolicy>
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers class="net.logstash.logback.composite.loggingevent.LoggingEventJsonProviders">
                <pattern>
                    <pattern>
                        {
                        "sysTime":"%d{yyyy-MM-dd HH:mm:ss.SSS}",
                        "level":"%level",
                        "ip":"%ip",
                        "appName":"${springAppName:-}",
                        "traceId": "%X{X-B3-TraceId:-}",
                        "spanId": "%X{X-B3-SpanId:-}",
                        "parentId": "%X{X-B3-ParentSpanId:-}",
                        "exportable": "%X{X-Span-Export:-}",
                        "pid": "${PID:-}",
                        "thread": "%thread",
                        "className": "%logger{40}:%L",
                        "methodName": "%logger{40}:%M-%L",
                        "message": "%cm",
                        "thrown":"%exception{10}"
                        }
                    </pattern>
                </pattern>
            </providers>
        </encoder>
    </appender>

    <springProfile name="dev,dev1,test,test1">
        <root level="${log.level}">
            <appender-ref ref="STDOUT"/>
        </root>
    </springProfile>
    <springProfile name="dev,dev1,test,test1,pre,prod">
        <root level="${log.level}">
            <appender-ref ref="ROLLING"/>
        </root>
    </springProfile>

    <logger name="RocketmqClient" additivity="false">
        <level value="ERROR" />
        <appender-ref ref="ROLLING"/>
        <appender-ref ref="STDOUT"/>
    </logger>

    <logger name="RocketmqRemoting" additivity="false">
        <level value="ERROR" />
        <appender-ref ref="ROLLING"/>
        <appender-ref ref="STDOUT"/>
    </logger>

</configuration>
