package com.ets.apply.application.common.vo.adminSalesUpgrade;

import com.ets.apply.application.common.bo.adminSaleUpgrade.SaleUpgradePackageBO;
import com.ets.apply.application.common.bo.adminSaleUpgrade.SaleUpgradeRuleConditionBO;
import com.ets.apply.application.infra.entity.sales.SalesUpgradePackageEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class SalesUpgradeInfoVO {
    private Integer id;
    private String planName;
    private Integer priority;
    private List<SaleUpgradeRuleConditionBO> ruleConditions;
    private Integer status;
    private String statusStr;
    private Integer releaseStatus;
    private List<SalesUpgradePackageEntity> packageSns;
    private String operator;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

}
