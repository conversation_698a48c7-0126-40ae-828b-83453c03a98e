package com.ets.apply.application.common.bo.external;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.util.HashMap;

@Data
public class ExternalCreateCustomerBO {

    /**
     * 服务类型 1退货退款，2换货，3仅退款, 4取消订单
     */
    @NotNull
    private Integer serviceType;

    /**
     * 用户id
     */
    private Long uid;

    /**
     * sku信息, skuSn => count, null 则表示订单的全部sku
     */
    @NotNull
    private HashMap<String, Integer> skuInfoMap;

    /**
     * 申请原因
     */
    private String applyReason;

    /**
     * 问题描述
     */
    private String problemDescription;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 是否预检查
     */
    private Boolean preCheck = false;

    private String operator;


    /*  非商品订单售后寄回操作相关参数**************************/
    /**
     * 回寄快递单号
     */
    @Length(max = 50, message = "快递单号长度不能超过50个字符")
    private String sendbackExpressNumber;

    /**
     * 回寄快递公司
     */
    @Length(max = 10, message = "快递公司长度不能超过10个字符")
    private String sendbackExpressCompany = "默认";


    /* 其他备用参数*****************************/

    /**
     * 业务订单类型，1 商品订单售后
     */
    private Integer businessType = 1;

    /**
     * 业务单号，非商品订单调用时传值
     */
    private String businessSn;

    /**
     * 业务起始时间，计算有效期用
     */
    private String businessBeginTime;

    /**
     * 通知回调地址，默认从商品订单继承
     */
    private CustomerNotifyUrlBO notifyUrl;


}
