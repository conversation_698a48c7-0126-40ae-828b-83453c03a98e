package com.ets.apply.application.app.business;

import com.alibaba.fastjson.JSONObject;
import com.ets.apply.application.app.thirdservice.feign.CallPhpPayFeign;
import com.ets.apply.application.common.bo.productPackage.PackageMarketConfigBO;
import com.ets.apply.application.common.dto.payment.PhpRefundDTO;
import com.ets.apply.application.common.vo.recovery.RecoveryCheckAllowApplyVO;
import com.ets.apply.application.common.vo.recovery.RecoveryRewardCheckVO;
import com.ets.apply.application.common.vo.recovery.RecoveryRewardVO;
import com.ets.apply.application.infra.entity.OrderOrderEntity;
import com.ets.apply.application.infra.entity.ProductPackageEntity;
import com.ets.common.ToolsHelper;
import com.ets.user.feign.feign.UsersCardsFeign;
import com.ets.user.feign.response.UsersCardsResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;

@Component
public class RecoveryBusiness {

    @Autowired
    private OrderBusiness orderBusiness;

    @Autowired
    private OrderPackageBusiness orderPackageBusiness;

    @Autowired
    private CallPhpPayFeign callPhpPayFeign;

    @Autowired
    private UsersCardsFeign usersCardsFeign;

    @Resource(name = "redisPermanentTemplate")
    private StringRedisTemplate redisPermanentTemplate;

    public Boolean isAllowRecovery(OrderOrderEntity orderEntity, ProductPackageEntity productPackage) {

        if (productPackage == null) {
            return false;
        }

        if (! checkRecoveryOpen(productPackage)) {
            return false;
        }

        // 没有支付
        if (! orderBusiness.hasPaid(orderEntity)) {
            return false;
        }
        if (StringUtils.isEmpty(orderEntity.getPaymentSn())) {
            return false;
        }

        // 是否激活判断
        if (! orderBusiness.isActivated(orderEntity)) {
            return false;
        }

        // 是否已退款判断
        if (orderBusiness.orderHasRefund(orderEntity)) {
            return false;
        }

        return true;
    }

    protected Boolean checkRecoveryOpen(ProductPackageEntity productPackage) {

        PackageMarketConfigBO.RecoveryConfig recoveryConfig = productPackage.getMarketConfigBO().getRecovery();

        if (recoveryConfig == null || recoveryConfig.getOpen() == null) {
            return false;
        }

        return recoveryConfig.getOpen();
    }

    public RecoveryCheckAllowApplyVO checkAllowApply(String orderSn) {

        OrderOrderEntity orderEntity = orderBusiness.getByOrderSn(orderSn);

        ProductPackageEntity productPackage = orderPackageBusiness.getOrderProductPackage(orderEntity);

        RecoveryCheckAllowApplyVO vo = new RecoveryCheckAllowApplyVO();

        Boolean isAllow = isAllowRecovery(orderEntity, productPackage);
        vo.setIsAllowToApply(isAllow);

        if (! isAllow) {
            return vo;
        }

        // 剩余有效期
        UsersCardsResponse userCard = usersCardsFeign.getByOrderSn(orderEntity.getOrderSn()).getData();

        LocalDateTime expireDate = userCard.getActivatedAt().plusDays(15);
        if (expireDate.isBefore(LocalDateTime.now())) {
            // 已过期
            vo.setIsAllowToApply(false);
            return vo;
        } else {
            vo.setTradeInEndTime(expireDate);
        }

        PackageMarketConfigBO.RecoveryConfig recoveryConfig = productPackage.getMarketConfigBO().getRecovery();

        vo.setTradeInFee(recoveryConfig.getAmount());

        return vo;
    }

    public RecoveryRewardCheckVO rewardCheck(String orderSn) {

        OrderOrderEntity orderEntity = orderBusiness.getByOrderSn(orderSn);

        ProductPackageEntity productPackage = orderPackageBusiness.getOrderProductPackage(orderEntity);

        if (! checkRecoveryOpen(productPackage)) {
            ToolsHelper.throwException("该申办订单不支持回收功能");
        }

        RecoveryRewardCheckVO vo = new RecoveryRewardCheckVO();

        Boolean isAllow = isAllowRecovery(orderEntity, productPackage);

        // 申办单已激活判断
        vo.setIsAllowToReward(isAllow);

        // 已取消或已退款，则永久失败
        vo.setPermanentFailed(orderBusiness.hasCanceled(orderEntity) || orderBusiness.orderHasRefund(orderEntity));

        return vo;
    }

    public RecoveryRewardVO reward(String orderSn, String returnOrderSn) {

        OrderOrderEntity orderEntity = orderBusiness.getByOrderSn(orderSn);

        ProductPackageEntity productPackage = orderPackageBusiness.getOrderProductPackage(orderEntity);

        Boolean isAllow = isAllowRecovery(orderEntity, productPackage);

        RecoveryRewardVO vo = new RecoveryRewardVO();

        if (! isAllow) {
            vo.setIsAllowReward(false);

            return vo;
        }

        vo.setIsAllowReward(true);

        // 奖励防重判断
        String hashKey = "RecoveryRewardRecord";
        String lockKey = orderSn + ":" + returnOrderSn;
        String cacheRefundSn = (String) redisPermanentTemplate.opsForHash().get(hashKey, lockKey);
        if (StringUtils.isNotEmpty(cacheRefundSn)) {

            vo.setRefundOrderNo(cacheRefundSn);

            return vo;
        }

        PackageMarketConfigBO.RecoveryConfig recoveryConfig = productPackage.getMarketConfigBO().getRecovery();

        // 发起退款，调gd-micro-pay接口
        PhpRefundDTO refundDTO = new PhpRefundDTO();
        refundDTO.setPaymentSn(orderEntity.getPaymentSn());
        refundDTO.setReason("回收奖励");
        refundDTO.setAmount(recoveryConfig.getAmount());

        JSONObject refundResponse = callPhpPayFeign.manualRefund(refundDTO).getDataWithCheckError();

        vo.setRefundOrderNo(refundResponse.getJSONObject("refund_order").getString("refund_sn"));

        // 缓存奖励
        redisPermanentTemplate.opsForHash().put(hashKey, lockKey, vo.getRefundOrderNo());

        return vo;
    }

}
