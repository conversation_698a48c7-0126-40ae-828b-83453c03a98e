package com.ets.apply.application.controller.admin;

import com.ets.apply.application.app.business.ApplyConfigBusiness;
import com.ets.apply.application.common.dto.request.applyConfig.ApplyConfigGetDto;
import com.ets.apply.application.common.dto.request.applyConfig.ApplyConfigSetDto;
import com.ets.apply.application.controller.BaseController;
import com.ets.apply.application.infra.entity.ApplyConfigEntity;
import com.ets.common.JsonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import jakarta.validation.Valid;

@Controller
@RequestMapping("/admin/applyConfig")
public class ApplyConfigController extends BaseController {

    @Autowired
    private ApplyConfigBusiness applyConfigBusiness;

    /**
     * 设置
     * @return
     */
    @RequestMapping("/setConfig")
    @ResponseBody
    public JsonResult<Boolean> setConfig(@RequestBody @Valid ApplyConfigSetDto applyConfigSetDto) {
        return JsonResult.ok(applyConfigBusiness.setConfig(applyConfigSetDto));
    }


    /**
     * 读取
     * @return
     */
    @RequestMapping("/getConfig")
    @ResponseBody
    public JsonResult<ApplyConfigEntity> getConfig(@RequestBody @Valid ApplyConfigGetDto applyConfigGetDto) {
        return JsonResult.ok(applyConfigBusiness.getConfig(applyConfigGetDto));
    }
}

