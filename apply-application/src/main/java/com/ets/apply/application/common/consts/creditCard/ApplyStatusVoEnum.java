package com.ets.apply.application.common.consts.creditCard;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ApplyStatusVoEnum {
    NO_APPLY(-1, "请先提交审核资料"),
    STATUS_UNKNOWN(0, "可继续办理"),
    STATUS_NOT_AUDIT(1, "已收到您的申请，系统将于3个工作日完成审核，如有疑问请联系在线客服。"),
    STATUS_FAIL_AUDIT(2, "审核不通过"),
    STATUS_PASS_AUDIT(3, "信用卡审核通过"),
    STATUS_ACTIVATE_BU_NOT_NEW_USER(4, "信用卡不满足奖励条件"),
    STATUS_CREDIT_CARD_ACTIVATE(5, "信用卡已激活成功"),
    QUALIFICATION_CONSUMED(6, "权益已使用"),
    ;

    private Integer code;
    private String message;





}
