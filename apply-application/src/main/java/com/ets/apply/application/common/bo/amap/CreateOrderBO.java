package com.ets.apply.application.common.bo.amap;

import com.ets.common.annotation.PhoneAnnotation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

@Data
public class CreateOrderBO {
    /**
     * {
     * "shop": {
     * "amapShopId": "1262089"
     * },
     * "totalSalePrice": 19500,
     * "createTime": 1749781790011,
     * "amapOrderId": "975018161000001620146429",
     * "currency": "CNY",
     * "totalOrderPrice": 19500,
     * "items": [
     * {
     * "product": {
     * "skuName": "xx",
     * "quantity": 1,
     * "salePrice": 19500,
     * "skuId": "5500002"
     * }
     * }
     * ],
     * "buyer": {
     * "openId": "6088175509856",
     * "mobile": "15056742981"
     * }
     * <p>
     * "fulfillment":{
     * "fulfillmentTarget":{
     * "targetType": "VEHICLE", // 履约目标类型。 车辆、房产、宠物等
     * "identifier": {
     * "type": "PLATE_NUMBER", // 标识类型。 车牌号，车架号等
     * "primaryIdentifier": "浙A12345", // 主要标识符
     * "secondIdentifier": "奔驰E3000", // 次要表示，可选
     * },
     * "verification": {
     * "required": true, // 是否必须验证
     * }
     * }
     * }
     * }
     */

    private Shop shop;
    private Integer totalSalePrice;
    private Long createTime;
    private String amapOrderId;
    private String currency;
    private Integer totalOrderPrice;
    private Item[] items;
    private Buyer buyer;

    private Delivery delivery;

    private Fulfillment fulfillment;

    @Data
    public static class Shop {
        private String amapShopId;
    }

    @Data
    public static class Item {
        private Product product;
    }

    @Data
    public static class Product {
        private String skuName;
        private Integer quantity;
        private Integer salePrice;
        private String skuId;
    }

    @Data
    public static class Buyer {
        private String openId;
        private String mobile;
    }

    @Data
    public static class Fulfillment {
        private FulfillmentTarget fulfillmentTarget;
    }

    @Data
    public static class FulfillmentTarget {
        private String targetType;
        private Identifier identifier;
        private Verification verification;
    }

    @Data
    public static class Identifier {
        private String type;
        private String primaryIdentifier;
        private String secondIdentifier;
    }

    @Data
    public static class Verification {
        private Boolean required;
    }

    @Data
    public static class Delivery {

        /**
         * receiver	Object	否	收货能力
         * fulfillment	Object	是	履约域
         */
        @Valid
        private Receiver receiver;
    }

    @Valid
    @Data
    public static class Receiver {

        /**
         * name	String	否	姓名
         * mobile	String	是	手机号
         * province	String	是	省名称
         * city	String	是	市名称
         * district	String	是	区名称
         * address	String	是	地址
         * addressDetail	String	否	地址详情（门牌号）
         * adcode	String	否	adcode（高德）
         * poiId	String	否	poiId
         * postcode	String	否	邮编
         * longitude	String	否	经度
         * latitude	String	否	维度
         */
        @NotBlank(message = "收货人姓名不能为空")
        @Pattern(regexp="(^[\u4e00-\u9fa5]+$)|([\u4e00-\u9fa5]+\\[(.*)\\]$)",message="收货人名称只能输入中文或者中文[任意内容]格式")
        @Length(min = 1,max = 20,message = "名称长度为1到20个字符")
        private String name;

        @PhoneAnnotation(message = "收货手机号校验错误")
        private String mobile;

        @NotBlank(message = "收货地址省份不能为空")
        private String province;
        @NotBlank(message = "收货地址城市不能为空")
        private String city;
        @NotBlank(message = "收货地址区不能为空")
        private String district;
        @Pattern(regexp="^[\\u4e00-\\u9fa5_a-zA-Z0-9]+$",message="收货地址不能包含特殊字符，请检查")
        @NotBlank(message = "收货地址不能为空")
        private String address;
        private String addressDetail;
        private String adcode;
        private String poiId;
        private String postcode;
        private String longitude;
        private String latitude;
    }
}
