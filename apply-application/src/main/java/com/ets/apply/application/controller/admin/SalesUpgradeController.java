package com.ets.apply.application.controller.admin;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.apply.application.app.business.sales.SalesUpgradeBusiness;
import com.ets.apply.application.common.dto.adminSalesUpgrade.*;
import com.ets.apply.application.common.vo.adminSalesUpgrade.SalesUpgradeInfoVO;
import com.ets.apply.application.common.vo.adminSalesUpgrade.SalesUpgradeListVO;
import com.ets.apply.application.controller.BaseController;
import com.ets.apply.application.infra.entity.sales.SalesUpgradeEntity;
import com.ets.common.JsonResult;
import com.ets.common.RequestHelper;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/admin/salesUpgrade")
public class SalesUpgradeController extends BaseController {
    @Autowired
    private SalesUpgradeBusiness salesUpgradeBusiness;

    /**
     * 营销列表查询
     * @return
     */
    @RequestMapping("/getList")
    @ResponseBody
    public JsonResult<IPage<SalesUpgradeListVO>> getList(@RequestBody(required = false) @Valid SalesUpgradeGetListDTO dto) {
        return JsonResult.ok(salesUpgradeBusiness.getList(dto));
    }




    /**
     *  新增计划
     * @return
     */
    @RequestMapping("/addPlan")
    @ResponseBody
    public JsonResult<Boolean> addPlan(@RequestBody(required = false) @Valid SalesUpgradeAddDTO dto) {
        salesUpgradeBusiness.addPlan(dto, RequestHelper.getHttpServletRequest().getHeader("loginCode"));
        return JsonResult.ok(true);
    }
    /**
     *  修改产品包
     * @return
     */
    @RequestMapping("/modifyPlan")
    @ResponseBody
    public JsonResult<Boolean> modifyPlan(@RequestBody(required = false) @Valid SalesUpgradeEditDTO dto) {
        salesUpgradeBusiness.modifyPlan(dto, RequestHelper.getHttpServletRequest().getHeader("loginCode"));
        return JsonResult.ok(true);
    }

    /**
     *  上下架计划
     * @return
     */
    @RequestMapping("/upOrDown")
    @ResponseBody
    public JsonResult<Boolean> upOrDown(@RequestBody(required = false) @Valid SalesUpgradeUpOrDownDTO dto) {
        salesUpgradeBusiness.upOrDown(dto, RequestHelper.getHttpServletRequest().getHeader("loginCode"));
        return JsonResult.ok(true);
    }

    /**
     * 获取详情
     * @return
     */
    @RequestMapping("/getInfo")
    @ResponseBody
    public JsonResult<SalesUpgradeInfoVO> getInfo(@RequestBody(required = false) @Valid SalesUpgradeGetInfoDTO dto) {
        return JsonResult.ok(salesUpgradeBusiness.getInfo(dto));
    }


    /**
     * 发布前对比
     * @return
     */
    @RequestMapping("/compareInfo")
    @ResponseBody
    public JsonResult<JSONObject> compareInfo(@RequestBody(required = false) @Valid SalesUpgradeGetInfoDTO dto) {
        return JsonResult.ok(salesUpgradeBusiness.compareInfo(dto));
    }

    /**
     * 发布
     * @return
     */
    @RequestMapping("/prod")
    @ResponseBody
    public JsonResult<Boolean> prod(@RequestBody(required = false) @Valid SalesUpgradeProdDTO dto) {
        return JsonResult.ok(salesUpgradeBusiness.prod(dto, RequestHelper.getHttpServletRequest().getHeader("loginCode")));
    }

    /**
     * 复制
     * @return
     */
    @RequestMapping("/copy")
    @ResponseBody
    public JsonResult<SalesUpgradeEntity> copy(@RequestBody(required = false) @Valid SalesUpgradeGetInfoDTO dto) {
        return JsonResult.ok(salesUpgradeBusiness.copy(dto, RequestHelper.getHttpServletRequest().getHeader("loginCode")));
    }
}
