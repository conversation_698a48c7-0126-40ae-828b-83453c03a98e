package com.ets.apply.application;

import com.ets.apply.application.app.job.TruckJob;
import org.junit.Test;

/**
 * 简单的语法测试，验证TruckJob类能正常加载
 */
public class TruckJobSyntaxTest {

    @Test
    public void testTruckJobClassLoading() {
        // 简单测试类是否能正常加载，验证语法正确性
        Class<?> clazz = TruckJob.class;
        assert clazz != null;
        assert clazz.getName().equals("com.ets.apply.application.app.job.TruckJob");
        
        // 验证方法存在
        try {
            clazz.getDeclaredMethod("checkDepositRefundStatusHandler", String.class);
            clazz.getDeclaredMethod("truckPayOrderRefundHandler", String.class);
            clazz.getDeclaredMethod("truckTermCouponSend", String.class);
        } catch (NoSuchMethodException e) {
            throw new AssertionError("Required methods not found in TruckJob", e);
        }
    }
}
