package com.ets.apply.application.controller.admin;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.apply.application.app.business.ProductPackageBusiness;
import com.ets.apply.application.common.config.LoginCodeLimitsConfig;
import com.ets.apply.application.common.dto.productPackage.UpDownDTO;
import com.ets.apply.application.common.dto.request.productPackage.*;
import com.ets.apply.application.common.vo.ProductPackageTmp.ProductPackageTmpAddVo;
import com.ets.apply.application.common.vo.productPackage.PackageTmpInfoVo;
import com.ets.apply.application.common.vo.productPackage.ProductPackageGetListVo;
import com.ets.apply.application.common.vo.productPackage.ProductPackageListOptionVo;
import com.ets.apply.application.common.vo.productPackage.ProductPackageListVo;
import com.ets.apply.application.controller.BaseController;
import com.ets.apply.application.infra.entity.PackageChangeLogEntity;
import com.ets.common.JsonResult;
import com.ets.common.RequestHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import jakarta.validation.Valid;
import java.util.HashMap;
import java.util.List;

/**
 * <p>
 * 商品套餐配置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-24
 */
@Controller
@RequestMapping("/admin/productPackage")
public class ProductPackageController extends BaseController {
    @Autowired
    private LoginCodeLimitsConfig loginCodeLimitsConfig;
    @Autowired
    private ProductPackageBusiness productPackageBusiness;

    /**
     * 产品包列表查询
     * @link http://yapi.golcer.cn/project/312/interface/api/12133
     * @return
     */
    @RequestMapping("/list")
    @ResponseBody
    public JsonResult<List<ProductPackageListOptionVo>> list() {

        //是否受限用户
        String loginCode = RequestHelper.getHttpServletRequest().getHeader("loginCode");

        return JsonResult.ok(productPackageBusiness.getAdminPackageList(loginCode));
    }


    /**
     * 产品包Tmp列表查询
     * @return
     */
    @RequestMapping("/get-list")
    @ResponseBody
    public JsonResult<IPage<ProductPackageGetListVo>> getList(@RequestBody(required = false) @Valid ProductPackageGetListDTO request) {
        return JsonResult.ok(productPackageBusiness.getAllList(request));
    }

    /**
     * 电商产品包列表
     *  <a href="https://yapi.etczs.net/project/312/interface/api/28524">电商产品包列表</a>
     * @param request
     * @return
     */
    @RequestMapping("/getShopPackageList")
    @ResponseBody
    public JsonResult<IPage<ProductPackageListVo>> getShopPackageList(@RequestBody(required = false) @Valid ProductPackageListDTO request) {
        return JsonResult.ok(productPackageBusiness.getProductPackageList(request));
    }

    /**
     * 产品包上下架
     * @return
     */
    @RequestMapping("/upDown")
    @ResponseBody
    public JsonResult<Boolean> upDown(@RequestBody @Valid UpDownDTO upDownDTO) {
        //是否受限用户
        String loginCode = RequestHelper.getHttpServletRequest().getHeader("loginCode");
        productPackageBusiness.upDown(upDownDTO,loginCode);
        return JsonResult.ok(true);
    }

    /**
     * 新增产品包
     * @return
     */
    @RequestMapping("/add")
    @ResponseBody
    public JsonResult<ProductPackageTmpAddVo> add(@RequestBody @Valid ProductPackageAddDTO productPackageAddDTO) {
        //是否受限用户
        String loginCode = RequestHelper.getHttpServletRequest().getHeader("loginCode");
        return JsonResult.ok(productPackageBusiness.add(productPackageAddDTO,loginCode));
    }
    /**
     * 新增产品包未发布前内容修改
     * @return
     */
    @RequestMapping("/modify")
    @ResponseBody
    public JsonResult<Boolean> modify(@RequestBody @Valid ProductPackageModifyDTO dto) {
        //是否受限用户
        String loginCode = RequestHelper.getHttpServletRequest().getHeader("loginCode");
        productPackageBusiness.modify(dto,loginCode);
        return JsonResult.ok(true);
    }
//    /**
//     * 申办内容修改 废弃
//     * @return
//     */
//    @RequestMapping("/applyConfigModify")
//    @ResponseBody
//    public JsonResult<Boolean> applyConfigModify(@RequestBody @Valid ProductPackageApplyModifyDTO dto) {
//        //是否受限用户
//        String loginCode = RequestHelper.getHttpServletRequest().getHeader("loginCode");
//        productPackageBusiness.applyConfigModify(dto,loginCode);
//        return JsonResult.ok(true);
//    }

    /**
     * 申办内容修改
     * @return
     */
    @RequestMapping("/frontedConfigModify")
    @ResponseBody
    public JsonResult<Boolean> frontedConfigModify(@RequestBody @Valid ProductPackageFrontedModifyDTO dto) {
        //是否受限用户
        String loginCode = RequestHelper.getHttpServletRequest().getHeader("loginCode");
        productPackageBusiness.frontedConfigModify(dto,loginCode);
        return JsonResult.ok(true);
    }
    /**
     * 申办内容修改
     * @return
     */
    @RequestMapping("/pageConfigModify")
    @ResponseBody
    public JsonResult<Boolean> pageConfigModify(@RequestBody @Valid ProductPackagePageModifyDTO dto) {
        //是否受限用户
        String loginCode = RequestHelper.getHttpServletRequest().getHeader("loginCode");
        productPackageBusiness.pageConfigModify(dto,loginCode);
        return JsonResult.ok(true);
    }

    /**
     * 发布生产
     * @return
     */
    @RequestMapping("/releaseTmpToProd")
    @ResponseBody
    public JsonResult<Boolean> releaseTmpToProd(@RequestBody @Valid ProductPackageReleaseDTO dto) {
        //是否受限用户
        String loginCode = RequestHelper.getHttpServletRequest().getHeader("loginCode");
        productPackageBusiness.releaseTmpToProd(dto,loginCode);
        return JsonResult.ok(true);
    }

    /**
     * 产品包编辑列表
     * @return
     */
    @RequestMapping("/getLoglist")
    @ResponseBody
    public JsonResult<IPage<PackageChangeLogEntity>> getLoglist(@RequestBody(required = false) @Valid PackageLogGetListDTO dto) {
        return JsonResult.ok(productPackageBusiness.getAllLogList(dto));
    }
    /**
     * 获取后台配置项
     * @return
     */
    @RequestMapping("/getAllConfig")
    @ResponseBody
    public JsonResult<Object> getAllConfig() {
        return JsonResult.ok(productPackageBusiness.getAllConfig());
    }

    /**
     * 产品包Tmp详情
     * @return
     */
    @RequestMapping("/getTmpInfo")
    @ResponseBody
    public JsonResult<PackageTmpInfoVo> getTmpInfo(@RequestBody(required = false) @Valid PackageTmpGetInfoDTO dto) {
        return JsonResult.ok(productPackageBusiness.getTmpInfo(dto));
    }

    /**
     * 比较前端内容更新
     * @return
     */
    @RequestMapping("/getFrontedConfigDiff")
    @ResponseBody
    public JsonResult<JSONObject> getFrontedConfigDiff(@RequestBody(required = false) @Valid PackageTmpGetInfoDTO dto) {
        return JsonResult.ok(productPackageBusiness.getFrontedConfigDiff(dto));
    }

    /**
     * 库库加减
     * @return
     */
    @RequestMapping("/updateStock")
    @ResponseBody
    public JsonResult<Boolean> updateStock(@RequestBody(required = false) @Valid PackageUpdateStockDTO dto) {
        return JsonResult.ok(productPackageBusiness.updateStock(dto));
    }

    /**
     * 产品包修改排序
     * <a href="https://yapi.etczs.net/project/312/interface/api/28533">...</a>
     * @return
     */
    @RequestMapping("/updateSort")
    @ResponseBody
    public JsonResult<Boolean> updateSort(@RequestBody @Valid ProductPackageUpdateSortDTO dto) {
        // 获取操作用户
        String loginCode = RequestHelper.getHttpServletRequest().getHeader("loginCode");
        productPackageBusiness.updateSort(dto,loginCode);
        return JsonResult.ok();
    }

    /**
     * 商城产品包设置是否允许创建订单
     * <a href="https://yapi.etczs.net/project/312/interface/api/28542">...</a>
     * @return
     */
    @RequestMapping("/updateIsShowAdd")
    @ResponseBody
    public JsonResult<Boolean> updateIsShowAdd(@RequestBody @Valid ProductPackageSetIsShowAddDTO dto) {
        // 获取操作用户
        String loginCode = RequestHelper.getHttpServletRequest().getHeader("loginCode");
        productPackageBusiness.updateIsShowAdd(dto,loginCode);
        return JsonResult.ok();
    }

    /**
     * 产品包枚举选择接口
     * <a href="https://yapi.etczs.net/project/312/interface/api/28560">...</a>
     */
    @RequestMapping("/getPackageSelectOption")
    @ResponseBody
    public JsonResult<HashMap<String, Object>> getPackageSelectOption() {
        return JsonResult.ok(productPackageBusiness.getPackageSelectOptions());
    }


}

