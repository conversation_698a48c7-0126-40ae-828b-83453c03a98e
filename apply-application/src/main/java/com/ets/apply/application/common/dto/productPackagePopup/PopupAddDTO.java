package com.ets.apply.application.common.dto.productPackagePopup;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 */
@Data
public class PopupAddDTO {
    private Integer popupType;

    private String name;

    private Integer style;

    private Integer size;

    @Length(max = 200, min = 1, message = "备注长度不能超过200")
    private String remark;

    private Integer firstChoose = 0;
    @Length(max = 200,  message = "url长度不能超过200")
    private String url;
}
