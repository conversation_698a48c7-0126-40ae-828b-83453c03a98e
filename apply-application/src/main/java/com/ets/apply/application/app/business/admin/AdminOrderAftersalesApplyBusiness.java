package com.ets.apply.application.app.business.admin;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.apply.application.app.business.BaseBusiness;
import com.ets.apply.application.app.business.ProductOrderBusiness;
import com.ets.apply.application.common.consts.order.AftersaleStatus;
import com.ets.apply.application.common.consts.orderAftersales.OrderAftersalesStatusEnum;
import com.ets.apply.application.common.consts.orderAftersales.OrderBusinessStatusEnum;
import com.ets.apply.application.common.dto.request.admin.orderAftersalesApply.AdminOrderAftersalesApplyGetListDTO;
import com.ets.apply.application.common.vo.admin.orderAftersalesApply.AdminOrderAftersalesApplyVO;
import com.ets.apply.application.infra.entity.*;
import com.ets.apply.application.infra.service.*;
import com.ets.common.ToolsHelper;
import com.ets.common.util.NumberUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

@Slf4j
@Component
public class AdminOrderAftersalesApplyBusiness extends BaseBusiness {
    @Autowired
    private OrderAftersalesApplyService orderAftersalesApplyService;
    @Autowired
    private ProductOrderBusiness productOrderBusiness;
    @Autowired
    private OrderOrderService orderOrderService;
    /*
     * 获取全部产品包列表
     */
    public IPage<AdminOrderAftersalesApplyVO> getList(AdminOrderAftersalesApplyGetListDTO dto) {
        // 查询条件设置
        LambdaQueryWrapper<OrderAftersalesApplyEntity> wrapper = new LambdaQueryWrapper<>();
        LocalDateTime beginTime = null;
        LocalDateTime endTime = null;
        if(StringUtils.isNotEmpty(dto.getCreateTimeBegin())){
            beginTime = ToolsHelper.getLocalDateTime(dto.getCreateTimeBegin() + " 00:00:00");
        }
        if(StringUtils.isNotEmpty(dto.getCreateTimeEnd())){
            endTime = ToolsHelper.getLocalDateTime(dto.getCreateTimeEnd() + " 23:59:59");
        }
        //如果有taskSn或者referSn，则忽略其他查询参数
        if(
             StringUtils.isEmpty(dto.getOrderSn()) &&
             StringUtils.isEmpty(dto.getApplySn()) &&
             StringUtils.isEmpty(dto.getPlateNo()) &&
             !NumberUtil.isPositive(dto.getUid())
        ){
            // 时间范围限制在一个月内
            if (StringUtils.isEmpty(dto.getCreateTimeBegin()) || StringUtils.isEmpty(dto.getCreateTimeEnd())) {
                ToolsHelper.throwException("请选择开始和结束时间");
            }
            if(ChronoUnit.MONTHS.between(beginTime, endTime) > 1){
                ToolsHelper.throwException("开始和结束时间不可超过一个月");
            }
        }

        wrapper.eq(StringUtils.isNotEmpty(dto.getApplySn()), OrderAftersalesApplyEntity::getApplySn, dto.getApplySn())
                .eq(StringUtils.isNotEmpty(dto.getOrderSn()), OrderAftersalesApplyEntity::getOrderSn, dto.getOrderSn())
                .eq(StringUtils.isNotEmpty(dto.getPlateNo()), OrderAftersalesApplyEntity::getPlateNo, dto.getPlateNo())
                .eq(NumberUtil.isPositive(dto.getStatus()), OrderAftersalesApplyEntity::getStatus, dto.getStatus())
                .eq(NumberUtil.isPositive(dto.getUid()), OrderAftersalesApplyEntity::getUid, dto.getUid())
                .eq(NumberUtil.isPositive(dto.getType()), OrderAftersalesApplyEntity::getType, dto.getType())
                .ge(StringUtils.isNotEmpty(dto.getCreateTimeBegin()), OrderAftersalesApplyEntity::getCreatedAt, beginTime)
                .le(StringUtils.isNotEmpty(dto.getCreateTimeEnd()), OrderAftersalesApplyEntity::getCreatedAt, endTime)
                .orderByDesc(OrderAftersalesApplyEntity::getCreatedAt);
        // 分页设置
        IPage<OrderAftersalesApplyEntity> oPage = new Page<>(dto.getPageNum(), dto.getPageSize(), true);
        IPage<OrderAftersalesApplyEntity> pageList = orderAftersalesApplyService.getPageListByWrapper(oPage, wrapper);
        if(pageList.getRecords().isEmpty()){
            return null;
        }

        return pageList.convert(record -> {
            AdminOrderAftersalesApplyVO vo = new AdminOrderAftersalesApplyVO();
            BeanUtils.copyProperties(record, vo);
            return vo;
        });
    }
    /*
     * 取消申请，更新订单状态
     */
    public boolean cancel(String applySn, String error) {
        OrderAftersalesApplyEntity orderAftersalesApply = orderAftersalesApplyService.getByApplySn(applySn);
        if(orderAftersalesApply == null){
            ToolsHelper.throwException("applySn不存在："+applySn);
        }
        if(orderAftersalesApply.getStatus().equals(OrderAftersalesStatusEnum.STATUS_FAIL.getStatus())){
            ToolsHelper.throwException("applySn已取消："+applySn);
        }
        OrderOrderEntity orderOrder = orderOrderService.getByOrderSn(orderAftersalesApply.getOrderSn());
        if(orderOrder == null){
            ToolsHelper.throwException("订单号"+orderAftersalesApply.getOrderSn()+"不存在");
        }
        //取消售后单申请，返回msg
        orderAftersalesApplyService.modify(
                applySn,
                OrderAftersalesStatusEnum.STATUS_FAIL.getStatus(),
                OrderBusinessStatusEnum.STATUS_PROCESSING.getStatus(),
                error
        );
        //更新申办订单为“售后中”
        orderOrderService.aftersaleStatusUpdate(orderOrder, AftersaleStatus.STATUS_NORMAL.getValue(),error);
        //更新电商订单状态
        productOrderBusiness.aftersalesMarkByOrderSn(orderOrder,2,applySn);

        return true;
    }
}
