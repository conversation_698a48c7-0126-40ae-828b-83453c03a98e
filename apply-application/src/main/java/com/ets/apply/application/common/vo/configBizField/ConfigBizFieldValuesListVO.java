package com.ets.apply.application.common.vo.configBizField;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ConfigBizFieldValuesListVO {

    private Integer id;
    private String bizField;
    private String name;
    private String key;
    private Integer sort;
    private String operator;
    private Integer parentId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
}
