package com.ets.apply.application.common.dto.external;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class ExternalOrderAfterSaleCreateDTO {


    @NotBlank(message = "订单号不能为空")
    private String thirdOrderSn;

    /**
     * 售后类型 1退货退款
     */
    @NotNull(message = "售后类型不能为空")
    private Integer serviceType = 1;

    /**
     * 问题描述
     */
    @NotBlank(message = "售后原因不能为空")
    private String problemDescription;

    @NotBlank(message = "用户寄回快的单号不能为空")
    private String sendbackExpressNumber;

    /**
     * 寄回的快递公司
     */
    private String sendbackExpressCompany = "默认";

}
