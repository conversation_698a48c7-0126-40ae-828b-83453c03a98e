package com.ets.apply.application.app.thirdservice.fallback;


import com.ets.apply.application.app.thirdservice.feign.CallCenterMarketingFeign;
import com.ets.apply.application.app.thirdservice.request.SendTermCouponDTO;
import com.ets.apply.application.common.dto.adminSalesCoupon.SalesCouponGetListDTO;
import com.ets.apply.application.common.dto.request.creditCard.CreditCardCoopDTO;
import com.ets.apply.application.common.dto.request.creditCard.CreditCardStreamDTO;
import com.ets.apply.application.common.dto.request.machine.MachineCommandDTO;
import com.ets.apply.application.common.dto.request.notice.SelectNoticeInfoDTO;
import com.ets.common.JsonResult;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Component
public class CallCenterMarketingFallBackFactory implements FallbackFactory<CallCenterMarketingFeign> {

    @Override
    public CallCenterMarketingFeign create(Throwable cause) {
        return new CallCenterMarketingFeign() {

            @Override
            public String receive(@RequestBody CreditCardStreamDTO param) {

                return JsonResult.error("请求etc-center-marketing 服务失败: " + cause.getMessage()).toString();
            }

            @Override
            public String selectNoticeInfo(SelectNoticeInfoDTO infoDTO) {
                return JsonResult.error("请求etc-center-marketing 服务失败: " + cause.getMessage()).toString();
            }

            @Override
            public String command(@RequestBody MachineCommandDTO commandDTO) {
                return JsonResult.error("请求etc-center-marketing 服务失败: " + cause.getMessage()).toString();
            }

            @Override
            public String coopReceive(@RequestBody CreditCardCoopDTO param){
                return JsonResult.error("请求etc-center-marketing 服务 coopReceive 失败: " + cause.getMessage()).toString();
            }

            @Override
            public String etcActCheckJoin(@RequestBody List<String> param){
                return JsonResult.error("请求etc-center-marketing 服务 etcActCheckJoin 失败: " + cause.getMessage()).toString();
            }

            @Override
            public String sendTermCoupon(SendTermCouponDTO couponDTO) {
                return JsonResult.error("请求etc-center-marketing 服务 sendTermCoupon 失败: " + cause.getMessage()).toString();
            }

            @Override
            public String getBusinessList(@RequestBody SalesCouponGetListDTO dto) {
                return JsonResult.error("请求etc-center-marketing 服务 getBusinessList 失败: " + cause.getMessage()).toString();
            }
        };
    }
}