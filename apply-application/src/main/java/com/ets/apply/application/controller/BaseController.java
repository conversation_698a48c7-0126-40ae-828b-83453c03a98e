package com.ets.apply.application.controller;

import com.ets.apply.application.common.config.AppParamsConfig;
import com.ets.common.RateLimiterHelper;
import com.ets.starter.alarm.EmailAlarm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;

public class BaseController {

    @Autowired
    protected AppParamsConfig appParamsConfig;

    @Resource(name = "commonMailSender")
    protected JavaMailSenderImpl mailSender;

    protected RateLimiterHelper rateLimiterHelper;


    @Autowired
    protected EmailAlarm emailAlarm;

    @Resource(name = "defaultRedisTemplate")
    protected StringRedisTemplate defaultRedisTemplate;

    @PostConstruct
    public void init() {
        System.out.println("controller init");
        rateLimiterHelper = new RateLimiterHelper(defaultRedisTemplate, emailAlarm);
    }

}
