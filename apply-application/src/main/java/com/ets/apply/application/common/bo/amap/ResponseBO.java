package com.ets.apply.application.common.bo.amap;

import lombok.Data;

@Data
public class ResponseBO {
    //{"response":{"msg":"接口调用成功","code":"10000","data":{"subCode":"200","amapOrderId":"988690251000001662378389","subMsg":"success","cpOrderId":"2507141552000063192"}}}
    private Response response;
    @Data
    public static class Response {
        private String msg;
        private String code;
        private ObjectData data;

    }

    @Data
    public static class ObjectData {
        private String subCode;
        private String amapOrderId;
        private String subMsg;
        private String cpOrderId;
    }

}
