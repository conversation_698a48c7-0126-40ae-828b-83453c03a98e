package com.ets.apply.application.common.bo.productOrder;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ProductOrderRefundBO {
    private String productOrderSn;
    private String thirdOrderSn;
    private String paymentSn;
    /**
     * 实际支付金额
     */
    private BigDecimal paidAmount;
    /**
     * 订单总金额
     */
    private BigDecimal totalAmount;
    /**
     * 退款时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String refundAt;

    /**
     * 退款原因
     */
    private String refundReason;

    /**
     * 是否已发货
     */
    private Boolean hasShip;

    private Integer referValue;

    private String operator;//操作人
}
