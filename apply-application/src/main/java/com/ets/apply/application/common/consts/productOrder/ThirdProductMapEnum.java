package com.ets.apply.application.common.consts.productOrder;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
@AllArgsConstructor
public enum ThirdProductMapEnum {

    GEELY("CarPackage00047", "京东旗舰99元基础", BigDecimal.valueOf(99));


    private final String code;

    private final String description;

    private final BigDecimal price;
    public static final Map<String, String> map;

    static {
        ThirdProductMapEnum[] enums = ThirdProductMapEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(
                LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getCode(), enums[index].getDescription()),
                Map::putAll
        );
    }

    public static String getDescByCode(String code) {
        for (ThirdProductMapEnum node : ThirdProductMapEnum.values()) {
            if (node.getCode().equals(code)) {
                return node.getDescription();
            }
        }

        return "";
    }

    public static BigDecimal getPriceByCode(String code) {
        for (ThirdProductMapEnum node : ThirdProductMapEnum.values()) {
            if (node.getCode().equals(code)) {
                return node.getPrice();
            }
        }

        return null;
    }

    public static List<Map<String, String>> getLabelList() {
        List<Map<String, String>> list = new ArrayList<>();

        for (ThirdProductMapEnum node : ThirdProductMapEnum.values()) {
            Map<String, String> row = new LinkedHashMap<>();
            row.put("label", node.getDescription());
            row.put("value", node.getCode());

            list.add(row);
        }

        return list;
    }


}
