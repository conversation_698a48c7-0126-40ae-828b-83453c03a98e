package com.ets.apply.application.app.job;

import com.alibaba.fastjson.JSONObject;
import com.ets.apply.application.app.business.TruckBusiness;
import com.ets.apply.application.app.factory.benefit.BenefitFactory;
import com.ets.apply.application.app.thirdservice.feign.CallPhpPayFeign;
import com.ets.apply.application.app.thirdservice.feign.UserApplicationFeign;
import com.ets.apply.application.app.thirdservice.request.pay.QueryRefundResultDTO;
import com.ets.apply.application.app.thirdservice.request.user.GetDepositRecordListDTO;
import com.ets.apply.application.app.thirdservice.request.user.UpdateDepositRecordStatusDTO;
import com.ets.apply.application.app.thirdservice.request.user.UpdateUserDepositDTO;
import com.ets.apply.application.app.thirdservice.response.user.UserDepositVO;
import com.ets.apply.application.app.thirdservice.response.user.UsersDepositRecordVO;
import com.ets.apply.application.common.consts.orderBenefitRecord.OrderBenefitRecordStatusEnum;
import com.ets.apply.application.common.consts.segmentBenefit.BenefitTypeEnum;
import com.ets.apply.application.infra.entity.OrderBenefitRecord;
import com.ets.apply.application.infra.entity.OrderOrderEntity;
import com.ets.apply.application.infra.service.OrderBenefitRecordService;
import com.ets.apply.application.infra.service.OrderLogService;
import com.ets.apply.application.infra.service.OrderOrderService;
import com.ets.common.JsonResult;
import com.ets.common.ToolsHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class TruckJob {

    @Autowired
    private OrderBenefitRecordService orderBenefitRecordService;

    @Autowired
    private OrderOrderService orderOrderService;

    @Autowired
    private OrderLogService orderLogService;

    @Autowired
    private TruckBusiness truckBusiness;

    @Autowired
    private UserApplicationFeign userApplicationFeign;

    @Autowired
    private CallPhpPayFeign callPhpPayFeign;

    private void processOrderBenefitRecord(OrderBenefitRecord record, String successLogMsg) {
        try {
            // 查询订单
            OrderOrderEntity order = orderOrderService.getByOrderSn(record.getOrderSn());
            if (ObjectUtils.isEmpty(order)) {
                ToolsHelper.throwException("订单不存在");
            }

            if (truckBusiness.checkTruckOrderMatch(record.getOrderSn())) {
                // 发放权益
                BenefitFactory.create(record.getBenefitType()).send(record.getId());

                // 更新状态为发放成功
                record.setStatus(OrderBenefitRecordStatusEnum.SUCCESS.getValue());
                record.setErrorMsg("");
                record.setUpdatedAt(LocalDateTime.now());
                orderBenefitRecordService.updateById(record);

                // 订单记录日志
                orderLogService.addLog(order, successLogMsg);
            }
        } catch (Exception e) {
            // 更新状态为发放失败
            record.setStatus(OrderBenefitRecordStatusEnum.FAIL.getValue());
            record.setErrorMsg(e.getMessage());
            record.setUpdatedAt(LocalDateTime.now());
            orderBenefitRecordService.updateById(record);

            log.error("货车权益处理失败，记录ID：{}，错误信息：{}", record.getId(), e.getMessage(), e);
        }
    }

    @XxlJob("truckPayOrderRefundHandler")
    public ReturnT<String> truckPayOrderRefundHandler(String params) {
        log.info("开始执行货车付费订单退款任务");
        int limit = Integer.parseInt(params);

        try {
            // 查询待处理的记录
            List<OrderBenefitRecord> records = orderBenefitRecordService.getNeedSendRecordList(
                    BenefitTypeEnum.TRUCK_REFUND.getType(),
                    limit
            );

            if (ObjectUtils.isEmpty(records)) {
                log.info("没有待处理的货车付费订单退款任务记录");
                return ReturnT.SUCCESS;
            }

            for (OrderBenefitRecord record : records) {
                processOrderBenefitRecord(record, "货车用户满足活动条件退款");
            }
        } catch (Exception e) {
            log.error("货车付费订单任务执行异常", e);
            throw e;
        }

        return ReturnT.SUCCESS;
    }


    @XxlJob("truckTermCouponSendHandler")
    public ReturnT<String> truckTermCouponSend(String params) {
        log.info("开始执行货车券包发放任务");
        int limit = Integer.parseInt(params);

        try {
            // 查询待处理的记录
            List<OrderBenefitRecord> records = orderBenefitRecordService.getNeedSendRecordList(
                    BenefitTypeEnum.TRUCK_TERM_COUPON.getType(),
                    limit
            );

            if (ObjectUtils.isEmpty(records)) {
                log.info("没有待处理的货车券包记录");
                return ReturnT.SUCCESS;
            }

            for (OrderBenefitRecord record : records) {
                processOrderBenefitRecord(record, "货车用户账期达标，发放权益券包至车牌");
            }
        } catch (Exception e) {
            log.error("货车券包发放任务执行异常", e);
            throw e;
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 定时查询货车保证金记录，检查退款状态并更新
     *
     * @param params 参数，格式：{"pageSize":10}
     * @return 执行结果
     */
    @XxlJob("checkDepositRefundStatusHandler")
    public ReturnT<String> checkDepositRefundStatusHandler(String params) {
        XxlJobLogger.log("开始执行货车保证金退款状态检查任务，参数：{}", params);

        // 解析参数
        int pageSize = 200; // 默认每页200条

        try {
            if (StringUtils.isNotEmpty(params)) {
                JSONObject paramsObj = JSONObject.parseObject(params);
                if (paramsObj.containsKey("pageSize")) {
                    pageSize = paramsObj.getInteger("pageSize");
                }
            }

            int pageNum = 1;
            int successCount = 0;
            List<UsersDepositRecordVO> allRecords = new ArrayList<>();
            boolean hasMoreRecords = true;

            // 循环分页查询直到没有更多记录
            while (hasMoreRecords) {
                // 分页查询货车保证金记录，指定type=1和status=0
                GetDepositRecordListDTO dto = new GetDepositRecordListDTO();
                dto.setPageNum(pageNum);
                dto.setPageSize(pageSize);
                dto.setStatus(0); // 指定状态为0
                dto.setType(1);  // 指定类型为1

                JsonResult<List<UsersDepositRecordVO>> result = userApplicationFeign.getDepositRecordList(dto);
                if (!result.isSuccess() || ObjectUtils.isEmpty(result.getData())) {
                    // 没有查询到符合条件的记录
                    hasMoreRecords = false;
                    continue;
                }

                List<UsersDepositRecordVO> recordList = result.getData();
                allRecords.addAll(recordList);

                // 查询到记录

                // 如果返回的记录数小于请求的数量，说明没有更多记录了
                if (recordList.size() < pageSize) {
                    hasMoreRecords = false;
                }

                // 页码递增
                pageNum++;
            }

            // 开始处理记录

            // 遍历货车保证金记录，检查退款状态
            for (UsersDepositRecordVO record : allRecords) {
                try {
                    // 只处理有关联单号的记录
                    if (StringUtils.isNotEmpty(record.getReferSn())) {
                        // 查询退款状态
                        QueryRefundResultDTO queryDto = new QueryRefundResultDTO();
                        queryDto.setRefundSn(record.getReferSn());
                        JsonResult<JSONObject> refundResult = callPhpPayFeign.queryRefundResult(queryDto);

                        if (refundResult.isSuccess() && refundResult.getData() != null) {
                            JSONObject refundData = refundResult.getData();
                            boolean isSuccess = refundData.getBooleanValue("isSuccess");

                            if (isSuccess) {
                                // 退款成功，更新货车保证金记录状态
                                // 退款成功，开始处理

                                // 更新货车保证金记录状态
                                UpdateDepositRecordStatusDTO updateDto = new UpdateDepositRecordStatusDTO();
                                updateDto.setId(record.getId());
                                updateDto.setStatus(1);

                                try {
                                    JsonResult<Void> updateResult = userApplicationFeign.updateDepositRecordStatus(updateDto);
                                    if (!updateResult.isSuccess()) {
                                        log.error("更新货车保证金记录状态失败，ID：{}", record.getId());
                                        continue;
                                    }
                                    // 查询货车保证金账户
                                    if (StringUtils.isNotEmpty(record.getUserDepositSn())) {
                                        JsonResult<UserDepositVO> depositResult = userApplicationFeign.getUserDepositByUserDepositSn(record.getUserDepositSn());
                                        if (depositResult.isSuccess() && depositResult.getData() != null) {
                                            UserDepositVO userDeposit = depositResult.getData();

                                            // 更新货车保证金账户状态为退款状态
                                            try {
                                                // 使用DTO对象传递参数
                                                UpdateUserDepositDTO updateUserDepositDTO = new UpdateUserDepositDTO();
                                                updateUserDepositDTO.setUserDepositSn(userDeposit.getUserDepositSn());
                                                updateUserDepositDTO.setStatus(4); // 4表示退款状态
                                                updateUserDepositDTO.setExpendAmount(userDeposit.getDepositAmount());
                                                JsonResult<Void> updateDepositResult = userApplicationFeign.updateUserDeposit(updateUserDepositDTO);
                                                if (!updateDepositResult.isSuccess()) {
                                                    log.error("更新货车保证金账户失败，账户ID：{}", userDeposit.getId());
                                                    continue;
                                                }
                                            } catch (Exception e) {
                                                log.error("更新货车保证金账户异常，账户ID：{}, 异常：{}", userDeposit.getId(), e.getMessage());
                                            }
                                        }
                                    }
                                } catch (Exception e) {
                                    log.error("更新货车保证金记录状态异常，ID：{}, 异常：{}", record.getId(), e.getMessage());
                                }

                                successCount++;
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("处理货车保证金记录异常，ID：{}，异常：{}", record.getId(), e.getMessage(), e);
                }
            }

            XxlJobLogger.log("货车保证金退款状态检查任务完成，总处理{}条记录，成功处理{}条记录", allRecords.size(), successCount);

        } catch (Exception e) {
            log.error("货车保证金退款状态检查任务异常：{}", e.getMessage(), e);
            return ReturnT.FAIL;
        }

        return ReturnT.SUCCESS;
    }
}
