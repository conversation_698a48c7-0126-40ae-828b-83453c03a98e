package com.ets.apply.application.app.business.creditCard;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.apply.application.app.factory.creditCard.CreditCardFactory;
import com.ets.apply.application.app.factory.creditCard.impl.CreditCardBase;
import com.ets.apply.application.app.service.bigdata.BigDataService;
import com.ets.apply.application.app.service.common.notice.TemplateMsgService;
import com.ets.apply.application.common.config.creditBank.CiticCreditBankConfig;
import com.ets.apply.application.common.config.creditBank.CommonCreditBankConfig;
import com.ets.apply.application.common.consts.activityCreditCardBankUser.*;
import com.ets.apply.application.common.bo.creditCard.CreditCardLogBO;
import com.ets.apply.application.common.config.CreditCardConfig;
import com.ets.apply.application.common.consts.activityCreditCardOperateLog.CreditCardOperateLogTypeEnum;
import com.ets.apply.application.common.consts.activityCreditCardUsersInfo.*;
import com.ets.apply.application.common.consts.common.YesOrNoEnum;
import com.ets.apply.application.common.consts.creditCard.*;
import com.ets.apply.application.common.consts.creditCardBankConfig.CreditCardBankConfigBankStatusEnum;
import com.ets.apply.application.common.consts.notice.MessageTypeEnum;
import com.ets.apply.application.common.consts.notice.MinaAppCodeEnum;
import com.ets.apply.application.common.consts.notice.MinaTemplateEnum;
import com.ets.apply.application.common.consts.redisCache.OrderNumCacheKeyConstants;
import com.ets.apply.application.common.consts.service.ServerErrorCodeEnum;
import com.ets.apply.application.common.dto.request.bank.common.CheckCanApply;
import com.ets.apply.application.common.dto.request.creditCard.*;
import com.ets.apply.application.common.vo.creditCard.*;
import com.ets.apply.application.common.vo.creditCard.pab.CheckNewUserByPhoneVO;
import com.ets.apply.application.infra.entity.ActivityCreditCardBankUsersEntity;
import com.ets.apply.application.infra.entity.ActivityCreditCardUsersInfoEntity;
import com.ets.apply.application.infra.mapper.ActivityCreditCardUsersInfoMapper;
import com.ets.apply.application.infra.service.*;
import com.ets.common.BizException;
import com.ets.common.RequestHelper;
import com.ets.common.ToolsHelper;
import com.ets.common.util.NumberUtil;
import com.ets.starter.config.AppConfig;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Component
@Slf4j
public class CreditCardBusiness extends CreditCardBase {
    @Autowired
    private ActivityCreditCardUsersInfoService activityCreditCardUsersInfoService;
    @Autowired
    private ActivityCreditCardBankUsersService activityCreditCardBankUsersService;

    @Autowired
    private ActivityCreditCardLogService activityCreditCardLogService;

    @Autowired
    private TemplateMsgService templateMsgService;


    @Autowired
    private CreditCardBaseBusiness creditCardBaseBusiness;
    @Autowired
    private AppConfig appConfig;

    @Autowired
    private CreditCardConfig creditCardConfig;

    @Autowired
    private CommonCreditBankConfig commonCreditBankConfig;
    @Autowired
    private ActivityCreditCardUsersInfoMapper usersInfoMapper;

    @Qualifier("redisPermanentTemplate")
    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private ActivityCreditCardLogService creditCardLogService;

    @Autowired
    private CiticCreditBankConfig citicCreditBankConfig;

    @Autowired
    private BigDataService bigDataService;

    @Autowired
    private ConfigBizFieldValuesService configBizFieldValuesService;

    @Autowired
    private CreditCardBankConfigService creditCardBankConfigService;


    public IPage<CreditCardListVO> getList(CreditCardListDTO dto) {

        // 分页设置
        IPage<ActivityCreditCardUsersInfoEntity> oPage = new Page<>(dto.getPageNum(), dto.getPageSize(), true);

        // 查询条件设置
        LambdaQueryWrapper<ActivityCreditCardUsersInfoEntity> wrapper = new LambdaQueryWrapper<>();

        wrapper.eq(StringUtils.isNotEmpty(dto.getPlateNo()), ActivityCreditCardUsersInfoEntity::getPlateNo, dto.getPlateNo())
                .eq(dto.getUid() != null, ActivityCreditCardUsersInfoEntity::getUid, dto.getUid())
                .eq(NumberUtil.isPositive(dto.getStatus()), ActivityCreditCardUsersInfoEntity::getStatus, dto.getStatus())
                .eq(NumberUtil.isPositive(dto.getWhichBank()), ActivityCreditCardUsersInfoEntity::getWhichBank, dto.getWhichBank())
                .eq(StringUtils.isNotEmpty(dto.getClassify()), ActivityCreditCardUsersInfoEntity::getClassify, dto.getClassify())
                .eq(StringUtils.isNotEmpty(dto.getOrderSn()), ActivityCreditCardUsersInfoEntity::getOrderSn, dto.getOrderSn())
                // 大于等于
                .ge(StringUtils.isNotEmpty(dto.getCreateTimeStart()), ActivityCreditCardUsersInfoEntity::getCreatedAt, dto.getCreateTimeStart())
                // 小于
                .le(StringUtils.isNotEmpty(dto.getCreateTimeEnd()), ActivityCreditCardUsersInfoEntity::getCreatedAt, dto.getCreateTimeEnd() + " 23:59:59")
                .orderByDesc(ActivityCreditCardUsersInfoEntity::getId);
        IPage<ActivityCreditCardUsersInfoEntity> pageList = activityCreditCardUsersInfoService.getPageListByWrapper(oPage, wrapper);
        return pageList.convert(record -> {
            CreditCardListVO vo = new CreditCardListVO();
            ActivityCreditCardBankUsersEntity activityCreditCardBankUsersEntity = activityCreditCardBankUsersService.getOneByColumn(record.getOrderSn(), ActivityCreditCardBankUsersEntity::getApplyNumber);
            if (activityCreditCardBankUsersEntity != null) {
                vo.setActiveTime(activityCreditCardBankUsersEntity.getActivateTime());
                vo.setAuditTime(activityCreditCardBankUsersEntity.getAuditTime());
                vo.setIsNewUser(activityCreditCardBankUsersEntity.getIsNewUser());
                vo.setFirstUseDate(activityCreditCardBankUsersEntity.getFirstUseDate());
            }
            // 审核通过 未激活 配置银行 显示置为激活按钮
            if (creditCardConfig.getManualActivateBankId().contains(record.getWhichBank())
                    && record.getStatus().equals(ActivityCreditCardUserInfoStatusEnum.STATUS_PASS_AUDIT.getCode())
                    && ObjectUtil.isEmpty(vo.getActiveTime())
            ) {
                vo.setShowManualActivateBtn(true);
            }
            BeanUtils.copyProperties(record, vo);
            return vo;
        });
    }

    public IPage<CreditCardListVO> getListV2(CreditCardListDTO dto) {
        // 分页设置
        QueryWrapper<CreditCardListVO> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotEmpty(dto.getOrderSn()), "ui.order_sn", dto.getOrderSn())
                .eq(StringUtils.isNotEmpty(dto.getPlateNo()), "ui.plate_no", dto.getPlateNo())
                .eq(dto.getUid() != null, "ui.uid", dto.getUid())
                .eq(NumberUtil.isPositive(dto.getStatus()), "ui.status", dto.getStatus())
                .eq(NumberUtil.isPositive(dto.getWhichBank()), "ui.which_bank", dto.getWhichBank())
                .eq(StringUtils.isNotEmpty(dto.getClassify()), "ui.classify", dto.getClassify())
                .eq(NumberUtil.isPositive(dto.getReferType()), "ui.refer_type", dto.getReferType())
                .eq(NumberUtil.isPositive(dto.getSubReferType()), "ui.sub_refer_type", dto.getSubReferType())
                .ge(StringUtils.isNotEmpty(dto.getCreateTimeStart()), "ui.created_at", dto.getCreateTimeStart())
                .eq(dto.getAuditStatus() != null, "bu.status", dto.getAuditStatus())
                .eq(dto.getActivatedStatus() != null, "bu.activated", dto.getActivatedStatus())
                .eq(dto.getFirstUseStatus() != null, "bu.is_first_use", dto.getFirstUseStatus())

                // 小于
                .le(StringUtils.isNotEmpty(dto.getCreateTimeEnd()), "ui.created_at", dto.getCreateTimeEnd() + " 23:59:59")
                // 审核时间
                .ge(StringUtils.isNotEmpty(dto.getAuditTimeStart()), "bu.audit_time", dto.getAuditTimeStart())
                // 小于
                .le(StringUtils.isNotEmpty(dto.getAuditTimeEnd()), "bu.audit_time", dto.getAuditTimeEnd() + " 23:59:59")

                // 激活时间
                .ge(StringUtils.isNotEmpty(dto.getActivateTimeStart()), "bu.activate_time", dto.getActivateTimeStart())
                // 小于
                .le(StringUtils.isNotEmpty(dto.getActivateTimeEnd()), "bu.activate_time", dto.getActivateTimeEnd() + " 23:59:59")

                // 首刷时间
                .ge(StringUtils.isNotEmpty(dto.getFirstUseDateStart()), "bu.first_use_date", dto.getFirstUseDateStart())
                // 小于
                .le(StringUtils.isNotEmpty(dto.getFirstUseDateEnd()), "bu.first_use_date", dto.getFirstUseDateEnd())

                .orderByDesc("ui.id");
        IPage<CreditCardListVO> userInfoIPage = usersInfoMapper.getCreditCardList(new Page(dto.getPageNum(), dto.getPageSize()), wrapper);

        return userInfoIPage.convert(record -> {
            // 非审核不通过 未激活 配置银行 有关联订单 显示置为激活按钮
            if (creditCardConfig.getManualActivateBankId().contains(record.getWhichBank()) && ObjectUtil.isEmpty(record.getActiveTime())
                    && ObjectUtil.isNotEmpty(record.getReferSn())
                    && !ActivityCreditCardUserInfoStatusEnum.STATUS_FAIL_AUDIT.getCode().equals(record.getStatus())
            ) {
                record.setShowManualActivateBtn(true);
            }
            return record;
        });
    }

    public HashMap<String, Object> getSelectOptions() {
        HashMap<String, Object> options = new HashMap<>();
        options.put("whichBank", ActivityCreditCardUserInfoWhichBankEnum.getLabelList());
        options.put("status", ActivityCreditCardUserInfoStatusBriefEnum.getLabelList());
        options.put("classify", ActivityCreditCardUserInfoClassifyEnum.getLabelList());
        options.put("referType", configBizFieldValuesService.getListByBizFieldKey("credit_refer_type",true));
        options.put("auditStatus", ActivityCreditCardBankUserStatusEnum.getLabelList());
        options.put("firstUseStatus", ActivityCreditCardBankUserIsFirstUseEnum.getLabelList());
        options.put("activatedStatus", ActivityCreditCardBankUserActivatedEnum.getLabelList());
        options.put("subReferType", configBizFieldValuesService.getListByBizFieldKey("credit_sub_refer_type",true));
        options.put("applyType", ActivityCreditCardUserInfoApplyTypeVOEnum.getLabelList());
        options.put("thirdType", ActivityCreditCardUserInfoThirdTypeVOEnum.getLabelList());

        return options;
    }

    /**
     * 校验是否已有领券资格
     *
     * @param dto
     * @param uid
     * @return
     */
    public Boolean checkQualification(CreditCardCheckQualificationDTO dto, Long uid) {
        return activityCreditCardUsersInfoService.hasSendQualification(uid);
    }

    /**
     * 信用卡申请
     *
     * @param applyOrderDTO
     * @return
     */
    public CreditCardApplyOrderVO applyOrder(ApplyOrderDTO applyOrderDTO){
        CreditCardApplyOrderVO creditCardApplyOrderVO = null;
        try {
            if (StringUtils.isEmpty(applyOrderDTO.getVersion())) {
                //获取银行版本号
                String version = creditCardConfig.getBankVersionMap().get(applyOrderDTO.getWhichBank());
                if (StringUtils.isNotEmpty(version)) {
                    applyOrderDTO.setVersion(version);
                }
            }
            // 新数据补齐classify兼容数据:如果这个值没有传，这个值以referType 的值补充，该值后续应该废弃，以referType 为准
            if (ObjectUtil.isNull(applyOrderDTO.getClassify())){
                applyOrderDTO.setClassify(applyOrderDTO.getReferType());
            }

            //查找是否存在正常申请当中的记录
            ActivityCreditCardUsersInfoEntity processingApplyEntity = activityCreditCardUsersInfoService.getProcessingApply(
                applyOrderDTO.getUid(),
                applyOrderDTO.getWhichBank(),
                applyOrderDTO.getClassify(),
                applyOrderDTO.getReferType(),
                applyOrderDTO.getReferSn(),
                applyOrderDTO.getVersion()
            );
            //存在进行中的申请
            if(processingApplyEntity != null){
                //非待定状态，报错提示
                if(!processingApplyEntity.getStatus().equals(ActivityCreditCardUserInfoStatusEnum.STATUS_WAIT.getCode())){
                    ToolsHelper.throwException("已申请过信用卡，请刷新页面查看办理进度", ServerErrorCodeEnum.SERVICE_ERROR_CODE_CREDIT_CARD_APPLIED.getCode());
                }
                //待定状态
                applyOrderDTO.setBankApplyNumber(processingApplyEntity.getOrderSn());
            }

            // 使用后台配置的是否可用银行配置
            if(!creditCardBankConfigService.checkBankCanApply(applyOrderDTO.getWhichBank())){
                ToolsHelper.throwException(commonCreditBankConfig.getLimitBankMsg(), ServerErrorCodeEnum.SERVICE_ERROR_CODE_CREDIT_CARD_NOT_ALLOW.getCode());
            }
            creditCardApplyOrderVO = CreditCardFactory.create(applyOrderDTO.getWhichBank()).applyOrder(applyOrderDTO);
        }catch (BizException e){
            ToolsHelper.throwException(e.getMessage(),e.getErrorCode());
        } catch (Exception ex){
            ToolsHelper.throwException("申请信用卡失败，稍后再试");
        }
        return creditCardApplyOrderVO;
    }

    /**
     * 信用卡申请页面回调
     */
    public CreditCardCallBackVO callback(CreditCardCallBackDTO dto) {
        CreditCardCallBackVO creditCardCallBackVO = null;
        try {
            creditCardCallBackVO = CreditCardFactory.create(dto.getWhichBank()).callback(dto);
        } catch (Exception e) {
            log.info("信用卡callback 异常：" + e.getMessage());
            ToolsHelper.throwException("回传数据失败，稍后再试");
        }
        return creditCardCallBackVO;
    }


    public void auditFailNotice(Long uid, String plateNo) {
        Map<String, Object> keywords = new HashMap<>();
        keywords.put("car_number1", plateNo);
        keywords.put("thing2", "信用卡状态更新");
        keywords.put("thing4", "点此查看最新进度");
        templateMsgService.sendMinaNotice(uid, "pages/newIndex/page/newGoodsDetail", MinaTemplateEnum.SERVICE_STATUS_NOTICE_LONG_TERM.getTemplate(),
                MinaAppCodeEnum.ETC_ASSISTANT, MessageTypeEnum.PERMANENT, keywords);
    }


    /**
     * 信用卡申办信息记录
     *  查找已前置处理，这里只创建新的
     * @param applyOrderDTO
     * @return
     */
    public String applyCreditCard(ApplyOrderDTO applyOrderDTO) {
        String orderSn;
//        List<Integer> whichBanks = new ArrayList<>();
//        whichBanks.add(applyOrderDTO.getWhichBank());
//        ActivityCreditCardUsersInfoEntity activityCreditCardUsersInfoEntity = null;
//        if (ActivityCreditCardUserInfoReferTypeEnum.TYPE_NEW_APPLY.getCode() == applyOrderDTO.getReferType()) {
//            // 新申请
//            activityCreditCardUsersInfoEntity =
//                    activityCreditCardUsersInfoService.findNewApplyBankUsersInfo(applyOrderDTO.getUid(),
//                            whichBanks,
//                            applyOrderDTO.getClassify(),
//                            applyOrderDTO.getReferSn(),
//                            applyOrderDTO.getVersion()
//                    );
//        } else {
//            activityCreditCardUsersInfoEntity = activityCreditCardUsersInfoService.findOneByUidAndReferSn(
//                    applyOrderDTO.getUid(),
//                    whichBanks,
//                    applyOrderDTO.getReferType(),
//                    applyOrderDTO.getReferSn()
//            );
//        }
//
//        if (ObjectUtil.isNull(activityCreditCardUsersInfoEntity)) {
//            orderSn = ToolsHelper.genNum(redisTemplate, OrderNumCacheKeyConstants.CREDIT_CARD_APPLY, appConfig.getEnv(), 8);
//            this.saveBankOrder(applyOrderDTO, orderSn);
//        } else {
//            orderSn = activityCreditCardUsersInfoEntity.getOrderSn();
//        }

        orderSn = ToolsHelper.genNum(redisTemplate, OrderNumCacheKeyConstants.CREDIT_CARD_APPLY, appConfig.getEnv(), 8);
        this.saveBankOrder(applyOrderDTO, orderSn);

        return orderSn;
    }


    /**
     * 获取申请状态接口
     *
     * @param uid
     * @param dto
     * @return
     */
    public CreditCardApplyStatusVO applyStatus(Long uid, CreditCardApplyStatusDTO dto) {
        // 初始化返回信息
        CreditCardApplyStatusVO statusVO = new CreditCardApplyStatusVO();
        statusVO.setReferSn(dto.getReferSn());
        statusVO.setActivated(ActivityCreditCardBankUserActivatedEnum.STATUS_ACTIVATED_UNKNOWN.getCode());
        statusVO.setIsFirstUse(ActivityCreditCardBankUserIsFirstUseEnum.FIRST_USER_DEFAULT.getCode());
        statusVO.setStatus(ApplyStatusVoEnum.NO_APPLY.getCode());
        statusVO.setMessage(ApplyStatusVoEnum.NO_APPLY.getMessage());
        statusVO.setReferSn(dto.getReferSn());
        statusVO.setOrderSn("");
        // 查找申请单
        ActivityCreditCardUsersInfoEntity usersInfoEntity = activityCreditCardUsersInfoService.getOneByReferSnAndUid(uid, dto.getReferSn(), this.bankMap(dto.getWhichBank()));
        //
        if (usersInfoEntity == null) {
            // 银行版本号处理
            String version = creditCardConfig.getBankVersionMap().get(this.bankMap(dto.getWhichBank()));
            if (StringUtils.isNotEmpty(version)) {
                statusVO.setVersion(version);
            }
            return statusVO;
        }

        // 查找银行单
        ActivityCreditCardBankUsersEntity bankUsersEntity = activityCreditCardBankUsersService.getOneByColumn(usersInfoEntity.getOrderSn(), ActivityCreditCardBankUsersEntity::getApplyNumber);
        if (bankUsersEntity == null) {
            // 银行版本号处理
            String version = creditCardConfig.getBankVersionMap().get(this.bankMap(dto.getWhichBank()));
            if (StringUtils.isNotEmpty(version)) {
                statusVO.setVersion(version);
            }
            return statusVO;
        }
        statusVO.setActivated(bankUsersEntity.getActivated());
        statusVO.setIsFirstUse(bankUsersEntity.getIsFirstUse());

        // 前端展示状态
        ApplyStatusVoEnum applyStatusVoEnum = ActivityCreditCardUserInfoStatusEnum.of(usersInfoEntity.getStatus()).toApplyStatusVoEnum();
        statusVO.setStatus(applyStatusVoEnum.getCode());
        statusVO.setMessage(StringUtils.isNotEmpty(usersInfoEntity.getFailVerifyReason()) ? usersInfoEntity.getFailVerifyReason() : applyStatusVoEnum.getMessage());
        statusVO.setOrderSn(bankUsersEntity.getApplyNumber());
        statusVO.setVersion(bankUsersEntity.getVersion());
        statusVO.setBankStatus(bankUsersEntity.getStatus());
        statusVO.setApplyCompleted(bankUsersEntity.getApplyCompleted());
        statusVO.setAuditNewUser(bankUsersEntity.getAuditNewUser());
        statusVO.setIsNewUser(bankUsersEntity.getIsNewUser());
        return statusVO;

    }


    /**
     * 信用卡合作-申请状态
     *
     * @param dto
     * @param uid
     * @return
     */
    public CreditCardCoopStatusVO coopStatus(CreditCardCoopStatusDTO dto, Long uid) {
        CreditCardCoopStatusVO coopStatusVO = new CreditCardCoopStatusVO();
        // 活动有一个完成资格即有权益
        Boolean hasRights = activityCreditCardUsersInfoService.hasSendQualification(uid, dto.getClassify());
        coopStatusVO.setRights(hasRights ? CoopStatusRightsEnum.HasRights.getCode() : CoopStatusRightsEnum.NoRights.getCode());

        // 活动对应银行有审核中、进件、激活 标记为进行中
        Boolean applyStatus = activityCreditCardUsersInfoService.hasProgressingApply(uid, dto.getClassify(), dto.getWhichBank());
        coopStatusVO.setApplyStatus(applyStatus ? CoopApplyStatusEnum.HasApply.getCode() : CoopApplyStatusEnum.NoProcessingApply.getCode());

        return coopStatusVO;
    }

    /**
     * 银行多对已一映射
     *
     * @param whichBank
     * @return
     */
    public Integer bankMap(Integer whichBank) {
        int bank;
        switch (whichBank) {
            // 平安银行
            case BankConstant.PING_AN_INTERFACE:
            case BankConstant.PING_AN_ID:
            case BankConstant.PING_AN_FREE:
            case BankConstant.PURCHASE_PARTY_PAB_CREDIT_CARD_GUANGXI:
            case BankConstant.PURCHASE_PARTY_PAB_CREDIT_CARD_JSUTONG:
            case BankConstant.PURCHASE_PARTY_PAB_CREDIT_CARD_NEIMENG:
            case BankConstant.PURCHASE_PARTY_PAB_CREDIT_BANK_WANG_LU_ZL:
                bank = BankConstant.PING_AN_INTERFACE;
                break;
            // 交通银行
            case BankConstant.JIAO_TONG:
            case BankConstant.JIAOTONG_FREE_ID:
            case BankConstant.PURCHASE_PARTY_COMM_CREDIT_CARD_GUANGXI:
            case BankConstant.PURCHASE_PARTY_COMM_CREDIT_CARD_JSUTONG:
            case BankConstant.PURCHASE_PARTY_COMM_CREDIT_CARD_NEIMENG:
            case BankConstant.PURCHASE_PARTY_COMM_CREDIT_BANK_WANG_LU_ZL:
                bank = BankConstant.JIAO_TONG;
                break;
            // 广发
            case BankConstant.GUANG_FA_INTERFACE:
            case BankConstant.GUANGFA_ID:
            case BankConstant.PURCHASE_PARTY_GDB_CREDIT_CARD_GUANGXI:
            case BankConstant.PURCHASE_PARTY_GDB_CREDIT_CARD_JSUTONG:
            case BankConstant.PURCHASE_PARTY_GDB_CREDIT_CARD_NEIMENG:
            case BankConstant.PURCHASE_PARTY_GDB_CREDIT_BANK_WANG_LU_ZL:
                bank = BankConstant.GUANG_FA_INTERFACE;
                break;
            // 中信银行

            // 中信银行
            case BankConstant.CITIC_CREDIT_BANK_ID_GUANGXI:
            case BankConstant.CITIC_CREDIT_BANK_ID_JSUTONG:
            case BankConstant.CITIC_CREDIT_BANK_ID_NEIMENG:
            case BankConstant.PURCHASE_PARTY_CITIC_CREDIT_BANK_WANG_LU_ZL:
                bank = BankConstant.CITIC_CREDIT_BANK_ID_GUANGXI;
                break;
            // 浦发
            case BankConstant.PURCHASE_PARTY_SPD_CREDIT_BANK_GUAGNXI:
            case BankConstant.PURCHASE_PARTY_SPD_CREDIT_BANK_JSUTONG:
            case BankConstant.PURCHASE_PARTY_SPD_CREDIT_BANK_NEIMENG:
            case BankConstant.PURCHASE_PARTY_SPD_CREDIT_BANK_WANG_LU_ZL:
                bank = BankConstant.PURCHASE_PARTY_SPD_CREDIT_BANK_GUAGNXI;
                break;
            // 民生
            case BankConstant.PURCHASE_PARTY_CMBC:
            case BankConstant.PURCHASE_PARTY_CMBC_CREDIT_BANK_WANG_LU_ZL:
            case BankConstant.PURCHASE_PARTY_CMBC_CREDIT_CARD_GUANGXI:
            case BankConstant.PURCHASE_PARTY_CMBC_CREDIT_CARD_JSUTONG:
            case BankConstant.PURCHASE_PARTY_CMBC_CREDIT_CARD_NEIMENG:
                bank = BankConstant.PURCHASE_PARTY_CMBC;
                break;
            default:
                bank = whichBank;
        }
        return bank;
    }

    public void manualActivate(CreditCardActivateDTO activateDTO) {
        ActivityCreditCardUsersInfoEntity usersInfo = activityCreditCardUsersInfoService.getById(activateDTO.getId());
        if (ObjectUtil.isNull(usersInfo)) {
            ToolsHelper.throwException("未找到信用卡申请记录");
        }


        // 检查银行是否支持
        if (!creditCardConfig.getManualActivateBankId().contains(usersInfo.getWhichBank())) {
            ToolsHelper.throwException("此银行暂不支持人工置为激活");
        }
        if(usersInfo.getStatus().equals(ActivityCreditCardUserInfoStatusEnum.STATUS_FAIL_AUDIT.getCode())){
            ToolsHelper.throwException("审核不通过状态不可激活");
        }
        // 没有关联单号不允许激活
        if(ObjectUtil.isEmpty(usersInfo.getReferSn())){
            ToolsHelper.throwException("未找到关联的订单记录，不允许激活");
        }

        ActivityCreditCardBankUsersEntity bankUsers = activityCreditCardBankUsersService.getOneByOrderSn(usersInfo.getOrderSn());
        if (ObjectUtil.isNull(bankUsers)) {
            ToolsHelper.throwException("银行记录为空");
        }

        // 已激活
        if (bankUsers.getActivated().equals(ActivityCreditCardBankUserActivatedEnum.STATUS_ACTIVATED_YES.getCode())) {
            return;
        }

        try {
            // 更新用户信息表数据,审核通过
            this.userInfoAuditPass(usersInfo, citicCreditBankConfig.getInnerVersion());
            // 更新激活时间、激活状态
            ActivityCreditCardBankUsersEntity updateBankUser = new ActivityCreditCardBankUsersEntity();
            updateBankUser.setId(bankUsers.getId());
            updateBankUser.setActivateTime(activateDTO.getActivateTime());
            updateBankUser.setActivated(ActivityCreditCardBankUserActivatedEnum.STATUS_ACTIVATED_YES.getCode());
            updateBankUser.setIsNewUser(ActivityCreditCardBankUserIsNewUserEnum.NEW_USER_YES.getCode());

            // 更新补充审核相关字段
            if (!bankUsers.getAuditNewUser().equals(BankUserAuditNewUserEnum.NEW_USER_YES.getCode())) {
                updateBankUser.setAuditNewUser(BankUserAuditNewUserEnum.NEW_USER_YES.getCode());
            }
            // 更新审核状态
            if (!bankUsers.getStatus().equals(ActivityCreditCardBankUserStatusEnum.STATUS_PASS_AUDIT.getCode())) {
                updateBankUser.setStatus(ActivityCreditCardBankUserStatusEnum.STATUS_PASS_AUDIT.getCode());
            }
            // 审核时间补充
            if (ObjectUtil.isNull(bankUsers.getFirstAuditTime())) {
                updateBankUser.setAuditTime(activateDTO.getActivateTime());
            }

            activityCreditCardBankUsersService.updateById(updateBankUser);

            // 发送激活事件
            creditCardBaseBusiness.creditCardActivateNotify(
                    usersInfo.getUid(),
                    usersInfo.getWhichBank(),
                    usersInfo.getClassify(),
                    bankUsers.getApplyNumber());

            // 记录激活日志
            String content = "人工置为激活时间：" + activateDTO.getActivateTime().format(DateTimeFormatter.ofPattern("yyyy-MM" +
                    "-dd HH:mm:ss")) +
                    "\n操作备注：" + activateDTO.getRemark();
            CreditCardLogBO logBO = new CreditCardLogBO();
            logBO.setOrderSn(usersInfo.getOrderSn());
            logBO.setOperateType(CreditCardOperateLogTypeEnum.TYPE_ACTIVATE.getValue());
            logBO.setOperateContent(content);
            logBO.setOperator(RequestHelper.getAdminOperator());
            activityCreditCardLogService.addLog(logBO);
        } catch (Throwable e) {
            log.error("信用卡置为激活操作异常：{}", e.getMessage());
            ToolsHelper.throwException("操作失败，请稍后重试");
        }
    }

    public IPage<CreditCardLogVO> getLog(CreditCardLogDTO logDTO) {
        return activityCreditCardLogService.getLogByOrderSn(logDTO)
                .convert(logList -> BeanUtil.copyProperties(logList, CreditCardLogVO.class));
    }


    /**
     * 银行状态信息
     *
     * @param dto dto
     * @return {@link CreditCardBankInfoVO}
     */
    public CreditCardBankInfoVO bankStatus(CreditCardBankInfoDTO dto) {
        // 查找申请单
        ActivityCreditCardUsersInfoEntity usersInfoEntity = activityCreditCardUsersInfoService.getOneByReferSnAndWhichBank(dto.getReferType(), dto.getReferSn(), dto.getWhichBank());
        //
        if (ObjectUtil.isNull(usersInfoEntity)) {
            return null;
        }

        // 查找银行单
        ActivityCreditCardBankUsersEntity bankUsersEntity = activityCreditCardBankUsersService.getOneByColumn(usersInfoEntity.getOrderSn(), ActivityCreditCardBankUsersEntity::getApplyNumber);
        if (ObjectUtil.isNull(bankUsersEntity)) {
            return null;
        }

        CreditCardBankInfoVO bankInfoVO = new CreditCardBankInfoVO();
        BeanUtils.copyProperties(bankUsersEntity, bankInfoVO);
        bankInfoVO.setUid(usersInfoEntity.getUid());
        bankInfoVO.setReferSn(usersInfoEntity.getReferSn());
        bankInfoVO.setReferType(usersInfoEntity.getReferType());
        bankInfoVO.setOrderSn(usersInfoEntity.getOrderSn());
        return bankInfoVO;

    }

    /**
     * 通过手机号码判断是否新户
     */
    public CheckNewUserByPhoneVO checkNewUserByPhone(CheckByPhoneDTO dto) {
        return CreditCardFactory.create(dto.getWhichBank()).checkNewUserByPhone(dto);
    }


    /**
     * 查找已首刷并且有首刷时间的信用卡记录，激活状态或者激活时间是空
     */
    public void activateFieldsCompensation(LocalDateTime startTime, LocalDateTime endTime) {

        // 查找首刷并且首刷时间不是'1970-01-01' 的数据，-- 已首刷并且有首刷时间，未激活或者没有激活时间
        List<ActivityCreditCardBankUsersEntity> dataList = activityCreditCardBankUsersService.getActivateCompensationData(startTime, endTime);
        if (!CollectionUtils.isEmpty(dataList)) {
            dataList.forEach(bankInfo -> {
                try {
                    //首刷状态不是已首刷或者 首刷时间是null  则不处理 -- 依赖首刷时间已处理
                    if (ActivityCreditCardBankUserIsFirstUseEnum.FIRST_USE_YES.getCode() != bankInfo.getIsFirstUse() || ObjectUtil.isNull(bankInfo.getFirstUseTime())) {
                        log.info(bankInfo.getApplyNumber() + " 首刷状态不是已首刷或者 首刷时间是null  则不处理");
                        return;
                    }

                    // 激活状态并且激活时间都正常则不处理
                    if (ObjectUtil.isNotNull(bankInfo.getActivateTime()) && bankInfo.getActivated() == ActivityCreditCardBankUserActivatedEnum.STATUS_ACTIVATED_YES.getCode()) {
                        log.info(bankInfo.getApplyNumber() + " 激活状态并且激活时间都正常则不处理");
                        return;
                    }
                    String content = "激活数据补偿处理:";
                    LambdaUpdateWrapper<ActivityCreditCardBankUsersEntity> bankUsersEntityLambdaUpdateWrapper = new LambdaUpdateWrapper<>();

                    bankUsersEntityLambdaUpdateWrapper.eq(ActivityCreditCardBankUsersEntity::getId, bankInfo.getId());
                    // 补充激活状态
                    if (bankInfo.getActivated() != ActivityCreditCardBankUserActivatedEnum.STATUS_ACTIVATED_YES.getCode()) {
                        bankUsersEntityLambdaUpdateWrapper.set(ActivityCreditCardBankUsersEntity::getActivated, ActivityCreditCardBankUserActivatedEnum.STATUS_ACTIVATED_YES.getCode());
                        content = content + "原激活状态：" + bankInfo.getActivated() + "修改为：" + ActivityCreditCardBankUserActivatedEnum.STATUS_ACTIVATED_YES.getCode();
                    }
                    // 补充激活时间
                    if (ObjectUtil.isNull(bankInfo.getActivateTime())) {
                        bankUsersEntityLambdaUpdateWrapper.set(ActivityCreditCardBankUsersEntity::getActivateTime, bankInfo.getFirstUseTime());
                        content = content + "原激活时间：" + bankInfo.getActivateTime() + "修改为：" + DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(bankInfo.getFirstUseTime());

                    }

                    //更新处理
                    activityCreditCardBankUsersService.updateByWrapper(bankUsersEntityLambdaUpdateWrapper);

                    // 增加处理日志
                    CreditCardLogBO logBO = new CreditCardLogBO();
                    logBO.setOrderSn(bankInfo.getApplyNumber());
                    logBO.setOperateType(CreditCardOperateLogTypeEnum.TYPE_ACTIVATE.getValue());
                    logBO.setOperateContent(content);
                    creditCardLogService.addLog(logBO);

                } catch (Exception e) {
                    log.info("activateFieldsCompensation 处理数据失败：" + bankInfo.getApplyNumber(), e);
                }
            });

        }

    }

    public void auditFieldsCompensation(LocalDateTime startTime, LocalDateTime endTime) {
        // 已激活并且有激活时间，未审核或者没有审核时间 --689
        List<ActivityCreditCardBankUsersEntity> dataList = activityCreditCardBankUsersService.getAuditCompensationData(startTime, endTime);
        if (!CollectionUtils.isEmpty(dataList)) {
            dataList.forEach(bankInfo -> {
                try {
                    Boolean statusChangeNotify = false;
                    //激活状态是未激活或者激活时间是空，不进行审核数据补偿处理
                    if (ActivityCreditCardBankUserActivatedEnum.STATUS_ACTIVATED_YES.getCode() != bankInfo.getActivated() || ObjectUtil.isNull(bankInfo.getActivateTime())) {
                        log.info(bankInfo.getApplyNumber() + " 激活状态是未激活或者激活时间是空  则不处理审核数据补偿");
                        return;
                    }

                    // 审核状态并且审核时间都正常则不处理
                    if (ObjectUtil.isNotNull(bankInfo.getAuditTime()) && Arrays.asList(ActivityCreditCardBankUserStatusEnum.STATUS_FAIL_AUDIT.getCode(),
                            ActivityCreditCardBankUserStatusEnum.STATUS_PASS_AUDIT.getCode()).contains(bankInfo.getStatus())) {
                        log.info(bankInfo.getApplyNumber() + " 审核状态并且审核时间都正常则不处理审核数据补偿");
                        return;
                    }
                    String content = "审核数据补偿处理:";
                    LambdaUpdateWrapper<ActivityCreditCardBankUsersEntity> bankUsersEntityLambdaUpdateWrapper = new LambdaUpdateWrapper<>();

                    bankUsersEntityLambdaUpdateWrapper.eq(ActivityCreditCardBankUsersEntity::getId, bankInfo.getId());
                    // 补充审核状态
                    if (bankInfo.getStatus() != ActivityCreditCardBankUserStatusEnum.STATUS_PASS_AUDIT.getCode()) {
                        bankUsersEntityLambdaUpdateWrapper.set(ActivityCreditCardBankUsersEntity::getStatus, ActivityCreditCardBankUserStatusEnum.STATUS_PASS_AUDIT.getCode());
                        content = content + "原审核状态：" + bankInfo.getStatus() + "修改为：" + ActivityCreditCardBankUserStatusEnum.STATUS_PASS_AUDIT.getCode();
                        statusChangeNotify = true;
                    }
                    // 补充审核时间
                    if (ObjectUtil.isNull(bankInfo.getAuditTime())) {
                        bankUsersEntityLambdaUpdateWrapper.set(ActivityCreditCardBankUsersEntity::getAuditTime, bankInfo.getActivateTime());
                        content = content + "原审核时间：" + bankInfo.getAuditTime() + "修改为：" + DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(bankInfo.getActivateTime());

                    }

                    //更新处理
                    activityCreditCardBankUsersService.updateByWrapper(bankUsersEntityLambdaUpdateWrapper);

                    // 增加处理日志
                    CreditCardLogBO logBO = new CreditCardLogBO();
                    logBO.setOrderSn(bankInfo.getApplyNumber());
                    logBO.setOperateType(CreditCardOperateLogTypeEnum.TYPE_AUDIT.getValue());
                    logBO.setOperateContent(content);
                    creditCardLogService.addLog(logBO);

                    if(statusChangeNotify){
                        ActivityCreditCardUsersInfoEntity usersInfoEntity = activityCreditCardUsersInfoService.getOneByColumn( bankInfo.getApplyNumber(), ActivityCreditCardUsersInfoEntity::getOrderSn);
                        if (usersInfoEntity == null) {
                            log.info("发起信用卡审核事件失败，无对应申请用户信息" + bankInfo.getApplyNumber());
                            return;
                        }
                        // 发起审核通过通知
                        creditCardBaseBusiness.creditCardAuditNotify(usersInfoEntity.getUid(),
                                usersInfoEntity.getReferType(),
                                usersInfoEntity.getClassify(),
                                bankInfo.getApplyNumber());
                    }
                } catch (Exception e) {
                    log.info("activateFieldsCompensation 处理数据失败：" + bankInfo.getApplyNumber(), e);
                }
            });

        }

    }

    public void submitFieldsCompensation(LocalDateTime startTime, LocalDateTime endTime) {
        // --已审核并且有审核时间，未进件或者没有进件时间--178634
        List<ActivityCreditCardBankUsersEntity> dataList = activityCreditCardBankUsersService.getSubmitCompensationData(startTime, endTime);
        if (!CollectionUtils.isEmpty(dataList)) {
            dataList.forEach(bankInfo -> {
                try {
                    //审核状态是未审核或者审核时间是null
                    if (ActivityCreditCardBankUserStatusEnum.STATUS_NOT_AUDIT.getCode() == bankInfo.getStatus() || ObjectUtil.isNull(bankInfo.getAuditTime())) {
                        log.info(bankInfo.getApplyNumber() + " 审核状态是未审核或者审核时间是null  则不处理审核数据补偿");
                        return;
                    }

                    // 激活状态并且激活时间都正常则不处理
                    if (ObjectUtil.isNotNull(bankInfo.getSubmitTime()) && bankInfo.getApplyCompleted() != ActivityCreditCardBankUserApplyCompleteEnum.APPLY_DEFAULT.getCode()) {
                        log.info(bankInfo.getApplyNumber() + " 进件状态并且进件时间都正常则不处理");
                        return;
                    }
                    String content = "进件数据补偿处理:";
                    LambdaUpdateWrapper<ActivityCreditCardBankUsersEntity> bankUsersEntityLambdaUpdateWrapper = new LambdaUpdateWrapper<>();

                    bankUsersEntityLambdaUpdateWrapper.eq(ActivityCreditCardBankUsersEntity::getId, bankInfo.getId());
                    // 补充进件状态
                    if (bankInfo.getApplyCompleted() == ActivityCreditCardBankUserApplyCompleteEnum.APPLY_DEFAULT.getCode()) {
                        bankUsersEntityLambdaUpdateWrapper.set(ActivityCreditCardBankUsersEntity::getApplyCompleted, ActivityCreditCardBankUserApplyCompleteEnum.APPLY_COMPLETED.getCode());
                        content = content + "原进件状态：" + bankInfo.getApplyCompleted() + "修改为：" + ActivityCreditCardBankUserApplyCompleteEnum.APPLY_COMPLETED.getCode();
                    }
                    // 补充进件时间
                    if (ObjectUtil.isNull(bankInfo.getSubmitTime())) {
                        bankUsersEntityLambdaUpdateWrapper.set(ActivityCreditCardBankUsersEntity::getSubmitTime, bankInfo.getAuditTime());
                        content = content + "原进件时间：" + bankInfo.getSubmitTime() + "修改为：" + DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(bankInfo.getAuditTime());

                    }

                    //更新处理
                    activityCreditCardBankUsersService.updateByWrapper(bankUsersEntityLambdaUpdateWrapper);

                    // 增加处理日志
                    CreditCardLogBO logBO = new CreditCardLogBO();
                    logBO.setOrderSn(bankInfo.getApplyNumber());
                    logBO.setOperateType(CreditCardOperateLogTypeEnum.TYPE_APPLY.getValue());
                    logBO.setOperateContent(content);
                    creditCardLogService.addLog(logBO);

                } catch (Exception e) {
                    log.info("submitFieldsCompensation 处理数据失败：" + bankInfo.getApplyNumber(), e);
                }
            });

        }
    }



    /**
     * 如果是申办订单模式，绑定已有的申请单号
     */
    public Boolean checkAndBindReferSn(ApplyOrderDTO applyOrderDTO) {
        if(StringUtil.isNullOrEmpty(applyOrderDTO.getReferSn())){
            return false;
        }
        // 申办类型的才进行绑定
        if(!applyOrderDTO.getClassify().equals(ActivityCreditCardUserInfoClassifyEnum.TYPE_NEW_APPLY.getCode())){
            return false;
        }
        //查找是否存在正常申请当中的记录
        ActivityCreditCardUsersInfoEntity processingApplyEntity = activityCreditCardUsersInfoService.getProcessingApply(
            applyOrderDTO.getUid(),
            applyOrderDTO.getWhichBank(),
            applyOrderDTO.getClassify(),
            applyOrderDTO.getReferType(),
            applyOrderDTO.getReferSn(),
            applyOrderDTO.getVersion()
        );
        //不存在进行中的申请
        if(processingApplyEntity == null){
            return true;
        }
        //存在进行中的申请
        if(processingApplyEntity.getReferSn().equals(applyOrderDTO.getReferSn())){
            ToolsHelper.throwException("已申请过信用卡，请刷新页面查看办理进度", ServerErrorCodeEnum.SERVICE_ERROR_CODE_CREDIT_CARD_APPLIED.getCode());
        }
        //refer_sn = ''
        if(StringUtil.isNullOrEmpty(processingApplyEntity.getReferSn())){
            processingApplyEntity.setReferSn(applyOrderDTO.getReferSn());
            processingApplyEntity.setPlateNo(applyOrderDTO.getPlateNo());
            activityCreditCardUsersInfoService.updateById(processingApplyEntity);
            ToolsHelper.throwException("已申请过信用卡，请刷新页面查看办理进度", ServerErrorCodeEnum.SERVICE_ERROR_CODE_CREDIT_CARD_APPLIED.getCode());
        }
        //refer_sn！= 申请的
        ToolsHelper.throwException("你已有1个进行中的" + ActivityCreditCardUserInfoWhichBankEnum.getDescByCode(applyOrderDTO.getWhichBank()) + "银行补贴套餐订单，请先完成该订单",ServerErrorCodeEnum.SERVICE_ERROR_CODE_CREDIT_CARD_APPLIED.getCode());
        return true;
    }

    public void userInfoAuditPass(ActivityCreditCardUsersInfoEntity activityCreditCardUsersInfoEntity,
                                  String innerVersion) {
        // 重复通知状态未变化不进行修改
        // 未审核、无需审核、审核失败、等待审核状态允许通知审核通过
        if (!Arrays.asList(
                ActivityCreditCardUserInfoStatusEnum.STATUS_CANCEL.getCode(),
                ActivityCreditCardUserInfoStatusEnum.STATUS_NOT_AUDIT.getCode(),
                ActivityCreditCardUserInfoStatusEnum.STATUS_FAIL_AUDIT.getCode(),
                ActivityCreditCardUserInfoStatusEnum.STATUS_WAIT.getCode()
        ).contains(activityCreditCardUsersInfoEntity.getStatus())) {
            return;
        }
        // 允许修改的状态才做变更
        LambdaUpdateWrapper<ActivityCreditCardUsersInfoEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ActivityCreditCardUsersInfoEntity::getId, activityCreditCardUsersInfoEntity.getId())
                .set(ActivityCreditCardUsersInfoEntity::getStatus,
                        ActivityCreditCardUserInfoStatusEnum.STATUS_PASS_AUDIT.getCode())
                .set(ActivityCreditCardUsersInfoEntity::getInnerVersion, innerVersion) // 新版本接口内部版本补偿记录
                .set(ActivityCreditCardUsersInfoEntity::getFailVerifyReason, "");
        activityCreditCardUsersInfoService.updateByWrapper(wrapper);

    }


    public List<CheckCanApplyVO> checkCanApply(CheckCanApply checkCanApply) {
        // 根据银行返回初始化信息
        if(CollectionUtil.isEmpty(checkCanApply.getWhichBankList())){
            return CollectionUtil.newArrayList();
        }
        String city;
        if(ObjectUtil.isEmpty(checkCanApply.getCity()) && YesOrNoEnum.YES.getCode().equals(checkCanApply.getAutoFillCity())){
            city = this.getUserBankCity(checkCanApply.getUid());
        }else{
            city = checkCanApply.getCity();
        }

        List<CheckCanApplyVO> resultList = CollectionUtil.newArrayList();
        // 根据whichBankList 初始化需要返回的数据
        for(Integer whichBank: checkCanApply.getWhichBankList()){
            CheckCanApplyVO checkCanApplyVO = new CheckCanApplyVO();
            checkCanApplyVO.setWhichBank(whichBank);
            checkCanApplyVO.setCanApply(checkBankCanApply(whichBank, city) ?
                    YesOrNoEnum.YES.getCode() :
                    YesOrNoEnum.NO.getCode());
            checkCanApplyVO.setUid(checkCanApply.getUid());
            resultList.add(checkCanApplyVO);
        }

        return resultList;

    }

    /**
     * 检查银行是否能办理
     */
    public CreditCardLimitBankVO checkCreditCardLimit(CreditCardLimitDTO creditCardLimitDTO) {
        CreditCardLimitBankVO creditCardLimitBankVO = new CreditCardLimitBankVO();
        List<CreditCardLimitBankVO.BankLimitStatus> bankLimitStatusList = new ArrayList<>();
        for (Integer whichBank : creditCardLimitDTO.getWhichBankList()) {
            CreditCardLimitBankVO.BankLimitStatus bankLimitStatus = new CreditCardLimitBankVO.BankLimitStatus();
            bankLimitStatus.setWhichBank(whichBank);
            bankLimitStatus.setLimitStatus(checkBankCanApply(whichBank, creditCardLimitDTO.getCity()) ?
                    YesOrNoEnum.YES.getCode() :
                    YesOrNoEnum.NO.getCode());
            bankLimitStatusList.add(bankLimitStatus);
        }
        creditCardLimitBankVO.setBankLimitStatus(bankLimitStatusList);
        return creditCardLimitBankVO;
    }

    /**
     * 检查银行是否能办理 :是否上架、是否在城市范围内
     */
    public boolean checkBankCanApply(Integer whichBank, String city) {
        // 获取到对应银行的城市地址枚举
        BankCityEnum bankCityEnum = BankCityEnum.getByCode(whichBank);
        if (bankCityEnum == null) {
            return false;
        }
        List<Integer> normalBankIds = creditCardBankConfigService.getBankIdListByBankStatus(
                CreditCardBankConfigBankStatusEnum.NORMAL.getStatus()
        );

        if (!normalBankIds.contains(whichBank)) {
            return false;
        }


        if (ObjectUtil.isNotEmpty(city) && !bankCityEnum.getCityList().contains(city)) {
            return false;
        }
        return true;

    }

    /**
     * 获取信用卡信息
     * @param dto
     * @return
     */
    public CreditCardBankInfoVO bankInfo(CreditCardBankInfoV2DTO dto) {
        // 查找申请单
        ActivityCreditCardUsersInfoEntity usersInfoEntity =
                activityCreditCardUsersInfoService.getOneByUidAndWhichBank(dto.getUid(), dto.getReferSn(),
                        dto.getWhichBank());
        //
        if (ObjectUtil.isNull(usersInfoEntity)) {
            return null;
        }

        // 查找银行单
        ActivityCreditCardBankUsersEntity bankUsersEntity = activityCreditCardBankUsersService.getOneByColumn(usersInfoEntity.getOrderSn(), ActivityCreditCardBankUsersEntity::getApplyNumber);
        if (ObjectUtil.isNull(bankUsersEntity)) {
            return null;
        }

        CreditCardBankInfoVO bankInfoVO = new CreditCardBankInfoVO();
        BeanUtils.copyProperties(bankUsersEntity, bankInfoVO);
        bankInfoVO.setUid(usersInfoEntity.getUid());
        bankInfoVO.setReferSn(usersInfoEntity.getReferSn());
        bankInfoVO.setReferType(usersInfoEntity.getReferType());
        bankInfoVO.setOrderSn(usersInfoEntity.getOrderSn());
        return bankInfoVO;

    }

    public void bindByParams(BindByParamsDTO dto) {
        // 检查是否存在同个银行，同个办理单号的进行中的申请单
        //存在其他进行中的申请
        if (activityCreditCardUsersInfoService.checkProcessingApply(
                dto.getUid(),
                dto.getWhichBank(),
                dto.getReferType(),
                dto.getReferSn())) {
            ToolsHelper.throwException("你已有1个进行中的" + ActivityCreditCardUserInfoWhichBankEnum.getDescByCode(dto.getWhichBank()) + "银行补贴套餐订单，请先完成该订单", ServerErrorCodeEnum.SERVICE_ERROR_CODE_CREDIT_CARD_APPLIED.getCode());
        }
    }

    /**
     * 解除申请单关联
     * @param dto
     */
    public void cleanReferSn(UnbindByParamsDTO dto) {

        // 获取同个referSn 下所有的信用卡申请单
        List<ActivityCreditCardUsersInfoEntity> usersInfoEntities =
                activityCreditCardUsersInfoService.getAllByUidAndReferSn(dto.getUid(), dto.getReferSn());
        if(CollectionUtil.isEmpty(usersInfoEntities)){
            return;
        }
        usersInfoEntities.forEach(usersInfoEntity -> {
            // 把referSn 设置为空
            if(!Objects.equals(usersInfoEntity.getStatus(), ActivityCreditCardUserInfoStatusEnum.STATUS_QUALIFICATION_FINISHED.getCode())){
                String content = "解除绑定关联，referSn:" + usersInfoEntity.getReferSn();
                usersInfoEntity.setReferSn("");
                activityCreditCardUsersInfoService.updateById(usersInfoEntity);
//                usersInfoEntity.updateById();
                // 增加处理日志
                CreditCardLogBO logBO = new CreditCardLogBO();
                logBO.setOrderSn(usersInfoEntity.getOrderSn());
                logBO.setOperateType(CreditCardOperateLogTypeEnum.TYPE_ACTION_RELEASE.getValue());
                logBO.setOperateContent(content);
                creditCardLogService.addLog(logBO);
            }
        });
    }

    /**
     * 信用卡城市带默认地址
     */
    public String getUserBankCity(Long uid) {
        String city = bigDataService.getUserCity(uid);
        if (StringUtils.isEmpty(city)) {
            city = commonCreditBankConfig.getDefaultCity();
        }
        return city;
    }
}
