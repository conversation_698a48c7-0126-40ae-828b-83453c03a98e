package com.ets.apply.application.common.consts.map;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MapRecommendTypeEnum {
    MAP_RECOMMEDN_TYPE_ENUM_CARDID("plateNo", "车牌推荐"),
    MAP_RECOMMEDN_TYPE_ENUM_PROVINCE("province", "省份地区推荐");
    private final String code;
    private final String description;

    public static String getDescByCode(String code) {
        for (MapRecommendTypeEnum node : MapRecommendTypeEnum.values()) {
            if (node.getCode().equals(code) ) {
                return node.getDescription();
            }
        }
        return "";
    }
    public static MapRecommendTypeEnum getByCode(String code) {
        for (MapRecommendTypeEnum node : MapRecommendTypeEnum.values()) {
            if (node.getCode().equals(code)) {
                return node;
            }
        }
        return null;
    }
}
