package com.ets.apply.application;

import com.ets.apply.application.app.service.thirdPartner.XconnectService;
import com.ets.apply.application.infra.entity.ProductOrderEntity;
import com.ets.apply.application.infra.service.ProductOrderService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;




@SpringBootTest(classes = ApplyApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringRunner.class)
@ActiveProfiles(value = "dev")
@Slf4j
@RefreshScope
public class XconnectTest {
    @Autowired
    private XconnectService xconnectService;
    @Autowired
    private ProductOrderService productOrderService;

    @Test
    public void saveOrder() {
        try {
            ProductOrderEntity orderEntity = productOrderService.getByProductOrderSn("2507071123000002281");
            xconnectService.etcOrderSave(orderEntity);
        } catch (Exception e) {
            System.out.println("999:"+e.getStackTrace());
        }

    }

}
