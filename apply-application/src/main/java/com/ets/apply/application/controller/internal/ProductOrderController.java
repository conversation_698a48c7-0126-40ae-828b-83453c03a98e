package com.ets.apply.application.controller.internal;

import com.ets.apply.application.app.business.ProductOrderBusiness;
import com.ets.apply.application.common.dto.productOrder.NotifyActivateDTO;
import com.ets.apply.application.common.dto.productOrder.ProductOrderByOrderSnDTO;
import com.ets.apply.application.common.dto.productOrder.ProductOrderGetFinishOrderDTO;
import com.ets.apply.application.infra.entity.ProductOrderEntity;
import com.ets.apply.application.infra.service.ProductOrderService;
import com.ets.apply.feign.response.ProductOrderResponse;
import com.ets.common.JsonResult;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Controller
@RefreshScope
@RestController
@RequestMapping("/product-order")
public class ProductOrderController {

    @Autowired
    private ProductOrderBusiness productOrderBusiness;
    @Autowired
    private ProductOrderService productOrderService;
    @RequestMapping("/notify-activate")
    public JsonResult<?> notifyActivate(@RequestBody @Valid NotifyActivateDTO notifyActivateDTO) {
        productOrderBusiness.activateNotify(notifyActivateDTO);
        return JsonResult.ok();
    }

    /*
     *  通过订单号获取到电商订单详情
     */
    @RequestMapping("/getInfoByApplyOrderSn")
    public JsonResult<ProductOrderEntity> getInfoByApplyOrderSn(@RequestBody @Valid ProductOrderByOrderSnDTO dto) {
        return JsonResult.ok(productOrderService.findByOrderSnAndSource(dto));
    }

    /**
     * 发票获取已完成订单信息
     * @param getFinishOrderDTO 参数uid
     * @return 已完成订单信息
     */
    @PostMapping("/get-finished-orders")
    public JsonResult<List<ProductOrderResponse>> getFinishedOrders(@RequestBody @Valid ProductOrderGetFinishOrderDTO getFinishOrderDTO) {
        return JsonResult.ok(productOrderBusiness.getFinishedOrders(getFinishOrderDTO));
    }
    /**
     * 通过商城订单号和uid获取商城订单
     * @param productOrderSn 商城订单号
     * @param uid 用户uid
     * @return 商城订单
     */
    @RequestMapping("/get-product-order")
    public JsonResult<ProductOrderResponse> getProductOrder(@RequestParam(value = "productOrderSn") String productOrderSn, @RequestParam(value = "uid") Long uid) {
        return JsonResult.ok(productOrderBusiness.getByProductOrderSnAndUid(productOrderSn,uid));
    }
}
