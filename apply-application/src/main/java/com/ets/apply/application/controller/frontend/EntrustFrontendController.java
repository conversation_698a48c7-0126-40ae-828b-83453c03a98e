package com.ets.apply.application.controller.frontend;

import com.ets.apply.application.app.business.entrust.EntrustBusiness;
import com.ets.apply.application.common.dto.request.entrust.CheckByOrderSnAndBankIdDTO;
import com.ets.apply.application.common.vo.entrust.EntrustCheckVO;
import com.ets.apply.application.controller.BaseController;
import com.ets.common.JsonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

@RestController
@RequestMapping("/frontend/entrust")
public class EntrustFrontendController extends BaseController {

    @Autowired
    private EntrustBusiness entrustBusiness;
    /*
     * 通过订单号和银行ID，查询签约状态
     */
    @PostMapping("/checkSignByOrderSnAndBankId")
    public JsonResult<EntrustCheckVO> checkSignByOrderSnAndBankId(@RequestBody @Valid CheckByOrderSnAndBankIdDTO dto) {
        return JsonResult.ok(entrustBusiness.checkIsSignByOrderSnAndBankId(dto));
    }

    /*
     * 通过订单号和银行ID，发起0.01扣费
     */
    @PostMapping("/sendBindVerification")
    public JsonResult<Boolean> sendBindVerification(@RequestBody @Valid CheckByOrderSnAndBankIdDTO dto) {
        return JsonResult.ok(entrustBusiness.sendBindVerification(dto));
    }
}
