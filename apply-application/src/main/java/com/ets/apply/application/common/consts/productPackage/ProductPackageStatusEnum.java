package com.ets.apply.application.common.consts.productPackage;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum ProductPackageStatusEnum {

    NORMAL(1, "有效"),
    FAIL(2, "无效");

    private final Integer value;
    private final String desc;

    public static String getDescByCode(int code) {
        for (ProductPackageStatusEnum node : ProductPackageStatusEnum.values()) {
            if (node.getValue().equals(code)) {
                return node.getDesc();
            }
        }
        return "";
    }

    public static List<Map<String, String>> getLabelList() {
        List<Map<String, String>> list = new ArrayList<>();

        for (ProductPackageStatusEnum node : ProductPackageStatusEnum.values()) {
            Map<String, String> row = new LinkedHashMap<>();
            row.put("label", node.getDesc());
            row.put("value", node.getValue().toString());

            list.add(row);
        }
        return list;
    }
}

